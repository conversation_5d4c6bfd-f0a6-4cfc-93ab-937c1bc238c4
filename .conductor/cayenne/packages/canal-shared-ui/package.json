{"name": "@ad/canal-shared-ui", "version": "2.6.3", "description": "大运河内的前端共享代码", "repository": {"type": "git", "url": "*************************:ks-ad/ad-fe/grandcanal/canal.git"}, "license": "MIT", "author": "AD", "sideEffects": false, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && rollup --config rollup.config.ts --configPlugin typescript", "start": "rollup --config rollup.config.ts --configPlugin typescript --watch", "test": "jest --passWithNoTests", "test:w": "jest --watch"}, "dependencies": {"@ad/canal-shared": "workspace:^", "@types/lodash": "^4.14.199", "@types/ua-parser-js": "^0.7.38", "lodash": "^4.17.21", "react-use": "^17.4.0", "ua-parser-js": "^1.0.36"}, "devDependencies": {"@m-ui/react": "^1.12.2", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.4", "@types/jest": "^29.5.5", "@types/react": "^18.2.23", "jest": "^29.7.0", "react": "^18.2.0", "rollup": "^3.29.3", "rollup-plugin-exclude-dependencies-from-bundle": "1.1.23", "ts-jest": "^29.1.1"}, "peerDependencies": {"@m-ui/react": ">=1.0.0", "react": ">=16.8.0"}}