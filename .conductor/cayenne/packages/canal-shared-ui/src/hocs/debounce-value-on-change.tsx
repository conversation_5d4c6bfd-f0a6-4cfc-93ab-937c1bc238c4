/* eslint-disable react-hooks/rules-of-hooks */
import { debounce } from 'lodash'
import React, {
  FC,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useLatest } from 'react-use'

/**
 * value + onChange 属性
 */
export interface ValueOnChangeProps<T = unknown> {
  /**
   * 值
   */
  value?: T
  /**
   * 值变化事件
   * @param newValue 新的值
   */
  onChange?: (newValue: T) => void
}

/**
 * value + onChange 防抖
 * @param Comp 组件
 * @param wait 防抖延时，默认 500 毫秒
 */
export function debounceValueOnChange<T>(
  Comp: FC<ValueOnChangeProps<T>>,
  wait = 500,
): FC<ValueOnChangeProps<T>> {
  return memo((({
    value: outsideValue,
    onChange: outsideOnChange,
    ...restProps
  }) => {
    const refSubmittedValue = useRef(outsideValue)
    const refLatestOutsideOnChange = useLatest(outsideOnChange)
    const debounceOutsideOnChange = useMemo(
      () =>
        debounce((newValue: T) => {
          refSubmittedValue.current = newValue
          refLatestOutsideOnChange.current?.(newValue)
        }, wait),
      [refLatestOutsideOnChange],
    )
    const [innerValue, setInnerValue] = useState(outsideValue)
    const innerOnChange = useCallback(
      (newValue: T) => {
        setInnerValue(newValue)
        debounceOutsideOnChange(newValue)
      },
      [debounceOutsideOnChange],
    )
    useEffect(() => {
      if (outsideValue !== refSubmittedValue.current) {
        refSubmittedValue.current = outsideValue
        setInnerValue(outsideValue)
      }
    }, [outsideValue])
    return <Comp {...restProps} value={innerValue} onChange={innerOnChange} />
  }) as FC<ValueOnChangeProps<T>>)
}
