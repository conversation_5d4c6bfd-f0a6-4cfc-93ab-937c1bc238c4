import { RuleObject } from '@m-ui/react/lib/form'
import { isNil, isNumber, isString } from 'lodash'
import {
  JSONRuleCreator,
  JSONRuleLength,
  JSONRuleNumber,
  JSONRuleRegExp,
  JSONRuleRequired,
  JSONRuleSpecialString,
  JSONRuleType,
} from './types'

/**
 * JSON 规则创建器
 */
export const jsonRuleCreators = {
  required(r: JSONRuleRequired) {
    return {
      required: true,
      message: r.errMsg,
    }
  },
  number(r: JSONRuleNumber) {
    const validator: RuleObject['validator'] = async (...[, v]) => {
      if (isNil(v)) {
        return
      }
      if (isNumber(v)) {
        v = `${v}`
      }
      if (!isString(v)) {
        throw r.errMsg || '必须为数字'
      }
      if (!v) {
        return
      }
      const n = Number(v)
      if (isNaN(n)) {
        throw r.errMsg || '必须为数字'
      }
      if (isNumber(r.min)) {
        if (r.equalMin) {
          if (n < r.min) {
            throw r.errMsg || `必须大于等于 ${r.min}`
          }
        } else {
          if (n <= r.min) {
            throw r.errMsg || `必须大于 ${r.min}`
          }
        }
      }
      if (isNumber(r.max)) {
        if (r.equalMax) {
          if (n > r.max) {
            throw r.errMsg || `必须小于等于 ${r.max}`
          }
        } else {
          if (n >= r.max) {
            throw r.errMsg || `必须小于 ${r.max}`
          }
        }
      }
      if (isNumber(r.precision) && r.precision >= 0) {
        const p = ~~r.precision
        const expr = p ? new RegExp(`^-?\\d+(\\.\\d{1,${p}})?$`) : /^-?\d+$/
        if (!expr.test(v)) {
          throw r.errMsg || (p ? `不能超过 ${p} 位小数` : `必须为整数`)
        }
      }
    }
    return {
      validator,
    }
  },
  length(r: JSONRuleLength) {
    const validator: RuleObject['validator'] = async (...[, v]) => {
      if (isNil(v)) {
        return
      }
      if (isNumber(v)) {
        v = `${v}`
      }
      if (!isString(v)) {
        throw r.errMsg || '必须为字符串或数字'
      }
      if (!v) {
        return
      }
      if (isNumber(r.min)) {
        if (v.length < r.min) {
          throw r.errMsg || `长度必须大于等于 ${r.min}`
        }
      }
      if (isNumber(r.max)) {
        if (v.length > r.max) {
          throw r.errMsg || `长度必须小于等于 ${r.max}`
        }
      }
    }
    return {
      validator,
    }
  },
  ['special-string'](r: JSONRuleSpecialString) {
    const validator: RuleObject['validator'] = async (...[, v]) => {
      if (isNil(v)) {
        return
      }
      if (isNumber(v)) {
        v = `${v}`
      }
      if (!isString(v)) {
        throw r.errMsg
      }
      if (!v) {
        return
      }
      switch (r.strType) {
        case 'mail': {
          if (
            !/^([A-Za-z0-9_\-.\u4e00-\u9fa5])+@([A-Za-z0-9_\-.])+\.([A-Za-z]{2,8})$/.test(
              v,
            )
          ) {
            throw r.errMsg
          }
          break
        }
        case 'china-mobile': {
          if (!/^1[3456789]\d{9}$/.test(v)) {
            throw r.errMsg
          }
          break
        }
        case 'url': {
          if (
            !/^https?:\/\/[-a-zA-Z0-9@:%._+~#=]{1,512}([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/.test(
              v,
            )
          ) {
            throw r.errMsg
          }
          break
        }
      }
    }
    return {
      validator,
    }
  },
  ['reg-exp'](r: JSONRuleRegExp) {
    return {
      pattern: new RegExp(r.expr),
      message: r.errMsg,
    }
  },
} as Record<JSONRuleType, JSONRuleCreator>
