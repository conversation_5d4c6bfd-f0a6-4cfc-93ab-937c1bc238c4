import type { Rule } from '@m-ui/react/lib/form'

/**
 * JSON 规则创建器
 */
export type JSONRuleCreator<R extends JSONRule = JSONRule> = (r: R) => Rule

/**
 * JSON 规则类型
 */
export type JSONRuleType = JSONRule['type']

/**
 * JSON 规则
 */
export type JSONRule =
  | JSONRuleRequired
  | JSONRuleNumber
  | JSONRuleLength
  | JSONRuleSpecialString
  | JSONRuleRegExp

/**
 * JSON 规则：必填
 */
export interface JSONRuleRequired {
  /**
   * 类型
   */
  type: 'required'
  /**
   * 错误信息
   */
  errMsg: string
}

/**
 * JSON 规则：数字
 */
export interface JSONRuleNumber {
  /**
   * 类型
   */
  type: 'number'
  /**
   * 错误信息
   */
  errMsg?: string
  /**
   * 最小值
   */
  min?: number
  /**
   * 可以等于最小值
   */
  equalMin?: boolean
  /**
   * 最大值
   */
  max?: number
  /**
   * 可以等于最大值
   */
  equalMax?: boolean
  /**
   * 精度，小数点个数
   */
  precision?: number
}

/**
 * JSON 规则：长度
 */
export interface JSONRuleLength {
  /**
   * 类型
   */
  type: 'length'
  /**
   * 错误信息
   */
  errMsg?: string
  /**
   * 最小长度（包括）
   */
  min?: number
  /**
   * 最大长度（包括）
   */
  max?: number
}

/**
 * JSON 规则：特殊字符串
 */
export interface JSONRuleSpecialString {
  /**
   * 类型
   */
  type: 'special-string'
  /**
   * 错误信息
   */
  errMsg: string
  /**
   * 字符串类型
   */
  strType: SpecialStringType
}

/**
 * 特殊字符串类型
 * * mail: 邮箱
 * * china-mobile: 中国手机号
 * * url: 链接
 */
export type SpecialStringType = 'mail' | 'china-mobile' | 'url'

/**
 * JSON 规则：正则表达式
 */
export interface JSONRuleRegExp {
  /**
   * 类型
   */
  type: 'reg-exp'
  /**
   * 错误信息
   */
  errMsg: string
  /**
   * 表达式
   */
  expr: string
}
