import { Promisable, sleepFrame } from '@ad/canal-shared'
import { useEffect, useState } from 'react'
import { useLatestFn } from './use-latest-fn'

/**
 * 使用等待刷新，函数返回 true 时刷新
 * @param fn 函数
 */
export function useWaitRefresh(fn: () => Promisable<boolean>): void {
  const [, setState] = useState(0)
  const f = useLatestFn(fn)
  useEffect(() => {
    ;(async (): Promise<void> => {
      while (!(await f())) {
        await sleepFrame()
      }
      setState(1)
    })()
  }, [f])
}
