import { defineConfig } from '@kmi/react'
import path from 'path'

// https://k-mi.corp.kuaishou.com/config/config.html
export default defineConfig({
  npmClient: 'pnpm',
  externals: {
    ['@ad/canal-react-component-context']: '@ad/canal-react-component-context',
    ['@ad/canal-shared-ui']: '@ad/canal-shared-ui',
    ['@m-ui/react']: '@m-ui/react',
    lodash: 'lodash',
    moment: 'moment',
    react: 'react',
    ['react/jsx-runtime']: 'react/jsx-runtime',
    ['react-use']: 'react-use',
    ['styled-components']: 'styled-components',
  },
  plugins: ['@kael/bundler-plugin-material'],
  material: {
    cwd: path.join(__dirname),
  },
}) as unknown
