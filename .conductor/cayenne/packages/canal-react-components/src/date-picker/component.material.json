{"schemaVersion": "0.0.1", "runtimeVersion": "1.3.10", "type": "DatePicker", "name": "日期选择器", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`number`", "declaration": "number", "path": ["value"], "setter": "String"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "选择器类型", "path": ["picker"], "setter": {"type": "Enum", "props": {"options": [{"label": "日（默认）", "value": "date"}, {"label": "周", "value": "week"}, {"label": "月", "value": "month"}, {"label": "季", "value": "quarter"}, {"label": "年", "value": "year"}]}}}, {"name": "显示今天", "path": ["showToday"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "显示时间", "path": ["showTime"], "setter": "Boolean"}, {"name": "显示此刻", "path": ["showNow"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "大", "value": "large"}, {"label": "中（默认）", "value": "middle"}, {"label": "小", "value": "small"}]}}}, {"name": "占位", "path": ["placeholder"], "setter": "String"}, {"name": "可清空", "path": ["allowClear"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}]}, {"type": "group", "name": "状态", "items": [{"name": "禁用", "path": ["disabled"], "setter": "Boolean"}, {"name": "输入框只读", "tip": "设置输入框为只读（避免在移动设备上打开虚拟键盘）", "path": ["inputReadOnly"], "setter": "Boolean"}]}, {"type": "group", "name": "高级", "items": [{"name": "自动聚焦", "path": ["autoFocus"], "setter": "Boolean"}, {"name": "边框", "path": ["bordered"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "日期格式", "tip": "默认为：YYYY-MM-DD", "path": ["format"], "setter": "String"}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "DatePicker"}}