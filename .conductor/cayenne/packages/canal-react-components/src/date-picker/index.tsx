import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import { DatePicker as MUIDatePicker } from '@m-ui/react'
import { isNil } from 'lodash'
import moment from 'moment'
import React, {
  memo,
  useCallback,
  useMemo,
  type ComponentProps,
  type FC,
} from 'react'

/**
 * 日期选择器属性
 */
export type DatePickerProps = Omit<
  ComponentProps<typeof MUIDatePicker>,
  'value' | 'onChange'
> &
  ValueOnChangeProps<DatePickerValue>

/**
 * 日期选择器值
 */
export type DatePickerValue = number | undefined

/**
 * 日期选择器
 */
export const DatePicker: FC<DatePickerProps> = memo(
  ({ value, onChange, ...restProps }) => {
    const finalValue = useMemo(() => toRaw(value), [value])
    const finalOnChange = useCallback(
      (v: moment.Moment | null) => {
        onChange?.(fromRaw(v))
      },
      [onChange],
    )
    return (
      <MUIDatePicker
        {...restProps}
        value={finalValue}
        onChange={finalOnChange}
      />
    )
  },
)

/**
 * 日期选择器原始值值
 */
type RawDatePickerValue = moment.Moment | null | undefined

/**
 * 从原始值转换
 * @param value 原始值
 */
function fromRaw(value: RawDatePickerValue): DatePickerValue {
  return isNil(value) ? undefined : +value
}

/**
 * 转为原始值
 * @param value 值
 */
function toRaw(value: DatePickerValue): RawDatePickerValue {
  return isNil(value) ? value : moment(value)
}
