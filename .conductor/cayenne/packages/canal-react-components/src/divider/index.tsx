import { Divider as MUIDivider } from '@m-ui/react'
import React, {
  memo,
  type ComponentProps,
  type FC,
  type ReactNode,
} from 'react'

/**
 * 分割线属性
 */
export interface DividerProps extends ComponentProps<typeof MUIDivider> {
  /**
   * 文本（内容）
   */
  text?: ReactNode
}

/**
 * 分割线
 */
export const Divider: FC<DividerProps> = memo(({ text, ...restProps }) => {
  return <MUIDivider {...restProps}>{text}</MUIDivider>
})
