{"schemaVersion": "0.0.1", "runtimeVersion": "1.7.1", "type": "Divider", "name": "分割线", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "文本", "path": ["text"], "setter": "String", "defaultValue": "文本"}, {"name": "虚线", "path": ["dashed"], "setter": "Boolean"}, {"name": "位置", "path": ["orientation"], "setter": {"type": "Enum", "props": {"options": [{"label": "左", "value": "left"}, {"label": "中", "value": "center"}, {"label": "右", "value": "right"}]}}}, {"name": "正文", "path": ["plain"], "setter": "Boolean"}, {"name": "类型", "path": ["type"], "setter": {"type": "Enum", "props": {"options": [{"label": "水平", "value": "horizontal"}, {"label": "垂直", "value": "vertical"}]}}}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Divider"}}