{"schemaVersion": "0.0.1", "runtimeVersion": "1.4.0", "type": "TableCell", "name": "表格单元格", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "数据项键值", "tip": "需要与数据项内的 key 字段对应", "path": ["<PERSON><PERSON>ey"], "setter": "String", "defaultValue": "name"}]}]}, {"type": "group", "name": "渲染", "items": []}], "implements": {"condition": true, "loop": true, "container": true, "nestLimit": {"parentTypeAllow": ["TableColumn"]}}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "TableCell"}}