{"schemaVersion": "0.0.1", "runtimeVersion": "1.4.0", "type": "Table", "name": "表格", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`{ pagination: { current: number, pageSize: number } }`", "declaration": "{ pagination: { current: number, pageSize: number } }", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "数据源", "path": ["dataSource"], "setter": "Json", "defaultValue": [{"key": "1", "name": "<PERSON>", "age": 32, "address": "New York No. 1 Lake Park"}, {"key": "2", "name": "<PERSON>", "age": 42, "address": "London No. 1 Lake Park"}, {"key": "3", "name": "<PERSON>", "age": 32, "address": "Sidney No. 1 Lake Park"}]}, {"name": "边框", "path": ["bordered"], "setter": "Boolean"}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "大（默认）", "value": "large"}, {"label": "中", "value": "middle"}, {"label": "小", "value": "small"}]}}}, {"name": "虚拟列表", "path": ["virtual"], "setter": "Boolean"}, {"name": "粘性", "tip": "设置粘性头部和滚动条", "path": ["sticky"], "setter": "Boolean"}, {"name": "空文案", "tip": "无数据文案", "path": ["emptyText"], "defaultValue": "暂无数据", "setter": "String"}]}, {"type": "group", "name": "分页", "path": ["pagination"], "items": [{"name": "条数选项", "tip": "类型为：`string[] | number[]`", "path": ["pageSizeOptions"], "setter": "JSON", "defaultValue": ["10", "20", "50", "100"]}, {"name": "显示条数切换", "tip": "显示条数切换器，当总条数大于 50 时默认显示", "path": ["showSizeChanger"], "setter": "Boolean"}, {"name": "自动隐藏", "tip": "只有一页时隐藏", "path": ["hideOnSinglePage"], "setter": "Boolean"}, {"name": "自动大小", "tip": "当大小未指定时，根据屏幕宽度自动调整尺寸", "path": ["responsive"], "setter": "Boolean"}, {"name": "减少显示", "tip": "显示较少页面内容", "path": ["showLessItems"], "setter": "Boolean"}, {"name": "快速跳转", "path": ["showQuickJumper"], "setter": "Boolean"}, {"name": "显示提示", "path": ["showTitle"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "展示总条数", "path": ["isShowTotal"], "setter": "Boolean"}, {"name": "简单分页", "path": ["simple"], "setter": "Boolean"}]}]}, {"type": "group", "name": "样式", "items": []}, {"type": "group", "name": "渲染", "items": []}], "defaultChildren": [{"type": "@ad/canal-components::TableColumn", "name": "表格列名称", "props": {"title": "名称", "dataIndex": "name"}}, {"type": "@ad/canal-components::TableColumn", "name": "表格列年龄", "props": {"title": "年龄", "dataIndex": "age"}}, {"type": "@ad/canal-components::TableColumn", "name": "表格列操作", "props": {"title": "操作", "dataIndex": "op"}, "children": [{"type": "@ad/canal-components::TableCell", "name": "表格单元格", "props": {"recordKey": "1"}, "children": [{"type": "@ad/canal-components::Button", "name": "查询按钮", "props": {"text": "查询"}}]}, {"type": "@ad/canal-components::TableCell", "name": "表格单元格", "props": {"recordKey": "2"}, "children": [{"type": "@ad/canal-components::Button", "name": "查询按钮", "props": {"text": "查询"}}]}, {"type": "@ad/canal-components::TableCell", "name": "表格单元格", "props": {"recordKey": "3"}, "children": [{"type": "@ad/canal-components::Button", "name": "查询按钮", "props": {"text": "查询"}}]}]}], "implements": {"style": true, "condition": true, "loop": true, "container": true, "nestLimit": {"childTypeAllow": ["TableColumn"]}}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Table"}}