{"schemaVersion": "0.0.1", "runtimeVersion": "1.4.0", "type": "TableColumn", "name": "表格列", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "表头", "path": ["title"], "setter": "String", "defaultValue": "名称"}, {"name": "键值", "tip": "列数据在数据项中的键值", "path": ["dataIndex"], "setter": "String", "defaultValue": "name"}, {"name": "宽度", "path": ["width"], "setter": "String"}, {"name": "对齐", "path": ["align"], "setter": {"type": "Enum", "props": {"options": [{"label": "居左（默认）", "value": "left"}, {"label": "居中", "value": "center"}, {"label": "居右", "value": "right"}]}}}, {"name": "固定", "path": ["fixed"], "setter": {"type": "Enum", "props": {"options": [{"label": "居左", "value": "left"}, {"label": "居右", "value": "right"}]}}}, {"name": "合并宽度", "path": ["colSpan"], "tip": "设置为 0 时，不渲染", "setter": "Number"}, {"name": "自动省略", "path": ["ellipsis"], "setter": "Boolean"}]}]}, {"type": "group", "name": "渲染", "items": []}], "implements": {"condition": true, "loop": true, "container": true, "nestLimit": {"childTypeAllow": ["TableCell"], "parentTypeAllow": ["Table"]}}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "TableColumn"}}