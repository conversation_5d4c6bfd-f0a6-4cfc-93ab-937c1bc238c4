import type { PaginationValue } from '@/pagination'
import type { AnyObject } from '@ad/canal-shared'
import {
  createFiberCollecterContext,
  useCollecterData,
  type ValueOnChangeProps,
} from '@ad/canal-shared-ui'
import { Table as MUITable } from '@m-ui/react'
import type { ColumnType, TablePaginationConfig } from '@m-ui/react/lib/table'
import type { TableLocale } from '@m-ui/react/lib/table/interface'
import { isString } from 'lodash'
import React, {
  createContext,
  useCallback,
  useContext,
  useMemo,
  type ComponentClass,
  type ComponentProps,
  type FC,
  type ReactNode,
} from 'react'

/**
 * 表格列数据项
 */
export interface TableColumnDataItem
  extends Omit<ColumnType<AnyObject>, 'key'> {
  /**
   * 类型
   */
  type: 'column'
  /**
   * 列键值
   */
  columnKey: string
}

/**
 * 表格单元格
 */
export interface TableCellDataItem {
  /**
   * 类型
   */
  type: 'cell'
  /**
   * 列键值，与 {@link TableColumnDataItem} 对应
   */
  columnKey: string
  /**
   * 记录键值，与表格的 dataSource 对应
   */
  recordKey: string
  /**
   * 单元格内容
   */
  children?: ReactNode
}

/**
 * 表格 Fiber 收集器数据项
 */
export type TableFccDataItem = TableColumnDataItem | TableCellDataItem

/**
 * 表格 Fiber 收集器
 */
export const TableFcc = createFiberCollecterContext<TableFccDataItem>()

const { withCollecter, withCollect } = TableFcc

/**
 * 表格值
 */
export interface TableValue {
  /**
   * 分页器
   */
  pagination: PaginationValue
}

/**
 * 表格属性
 */
export interface TableProps
  extends Omit<
      ComponentProps<typeof MUITable>,
      'columns' | 'pagination' | 'onChange'
    >,
    ValueOnChangeProps<TableValue> {
  children?: ReactNode
  /**
   * 空文案
   */
  emptyText?: string
  /**
   * 分页器
   */
  pagination?: TablePagination
}

/**
 * 表格分页器
 */
export interface TablePagination
  extends Omit<TablePaginationConfig, 'current' | 'pageSize' | 'onChange'> {
  /**
   * 是否展示总数
   */
  isShowTotal?: boolean
}

/**
 * 表格
 */
export const Table: FC<TableProps> = withCollecter(
  ({
    fiberCollecter,
    value: {
      pagination: {
        current: valuePaginationCurrent = 1,
        pageSize: valuePaginationPageSize = 10,
      } = {},
    } = {} satisfies Partial<TableValue>,
    onChange: rawOnChange,
    emptyText,
    pagination,
    ...restProps
  }) => {
    const collectedData = useCollecterData(fiberCollecter)
    const columns = useMemo(() => {
      const columnMap = new Map<
        string,
        ColumnType<AnyObject> & {
          renderItems?: Map<string, TableCellDataItem>
        }
      >()
      for (const item of collectedData.values()) {
        if (item.type === 'column') {
          columnMap.set(item.columnKey, {
            ...item,
            key: item.columnKey,
          })
        } else {
          const c = columnMap.get(item.columnKey)
          if (c) {
            if (!c.renderItems) {
              const m = (c.renderItems = new Map<string, TableCellDataItem>())
              c.render = (...[, record]): ReactNode => {
                return m.get(record.key)?.children
              }
            }
            c.renderItems.set(item.recordKey, item)
          }
        }
      }
      return [...columnMap.values()]
    }, [collectedData])
    const finalPagination: TablePaginationConfig | undefined = useMemo(() => {
      if (!pagination) return pagination
      const { isShowTotal, showTotal, ...restPagination } = pagination
      return {
        ...restPagination,
        showTotal:
          showTotal ||
          (isShowTotal
            ? (total: number): string => {
                return `总计 ${total} 条`
              }
            : undefined),
        current: valuePaginationCurrent,
        pageSize: valuePaginationPageSize,
      } satisfies TablePaginationConfig
    }, [pagination, valuePaginationCurrent, valuePaginationPageSize])
    const locale: TableLocale | undefined = useMemo(() => {
      if (isString(emptyText)) {
        return { emptyText }
      }
    }, [emptyText])
    const onChange = useCallback(
      ({ current, pageSize }: TablePaginationConfig) => {
        rawOnChange?.({
          pagination: {
            current,
            pageSize,
          },
        })
      },
      [rawOnChange],
    )
    return (
      <MUITable
        {...restProps}
        locale={locale}
        columns={columns}
        pagination={finalPagination}
        onChange={onChange}
      />
    )
  },
)

/**
 * 表格列属性
 */
export interface TableColumnProps
  extends Omit<TableColumnDataItem, 'type' | 'columnKey'> {
  children?: ReactNode
}

/**
 * 表格列键值上下文
 */
export const TableColumnKeyContext = createContext<string | null>(null)

/**
 * 表格列
 */
export const TableColumn: FC<TableColumnProps> = ({
  children,
  ...restProps
}) => {
  const columnKey = `${restProps.dataIndex}`
  return (
    <>
      <TableColumnWithCollect {...restProps} columnKey={columnKey} />
      <TableColumnKeyContext.Provider value={columnKey}>
        {children}
      </TableColumnKeyContext.Provider>
    </>
  )
}

/**
 * 带收集功能的表格列属性
 */
export interface TableColumnWithCollectProps
  extends Omit<TableColumnDataItem, 'type'> {}

/**
 * 带收集功能的表格列
 */
export const TableColumnWithCollect: ComponentClass<TableColumnWithCollectProps> =
  withCollect((props) => {
    return {
      type: 'column',
      ...props,
    }
  })

/**
 * 表格单元格属性
 */
export interface TableCellProps
  extends Omit<TableCellDataItem, 'type' | 'columnKey'> {}

/**
 * 表格单元格
 */
export const TableCell: FC<TableCellProps> = (props) => {
  const columnKey = useContext(TableColumnKeyContext)
  return columnKey && <TableCellWithCollect {...props} columnKey={columnKey} />
}

/**
 * 带收集功能的表格单元格
 */
export const TableCellWithCollect: ComponentClass<
  TableCellProps & { columnKey: string }
> = withCollect((props) => {
  return {
    type: 'cell',
    ...props,
  }
})
