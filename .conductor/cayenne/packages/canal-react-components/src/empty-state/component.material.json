{"schemaVersion": "0.0.1", "runtimeVersion": "1.7.1", "type": "EmptyState", "name": "空状态", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "描述", "path": ["description"], "setter": "String"}, {"name": "图片 URL", "path": ["image"], "setter": "String"}, {"name": "图片样式", "path": ["imageStyle"], "setter": "JSON"}]}]}], "implements": {"container": true, "style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "EmptyState"}}