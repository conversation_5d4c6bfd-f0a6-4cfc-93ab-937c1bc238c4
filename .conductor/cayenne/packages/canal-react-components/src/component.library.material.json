{"schemaVersion": "0.0.1", "displayName": "大运河组件", "components": [], "groups": [{"name": "布局", "items": ["Container", "Submodule"]}, {"name": "展示", "items": ["Text", "Divider", "Toast", "Table", "TableColumn", "TableCell", "Tabs", "TabPane", "EmptyState"]}, {"name": "交互", "items": ["<PERSON><PERSON>"]}, {"name": "输入", "items": ["Form", "FormItem", "Checkbox", "CheckboxGroup", "RadioGroup", "Input", "TextArea", "Select", "DatePicker", "RangePicker", "Pagination", "Switch"]}]}