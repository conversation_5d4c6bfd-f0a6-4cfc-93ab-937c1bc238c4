import {
  debounceValueOnChange,
  type ValueOnChangeProps,
} from '@ad/canal-shared-ui'
import { Input as MUIInput } from '@m-ui/react'
import React, {
  useCallback,
  type ChangeEvent,
  type ComponentProps,
  type FC,
} from 'react'

/**
 * 多行输入框属性
 */
export type TextAreaProps = Omit<
  ComponentProps<typeof MUIInput>,
  'value' | 'onChange'
> &
  ValueOnChangeProps<string>

/**
 * 多行输入框
 */
export const TextArea: FC<TextAreaProps> = debounceValueOnChange(
  ({ value, onChange, ...restProps }) => {
    const handleChange = useCallback(
      (e: ChangeEvent<HTMLTextAreaElement>) => {
        onChange?.(e.target.value)
      },
      [onChange],
    )
    return (
      <MUIInput.TextArea {...restProps} value={value} onChange={handleChange} />
    )
  },
)
