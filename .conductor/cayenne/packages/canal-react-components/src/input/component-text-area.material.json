{"schemaVersion": "0.0.1", "runtimeVersion": "1.0.0", "type": "TextArea", "name": "多行输入框", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`string`", "declaration": "string", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "占位", "path": ["placeholder"], "setter": "String"}, {"name": "可清空", "path": ["allowClear"], "setter": "Boolean"}, {"name": "边框", "path": ["bordered"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "大", "value": "large"}, {"label": "中（默认）", "value": "middle"}, {"label": "小", "value": "small"}]}}}]}, {"type": "group", "name": "状态", "items": [{"name": "禁用", "path": ["disabled"], "setter": "Boolean"}, {"name": "只读", "path": ["readOnly"], "setter": "Boolean"}]}, {"type": "group", "name": "限制", "items": [{"name": "最大长度", "path": ["max<PERSON><PERSON><PERSON>"], "setter": {"type": "Number", "props": {"precision": 0, "min": 0}}}, {"name": "展示字数", "path": ["showCount"], "setter": "Boolean"}, {"name": "字数位置", "path": ["countPos"], "setter": {"type": "Enum", "props": {"options": [{"label": "外部（默认）", "value": "outer"}, {"label": "内部", "value": "inner"}]}}}, {"name": "大小自动", "tip": "自适应内容高度，可设置为 `true | false` 或对象：`{ minRows: 2, maxRows: 6 }`", "path": ["autoSize"], "setter": "JSON"}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./text-area.tsx"}, "exportIdentifier": "TextArea"}}