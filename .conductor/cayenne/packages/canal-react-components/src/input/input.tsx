import {
  debounceValueOnChange,
  type ValueOnChangeProps,
} from '@ad/canal-shared-ui'
import { Input as MUIInput } from '@m-ui/react'
import React, {
  useCallback,
  type ChangeEvent,
  type ComponentProps,
  type FC,
} from 'react'

/**
 * 输入框属性
 */
export type InputProps = Omit<
  ComponentProps<typeof MUIInput>,
  'value' | 'onChange'
> &
  ValueOnChangeProps<string>

/**
 * 输入框
 */
export const Input: FC<InputProps> = debounceValueOnChange(
  ({ value, onChange, ...restProps }) => {
    const handleChange = useCallback(
      (e: ChangeEvent<HTMLInputElement>) => {
        onChange?.(e.target.value)
      },
      [onChange],
    )
    return <MUIInput {...restProps} value={value} onChange={handleChange} />
  },
)
