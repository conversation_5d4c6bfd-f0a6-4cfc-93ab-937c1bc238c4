{"schemaVersion": "0.0.1", "type": "Submodule", "name": "子模块", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "path": [], "items": [{"name": "模块 ID", "path": ["schemaId"], "setter": "String"}, {"name": "参数", "path": ["params"], "setter": "JSON"}]}]}], "implements": {"condition": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Submodule"}}