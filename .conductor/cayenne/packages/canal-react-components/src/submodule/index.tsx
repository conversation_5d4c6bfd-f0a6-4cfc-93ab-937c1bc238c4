import { canalReactRuntimeSubmoduleFCContext } from '@ad/canal-react-component-context'
import { useContext, type FC } from 'react'

/**
 * 子模块属性
 */
export interface SubmoduleProps {
  /**
   * Schema 文件 ID
   */
  schemaId: string | number
  /**
   * 配置参数
   */
  params?: Record<string, unknown>
}

/**
 * 子模块
 */
export const Submodule: FC<SubmoduleProps> = ({ schemaId, params }) => {
  const submoduleRenderFn = useContext(canalReactRuntimeSubmoduleFCContext)
  if (!submoduleRenderFn) {
    console.error(`The Submodule must be used in CanalReactRuntimeFC!`)
    return null
  }
  return submoduleRenderFn({
    schema: undefined,
    schemaId,
    refCanalReactRuntime: undefined,
    params,
  })
}
