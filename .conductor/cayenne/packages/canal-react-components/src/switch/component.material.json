{"schemaVersion": "0.0.1", "runtimeVersion": "1.7.1", "type": "Switch", "name": "开关", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`boolean`", "declaration": "boolean", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "选中", "tip": "选中时的内容", "path": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "setter": "String"}, {"name": "未选中", "tip": "未选中时的内容", "path": ["unChecked<PERSON><PERSON><PERSON>n"], "setter": "String"}, {"name": "自动聚焦", "path": ["autoFocus"], "setter": "Boolean"}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "中（默认）", "value": "default"}, {"label": "小", "value": "small"}]}}}]}, {"type": "group", "name": "状态", "items": [{"name": "禁用", "path": ["disabled"], "setter": "Boolean"}, {"name": "加载中", "path": ["loading"], "setter": "Boolean"}]}]}, {"type": "group", "name": "样式", "items": []}, {"type": "group", "name": "渲染", "items": []}, {"type": "group", "name": "交互", "items": [{"type": "group", "name": "事件", "items": [{"name": "点击", "path": ["onClick"], "setter": "Action"}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Switch"}}