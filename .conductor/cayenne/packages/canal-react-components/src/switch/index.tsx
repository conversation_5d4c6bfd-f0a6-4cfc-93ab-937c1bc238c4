import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Switch as MUISwitch } from '@m-ui/react'
import React, { memo, type ComponentProps, type FC } from 'react'

/**
 * 开关属性
 */
export type SwitchProps = Omit<
  ComponentProps<typeof MUISwitch>,
  'value' | 'onChange'
> &
  ValueOnChangeProps<boolean>

/**
 * 开关
 */
export const Switch: FC<SwitchProps> = memo(({ value, ...restProps }) => {
  return <MUISwitch {...restProps} checked={value} />
})
