import { Button as MUIButton, message } from '@m-ui/react'
import type { ArgsProps } from '@m-ui/react/lib/message'
import { isString } from 'lodash'
import { type ComponentProps, type FC } from 'react'
import { useDeepCompareEffect } from 'react-use'

/**
 * 轻提示属性
 */
export interface ToastProps extends ComponentProps<typeof MUIButton> {
  /**
   * 提示消息
   */
  msgs?: ToastMsg[]
}

/**
 * 轻提示消息，字符串默认为错误消息
 */
export type ToastMsg = string | ToastMsgObj

/**
 * 轻提示消息对象形式
 */
export interface ToastMsgObj extends ArgsProps {}

/**
 * 轻提示
 */
export const Toast: FC<ToastProps> = ({ msgs = [] }) => {
  useDeepCompareEffect(() => {
    for (const msg of msgs) {
      const msgObj: ToastMsgObj = {
        type: 'error',
        ...(isString(msg)
          ? {
              content: msg,
            }
          : msg),
      }
      message.open(msgObj)
    }
  }, [msgs])
  return null
}
