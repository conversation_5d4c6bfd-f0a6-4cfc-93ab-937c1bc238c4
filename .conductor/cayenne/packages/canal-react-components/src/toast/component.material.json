{"schemaVersion": "0.0.1", "runtimeVersion": "1.0.5", "type": "Toast", "name": "轻提示", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "消息", "tip": "类型为：`ToastMsg[]`\n```ts\n/**\n * 轻提示消息，字符串默认为错误消息\n */\nexport type ToastMsg = string | ToastMsgObj;\n\n/**\n * 轻提示消息对象形式\n */\nexport interface ToastMsgObj {\n  /**\n   * 消息内容\n   */\n  content: string;\n  /**\n   * 自动关闭的延时，单位秒\n   */\n  duration: number;\n\n  // 更多的属性\n}\n```\n<a href=\"https://m-ui.corp.kuaishou.com/components/message-cn/#API\" target=\"_blank\" rel=\"noreferrer\">更多的属性参考</a>", "path": ["msgs"], "setter": "JSON"}]}]}], "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Toast"}}