{"schemaVersion": "0.0.1", "type": "Container", "name": "容器", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "path": [], "items": [{"name": "模块刷新加载中", "tip": "会在模块刷新时，自动显示加载中蒙层。运行时需要升级到 0.3.3 及以上。", "path": ["enableLoading"], "setter": "Boolean"}]}]}, {"type": "group", "name": "样式", "items": []}, {"type": "group", "name": "渲染", "items": []}, {"type": "group", "name": "交互", "items": [{"type": "group", "name": "事件", "items": [{"name": "挂载", "path": ["onMount"], "setter": "Action"}]}]}], "implements": {"container": true, "style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Container"}}