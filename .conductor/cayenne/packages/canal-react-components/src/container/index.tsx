import { canalReactRuntimeLoadingInfoContext } from '@ad/canal-react-component-context'
import React, {
  memo,
  useContext,
  useEffect,
  useMemo,
  type CSSProperties,
  type FC,
  type ReactNode,
} from 'react'
import { useLatest } from 'react-use'

/**
 * 容器属性
 */
export interface ContainerProps {
  /**
   * 启用加载中
   */
  enableLoading?: boolean
  /**
   * 挂载回调
   */
  onMount?(): void
  /**
   * 样式
   */
  style?: CSSProperties
  /**
   * 子元素
   */
  children?: ReactNode
}

/**
 * 容器
 */
export const Container: FC<ContainerProps> = memo(
  ({ enableLoading, onMount, ...restProps }) => {
    const loadingInfo = useContext(canalReactRuntimeLoadingInfoContext)
    const finalStyleForLoading: CSSProperties = useMemo(() => {
      return {
        ...restProps.style,
        position:
          !restProps.style?.position || restProps.style.position === 'static'
            ? 'relative'
            : restProps.style.position,
      }
    }, [restProps.style])
    const refLatestOnMount = useLatest(onMount)
    useEffect(() => {
      refLatestOnMount.current?.()
    }, [refLatestOnMount])
    return enableLoading && loadingInfo?.isLoading ? (
      <div {...restProps} style={finalStyleForLoading}>
        {restProps.children}
        <loadingInfo.Loading
          isRefreshing={loadingInfo.isRefreshing}
          refreshingType={loadingInfo.refreshingType}
          isLoadingDependencies={loadingInfo.isLoadingDependencies}
          schema={loadingInfo.schema}
          position="container"
        />
      </div>
    ) : (
      <div {...restProps} />
    )
  },
)
