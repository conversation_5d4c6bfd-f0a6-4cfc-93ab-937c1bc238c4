import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import { DatePicker as MUIDatePicker } from '@m-ui/react'
import { isNil } from 'lodash'
import moment from 'moment'
import React, {
  memo,
  useCallback,
  useMemo,
  type ComponentProps,
  type FC,
} from 'react'

const { RangePicker: MUIRangePicker } = MUIDatePicker

/**
 * 日期范围选择器属性
 */
export type RangePickerProps = Omit<
  ComponentProps<typeof MUIRangePicker>,
  'value' | 'onChange'
> &
  ValueOnChangeProps<RangePickerValue>

/**
 * 日期范围选择器值
 */
export type RangePickerValue = [number, number] | undefined

/**
 * 日期范围选择器
 */
export const RangePicker: FC<RangePickerProps> = memo(
  ({ value, onChange, ...restProps }) => {
    const finalValue = useMemo(() => toRaw(value), [value])
    const finalOnChange = useCallback(
      (v: [moment.Moment | null, moment.Moment | null] | null) => {
        onChange?.(fromRaw(v))
      },
      [onChange],
    )
    return (
      <MUIRangePicker
        {...restProps}
        value={finalValue}
        onChange={finalOnChange}
      />
    )
  },
)

/**
 * 日期范围选择器原始值值
 */
type RawRangePickerValue =
  | [moment.Moment | null, moment.Moment | null]
  | null
  | undefined

/**
 * 从原始值转换
 * @param value 原始值
 */
function fromRaw(value: RawRangePickerValue): RangePickerValue {
  if (!value || isNil(value[0]) || isNil(value[1])) {
    return undefined
  }
  return [+value[0], +value[1]]
}

/**
 * 转为原始值
 * @param value 值
 */
function toRaw(value: RangePickerValue): RawRangePickerValue {
  return value && [moment(value[0]), moment(value[1])]
}
