{"schemaVersion": "0.0.1", "runtimeVersion": "1.1.0", "type": "Pagination", "name": "分页器", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`{ current: number, pageSize: number }`", "declaration": "{ current: number, pageSize: number }", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "总条数", "path": ["total"], "defaultValue": 500, "setter": "Number"}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "中（默认）", "value": "middle"}, {"label": "小", "value": "small"}]}}}, {"name": "条数选项", "tip": "类型为：`string[] | number[]`", "path": ["pageSizeOptions"], "setter": "JSON", "defaultValue": ["10", "20", "50", "100"]}, {"name": "显示条数切换", "tip": "显示条数切换器，当总条数大于 50 时默认显示", "path": ["showSizeChanger"], "setter": "Boolean"}]}, {"type": "group", "name": "状态", "items": [{"name": "禁用", "path": ["disabled"], "setter": "Boolean"}]}, {"type": "group", "name": "布局", "items": [{"name": "自动隐藏", "tip": "只有一页时隐藏", "path": ["hideOnSinglePage"], "setter": "Boolean"}, {"name": "自动大小", "tip": "当大小未指定时，根据屏幕宽度自动调整尺寸", "path": ["responsive"], "setter": "Boolean"}, {"name": "减少显示", "tip": "显示较少页面内容", "path": ["showLessItems"], "setter": "Boolean"}, {"name": "快速跳转", "path": ["showQuickJumper"], "setter": "Boolean"}, {"name": "显示提示", "path": ["showTitle"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "展示总条数", "path": ["isShowTotal"], "setter": "Boolean"}, {"name": "简单分页", "path": ["simple"], "setter": "Boolean"}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Pagination"}}