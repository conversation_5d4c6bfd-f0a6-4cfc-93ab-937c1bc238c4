import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Pagination as MuiPagination } from '@m-ui/react'
import React, {
  memo,
  useCallback,
  useMemo,
  type ComponentProps,
  type FC,
} from 'react'

/**
 * 分页器值
 */
export type PaginationValue = Pick<
  ComponentProps<typeof MuiPagination>,
  'current' | 'pageSize'
>

/**
 * 分页器属性
 */
export interface PaginationProps
  extends Omit<
      ComponentProps<typeof MuiPagination>,
      'current' | 'pageSize' | 'onChange'
    >,
    ValueOnChangeProps<PaginationValue> {
  /**
   * 是否展示总数
   */
  isShowTotal?: boolean
}

/**
 * 分页器
 */
export const Pagination: FC<PaginationProps> = memo(
  ({
    value: { current = 1, pageSize = 10 } = {},
    onChange: rawOnChange,
    showTotal: rawShowTotal,
    isShowTotal,
    ...restProps
  }) => {
    const onChange = useCallback(
      (p: number, ps: number) => {
        rawOnChange?.({
          current: p,
          pageSize: ps,
        })
      },
      [rawOnChange],
    )
    const showTotal = useMemo(() => {
      if (rawShowTotal) {
        return rawShowTotal
      }
      if (isShowTotal) {
        return (total: number): string => {
          return `总计 ${total} 条`
        }
      }
    }, [isShowTotal, rawShowTotal])

    return (
      <MuiPagination
        {...restProps}
        current={current}
        pageSize={pageSize}
        onChange={onChange}
        showTotal={showTotal}
      />
    )
  },
)
