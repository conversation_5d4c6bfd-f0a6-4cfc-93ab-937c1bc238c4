import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Radio as MUIRadio, type RadioChangeEvent } from '@m-ui/react'
import type { CheckboxValueType } from '@m-ui/react/lib/checkbox/Group'
import React, { memo, useCallback, type ComponentProps, type FC } from 'react'

/**
 * 单选框组属性
 */
export type RadioGroupProps = Omit<
  ComponentProps<typeof MUIRadio.Group>,
  'value' | 'onChange'
> &
  ValueOnChangeProps<CheckboxValueType>

/**
 * 单选框组
 */
export const RadioGroup: FC<RadioGroupProps> = memo(
  ({ value, onChange, ...restProps }) => {
    const handleChange = useCallback(
      (e: RadioChangeEvent) => {
        onChange?.(e.target.value)
      },
      [onChange],
    )
    return (
      <MUIRadio.Group {...restProps} value={value} onChange={handleChange} />
    )
  },
)
