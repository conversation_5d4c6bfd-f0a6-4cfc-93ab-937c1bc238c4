{"schemaVersion": "0.0.1", "runtimeVersion": "1.0.2", "type": "RadioGroup", "name": "单选框组", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`string | number | boolean`，取决于选项的配置", "declaration": "string | number | boolean", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "选项", "tip": "类型为：`string[] | { label: string, value: (string | number | boolean)[] }[]`", "path": ["options"], "setter": "JSON", "defaultValue": [{"label": "a", "value": 1}, {"label": "b", "value": 2}]}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "大", "value": "large"}, {"label": "中（默认）", "value": "middle"}, {"label": "小", "value": "small"}]}}}, {"name": "选项类型", "path": ["optionType"], "setter": {"type": "Enum", "props": {"options": [{"label": "默认", "value": "default"}, {"label": "按钮", "value": "button"}]}}}, {"name": "按钮风格", "path": ["buttonStyle"], "setter": {"type": "Enum", "props": {"options": [{"label": "轮廓（默认）", "value": "outline"}, {"label": "实心", "value": "solid"}]}}}]}, {"type": "group", "name": "状态", "items": [{"name": "禁用", "path": ["disabled"], "setter": "Boolean"}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./radio-group.tsx"}, "exportIdentifier": "RadioGroup"}}