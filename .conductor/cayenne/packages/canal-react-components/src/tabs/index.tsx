import {
  createFiberCollecterContext,
  useCollecterData,
  type ValueOnChangeProps,
} from '@ad/canal-shared-ui'
import {
  Tabs as MUITabs,
  type TabPaneProps as MUITabPaneProps,
} from '@m-ui/react'
import React, { type ComponentClass, type ComponentProps, type FC } from 'react'

/**
 * m-ui 的标签面板
 */
const MUITabPane = MUITabs.TabPane

/**
 * 标签页数据项
 */
export interface TabsDataItem extends MUITabPaneProps {
  /**
   * 标签页面板键值
   */
  tabPaneKey: string
}

/**
 * 标签页 Fiber 收集器
 */
export const TabsFcc = createFiberCollecterContext<TabsDataItem>()

const { withCollecter, withCollect } = TabsFcc

/**
 * 标签页属性
 */
export interface TabsProps
  extends ComponentProps<typeof MUITabs>,
    ValueOnChangeProps<string> {}

/**
 * 标签页
 */
export const Tabs: FC<TabsProps> = withCollecter(
  ({ fiberCollecter, value, ...restProps }) => {
    const collectedData = useCollecterData(fiberCollecter)
    return (
      <MUITabs {...restProps} activeKey={value}>
        {[...collectedData.values()].map(({ tabPaneKey, ...restPaneProps }) => (
          <MUITabPane {...restPaneProps} key={tabPaneKey} />
        ))}
      </MUITabs>
    )
  },
)

/**
 * 标签页面板属性
 */
export interface TabPaneProps extends TabsDataItem {}

/**
 * 标签页面板
 */
export const TabPane: ComponentClass<TabPaneProps> = withCollect(
  (props) => props,
)
