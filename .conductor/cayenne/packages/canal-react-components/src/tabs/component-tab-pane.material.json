{"schemaVersion": "0.0.1", "runtimeVersion": "1.6.0", "type": "TabPane", "name": "标签页面板", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "键值", "path": ["tabPaneKey"], "setter": "String"}, {"name": "标题", "path": ["tab"], "defaultValue": "标签页面板", "setter": "String"}, {"name": "隐藏时渲染", "path": ["forceRender"], "setter": "Boolean"}]}]}], "implements": {"condition": true, "loop": true, "container": true, "nestLimit": {"parentTypeAllow": ["Tabs"]}}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "TabPane"}}