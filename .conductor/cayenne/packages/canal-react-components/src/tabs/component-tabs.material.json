{"schemaVersion": "0.0.1", "runtimeVersion": "1.6.0", "type": "Tabs", "name": "标签页", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`string`", "declaration": "string", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "切换动画", "path": ["animated"], "setter": "Boolean"}, {"name": "标签居中", "path": ["centered"], "setter": "Boolean"}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "常规", "value": "default"}, {"label": "大", "value": "large"}, {"label": "小", "value": "small"}]}}}, {"name": "标签间隔", "path": ["tabBarGutter"], "setter": "Number"}, {"name": "标签位置", "path": ["tabPosition"], "setter": {"type": "Enum", "props": {"options": [{"label": "上", "value": "top"}, {"label": "右", "value": "right"}, {"label": "下", "value": "bottom"}, {"label": "左", "value": "left"}]}}}, {"name": "标签样式", "path": ["type"], "setter": {"type": "Enum", "props": {"options": [{"label": "标准", "value": "line"}, {"label": "卡片", "value": "card"}]}}}]}]}], "defaultChildren": [{"type": "@ad/canal-components::TabPane", "name": "标签页面板 1", "props": {"tabPaneKey": "tab1", "tab": "面板 1"}, "children": [{"type": "@ad/canal-components::Container", "name": "面板容器 1", "children": [{"type": "@ad/canal-components::Button", "name": "面板按钮 1", "props": {"text": "面板按钮 1"}}]}]}, {"type": "@ad/canal-components::TabPane", "name": "标签页面板 2", "props": {"tabPaneKey": "tab2", "tab": "面板 2"}, "children": [{"type": "@ad/canal-components::Container", "name": "面板容器 2", "children": [{"type": "@ad/canal-components::Button", "name": "面板按钮 2", "props": {"text": "面板按钮 2"}}]}]}], "implements": {"style": true, "condition": true, "loop": true, "container": true, "nestLimit": {"childTypeAllow": ["TabPane"]}}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Tabs"}}