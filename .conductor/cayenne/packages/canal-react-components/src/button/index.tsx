import { Button as MUIButton } from '@m-ui/react'
import React, {
  memo,
  type ComponentProps,
  type FC,
  type ReactNode,
} from 'react'

/**
 * 按钮属性
 */
export interface ButtonProps extends ComponentProps<typeof MUIButton> {
  /**
   * 文本（内容）
   */
  text?: ReactNode
}

/**
 * 按钮
 */
export const Button: FC<ButtonProps> = memo(({ text, ...restProps }) => {
  return <MUIButton {...restProps}>{text}</MUIButton>
})
