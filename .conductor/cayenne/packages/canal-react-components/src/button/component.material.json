{"schemaVersion": "0.0.1", "runtimeVersion": "0.3.6", "type": "<PERSON><PERSON>", "name": "按钮", "icon": "./icon.png", "props": [{"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "文本", "path": ["text"], "setter": "String", "defaultValue": "查询"}, {"name": "类型", "path": ["type"], "setter": {"type": "Enum", "props": {"options": [{"label": "默认", "value": "default"}, {"label": "主要", "value": "primary"}, {"label": "次要", "value": "secondary"}, {"label": "虚线", "value": "dashed"}, {"label": "链接", "value": "link"}, {"label": "文本", "value": "text"}]}}}, {"name": "形状", "path": ["shape"], "setter": {"type": "Enum", "props": {"options": [{"label": "默认", "value": "default"}, {"label": "圆形", "value": "circle"}, {"label": "圆角", "value": "round"}]}}}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "大", "value": "large"}, {"label": "中（默认）", "value": "middle"}, {"label": "小", "value": "small"}]}}}, {"name": "HTML 类型", "path": ["htmlType"], "setter": {"type": "Enum", "props": {"options": [{"label": "默认", "value": "button"}, {"label": "提交", "value": "submit"}, {"label": "重置", "value": "reset"}]}}}]}, {"type": "group", "name": "状态", "items": [{"name": "危险", "path": ["danger"], "setter": "Boolean"}, {"name": "幽灵", "path": ["ghost"], "setter": "Boolean"}, {"name": "禁用", "path": ["disabled"], "setter": "Boolean"}, {"name": "加载中", "path": ["loading"], "setter": "Boolean"}]}]}, {"type": "group", "name": "样式", "items": []}, {"type": "group", "name": "渲染", "items": []}, {"type": "group", "name": "交互", "items": [{"type": "group", "name": "事件", "items": [{"name": "点击", "path": ["onClick"], "setter": "Action"}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "<PERSON><PERSON>"}}