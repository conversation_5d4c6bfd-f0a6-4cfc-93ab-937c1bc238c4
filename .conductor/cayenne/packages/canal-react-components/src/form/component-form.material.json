{"schemaVersion": "0.0.1", "runtimeVersion": "0.3.6", "type": "Form", "name": "表单", "icon": "./icon.png", "props": [{"name": "值", "tip": "表单项没有值，请使用内部表单控件的值", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "冒号", "path": ["colon"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "大", "value": "large"}, {"label": "中（默认）", "value": "middle"}, {"label": "小", "value": "small"}]}}}, {"name": "标签对齐", "path": ["labelAlign"], "setter": {"type": "Enum", "props": {"options": [{"label": "右", "value": "right"}, {"label": "左", "value": "left"}]}}}]}, {"type": "group", "name": "布局", "items": [{"name": "标签布局", "tip": "即 labelCol，例如：`{ \"span\": 3, \"offset\": 12 }`", "path": ["labelCol"], "setter": "JSON"}, {"name": "控件布局", "tip": "即 wrapperCol，例如：`{ \"span\": 12, \"offset\": 3 }`", "path": ["wrapperCol"], "setter": "JSON"}, {"name": "排列", "path": ["layout"], "setter": {"type": "Enum", "props": {"options": [{"label": "横向（默认）", "value": "horizontal"}, {"label": "纵向", "value": "vertical"}, {"label": "内联", "value": "inline"}]}}}]}]}, {"type": "group", "name": "样式", "items": []}, {"type": "group", "name": "渲染", "items": []}, {"type": "group", "name": "交互", "items": [{"type": "group", "name": "事件", "items": [{"name": "校验通过", "path": ["onFinish"], "setter": "Action"}]}]}], "defaultChildren": [{"type": "@ad/canal-components::FormItem", "name": "表单项", "props": {"label": "标签123", "jsonRules": [{"type": "required", "errMsg": "必填234234"}]}, "children": [{"type": "@ad/canal-components::Checkbox", "name": "多选框", "props": {"text": "显示"}}]}, {"type": "@ad/canal-components::FormItem", "name": "表单项", "props": {"label": "标签", "jsonRules": [{"type": "required", "errMsg": "必填"}]}, "children": [{"type": "@ad/canal-components::Input", "name": "输入框"}]}, {"type": "@ad/canal-components::Button", "name": "提交按钮", "props": {"text": "提交", "style": {"type": "static", "value": {"margin": "50px"}}, "type": "primary", "htmlType": "submit"}}], "implements": {"container": true, "style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Form"}}