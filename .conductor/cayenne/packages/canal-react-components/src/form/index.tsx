import { jsonRuleCreators, type JSONRule } from '@ad/canal-shared-ui'
import { Form as MUIForm } from '@m-ui/react'
import type { FormInstance, Rule } from '@m-ui/react/lib/form'
import { uniqueId } from 'lodash'
import React, {
  memo,
  useMemo,
  useRef,
  type ComponentProps,
  type FC,
  type ReactNode,
} from 'react'
import { useDeepCompareEffect, useFirstMountState, useLatest } from 'react-use'

/**
 * 表单属性
 */
export interface FormProps extends ComponentProps<typeof MUIForm> {}

/**
 * 表单
 */
export const Form: FC<FormProps> = memo((props) => {
  return <MUIForm {...props} />
})

/**
 * 表单项属性
 */
export interface FormItemProps extends ComponentProps<typeof MUIForm.Item> {
  /**
   * JSON 规则
   */
  jsonRules?: JSONRule[]
  /**
   * （校验）错误信息，由前后端脚本动态校验生成
   */
  errMsgs?: string[]
}

/**
 * 表单项
 */
export const FormItem: FC<FormItemProps> = memo(
  ({ jsonRules, errMsgs, rules, name, ...restProps }) => {
    const finalJSONRules = useMemo(() => jsonRules || [], [jsonRules])
    const finalErrMsgs = useMemo(() => errMsgs || [], [errMsgs])
    const finalName = useMemo(() => name || uniqueId('n'), [name])
    const finalRules: Rule[] = useMemo(() => {
      const rs: Rule[] = [...(rules || [])]
      for (const jsonRule of finalJSONRules) {
        rs.push(jsonRuleCreators[jsonRule.type](jsonRule))
      }
      for (const errMsg of finalErrMsgs) {
        rs.push({
          validator() {
            return Promise.reject(errMsg)
          },
        })
      }
      return rs
    }, [finalErrMsgs, finalJSONRules, rules])
    const refForm = useRef<FormInstance>()
    const isFirstMount = useFirstMountState()
    const refLatestIsFirstMount = useLatest(isFirstMount)
    useDeepCompareEffect(() => {
      if (refLatestIsFirstMount.current) {
        return
      }
      // finalErrMsgs 变化后，自动校验一次
      void finalJSONRules, finalErrMsgs
      refForm.current?.validateFields([finalName])
    }, [finalErrMsgs, finalJSONRules, finalName, refLatestIsFirstMount])
    return (
      <MUIForm.Item noStyle shouldUpdate>
        {(form): ReactNode => {
          refForm.current = form as FormInstance
          return (
            <MUIForm.Item {...restProps} name={finalName} rules={finalRules} />
          )
        }}
      </MUIForm.Item>
    )
  },
)
