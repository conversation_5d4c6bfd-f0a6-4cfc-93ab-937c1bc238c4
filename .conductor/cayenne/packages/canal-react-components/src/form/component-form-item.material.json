{"schemaVersion": "0.0.1", "runtimeVersion": "0.3.6", "type": "FormItem", "name": "表单项", "icon": "./icon.png", "props": [{"name": "值", "tip": "表单项没有值，请使用内部表单控件的值", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "标签", "path": ["label"], "defaultValue": "标签", "setter": "String"}, {"name": "冒号", "path": ["colon"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "标签对齐", "path": ["labelAlign"], "setter": {"type": "Enum", "props": {"options": [{"label": "右", "value": "right"}, {"label": "左", "value": "left"}]}}}, {"name": "名称", "tip": "一般不需要填，默认会生成一个唯一的值", "path": ["name"], "setter": "String"}]}, {"type": "group", "name": "校验", "items": [{"name": "规则", "tip": "由前端执行", "path": ["jsonRules"], "setter": "JSONRule"}, {"name": "错误信息", "tip": "建议绑定表达式，在后端执行生成，类型：`string[]`", "path": ["errMsgs"], "setter": "JSON"}]}, {"type": "group", "name": "布局", "items": [{"name": "标签布局", "tip": "即 labelCol，例如：`{ \"span\": 3, \"offset\": 12 }`", "path": ["labelCol"], "setter": "JSON"}, {"name": "控件布局", "tip": "即 wrapperCol，例如：`{ \"span\": 12, \"offset\": 3 }`", "path": ["wrapperCol"], "setter": "JSON"}]}, {"type": "group", "name": "提示", "items": [{"name": "底部", "path": ["extra"], "setter": "String"}, {"name": "悬浮", "path": ["tooltip"], "setter": "String"}]}]}], "implements": {"container": true, "style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "FormItem"}}