{"schemaVersion": "0.0.1", "runtimeVersion": "0.3.6", "type": "Checkbox", "name": "多选框", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`boolean`", "declaration": "boolean", "path": ["value"], "setter": "Boolean"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "文本", "path": ["text"], "setter": "String", "defaultValue": "显示"}]}, {"type": "group", "name": "状态", "items": [{"name": "禁用", "path": ["disabled"], "setter": "Boolean"}, {"name": "未决定", "path": ["indeterminate"], "setter": "Boolean"}, {"name": "自动聚焦", "path": ["autoFocus"], "setter": "Boolean"}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./checkbox.tsx"}, "exportIdentifier": "Checkbox"}}