import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Checkbox as MUICheckbox } from '@m-ui/react'
import type { CheckboxChangeEvent } from '@m-ui/react/lib/checkbox'
import React, {
  memo,
  useCallback,
  type ComponentProps,
  type FC,
  type ReactNode,
} from 'react'

/**
 * 多选框属性
 */
export type CheckboxProps = Omit<
  ComponentProps<typeof MUICheckbox>,
  'value' | 'onChange'
> &
  ValueOnChangeProps<boolean> & {
    /**
     * 文本（内容）
     */
    text?: ReactNode
  }

/**
 * 多选框
 */
export const Checkbox: FC<CheckboxProps> = memo(
  ({ text, value, onChange, ...restProps }) => {
    const handleChange = useCallback(
      (e: CheckboxChangeEvent) => {
        onChange?.(e.target.checked)
      },
      [onChange],
    )
    return (
      <MUICheckbox {...restProps} checked={!!value} onChange={handleChange}>
        {text}
      </MUICheckbox>
    )
  },
)
