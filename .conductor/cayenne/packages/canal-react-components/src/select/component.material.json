{"schemaVersion": "0.0.1", "runtimeVersion": "1.2.2", "type": "Select", "name": "选择器", "icon": "./icon.png", "props": [{"name": "值", "tip": "值类型：`string | number | (string | number)[]`", "declaration": "string | number | (string | number)[]", "path": ["value"], "setter": "String"}, {"type": "group", "name": "内容", "items": [{"type": "group", "name": "常用", "items": [{"name": "选项", "tip": "类型为：`(string | number)[] | { label: string, value: (string | number | boolean)[] }[]`", "path": ["options"], "setter": "JSON", "defaultValue": [{"label": "a", "value": 1}, {"label": "b", "value": 2}]}, {"name": "大小", "path": ["size"], "setter": {"type": "Enum", "props": {"options": [{"label": "大", "value": "large"}, {"label": "中（默认）", "value": "middle"}, {"label": "小", "value": "small"}]}}}, {"name": "模式", "path": ["mode"], "setter": {"type": "Enum", "props": {"options": [{"label": "多选", "value": "multiple"}, {"label": "标签", "value": "tags"}]}}}, {"name": "占位", "path": ["placeholder"], "setter": "String"}, {"name": "可清空", "path": ["allowClear"], "setter": "Boolean"}]}, {"type": "group", "name": "状态", "items": [{"name": "禁用", "path": ["disabled"], "setter": "Boolean"}, {"name": "加载中", "path": ["loading"], "setter": "Boolean"}]}, {"type": "group", "name": "搜索", "items": [{"name": "支持搜索", "path": ["showSearch"], "setter": "Boolean"}, {"name": "无内容提示", "path": ["notFoundContent"], "setter": "String"}]}, {"type": "group", "name": "高级", "items": [{"name": "虚拟滚动", "path": ["virtual"], "setter": "Boolean"}, {"name": "自动聚焦", "path": ["autoFocus"], "setter": "Boolean"}, {"name": "边框", "path": ["bordered"], "setter": {"type": "Boolean", "props": {"defaultValue": true}}}, {"name": "列表高度", "path": ["listHeight"], "setter": "Number"}]}]}], "implements": {"style": true, "condition": true, "loop": true}, "code": {"code": {"js": "./index.tsx"}, "exportIdentifier": "Select"}}