{"name": "@ad/canal-react-components", "version": "2.6.3", "description": "大运河 React 组件", "repository": {"type": "git", "url": "*************************:ks-ad/ad-fe/grandcanal/canal.git"}, "license": "MIT", "author": "AD", "sideEffects": false, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "scripts": {"build": "pnpm run build:online", "build:online": "rm -rf dist && kmi build && rollup --config rollup.config.ts --configPlugin typescript", "build:staging": "rm -rf dist && kmi build --env staging && rollup --config rollup.config.ts --configPlugin typescript", "start": "HMR=false PORT=8071 kmi dev & rollup --config rollup.config.ts --configPlugin typescript --watch", "upload": "kmi upload", "upload:staging": "kmi upload --env staging"}, "dependencies": {"@ad/canal-react-component-context": "workspace:^", "@ad/canal-shared": "workspace:^", "@ad/canal-shared-ui": "workspace:^", "@ad/e2e-schema": "workspace:^", "@types/lodash": "^4.14.199", "lodash": "^4.17.21", "moment": "^2.30.1", "react-use": "^17.4.0"}, "devDependencies": {"@kael/bundler-plugin-material": "1.0.0-rc.190", "@kmi/react": "^1.2.24", "@m-ui/react": "^1.12.2", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.4", "@types/react": "^18.2.23", "react": "^18.2.0", "rollup": "^3.29.3", "rollup-plugin-exclude-dependencies-from-bundle": "1.1.23", "styled-components": "^6.0.8"}, "peerDependencies": {"@m-ui/react": ">=1.0.0", "react": ">=16.8.0", "styled-components": ">=6.0.0"}}