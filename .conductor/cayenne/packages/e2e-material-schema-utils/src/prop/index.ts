import { E2ECMSFieldProp } from '@ad/e2e-material-schema'
import { CMSProp } from '@kael/material-schema'

/**
 * 判断是否是字段属性
 * @param prop 属性
 */
export function isFieldProp(prop: CMSProp): prop is E2ECMSFieldProp {
  return prop.type !== 'group'
}

/**
 * 判断是否是 px2vw/px2rem 属性
 * @param prop 属性
 */
export function isPx2Prop(prop: CMSProp): prop is E2ECMSFieldProp {
  return isFieldProp(prop) && !!(prop.px2vw || prop.px2rem)
}
