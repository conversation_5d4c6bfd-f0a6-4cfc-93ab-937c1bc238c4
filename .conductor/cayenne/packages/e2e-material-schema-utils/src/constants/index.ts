import type { E2EImplementsProps } from '@ad/e2e-material-schema'
import { CanalImplementsField } from '@ad/e2e-schema-utils'
import { DEFAULT_IMPLEMENTS_PROPS } from '@kael/material-utils'

/**
 * 内部组件库（本地）端口
 */
export const INTERNAL_COMPONENT_LIB_PORT = 8071

/**
 * 内部组件库名
 */
export const INTERNAL_COMPONENT_LIB_NAME = '@ad/canal-components'

/**
 * 内部 React 组件库名
 */
export const INTERNAL_REACT_COMPONENT_LIB_NAME = '@ad/canal-react-components'

/**
 * 老业务组件库名，用于收拢 Schema 里用的老业务组件
 */
export const OLD_BIZ_COMPONENT_LIB_NAME = '@ad/old-canal-biz-components'

/**
 * 本地组件库名
 */
export const LOCAL_COMPONENT_LIB_NAME = '@ad/canal-local-components'

/**
 * 继承属性
 */
export const IMPLEMENTS_PROPS: E2EImplementsProps = {
  ...DEFAULT_IMPLEMENTS_PROPS,
  style: [
    {
      type: 'group',
      name: '样式',
      items: [
        {
          type: 'group',
          name: '布局',
          path: ['style'],
          items: [
            {
              name: '宽度',
              path: ['width'],
              setter: 'String',
              px2vw: true,
            },
            {
              name: '高度',
              path: ['height'],
              setter: 'String',
              px2vw: true,
            },
            {
              name: '内边距',
              path: ['padding'],
              setter: 'EdgeShorthand',
              px2vw: true,
            },
            {
              name: '外边距',
              path: ['margin'],
              setter: 'EdgeShorthand',
              px2vw: true,
            },
            {
              name: '排版方式',
              type: 'field',
              path: ['display'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    { label: '隐藏（none）', value: 'none' },
                    { label: '块（block）', value: 'block' },
                    { label: '内联（inline）', value: 'inline' },
                    { label: '内联块（inline-block）', value: 'inline-block' },
                    { label: '弹性（flex）', value: 'flex' },
                  ],
                },
              },
            },
            {
              name: '主轴方向',
              type: 'field',
              path: ['flexDirection'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    { label: '行（row）', value: 'row' },
                    { label: '反向行（row-reverse）', value: 'row-reverse' },
                    { label: '列（column）', value: 'column' },
                    {
                      label: '反向列（column-reverse）',
                      value: 'column-reverse',
                    },
                  ],
                },
              },
              effect: {
                dependencies: [['style', 'display']],
                fn: `(propValues) => {
                  return { visible: propValues.style.display === 'flex' }
                }`,
              },
            },
            {
              name: '主轴对齐',
              type: 'field',
              path: ['justifyContent'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    { label: '起点对齐（flex-start）', value: 'flex-start' },
                    { label: '终点对齐（flex-end）', value: 'flex-end' },
                    { label: '居中（center）', value: 'center' },
                    {
                      label: '空白间隔（space-between）',
                      value: 'space-between',
                    },
                    {
                      label: '空白环绕（space-around）',
                      value: 'space-around',
                    },
                  ],
                },
              },
              effect: {
                dependencies: [['style', 'display']],
                fn: `(propValues) => {
                  return { visible: propValues.style.display === 'flex' }
                }`,
              },
            },
            {
              name: '辅轴对齐',
              type: 'field',
              path: ['alignItems'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    { label: '起点对齐（flex-start）', value: 'flex-start' },
                    { label: '终点对齐（flex-end）', value: 'flex-end' },
                    { label: '居中（center）', value: 'center' },
                    {
                      label: '文字对齐（baseline）',
                      value: 'baseline',
                    },
                    {
                      label: '高度占满（stretch）',
                      value: 'stretch',
                    },
                  ],
                },
              },
              effect: {
                dependencies: [['style', 'display']],
                fn: `(propValues) => {
                  return { visible: propValues.style.display === 'flex' }
                }`,
              },
            },
            {
              name: '换行',
              type: 'field',
              path: ['flexWrap'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    { label: '不换行（nowrap）', value: 'nowrap' },
                    { label: '换行（wrap）', value: 'wrap' },
                    {
                      label: '反向换行（wrap-reverse）',
                      value: 'wrap-reverse',
                    },
                  ],
                },
              },
              effect: {
                dependencies: [['style', 'display']],
                fn: `(propValues) => {
                  return { visible: propValues.style.display === 'flex' }
                }`,
              },
            },
            {
              name: '弹性缩放',
              tip: '弹性布局中子元素的比例，即: style.flex',
              path: ['flex'],
              setter: 'String',
            },
          ],
        },
        {
          type: 'group',
          name: '字体',
          path: ['style'],
          items: [
            {
              name: '大小',
              type: 'field',
              path: ['fontSize'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    { label: 12, value: '12px' },
                    { label: 14, value: '14px' },
                    { label: 16, value: '16px' },
                    { label: 18, value: '18px' },
                    { label: 20, value: '20px' },
                    { label: 22, value: '22px' },
                    { label: 24, value: '24px' },
                    { label: 26, value: '26px' },
                    { label: 28, value: '28px' },
                    { label: 30, value: '30px' },
                    { label: 32, value: '32px' },
                    { label: 34, value: '34px' },
                    { label: 36, value: '36px' },
                    { label: 38, value: '38px' },
                    { label: 40, value: '40px' },
                    { label: 42, value: '42px' },
                    { label: 44, value: '44px' },
                    { label: 46, value: '46px' },
                    { label: 48, value: '48px' },
                    { label: 50, value: '50px' },
                    { label: 52, value: '52px' },
                    { label: 54, value: '54px' },
                    { label: 56, value: '56px' },
                    { label: 58, value: '58px' },
                    { label: 60, value: '60px' },
                  ],
                },
              },
              px2vw: true,
            },
            {
              name: '粗细',
              type: 'field',
              path: ['fontWeight'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    { label: '100', value: 100 },
                    { label: '200', value: 200 },
                    { label: '300', value: 300 },
                    { label: '400 (normal)', value: 400 },
                    { label: '500', value: 500 },
                    { label: '600', value: 600 },
                    { label: '700 (bold)', value: 700 },
                    { label: '800', value: 800 },
                    { label: '900', value: 900 },
                  ],
                },
              },
            },
            {
              name: '颜色',
              path: ['color'],
              setter: 'Color',
            },
          ],
        },
        {
          type: 'group',
          name: '背景',
          path: ['style'],
          items: [
            {
              name: '颜色',
              path: ['background'],
              setter: 'Color',
            },
          ],
        },
        {
          type: 'group',
          name: '边框',
          path: ['style'],
          items: [
            {
              name: '宽度',
              path: ['borderWidth'],
              setter: 'String',
              px2vw: true,
            },
            {
              name: '类型',
              path: ['borderStyle'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    { label: '实线', value: 'solid' },
                    { label: '虚线', value: 'dashed' },
                    { label: '点线', value: 'dotted' },
                  ],
                },
              },
            },
            {
              name: '颜色',
              path: ['borderColor'],
              setter: 'Color',
            },
            {
              name: '圆角',
              path: ['borderRadius'],
              setter: 'BorderRadius',
              px2vw: true,
            },
          ],
        },
        {
          type: 'group',
          name: '位置',
          path: ['style'],
          items: [
            {
              name: '定位',
              path: ['position'],
              setter: {
                type: 'Enum',
                props: {
                  options: [
                    {
                      label: 'static',
                      value: 'static',
                    },
                    {
                      label: 'relative',
                      value: 'relative',
                    },
                    {
                      label: 'absolute',
                      value: 'absolute',
                    },
                    {
                      label: 'fixed',
                      value: 'fixed',
                    },
                    {
                      label: 'sticky',
                      value: 'sticky',
                    },
                  ],
                },
              },
            },
            {
              name: '上',
              path: ['top'],
              setter: 'String',
              px2vw: true,
            },
            {
              name: '下',
              path: ['bottom'],
              setter: 'String',
              px2vw: true,
            },
            {
              name: '左',
              path: ['left'],
              setter: 'String',
              px2vw: true,
            },
            {
              name: '右',
              path: ['right'],
              setter: 'String',
              px2vw: true,
            },
            {
              name: 'z 轴顺序',
              path: ['zIndex'],
              setter: 'Number',
            },
          ],
        },
      ],
    },
  ],
  condition: [
    {
      type: 'group',
      name: '渲染',
      items: [
        {
          type: 'group',
          name: '条件渲染',
          items: [
            {
              name: '是否渲染',
              tip: '是否创建（虚拟）DOM 节点',
              path: [CanalImplementsField.IF],
              setter: {
                type: 'Boolean',
                props: {
                  defaultValue: true,
                },
              },
            },
          ],
        },
      ],
    },
  ],
  loop: [
    {
      type: 'group',
      name: '渲染',
      items: [
        {
          type: 'group',
          name: '列表渲染',
          path: [CanalImplementsField.FOR],
          items: [
            {
              name: '列表数据',
              tip: '值类型：`unknown[]`',
              path: ['items'],
              setter: 'JSON',
            },
            {
              name: '数据项键值',
              tip: '值类型：`string`',
              path: ['key'],
              setter: 'String',
            },
          ],
        },
      ],
    },
  ],
}
