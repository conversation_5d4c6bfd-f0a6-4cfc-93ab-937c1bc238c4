import {
  E2ERemoteComponentLibraryMaterialSchema,
  E2ERemoteComponentMaterialSchema,
} from '@ad/e2e-material-schema'
import type { CLMSGroup } from '@kael/material-schema'
import {
  ImplementsProps,
  getComponentMaterialPropsMergedByImplements,
  getKaelMaterialLibSchemaUrl,
  getVersions,
} from '@kael/material-utils'
import { isString, last } from 'lodash'
import { IMPLEMENTS_PROPS } from '../constants'

/**
 * 合并继承属性到组件物料 Schema
 * @param componentMaterialSchema 组件物料 Schema
 */
export function mergeComponentWithImplementProps(
  componentMaterialSchema: E2ERemoteComponentMaterialSchema,
): E2ERemoteComponentMaterialSchema {
  return {
    ...componentMaterialSchema,
    props: getComponentMaterialPropsMergedByImplements(
      componentMaterialSchema,
      IMPLEMENTS_PROPS as ImplementsProps,
    ),
  }
}

/**
 * 合并继承属性到组件库物料 Schema
 * @param materialSchema 组件库物料 Schema
 */
export function mergeComponentLibWithImplementProps(
  materialSchema: E2ERemoteComponentLibraryMaterialSchema,
): E2ERemoteComponentLibraryMaterialSchema {
  return {
    ...materialSchema,
    components: materialSchema.components.map(mergeComponentWithImplementProps),
  }
}

/**
 * 获取组件分组名
 * @param groups 所有分组
 * @param componentType 组件类型
 */
export function getComponentGroupName(
  groups: CLMSGroup[],
  componentType: string,
): string | null {
  const groupPath = findPath(groups)
  return groupPath.length ? groupPath.map((g) => `${g.name}`).join('-') : null

  function findPath(gs: CLMSGroup[]): CLMSGroup[] {
    for (const g of gs) {
      const { items } = g
      if (isString(items[0])) {
        if ((items as string[]).includes(componentType)) {
          return [g]
        }
      } else {
        const path = findPath(items as CLMSGroup[])
        if (path.length) {
          return [g, ...path]
        }
      }
    }
    return []
  }
}

/**
 * 获取最新版本的组件库物料 Schema URL
 * @param libName 组件库名
 * @param env 环境
 */
export async function getLatestMaterialLibSchemaUrl(
  libName: string,
  env: 'production' | 'staging',
): Promise<string> {
  const versions = await getVersions(libName, env)
  return await getKaelMaterialLibSchemaUrl(
    libName,
    last(versions) as string,
    env,
  )
}
