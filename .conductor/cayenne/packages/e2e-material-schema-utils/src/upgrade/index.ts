import { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import {
  E2ESchema,
  E2ESchemaComponent,
  E2ESchemaExpression,
} from '@ad/e2e-schema'
import {
  Arrayable,
  arrayableToArray,
  isBindableExpression,
} from '@ad/e2e-schema-utils'
import {
  deleteComponentPropExpression,
  getComponentPropExpression,
  setComponentPropExpression,
} from '@kael/schema-utils'
import { cloneDeep, fromPairs, intersection, isUndefined, xor } from 'lodash'
import { genAllFieldPropDetails } from '../traverse'

/**
 * 升级组件属性
 * @param component 组件
 * @param oldComponentMaterial 旧的组件物料
 * @param newComponentMaterial 新的组件物料
 */
export function upgradeComponentProps(
  component: Arrayable<E2ESchemaComponent>,
  oldComponentMaterial: E2ERemoteComponentMaterialSchema,
  newComponentMaterial: E2ERemoteComponentMaterialSchema,
): void {
  const oldFieldPropDetailsByPath = fromPairs(
    Array.from(genAllFieldPropDetails(oldComponentMaterial), (pd) => [
      JSON.stringify(pd.path),
      pd,
    ]),
  )
  const newFieldPropDetailsByPath = fromPairs(
    Array.from(genAllFieldPropDetails(newComponentMaterial), (pd) => [
      JSON.stringify(pd.path),
      pd,
    ]),
  )
  const upgradePathStrs = intersection(
    Object.keys(oldFieldPropDetailsByPath),
    Object.keys(newFieldPropDetailsByPath),
  )
  const deletePathStrs = xor(
    Object.keys(oldFieldPropDetailsByPath),
    upgradePathStrs,
  )
  const addPathStrs = xor(
    Object.keys(newFieldPropDetailsByPath),
    upgradePathStrs,
  )
  for (const comp of arrayableToArray(component)) {
    for (const deletePathStr of deletePathStrs) {
      deleteComponentPropExpression(
        comp,
        oldFieldPropDetailsByPath[deletePathStr].path,
      )
    }
    for (const addPathStr of addPathStrs) {
      const newDetail = newFieldPropDetailsByPath[addPathStr]
      if (isUndefined(newDetail.filedProp.defaultValue)) {
        continue
      }
      setComponentPropExpression(
        comp,
        newDetail.path,
        cloneDeep(newDetail.filedProp.defaultValue),
      )
    }
    for (const upgradePathStr of upgradePathStrs) {
      const newDetail = newFieldPropDetailsByPath[upgradePathStr]
      switch (newDetail.filedProp.upgrade?.type) {
        case 'overwrite': {
          if (
            Number(oldComponentMaterial.version) >
            Number(newDetail.filedProp.upgrade.versionLTE)
          ) {
            continue
          }
          const currentExpr = getComponentPropExpression<E2ESchema>(
            comp,
            newDetail.path,
          )
          if (isBindableExpression(currentExpr)) {
            const newExpr = {
              ...currentExpr,
            }
            if (isUndefined(newDetail.filedProp.defaultValue)) {
              delete newExpr.defaultValue
            } else {
              newExpr.defaultValue = cloneDeep(
                newDetail.filedProp.defaultValue,
              ) as E2ESchemaExpression
            }
            setComponentPropExpression(comp, newDetail.path, newExpr)
          } else {
            if (isUndefined(newDetail.filedProp.defaultValue)) {
              deleteComponentPropExpression(comp, newDetail.path)
            } else {
              setComponentPropExpression(
                comp,
                newDetail.path,
                cloneDeep(newDetail.filedProp.defaultValue),
              )
            }
          }
          break
        }
      }
    }
  }
}
