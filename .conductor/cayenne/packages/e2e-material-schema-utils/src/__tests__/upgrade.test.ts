import { E2ESchemaComponent } from '@ad/e2e-schema'
import { upgradeComponentProps } from '../upgrade'

describe('upgrade', () => {
  it('upgradeComponentProps', () => {
    let comp: E2ESchemaComponent | E2ESchemaComponent[]

    upgradeComponentProps(
      (comp = {
        id: 'comp_wOqCPhSCVnDplr-i5V4nY',
        type: '@ad/canal-biz-components::todo',
        name: '按钮::4nY',
        props: {
          ddd: 'dd',
          text: '文本',
          style: {
            type: 'static',
            value: {
              fontSize: 3,
            },
          },
        },
      }),
      {
        schemaVersion: '0.0.1',
        type: 'todo',
        name: '按钮',
        icon: 'iiii',
        description: 'dddd',
        code: {
          code: {
            js: 'j',
          },
        },
        version: '1',
        props: [
          {
            type: 'group',
            name: '内容',
            items: [
              {
                type: 'group',
                name: '常用',
                path: [],
                items: [
                  {
                    name: '文本',
                    type: 'field',
                    path: ['ddd'],
                    defaultValue: '文本',
                    setter: 'String',
                  },
                  {
                    name: '文本',
                    type: 'field',
                    path: ['text'],
                    defaultValue: '文本',
                    setter: 'String',
                  },
                ],
              },
              {
                type: 'group',
                name: 'sssss',
                path: ['style'],
                items: [
                  {
                    name: 'daxxx',
                    type: 'field',
                    path: ['fontSize'],
                    defaultValue: 33,
                    setter: 'Number',
                  },
                ],
              },
            ],
          },
        ],
        implements: {
          style: true,
        },
      },
      {
        schemaVersion: '0.0.1',
        type: 'todo',
        name: '按钮',
        icon: 'iiii',
        description: 'dddd',
        code: {
          code: {
            js: 'j',
          },
        },
        version: '3',
        props: [
          {
            type: 'group',
            name: '内容',
            items: [
              {
                type: 'group',
                name: '常用',
                path: [],
                items: [
                  {
                    name: '文本',
                    type: 'field',
                    path: ['text'],
                    defaultValue: '文本33',
                    setter: 'String',
                  },
                ],
              },
              {
                type: 'group',
                name: '常用333',
                path: ['a'],
                items: [
                  {
                    name: '文本',
                    type: 'field',
                    path: ['text'],
                    defaultValue: '文本33',
                    setter: 'String',
                  },
                ],
              },
              {
                type: 'group',
                name: 'sssss',
                path: ['style'],
                items: [
                  {
                    name: 'daxxx',
                    type: 'field',
                    path: ['fontSize'],
                    defaultValue: {
                      type: 'js',
                      code: '3 + 1',
                    },
                    setter: 'Number',
                    upgrade: {
                      type: 'overwrite',
                      versionLTE: '1',
                    },
                  },
                ],
              },
            ],
          },
        ],
        implements: {
          style: true,
        },
      },
    )
    expect(comp).toEqual({
      id: 'comp_wOqCPhSCVnDplr-i5V4nY',
      type: '@ad/canal-biz-components::todo',
      name: '按钮::4nY',
      props: {
        ddd: undefined,
        text: '文本',
        a: {
          type: 'static',
          value: {
            text: '文本33',
          },
        },
        style: {
          type: 'object',
          value: {
            fontSize: {
              type: 'js',
              code: '3 + 1',
            },
          },
        },
      },
    })

    upgradeComponentProps(
      (comp = {
        id: 'comp_wOqCPhSCVnDplr-i5V4nY',
        type: '@ad/canal-biz-components::todo',
        name: '按钮::4nY',
        props: {
          text: {
            type: 'api',
            apiId: 'dfs',
            defaultValue: '文本',
          },
          text2: {
            type: 'api',
            apiId: 'dfs',
            defaultValue: '文本2',
          },
        },
      }),
      {
        schemaVersion: '0.0.1',
        type: 'todo',
        name: '按钮',
        icon: 'iiii',
        description: 'dddd',
        code: {
          code: {
            js: 'j',
          },
        },
        version: '1',
        props: [
          {
            type: 'group',
            name: '内容',
            items: [
              {
                type: 'group',
                name: '常用',
                path: [],
                items: [
                  {
                    name: '文本',
                    type: 'field',
                    path: ['text'],
                    defaultValue: '文本dd',
                    setter: 'String',
                  },
                  {
                    name: '文本',
                    type: 'field',
                    path: ['text2'],
                    defaultValue: '文本dd',
                    setter: 'String',
                  },
                ],
              },
            ],
          },
        ],
        implements: {
          style: true,
        },
      },
      {
        schemaVersion: '0.0.1',
        type: 'todo',
        name: '按钮',
        icon: 'iiii',
        description: 'dddd',
        code: {
          code: {
            js: 'j',
          },
        },
        version: '3',
        props: [
          {
            type: 'group',
            name: '内容',
            items: [
              {
                type: 'group',
                name: '常用',
                path: [],
                items: [
                  {
                    name: '文本',
                    type: 'field',
                    path: ['text'],
                    defaultValue: 11111,
                    setter: 'Number',
                  },
                  {
                    name: '文本',
                    type: 'field',
                    path: ['text2'],
                    defaultValue: 22222,
                    setter: 'Number',
                    upgrade: {
                      type: 'overwrite',
                      versionLTE: '2',
                    },
                  },
                ],
              },
            ],
          },
        ],
        implements: {
          style: true,
        },
      },
    )
    expect(comp).toEqual({
      id: 'comp_wOqCPhSCVnDplr-i5V4nY',
      type: '@ad/canal-biz-components::todo',
      name: '按钮::4nY',
      props: {
        text: {
          type: 'api',
          apiId: 'dfs',
          defaultValue: '文本',
        },
        text2: {
          type: 'api',
          apiId: 'dfs',
          defaultValue: 22222,
        },
      },
    })
  })
})
