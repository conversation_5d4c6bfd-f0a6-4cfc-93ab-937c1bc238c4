import {
  E2ECMSFieldProp,
  E2ECMSGroupProp,
  E2ECMSProp,
  E2ERemoteComponentLibraryMaterialSchema,
  E2ERemoteComponentMaterialSchema,
} from '@ad/e2e-material-schema'

/**
 * 字段属性详情
 */
export interface FieldPropDetail {
  /**
   * 字段属性
   */
  filedProp: E2ECMSFieldProp
  /**
   * 路径，合并了上层的属性组路径
   */
  path: string[]
  /**
   * 所属的属性组
   */
  groupProps: E2ECMSGroupProp[]
}

/**
 * 生成所有的字段属性详情
 * @param component 组件物料 Schema
 */
export function* genAllFieldPropDetails(
  component: E2ERemoteComponentMaterialSchema,
): Generator<FieldPropDetail> {
  for (const prop of component.props || []) {
    yield* genAllFieldPropDetailsByProp(prop)
  }
}

/**
 * 通过属性生成所有的字段属性详情
 * @param filedProp 字段属性
 * @param groupProps 所属的属性组
 */
export function* genAllFieldPropDetailsByProp(
  prop: E2ECMSProp,
  groupProps: E2ECMSGroupProp[] = [],
): Generator<FieldPropDetail> {
  if (prop.type === 'group') {
    for (const childProp of prop.items) {
      yield* genAllFieldPropDetailsByProp(childProp, [...groupProps, prop])
    }
  } else {
    yield {
      filedProp: prop,
      path: [...groupProps.flatMap((g) => g.path || []), ...prop.path],
      groupProps,
    }
  }
}

/**
 * 字段属性详情（在组件库）
 */
export interface FieldPropDetailInLibrary extends FieldPropDetail {
  /**
   * 组件物料 Schema
   */
  component: E2ERemoteComponentMaterialSchema
}

/**
 * 生成所有的字段属性详情（在组件库）
 * @param library 组件库物料 Schema
 */
export function* genAllFieldPropDetailsInLibrary(
  library: E2ERemoteComponentLibraryMaterialSchema,
): Generator<FieldPropDetailInLibrary> {
  for (const component of library.components || []) {
    for (const detail of genAllFieldPropDetails(component)) {
      yield {
        component,
        ...detail,
      }
    }
  }
}

/**
 * 字段属性详情（在多个组件库）
 */
export interface FieldPropDetailInLibraries extends FieldPropDetailInLibrary {
  /**
   * 组件库名
   */
  libraryName: string
}

/**
 * 生成所有的字段属性详情（在多个组件库）
 * @param library 组件库物料 Schema
 */
export function* genAllFieldPropDetailsInLibraries(
  libraries: Record<string, E2ERemoteComponentLibraryMaterialSchema>,
): Generator<FieldPropDetailInLibraries> {
  for (const libraryName in libraries) {
    const library = libraries[libraryName]
    for (const detail of genAllFieldPropDetailsInLibrary(library)) {
      yield {
        libraryName,
        ...detail,
      }
    }
  }
}
