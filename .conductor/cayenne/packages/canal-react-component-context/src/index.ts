import { E2ESchema, E2EServerSchema, RefreshType } from '@ad/e2e-schema'
import { Component, PurePlugin } from '@kael/runtime'
import type { Weblog } from '@ks/weblogger'
import { MutableRefObject, createContext, useContext, type FC } from 'react'

/**
 * 大运河 React 运行时子模块（React）函数组件属性
 */
export interface CanalReactRuntimeSubmoduleProps {
  /**
   * 端到端 Schema
   */
  schema?: E2ESchema | E2EServerSchema
  /**
   * Schema 文件 ID
   */
  schemaId?: string | number
  /**
   * 配置
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  config?: any
  /**
   * 额外需要加载的插件
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  plugins?: PurePlugin<any, any>[]
  /**
   * 配置参数
   */
  params?: Record<string, unknown>
  /**
   * 默认组件库
   */
  defaultComponentLib?: Record<string, unknown>
  /**
   * 大运河 React 运行时引用
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  refCanalReactRuntime?: MutableRefObject<any>
  /**
   * 最大嵌套层数
   */
  maxNestCount?: number
}

/**
 * 子模块渲染函数，可以防止动态创建函数组件导致的 Fiber 子树销毁
 */
export interface SubmoduleRenderFn {
  /**
   * 子模块渲染函数
   */
  (props: CanalReactRuntimeSubmoduleProps): JSX.Element
}

/**
 * 大运河 React 运行时子模块（React）函数组件上下文
 */
export const canalReactRuntimeSubmoduleFCContext =
  createContext<SubmoduleRenderFn | null>(null)

/**
 * 大运河 React 运行时加载中信息
 */
export interface CanalReactRuntimeLoadingInfo {
  /**
   * 是否加载中
   */
  isLoading: boolean
  /**
   * 是否刷新中
   */
  isRefreshing: boolean
  /**
   * 刷新中的类型
   */
  refreshingType?: RefreshType
  /**
   * 是否依赖加载中
   */
  isLoadingDependencies: boolean
  /**
   * 加载中（组件）
   */
  Loading: FC<LoadingProps>
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
}

/**
 * 加载中属性
 */
export type LoadingProps = Omit<
  CanalReactRuntimeLoadingInfo,
  'isLoading' | 'Loading'
> & {
  /**
   * 位置
   */
  position: LoadingPosition
}

/**
 * 加载中位置
 * * runtime: 运行时直接渲染
 * * container: 运行时内置容器渲染
 */
export type LoadingPosition = 'runtime' | 'container'

/**
 * 大运河 React 运行时加载中信息上下文
 */
export const canalReactRuntimeLoadingInfoContext =
  createContext<CanalReactRuntimeLoadingInfo | null>(null)

/**
 * 大运河 React 运行时组件上下文
 */
export const canalReactRuntimeComponentContext =
  createContext<Component | null>(null)

/**
 * 使用 welogger 实例，即用户传递给 CanalReactRuntimeFC 的 weblog 属性
 */
export function useWeblog(): Weblog | null {
  const comp = useContext(canalReactRuntimeComponentContext)
  return (
    (
      comp?.runtime.constructorOptions.config as {
        weblog?: Weblog
      }
    )?.weblog || null
  )
}
