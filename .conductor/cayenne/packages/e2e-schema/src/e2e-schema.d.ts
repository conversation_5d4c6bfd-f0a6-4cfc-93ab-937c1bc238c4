import { Code } from '@kael/material-schema'
import { Schema, SchemaComponent } from '@kael/schema'
import { PropertyPath } from '@kael/schema-utils'
import { EventOptions, EventType } from '@ks/weblogger/lib/types/types/event'
import {
  E2ESchemaExpression,
  E2ESchemaExpressionJS,
  RefreshType,
} from './e2e-expression'

/**
 * 端到端 Schema
 */
export interface E2ESchema extends Pick<Schema, 'schemaVersion'> {
  /**
   * （当前 Schema 文件的）版本
   */
  version?: string
  /**
   * 视图，即组件树根组件
   */
  view: E2ESchemaComponent
  /**
   * （全局）数据（初始值）
   *
   * 刷新端到端 Schema 时，后端可以用这个字段修改前端的数据
   */
  data?: Record<string, unknown>
  /**
   * 前端模型
   */
  model?: Model
  /**
   * 后端模型
   */
  backModel?: Model
  /**
   * 全局前端模型
   */
  globalFrontModel?: Model
  /**
   * 全局后端模型
   */
  globalBackModel?: Model
  /**
   * 联动
   */
  linkage?: Linkage
  /**
   * 埋点
   */
  tracks?: Track[]
  /**
   * 动画
   */
  animations?: Animation[]
  /**
   * 组件代码，组件类型 -> 代码定义
   */
  componentCodes?: Record<string, ComponentCode>
  /**
   * 立即执行的函数表达式，表达式会被立即执行，如果表达式返回一个函数，函数也会被立即执行
   *
   * 主要用于刷新 Schema 时，后端要求执行的操作，比如页面跳转
   */
  iife?: E2ESchemaExpression
  /**
   * 接口
   */
  apis?: E2EAPI[]
  /**
   * 骨架
   */
  skeleton?: Skeleton
  /**
   * 像素转视口宽度
   */
  px2vw?: Px2vwConfig
  /**
   * 像素转根元素字体大小宽度
   */
  px2rem?: Px2remConfig
}

/**
 * 端到端组件（实例）
 */
export interface E2ESchemaComponent extends Pick<SchemaComponent, 'type'> {
  /**
   * 组件实例 ID，自动生成
   */
  id: string
  /**
   * 组件实例名称，自动生成，可修改
   */
  name: string
  /**
   * 组件属性
   */
  props?: Record<string, E2ESchemaExpression>
  /**
   * 子组件
   */
  children?: E2ESchemaComponent[]
  /**
   * 副作用
   */
  effect?: E2ESchemaExpression
  /**
   * 接口，由后端计算并用于填充 props
   */
  apis?: E2EAPI[]
  /**
   * 条件渲染
   */
  if?: E2ESchemaExpression
  /**
   * 后端列表渲染
   */
  backFor?: E2ESchemaBackFor
  /**
   * 挂载区域
   */
  mountArea?: ClientMountArea
  /**
   * AB 实验
   */
  ab?: ABTest[]
}

/**
 * 端到端接口
 */
export type E2EAPI = E2EAPIRPC

/**
 * 端到端 RPC 接口
 */
export interface E2EAPIRPC {
  /**
   * 接口 ID，自动生成
   */
  id: string
  /**
   * 名称
   */
  name?: string
  /**
   * 类型
   */
  type?: 'rpc'
  /**
   * 服务
   */
  service?: string
  /**
   * 方法
   */
  method?: string
  /**
   * 数据源 ID
   */
  dataSourceId?: string
  /**
   * 参数
   */
  args?: E2ESchemaExpression[]
  /**
   * 是否执行（执行条件）
   */
  if?: E2ESchemaExpression
  /**
   * Mock 响应
   */
  mockRes?: Record<string, unknown>
}

/**
 * 后端列表渲染
 */
export interface E2ESchemaBackFor {
  /**
   * 列表数据
   */
  items: E2ESchemaExpression
  /**
   * 数据项键值
   */
  key?: E2ESchemaExpression
}

/**
 * 客户端挂载区域
 */
export interface ClientMountArea {
  /**
   * 容器 ID
   */
  containerId: string
}

/**
 * AB 实验
 */
export interface ABTest {
  /**
   * （AB 实验）键值
   */
  key: string
  /**
   * （命中的 AB 实验当然的）值
   */
  value: number | string
}

/**
 * 模型
 */
export interface Model {
  /**
   * JS 代码，编译后的
   */
  code: string
  /**
   * JS 代码，编译前的，编译到 ES5
   */
  codeES?: string
  /**
   * TS 代码，编译前的，后端编译到最新的 CJS，前端编译到 ES5
   */
  codeTS?: string
}

/**
 * 联动，通过后端刷新 Schema 来达成视图联动的能力
 */
export interface Linkage {
  /**
   * 通用（静态）参数，所有刷新请求都会携带，但优先级最低，可能会被覆盖部分字段
   */
  commonParams?: Record<string, unknown>
  /**
   * 组件（产生的）数据参数
   */
  componentDataParams?: LinkageComponentDataParams
  /**
   * 组件 ID 数组
   *
   * 通过组件（值变化）自动刷新
   *
   * 等价于组件值变化时，自动执行刷新表达式: `{ type: 'refresh', refreshType: 'auto' }`
   */
  autoRefreshByComponent?: string[]
}

/**
 * 联动里组件（产生的）数据参数
 */
export interface LinkageComponentDataParams {
  /**
   * 组件 ID 数组
   *
   * 通用组件产生的数据参数，所有刷新请求都会携带，优先级仅高于外层的 commonParams
   */
  common?: string[]
  /**
   * 刷新类型 -> 组件 ID 数组
   *
   * 通过刷新类型指定的组件产生的数据参数，优先级仅高于同层级的 common
   */
  byRefreshType?: Partial<Record<RefreshType, string[]>>
}

/**
 * 埋点
 * https://component.corp.kuaishou.com/docs/weblogger/views/docs/api.html#%E6%94%B6%E9%9B%86%E4%B8%8A%E6%8A%A5
 */
export interface Track {
  /**
   * ID
   */
  id: string
  /**
   * 事件类型
   */
  eventType: EventType
  /**
   * 事件参数
   */
  eventOptions: EventOptions<EventType>
  /**
   * 可重复上报，默认：true
   */
  canRepeat?: boolean
  /**
   * 埋点类型，比如：adLog、clientLog
   */
  logType?: string
  /**
   * 触发事件
   */
  trigger?: string
}

/**
 * 动画
 */
export interface Animation {
  /**
   * ID
   */
  id: string
  /**
   * 作用于哪个组件
   */
  componentId: string
  /**
   * 是否自动播放
   */
  autoplay: boolean
  /**
   * 触发事件
   */
  triggers: string[]
  /**
   * 时长
   */
  duration: number
  /**
   * 延迟时间
   */
  delay?: number
  /**
   * 播放次数，-1 表示循环播放
   */
  repeatCount: number
  /**
   * 动画（协议）数据
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  animationProtocol: any
  /**
   * 动画开始事件
   */
  onStart?: E2ESchemaExpression
  /**
   * 动画结束事件
   */
  onEnd?: E2ESchemaExpression
}

/**
 * 组件代码
 */
export type ComponentCode = Code & {
  /**
   * 组件版本
   */
  version?: string
}

/**
 * 骨架
 */
export interface Skeleton {
  /**
   * 图片 URL
   */
  imageUrl: string
  /**
   * 高
   */
  height: number
  /**
   * 宽
   */
  width: number
}

/**
 * 绑定立即执行（前端动作）动作
 */
export type BindIIFEAction =
  | BindIIFEActionOpenUrl
  | BindIIFEActionExecExternalFn
  | BindIIFEActionExecEffect

/**
 * 绑定立即执行（前端动作）动作类型
 */
export type BindIIFEActionType = BindIIFEAction['type']

/**
 * 动作：打开链接
 */
export interface BindIIFEActionOpenUrl {
  /**
   * 类型
   */
  type: 'open-url'
  /**
   * 链接
   */
  url: string
  /**
   * url 字段是一个路径
   */
  urlIsPath: boolean
  /**
   * 原地打开
   */
  inPlace: boolean
}

/**
 * 动作：执行外部函数
 */
export interface BindIIFEActionExecExternalFn {
  /**
   * 类型
   */
  type: 'exec-external-fn'
  /**
   * 函数路径（在运行时 data 里的路径）
   */
  fnPath: string
  /**
   * 参数（字符串）
   */
  arg0: string
  /**
   * arg0 字段是一个路径
   */
  arg0IsPath: boolean
}

/**
 * 动作：执行副作用
 */
export interface BindIIFEActionExecEffect {
  /**
   * 类型
   */
  type: 'exec-effect'
  /**
   * 表达式
   */
  expr: E2ESchemaExpressionJS
  /**
   * 参数（字符串）
   */
  arg0: string
  /**
   * arg0 字段是一个路径
   */
  arg0IsPath: boolean
}

/**
 * 像素转视口宽度配置
 */
export interface Px2vwConfig {
  /**
   * 设计宽度
   */
  designWidth: number
  /**
   * 组件类型 -> 属性逻辑路径
   */
  propLogicPaths: Record<string, PropertyPath[]>
}

/**
 * 像素转根元素字体大小宽度配置
 */
export interface Px2remConfig {
  /**
   * 根元素字体大小
   */
  rootElementFontSize: number
  /**
   * 组件类型 -> 属性逻辑路径
   */
  propLogicPaths: Record<string, PropertyPath[]>
}
