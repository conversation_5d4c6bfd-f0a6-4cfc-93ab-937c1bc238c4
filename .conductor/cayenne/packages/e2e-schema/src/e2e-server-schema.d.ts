import {
  PropertyPath,
  SchemaExpressionArray,
  SchemaExpressionComponent,
  SchemaExpressionObject,
} from '@kael/schema-utils'
import {
  E2ESchemaExpression,
  E2ESchemaExpressionAPI,
  E2ESchemaExpressionAPIs,
  E2ESchemaExpressionDegradedJSONata,
  E2ESchemaNormalizedExpression,
} from './e2e-expression'
import { E2EAPIRPC, E2ESchema, E2ESchemaComponent } from './e2e-schema'

/**
 * 端到端后端 Schema
 */
export interface E2EServerSchema
  extends Omit<E2ESchema, 'view' | 'linkage' | 'apis'> {
  /**
   * 打平的视图
   */
  flattenedView: E2EServerSchemaFlattenedView
  /**
   * 联动，兼容后端下发 null
   */
  linkage?: E2ESchema['linkage'] | null
  /**
   * 接口
   */
  apis?: Record<string, E2EServerAPI> | null
}

/**
 * 端到端打平的视图
 */
export interface E2EServerSchemaFlattenedView {
  /**
   * 根组件 ID
   */
  rootComponentId: string
  /**
   * 所有组件
   */
  components: E2EServerSchemaComponent[]
  /**
   * 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
   */
  childComponentIdMap: Record<
    string,
    (string | E2ESchemaChildComponentIdAndPath)[]
  >
}

/**
 * 端到端后端组件（实例）
 */
export interface E2EServerSchemaComponent
  extends Omit<E2ESchemaComponent, 'props' | 'children' | 'apis' | 'if'> {
  /**
   * 组件属性
   */
  props?: Record<string, E2EServerSchemaExpression>
  /**
   * 子组件
   */
  children?: E2EServerSchemaComponent[]
  /**
   * 接口，为了方便后端读取转为 kv 结构，key 是 id
   */
  apis?: Record<string, E2EServerAPI> | null
}

/**
 * 端到端后端接口
 */
export type E2EServerAPI = E2EServerAPIRPC

/**
 * 端到端后端 RPC 接口
 */
export interface E2EServerAPIRPC extends Omit<E2EAPIRPC, 'args' | 'if'> {
  /**
   * 参数，后端目前只支持对象形式的一个参数，降级的 JSONata 表达式需要直接给出对象
   */
  args?: E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue
  /**
   * 是否执行（执行条件），同 args
   */
  if?: E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue
}

/**
 * 接口表达式，通过接口获取数据，由后端计算
 */
export interface E2EServerSchemaExpressionAPI<Expr = E2ESchemaExpression>
  extends Omit<E2ESchemaExpressionAPI<Expr>, 'transform'> {
  /**
   * （接口返回值）转换，降级的 JSONata 表达式需要直接给出对象
   */
  transform?: E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue
}

/**
 * 接口表达式，通过接口获取数据，由后端计算
 */
export interface E2EServerSchemaExpressionAPIs<Expr = E2ESchemaExpression>
  extends Omit<E2ESchemaExpressionAPIs<Expr>, 'transform'> {
  /**
   * （接口返回值）转换，降级的 JSONata 表达式需要直接给出对象
   */
  transform?: E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue
}

/**
 * 标准化的端到端后端表达式加上降级的 JSONata 表达式的值
 */
export type E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue =
  | E2ESchemaNormalizedExpression
  | E2ESchemaExpressionDegradedJSONata['value']

/**
 * 端到端后端表达式
 */
export type E2EServerSchemaExpression =
  | Exclude<
      E2ESchemaExpression,
      | SchemaExpressionArray<E2ESchemaExpression>
      | SchemaExpressionObject<E2ESchemaExpression>
      | SchemaExpressionComponent<E2ESchemaComponent>
      | E2ESchemaExpressionAPI
      | E2ESchemaExpressionAPIs
    >
  | SchemaExpressionArray<E2EServerSchemaExpression>
  | SchemaExpressionObject<E2EServerSchemaExpression>
  | Omit<SchemaExpressionComponent<E2EServerSchemaComponent>, 'value'>
  | E2EServerSchemaExpressionAPI
  | E2EServerSchemaExpressionAPIs

/**
 * 子组件 ID 以及路径
 */
export interface E2ESchemaChildComponentIdAndPath {
  /**
   * 子组件 ID
   */
  id: string
  /**
   * 子组件相对路径，默认按顺序添加到 children
   */
  path?: PropertyPath
}
