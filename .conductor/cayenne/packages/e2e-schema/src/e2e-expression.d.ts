import {
  SchemaExpressionArray,
  SchemaExpressionComponent,
  SchemaExpressionObject,
  SchemaExpressionStatic,
} from '@kael/schema-utils'
import { PropertyPath } from 'lodash'
import { E2ESchemaComponent } from './e2e-schema'

/**
 * 标准化的端到端表达式
 */
export type E2ESchemaNormalizedExpression =
  | SchemaExpressionStatic
  | SchemaExpressionArray<E2ESchemaExpression>
  | SchemaExpressionObject<E2ESchemaExpression>
  | SchemaExpressionComponent<E2ESchemaComponent>
  | E2ESchemaExpressionRefresh
  | E2ESchemaExpressionTrack
  | E2ESchemaExpressionOpenUrl<E2ESchemaExpression>
  | E2ESchemaExpressionActions<E2ESchemaExpression>
  | E2ESchemaExpressionGetData
  | E2ESchemaExpressionBind
  | E2ESchemaExpressionAPI
  | E2ESchemaExpressionAPIs
  | E2ESchemaExpressionJSONata
  | E2ESchemaExpressionDegradedJSONata
  | E2ESchemaExpressionJS

/**
 * 刷新表达式，更新整个搭建 Schema，表达式会返回一个函数
 */
export interface E2ESchemaExpressionRefresh {
  /**
   * 类型
   */
  type: 'refresh'
  /**
   * 刷新类型，默认值: `'default'`
   */
  refreshType?: RefreshType
  /**
   * 参数，优先最高，会覆盖其他参数
   */
  params?: Record<string, unknown>
}

/**
 * 刷新类型
 * * default: 默认，比如第一次刷新
 * * submit: 表单提交
 * * auto: 自动刷新，由 linkage 定义什么时候刷新
 * * outside-params: 外部参数，由配置参数变化引起的刷新
 */
export type RefreshType =
  | 'default'
  | 'submit'
  | 'auto'
  | 'outside-params'
  // eslint-disable-next-line @typescript-eslint/ban-types
  | (string & {})

/**
 * 埋点表达式
 */
export interface E2ESchemaExpressionTrack {
  /**
   * 类型
   */
  type: 'track'
  /**
   * 埋点 ID，引用最外层的 tracks
   */
  trackId: string
}

/**
 * 打开 URL 表达式，返回一个函数
 */
export interface E2ESchemaExpressionOpenUrl<Expr = E2ESchemaExpression> {
  /**
   * 类型
   */
  type: 'open-url'
  /**
   * （需要打开的）URL
   */
  url: Expr
  /**
   * 原地打开（当前页面跳转），默认: false
   */
  inPlace?: boolean
}

/**
 * 多操作表达式，用于合并多个函数
 */
export interface E2ESchemaExpressionActions<Expr = E2ESchemaExpression> {
  /**
   * 类型
   */
  type: 'actions'
  /**
   * 多个函数
   */
  fns: Expr[]
  /**
   * 是否并行，默认: true
   */
  parallel?: boolean
}

/**
 * 获取数据表达式
 */
export interface E2ESchemaExpressionGetData {
  /**
   * 类型
   */
  type: 'get-data'
  /**
   * 路径
   */
  path: PropertyPath
}

/**
 * 绑定表达式，用于函数绑定
 */
export interface E2ESchemaExpressionBind<Expr = E2ESchemaExpression> {
  /**
   * 类型
   */
  type: 'bind'
  /**
   * 需要绑定的函数
   */
  fn: Expr
  /**
   * 绑定的 this 对象
   */
  this?: Expr
  /**
   * 绑定的参数
   */
  args?: Expr[]
}

/**
 * 接口表达式，通过接口获取数据，由后端计算，当 apiId 为数组时，提交后端转为 apis
 */
export interface E2ESchemaExpressionAPI<Expr = E2ESchemaExpression> {
  /**
   * 类型
   */
  type: 'api'
  /**
   * 接口 ID
   */
  apiId: string | string[]
  /**
   * （接口返回值）转换
   */
  transform?: Expr
  /**
   * 默认值
   */
  defaultValue?: Expr
}

/**
 * 多接口表达式，通过接口获取数据，由后端计算
 */
export interface E2ESchemaExpressionAPIs<Expr = E2ESchemaExpression> {
  /**
   * 类型
   */
  type: 'apis'
  /**
   * 接口 ID
   */
  apiIds: string[]
  /**
   * （接口返回值）转换
   */
  transform?: Expr
  /**
   * 默认值
   */
  defaultValue?: Expr
}

/**
 * JSONata 表达式，返回一个数据转换函数，参考：https://jsonata.org/， Java：https://github.com/IBM/JSONata4Java
 */
export interface E2ESchemaExpressionJSONata {
  /**
   * 类型
   */
  type: 'jsonata'
  /**
   * JSONata 编码
   */
  code: string
}

/**
 * JS 表达式
 */
export interface E2ESchemaExpressionJS<Expr = E2ESchemaExpression> {
  /**
   * 类型
   */
  type: 'js'
  /**
   * JS 代码，编译后的
   */
  code: string
  /**
   * JS 代码，编译前的，编译到 ES5
   */
  codeES?: string
  /**
   * TS 代码，编译前的，后端编译到最新的 CJS，前端编译到 ES5
   */
  codeTS?: string
  /**
   * 默认值
   */
  defaultValue?: Expr
}

/**
 * 降级的 JSONata 表达式，文档：https://docs.corp.kuaishou.com/k/home/<USER>/fcACEw7VnmnuR34zyefTxvvmn
 */
export interface E2ESchemaExpressionDegradedJSONata {
  /**
   * 类型
   */
  type: 'degraded-jsonata'
  /**
   * 值，内部任意位置可以含表达式
   */
  value: Record<string, unknown>
}

/**
 * 端到端表达式
 */
export type E2ESchemaExpression =
  | E2ESchemaNormalizedExpression
  | null
  | boolean
  | string
  | number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  | any[]
