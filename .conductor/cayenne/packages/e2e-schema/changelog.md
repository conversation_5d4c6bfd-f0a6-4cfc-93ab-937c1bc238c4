# @ad/e2e-schema

## 2.6.3

### Patch Changes

- 添加 Failed to fetch 相关的错误上下文

## 2.6.2

### Patch Changes

- 兼容 UC SDK fetch 问题

## 2.6.1

### Patch Changes

- 兼容 UC SDK fetch 问题

## 2.6.0

### Minor Changes

- 63efd31a: 锁死 kael 的版本

## 2.6.0-beta.0

### Minor Changes

- 锁死 kael 的版本

## 2.5.1

### Patch Changes

- 优化 kmi 插件轮询机制

## 2.5.0

### Minor Changes

- 21ff033d: 全局模块支持类型导出

### Patch Changes

- 59d06c21: kmi 插件 getModuleDetail
- 87f7c6c3: 改名为 @ad/kmi-plugin-canal

## 2.5.0-alpha.2

### Patch Changes

- kmi 插件 getModuleDetail

## 2.5.0-alpha.1

### Patch Changes

- 改名为 @ad/kmi-plugin-canal

## 2.5.0-alpha.0

### Minor Changes

- 全局模块支持类型导出

## 2.4.1

### Patch Changes

- 00c8244c: 运行时容器 onMount

## 2.4.1-alpha.0

### Patch Changes

- 运行时容器 onMount

## 2.4.0

### Minor Changes

- cdnPublicPath 兼容无协议/纯域名场景

## 2.3.3

### Patch Changes

- 4d934a94: 支持 px2rem

## 2.3.3-alpha.0

### Patch Changes

- 支持 px2rem

## 2.3.2

### Patch Changes

- 添加 props 缓存

## 2.3.1

### Patch Changes

- b4da3cd2: 运行时 ref 支持函数

## 2.3.1-alpha.0

### Patch Changes

- 运行时 ref 支持函数

## 2.3.0

### Minor Changes

- f8d9e574: 加载全局模块

## 2.3.0-alpha.0

### Minor Changes

- 加载全局模块

## 2.2.0

### Minor Changes

- 75ff1bcb: px2vw

## 2.2.0-alpha.0

### Minor Changes

- px2vw

## 2.1.0

### Minor Changes

- 运行时加载本地组件库

## 2.0.0

### Major Changes

- e41e653b: 开发者工具

## 2.0.0-alpha.0

### Major Changes

- 开发者工具

## 1.8.1

### Patch Changes

- 执行信息

## 1.8.0

### Minor Changes

- eb0365dd: 全局模型

## 1.8.0-alpha.0

### Minor Changes

- 全局模型

## 1.7.3

### Patch Changes

- 添加traceUrl上报

## 1.7.2

### Patch Changes

- 上报apiError/renderError和argsError等后端运行时执行Error

## 1.7.1

### Patch Changes

- Divider Switch Empty

## 1.7.0

### Minor Changes

- 组件代码回退

## 1.6.4

### Patch Changes

- a0aaa02: RenderRes 优化
- 消费预请求
- 1c532d8: consumePrefetch 测试

## 1.6.4-alpha.1

### Patch Changes

- RenderRes 优化

## 1.6.4-alpha.0

### Patch Changes

- consumePrefetch 测试

## 1.6.3

### Patch Changes

- err msg 加 [canal]

## 1.6.2

### Patch Changes

- mock

## 1.6.1

### Patch Changes

- loadJS 加错误信息

## 1.6.0

### Minor Changes

- 加 Tabs 组件

## 1.5.5

### Patch Changes

- 产物 format

## 1.5.4

### Patch Changes

- 表达式错误信息补充

## 1.5.3

### Patch Changes

- 组件控制器动态（渲染器）

## 1.5.2

### Patch Changes

- 支持父组件劫持子组件的 props

## 1.5.1

### Patch Changes

- useWeblog

## 1.5.0

### Minor Changes

- 9fafaa0: createAutoFetchSchema 自动切换 preview 接口

### Patch Changes

- 63e1c26: 修复 createAutoFetchSchema
- 1f158eb: 预览本地业务页面

## 1.5.0-alpha.2

### Patch Changes

- 修复 createAutoFetchSchema

## 1.5.0-alpha.1

### Minor Changes

- createAutoFetchSchema 自动切换 preview 接口

## 1.4.15-alpha.0

### Patch Changes

- 预览本地业务页面

## 1.4.14

### Patch Changes

- 升级 @ks-radar

## 1.4.13

### Patch Changes

- c36b00e: 前端模型

## 1.4.13-alpha.0

### Patch Changes

- 前端模型

## 1.4.12

### Patch Changes

- isLoading 加 computed

## 1.4.11

### Patch Changes

- 状态码异常捕获

## 1.4.10

### Patch Changes

- 设计器 URL 日志

## 1.4.9

### Patch Changes

- 支持自定义 fetch

## 1.4.8

### Patch Changes

- 增加灰度宿主版本

## 1.4.7

### Patch Changes

- d2d35f9: 后端数据

## 1.4.7-alpha.0

### Patch Changes

- 后端数据

## 1.4.6

### Patch Changes

- 3abdd11: 埋点接口

## 1.4.6-alpha.0

### Patch Changes

- 埋点接口

## 1.4.5

### Patch Changes

- 支持 TS 表达式

## 1.4.4

### Patch Changes

- 修复 params 更新问题

## 1.4.3

### Patch Changes

- c2a8142: 前端表达式 alpha 测试

## 1.4.3-alpha.0

### Patch Changes

- 前端表达式 alpha 测试

## 1.4.2

### Patch Changes

- 83757fe: fix genAllRuntimes
- 跨模块副作用
- e6ac88e: 跨模块副作用 测试
- 13b1bcb: 跨模块副作用
- 0d905fc: fix prepareSetDataInAllRuntimes

## 1.4.2-alpha.3

### Patch Changes

- fix prepareSetDataInAllRuntimes

## 1.4.2-alpha.2

### Patch Changes

- fix genAllRuntimes

## 1.4.2-alpha.1

### Patch Changes

- 跨模块副作用 测试

## 1.4.2-alpha.0

### Patch Changes

- 跨模块副作用

## 1.4.1

### Patch Changes

- 运行时渲染异常通过 setTimeout 抛到全局

## 1.4.0

### Minor Changes

- 加表格组件

## 1.3.11

### Patch Changes

- 后端列表渲染

## 1.3.10

### Patch Changes

- 日期选择器

## 1.3.9

### Patch Changes

- CDN 容灾

## 1.3.8

### Patch Changes

- 添加日期范围选择

## 1.3.7

### Patch Changes

- 事件 transformParams 动态修改参数

## 1.3.6

### Patch Changes

- 前端副作用

## 1.3.5

### Patch Changes

- 前端副作用测试

## 1.3.4

### Patch Changes

- 支持接口多选

## 1.3.3

### Patch Changes

- 优化 js 加载缓存

## 1.3.2

### Patch Changes

- 埋点使用 sendImmediately 上传

## 1.3.1

### Patch Changes

- weblog 从 config 提出

## 1.3.0

### Minor Changes

- 埋点支持

## 1.2.3

### Patch Changes

- 加 data prop

## 1.2.2

### Patch Changes

- 加选择器组件

## 1.2.1

### Patch Changes

- 修复雷达依赖版本问题

## 1.2.0

### Minor Changes

- 运行时加雷达

## 1.1.0

### Minor Changes

- 加分页器

## 1.0.12

### Patch Changes

- 首次刷新 Promise

## 1.0.11

### Patch Changes

- fix: 🐛 插槽组件刷新时销毁

## 1.0.10

### Patch Changes

- 运行时加 beta 环境

## 1.0.9

### Patch Changes

- fetchSchemaFromCapiE

## 1.0.8

### Patch Changes

- capi fetchSchema

## 1.0.7

### Patch Changes

- 内置组件库切换 jsx 编译方式

## 1.0.6

### Patch Changes

- 运行时替换业务组件 JS URL

## 1.0.5

### Patch Changes

- 轻提示

## 1.0.4

### Patch Changes

- 更新运行时 README

## 1.0.3

### Patch Changes

- createFetchSchema

## 1.0.2

### Patch Changes

- 1.0.2

## 1.0.1

### Patch Changes

- 添加多选框组/单选框组

## 1.0.0

### Major Changes

- 1.0.0

## 0.3.6

### Patch Changes

- 表单前后端混合校验

## 0.3.5

### Patch Changes

- 添加 Checkbox 组件

## 0.3.4

### Patch Changes

- 内部组件库加按钮

## 0.3.3

### Patch Changes

- 优化 AutoLoading 性能

## 0.3.2

### Patch Changes

- AutoLoading

## 0.3.1

### Patch Changes

- 容器支持 Loading

## 0.3.0

### Minor Changes

- 增加后端执行的 js 表达式

## 0.2.14

### Patch Changes

- fetchSchema 加 version

## 0.2.13

### Patch Changes

- fetchSchema 添加 version 字段

## 0.2.12

### Patch Changes

- 运行时支持自定义 loading

## 0.2.11

### Patch Changes

- b2f 加参数

## 0.2.10

### Patch Changes

- 调整后端下发 Schema 接口路径

## 0.2.9

### Patch Changes

- 添加 env 字段

## 0.2.8

### Patch Changes

- 下发接口 schemaId 改为 moduleId

## 0.2.7

### Patch Changes

- 降级 jsonata 表达式

## 0.2.6

### Patch Changes

- 更新 Schema 下发接口

## 0.2.5

### Patch Changes

- 默认的获取 Schema 文件函数

## 0.2.4

### Patch Changes

- 兼容后端 Schema null 数据

## 0.2.3

### Patch Changes

- 完善 Schema 转换

## 0.2.2

### Patch Changes

- 运行时兼容 moduleId

## 0.2.1

### Patch Changes

- khanun 改为 canal

## 0.2.0

### Minor Changes

- Schema 后端处理部分改为 api 字段和 api 表达式

## 0.1.9

### Patch Changes

- 运行时嵌套限制

## 0.1.8

### Patch Changes

- 优化 setData

## 0.1.7

### Patch Changes

- 支持子模块

## 0.1.6

### Patch Changes

- 外部 params 刷新

## 0.1.5

### Patch Changes

- 支持 config 覆盖 data

## 0.1.4

### Patch Changes

- 增加 iife 去掉 loop

## 0.1.3

### Patch Changes

- 运行时支持远程操作

## 0.1.2

### Patch Changes

- 大运河运行时

## 0.1.1

### Patch Changes

- 更新 readme

## 0.1.0

### Minor Changes

- MVP 版本
