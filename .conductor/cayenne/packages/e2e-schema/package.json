{"name": "@ad/e2e-schema", "version": "2.6.3", "description": "端到端协议", "repository": {"type": "git", "url": "*************************:ks-ad/ad-fe/grandcanal/canal.git"}, "license": "MIT", "author": "AD", "sideEffects": false, "main": "src/index.js", "module": "src/index.js", "types": "src/index.d.ts", "dependencies": {"@kael/material-schema": "1.0.0-rc.190", "@kael/schema": "1.0.0-rc.190", "@kael/schema-utils": "1.0.0-rc.190", "@types/lodash": "^4.14.199"}, "devDependencies": {"@ks/weblogger": "^3.10.30"}, "peerDependencies": {"@ks/weblogger": "*"}}