import terser from '@rollup/plugin-terser'
import typescript from '@rollup/plugin-typescript'
import excludeDependenciesFromBundle from 'rollup-plugin-exclude-dependencies-from-bundle'

export default [
  {
    input: 'src/index.ts',
    output: [
      {
        file: 'dist/index.cjs.js',
        format: 'cjs',
        sourcemap: true,
      },
      {
        file: 'dist/index.es.js',
        format: 'es',
        sourcemap: true,
      },
    ],
    plugins: [
      excludeDependenciesFromBundle(),
      typescript(),
      terser({
        mangle: {
          properties: {
            regex: /^_[^_]*$/,
          },
        },
        format: {
          beautify: true,
        },
      }),
    ],
  },
]
