import {
  BindIIFEAction,
  E2EAPI,
  E2ESchema,
  E2ESchemaExpression,
  E2ESchemaExpressionAPI,
  E2ESchemaExpressionAPIs,
  E2ESchemaExpressionActions,
  E2ESchemaExpressionBind,
  E2ESchemaExpressionOpenUrl,
} from '@ad/e2e-schema'
import {
  EXPRESSION_ENTITY_DETAIL_TYPE,
  PropertyPath,
  TraverseOptions,
  dfsGenComponentDetailBySchema,
  dfsGenExpressionDetailBySchema,
  dfsGenExpressionDetailBySchemaExpression,
} from '@kael/schema-utils'
import { isUndefined } from 'lodash'
import { INNER_JSON_PATH_IIFE_ACTIONS } from '../constants'
import { isApiExpression, isJsExpression } from '../expression'

/**
 * 端到端 Schema 遍历选项
 */
export const e2eSchemaTraverseOptions: Partial<TraverseOptions<E2ESchema>> = {
  expressionGeneratorFns: {
    *['open-url'](expression) {
      const expr = expression as E2ESchemaExpressionOpenUrl
      yield {
        type: EXPRESSION_ENTITY_DETAIL_TYPE,
        expression: expr.url,
        path: ['url'],
      }
    },
    *['actions'](expression) {
      const expr = expression as E2ESchemaExpressionActions
      for (let i = 0; i < expr.fns.length; i++) {
        yield {
          type: EXPRESSION_ENTITY_DETAIL_TYPE,
          expression: expr.fns[i],
          path: ['fns', i],
        }
      }
    },
    *['bind'](expression) {
      const expr = expression as E2ESchemaExpressionBind
      yield {
        type: EXPRESSION_ENTITY_DETAIL_TYPE,
        expression: expr.fn,
        path: ['fn'],
      }
      if (!isUndefined(expr.this)) {
        yield {
          type: EXPRESSION_ENTITY_DETAIL_TYPE,
          expression: expr.this,
          path: ['this'],
        }
      }
      if (expr.args) {
        for (let i = 0; i < expr.args.length; i++) {
          yield {
            type: EXPRESSION_ENTITY_DETAIL_TYPE,
            expression: expr.args[i],
            path: ['args', i],
          }
        }
      }
    },
    *['api'](expression) {
      const expr = expression as E2ESchemaExpressionAPI
      if (!isUndefined(expr.transform)) {
        yield {
          type: EXPRESSION_ENTITY_DETAIL_TYPE,
          expression: expr.transform,
          path: ['transform'],
        }
      }
      if (!isUndefined(expr.defaultValue)) {
        yield {
          type: EXPRESSION_ENTITY_DETAIL_TYPE,
          expression: expr.defaultValue,
          path: ['defaultValue'],
        }
      }
    },
    *['apis'](expression) {
      const expr = expression as E2ESchemaExpressionAPIs
      if (!isUndefined(expr.transform)) {
        yield {
          type: EXPRESSION_ENTITY_DETAIL_TYPE,
          expression: expr.transform,
          path: ['transform'],
        }
      }
      if (!isUndefined(expr.defaultValue)) {
        yield {
          type: EXPRESSION_ENTITY_DETAIL_TYPE,
          expression: expr.defaultValue,
          path: ['defaultValue'],
        }
      }
    },
  },
}

/**
 * 选项：深度优先，生成搭建 Schema 里的所有表达式详情
 */
export interface DfsGenExpressionDetailByE2ESchemaOptions {
  /**
   * 是否遍历：属性
   */
  props?: boolean
  /**
   * 是否遍历：组件
   */
  component?: boolean
  /**
   * 是否遍历：立即执行的函数表达式
   */
  iife?: boolean
  /**
   * 是否遍历：立即执行的函数表达式动作
   */
  iifeActions?: boolean
}

/**
 * 深度优先，生成搭建 Schema 里的所有表达式详情
 * @param schema 端到端 Schema
 * @param options 选项
 */
export function* dfsGenExpressionDetailByE2ESchema(
  schema: E2ESchema,
  {
    props: optionsProps = true,
    component: optionsComponent = true,
    iife: optionsIIFE = true,
    iifeActions: optionsIIFEActions = true,
  }: DfsGenExpressionDetailByE2ESchemaOptions = {},
): ReturnType<typeof dfsGenExpressionDetailBySchema<E2ESchema>> {
  if (optionsProps) {
    yield* dfsGenExpressionDetailBySchema(schema, e2eSchemaTraverseOptions)
  }
  if (optionsComponent) {
    const rootId = schema.view.id
    for (const cd of dfsGenComponentDetailBySchema(schema)) {
      const {
        component: { id: componentId, apis: componentApis = [] },
      } = cd
      yield* dfsGenExpressionDetailByApis(componentApis, cd.componentIdPath, [
        ...cd.path,
        'apis',
      ])
      if (rootId === componentId) {
        if (optionsIIFE) {
          yield* dfsGenExpressionDetailByIIFE(
            schema.iife,
            [rootId],
            optionsIIFEActions,
          )
        }
        yield* dfsGenExpressionDetailByApis(schema.apis, cd.componentIdPath, [
          'apis',
        ])
      } else {
        const {
          component: { effect },
        } = cd
        if (effect) {
          yield* dfsGenExpressionDetailBySchemaExpression(
            effect,
            cd.componentIdPath,
            [...cd.path, 'effect'],
            e2eSchemaTraverseOptions,
          )
        }
      }
    }
  }
}

/**
 * 深度优先，通过立即执行的函数表达式生成表达式详情
 * @param iife 立即执行的函数表达式
 * @param parentComponentIdPath 父组件 ID 路径
 * @param optionsIIFEActions 是否遍历：立即执行的函数表达式动作
 */
function* dfsGenExpressionDetailByIIFE(
  iife: E2ESchemaExpression | undefined,
  parentComponentIdPath: string[],
  optionsIIFEActions: boolean,
): ReturnType<typeof dfsGenExpressionDetailBySchema<E2ESchema>> {
  if (iife) {
    yield* dfsGenExpressionDetailBySchemaExpression(
      iife,
      parentComponentIdPath,
      ['iife'],
      e2eSchemaTraverseOptions,
    )
    if (optionsIIFEActions) {
      const actions = getBindIIFEActionsFromIIFE(iife)
      for (let i = 0; i < actions.length; i++) {
        const action = actions[i]
        if (action.type === 'exec-effect') {
          yield* dfsGenExpressionDetailBySchemaExpression(
            action.expr,
            parentComponentIdPath,
            [...INNER_JSON_PATH_IIFE_ACTIONS, i, 'expr'],
            e2eSchemaTraverseOptions,
          )
        }
      }
    }
  }
}

/**
 * 深度优先，通过接口生成表达式详情
 * @param apis 接口
 * @param parentComponentIdPath 父组件 ID 路径
 * @param path 当前路径
 */
function* dfsGenExpressionDetailByApis(
  apis: E2EAPI[] = [],
  parentComponentIdPath: string[],
  path: PropertyPath,
): ReturnType<typeof dfsGenExpressionDetailBySchema<E2ESchema>> {
  for (let i = 0; i < apis.length; i++) {
    const api = apis[i]
    yield* dfsGenExpressionDetailByApi(api, parentComponentIdPath, [...path, i])
  }
}

/**
 * 深度优先，通过接口生成表达式详情
 * @param api 接口
 * @param parentComponentIdPath 父组件 ID 路径
 * @param path 当前路径
 */
function* dfsGenExpressionDetailByApi(
  api: E2EAPI,
  parentComponentIdPath: string[],
  path: PropertyPath,
): ReturnType<typeof dfsGenExpressionDetailBySchema<E2ESchema>> {
  const { args = [] } = api
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    yield* dfsGenExpressionDetailBySchemaExpression(
      arg,
      parentComponentIdPath,
      [...path, 'args', i],
      e2eSchemaTraverseOptions,
    )
  }
  if (api.if) {
    yield* dfsGenExpressionDetailBySchemaExpression(
      api.if,
      parentComponentIdPath,
      [...path, 'if'],
      e2eSchemaTraverseOptions,
    )
  }
}

/**
 * 从立即执行的函数表达式中获取立即执行的函数表达式动作
 * @param iife 立即执行的函数表达式
 */
export function getBindIIFEActionsFromIIFE(
  iife?: E2ESchemaExpression,
): BindIIFEAction[] {
  if (isApiExpression(iife)) {
    if (isJsExpression(iife.transform)) {
      return getBindIIFEActionsFromCode(iife.transform.code)
    }
  }
  return []
}

/**
 * 从代码中获取立即执行的函数表达式动作
 * @param code 代码
 */
export function getBindIIFEActionsFromCode(code: string): BindIIFEAction[] {
  const m = code.match(/actions = JSON.parse\((.*)\);\n/)
  let actions: BindIIFEAction[] = []
  if (m?.[1]) {
    try {
      actions = JSON.parse(JSON.parse(m[1])) || actions
    } catch (err) {
      console.error('getBindIIFEActionsFromCode err', err)
    }
  }
  return actions
}

/**
 * 深度优先，生成接口相关的表达式详情
 * @param schema 端到端 Schema
 */
export function* dfsGenApiRelatedExpressionDetailByE2ESchema(
  schema: E2ESchema,
): ReturnType<typeof dfsGenExpressionDetailBySchema<E2ESchema>> {
  const rootId = schema.view.id
  for (const cd of dfsGenComponentDetailBySchema(schema)) {
    const {
      component: { id: componentId, apis: componentApis = [] },
    } = cd
    yield* dfsGenExpressionDetailByApis(componentApis, cd.componentIdPath, [
      ...cd.path,
      'apis',
    ])
    if (rootId === componentId) {
      yield* dfsGenExpressionDetailByApis(schema.apis, cd.componentIdPath, [
        'apis',
      ])
    }
  }
}
