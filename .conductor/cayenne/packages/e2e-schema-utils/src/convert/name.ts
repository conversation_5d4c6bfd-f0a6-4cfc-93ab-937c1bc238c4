import { E2ESchema, E2ESchemaComponent } from '@ad/e2e-schema'
import { dfsGenComponentDetailBySchemaComponent } from '@kael/schema-utils'

/**
 * 根据 ID 重命名所有组件并且格式化 ID
 * @param component 组件
 */
export function renameAllComponentsAndFormatId(
  component: E2ESchemaComponent,
): void {
  for (const cd of dfsGenComponentDetailBySchemaComponent<E2ESchema>(
    component,
    [],
    [],
  )) {
    cd.component.id = cd.component.id.replace(/-/g, '$')
    renameComponentById(cd.component)
  }
}

/**
 * 根据 ID 重命名组件
 * @param component 组件
 */
export function renameComponentById(component: E2ESchemaComponent): void {
  const parts = component.name.split('::')
  component.name = `${parts[0]}::${component.id.slice(-3)}`
}
