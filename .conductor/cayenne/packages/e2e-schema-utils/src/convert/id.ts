import { E2ESchema, E2ESchemaComponent } from '@ad/e2e-schema'
import {
  dfsGenComponentDetailBySchema,
  dfsGenComponentDetailBySchemaComponent,
} from '@kael/schema-utils'
import { nanoid } from 'nanoid'

/**
 * 替换为变量 ID
 * @param id 普通的组件 ID
 */
export function replaceToVarId(id: string): string {
  return id.replace(/-/g, '$')
}

/**
 * 创建变量 ID
 */
export function createVarId(): string {
  return replaceToVarId(nanoid())
}

/**
 * 创建变量组件 ID
 */
export function createVarComponentId(): string {
  return `comp_${createVarId()}`
}

/**
 * 获取 Schema 文件中所有组件的 id
 * @param schema Schema 文件
 */
export function getAllIdsOfSchema(schema?: E2ESchema | null): string[] {
  if (!schema) return []
  return [...dfsGenComponentDetailBySchema(schema)].map((c) => c.component.id)
}

/**
 * 获取 Schema 组件实例中所有组件的 id
 * @param schemaComponent Schema 组件实例
 */
export function getAllIdsOfSchemaComponent(
  schemaComponent?: E2ESchemaComponent | null,
): string[] {
  if (!schemaComponent) return []
  return [
    ...dfsGenComponentDetailBySchemaComponent<E2ESchema>(
      schemaComponent,
      [],
      [],
    ),
  ].map((c) => c.component.id)
}
