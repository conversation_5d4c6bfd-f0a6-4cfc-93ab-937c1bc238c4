import {
  E2EAPI,
  E2ESchema,
  E2ESchemaComponent,
  E2ESchemaExpression,
  E2ESchemaExpressionAPI,
  E2ESchemaExpressionAPIs,
  E2ESchemaExpressionDegradedJSONata,
  E2ESchemaNormalizedExpression,
  E2EServerAPI,
  E2EServerSchema,
  E2EServerSchemaComponent,
  E2EServerSchemaExpressionAPI,
  E2EServerSchemaExpressionAPIs,
  E2EServerSchemaFlattenedView,
  E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue,
} from '@ad/e2e-schema'
import {
  ComponentDetailOfSchema,
  ENTER_TRAVERSE_TIME,
  EXIT_TRAVERSE_TIME,
  STATIC_EXPRESSION_TYPE,
  STR_CHILDREN,
  dfsGenComponentDetailBySchema,
  getComponentPropExpression,
  normalizeExpression,
  setComponentPropExpression,
  unnormalizeExpression,
} from '@kael/schema-utils'
import {
  cloneDeep,
  fromPairs,
  get,
  isArray,
  isNil,
  isNumber,
  isObject,
  isString,
  isUndefined,
  last,
  noop,
  omit,
  set,
  unset,
} from 'lodash'
import { CanalImplementsField, CanalRootComponentField } from '../constants'
import { dfsGenExpressionDetailByE2ESchema } from '../traverse'

/**
 * 端到端 Schema 转端到端后端 Schema，返回新的结构，不会修改入参
 * @param schema 端到端 Schema
 */
export function f2bE2ESchema(schema: E2ESchema): E2EServerSchema {
  const clonedSchema = cloneDeep(schema)
  const { view, apis } = clonedSchema
  let serverSchemaApis: Record<string, E2EServerAPI> | undefined
  const flattenedView: E2EServerSchemaFlattenedView = {
    rootComponentId: view.id,
    components: [],
    childComponentIdMap: {},
  }
  const cdm: Record<string, ComponentDetailOfSchema<E2ESchema>> = {}
  const clearEmptyArrayFns: (typeof noop)[] = []
  for (const ed of dfsGenExpressionDetailByE2ESchema(clonedSchema, {
    iifeActions: false,
  })) {
    const expr = normalizeExpression(ed.expression)
    switch (expr.type) {
      case 'api': {
        if (!isUndefined(expr.transform)) {
          ;(expr as E2EServerSchemaExpressionAPI).transform =
            unnormalizeWithDegradedJSONataValueExpression(expr.transform)
        }
        if (isArray(expr.apiId)) {
          set(clonedSchema, ed.path, {
            ...omit(expr, 'type', 'apiId'),
            type: 'apis',
            apiIds: expr.apiId,
          } satisfies E2ESchemaExpressionAPIs)
        }
        break
      }
    }
  }
  for (const cd of dfsGenComponentDetailBySchema(clonedSchema, {
    traverseTimes: [ENTER_TRAVERSE_TIME, EXIT_TRAVERSE_TIME],
  })) {
    switch (cd.traverseTime) {
      case ENTER_TRAVERSE_TIME: {
        cdm[cd.component.id] = cd
        const comp = cd.component as E2EServerSchemaComponent
        const compApis = cd.parentComponentId
          ? cd.component.apis
          : [...(cd.component.apis || []), ...(apis || [])]
        if (isArray(compApis)) {
          if (!compApis.length) {
            delete comp.apis
          } else {
            comp.apis = fromPairs(
              compApis.map(({ args, if: apiIf, ...restApi }) => {
                const newApi: E2EServerAPI = restApi
                if (args?.length) {
                  newApi.args = unnormalizeWithDegradedJSONataValueExpression(
                    args[0],
                  )
                }
                if (apiIf) {
                  newApi.if =
                    unnormalizeWithDegradedJSONataValueExpression(apiIf)
                }
                return [restApi.id, newApi]
              }),
            )
            if (!cd.parentComponentId) {
              serverSchemaApis = comp.apis
              delete comp.apis
            }
          }
        }
        //#region 后端列表渲染处理
        const forItemsExpr = getComponentPropExpression<E2ESchema>(
          cd.component,
          [CanalImplementsField.FOR, 'items'],
        )
        const forKeyExpr = getComponentPropExpression<E2ESchema>(cd.component, [
          CanalImplementsField.FOR,
          'key',
        ])
        if (forItemsExpr.type !== 'static' || isArray(forItemsExpr.value)) {
          comp.backFor = {
            items:
              forItemsExpr.type === 'static'
                ? (forItemsExpr.value as unknown[])
                : forItemsExpr,
          }
          if (forKeyExpr.type === 'static') {
            if (isString(forKeyExpr.value)) {
              comp.backFor.key = forKeyExpr.value
            }
          } else {
            comp.backFor.key = forKeyExpr
          }
          delete comp.props?.[CanalImplementsField.FOR]
        }
        //#endregion
        flattenedView.components.push(comp)
        break
      }
      case EXIT_TRAVERSE_TIME: {
        delete cd.component.children
        if (cd.parentComponentId) {
          if (!flattenedView.childComponentIdMap[cd.parentComponentId]) {
            flattenedView.childComponentIdMap[cd.parentComponentId] = []
          }
          const arr = flattenedView.childComponentIdMap[cd.parentComponentId]
          const relativePath = cd.path.slice(
            cdm[cd.parentComponentId].path.length,
          )
          if (relativePath[0] === STR_CHILDREN) {
            arr.push(cd.component.id)
          } else {
            arr.push({
              id: cd.component.id,
              path: relativePath,
            })
            const parentComp = cdm[cd.parentComponentId].component
            unset(parentComp, relativePath)
            if (isNumber(last(relativePath))) {
              const aPath = relativePath.slice(0, -1)
              const a = get(parentComp, aPath)
              if (Array.isArray(a) && a.every((item) => isUndefined(item))) {
                // 推迟清理，避免遍历报错
                clearEmptyArrayFns.push(() => {
                  unset(parentComp, aPath)
                })
              }
            }
          }
        }
        break
      }
    }
  }
  for (const fn of clearEmptyArrayFns) {
    fn()
  }
  const serverSchema: E2EServerSchema = {
    ...omit(clonedSchema, 'view', 'apis'),
    flattenedView,
    ...(serverSchemaApis
      ? {
          apis: serverSchemaApis,
        }
      : {}),
  }
  return serverSchema
}

/**
 * 端到端后端 Schema 转端到端 Schema 选项
 */
export interface B2FE2ESchemaOptions {
  /**
   * 保留（组件的）__$backData 属性，默认：false
   */
  keepBackDataProp?: boolean
  /**
   * 保留（组件的）value 属性，默认：false
   */
  keepValueProp?: boolean
  /**
   * 保留（组件的）__$if 属性，默认：false
   */
  keepIfProp?: boolean
}

/**
 * 端到端后端 Schema 转端到端 Schema，返回新的结构，不会修改入参
 * @param serverSchema 端到端后端 Schema
 * @param options 选项
 */
export function b2fE2ESchema(
  serverSchema: E2EServerSchema,
  {
    keepBackDataProp = false,
    keepValueProp = false,
    keepIfProp = false,
  }: B2FE2ESchemaOptions = {},
): E2ESchema {
  const clonedServerSchema = cloneDeep(serverSchema)
  const {
    flattenedView,
    linkage,
    data = {},
    apis,
    ...restServerSchema
  } = clonedServerSchema
  for (const component of flattenedView.components) {
    const comp = component as E2ESchemaComponent
    const compApis =
      component.id !== flattenedView.rootComponentId
        ? { ...component.apis }
        : {
            ...component.apis,
            ...apis,
          }
    if (Object.keys(compApis).length) {
      comp.apis = Object.values(compApis).map(
        ({ args, if: apiIf, ...restApi }) => {
          const newApi: E2EAPI = restApi
          if (args) {
            newApi.args = [normalizeWithDegradedJSONataValueExpression(args)]
          }
          if (apiIf) {
            newApi.if = normalizeWithDegradedJSONataValueExpression(apiIf)
          }
          return newApi
        },
      )
    } else {
      delete comp.apis
    }
    if (!keepBackDataProp) {
      const valueExpr = getComponentPropExpression<E2ESchema>(comp, [
        CanalRootComponentField.BACK_DATA,
      ])
      // __$backData 为对象，则强制挪入 data
      if (
        valueExpr.type === STATIC_EXPRESSION_TYPE &&
        isObject(valueExpr.value) &&
        !isArray(valueExpr.value)
      ) {
        Object.assign(data, valueExpr.value)
      }
      delete comp.props?.[CanalRootComponentField.BACK_DATA]
    }
    if (!keepValueProp) {
      const valueExpr = getComponentPropExpression<E2ESchema>(comp, ['value'])
      // value 非 undefined、null 的静态值，则强制挪入 data
      if (
        valueExpr.type === STATIC_EXPRESSION_TYPE &&
        !isNil(valueExpr.value)
      ) {
        data[comp.id] = valueExpr.value
      }
      delete comp.props?.value
    }
    if (!keepIfProp) {
      const ifExpr = unnormalizeExpression(
        getComponentPropExpression<E2ESchema>(comp, [CanalImplementsField.IF]),
      )
      if (!isUndefined(ifExpr)) {
        comp.if = ifExpr
      }
      delete comp.props?.[CanalImplementsField.IF]
    }
    //#region 后端列表渲染处理
    if (comp.backFor) {
      setComponentPropExpression(
        comp,
        [CanalImplementsField.FOR, 'items'],
        comp.backFor.items,
      )
      if (!isNil(comp.backFor.key)) {
        setComponentPropExpression(
          comp,
          [CanalImplementsField.FOR, 'key'],
          comp.backFor.key,
        )
      }
      delete comp.backFor
    }
    //#endregion
  }
  const componentMap = fromPairs(
    flattenedView.components.map((comp) => [comp.id, comp]),
  )
  const view = componentMap[flattenedView.rootComponentId] as E2ESchemaComponent
  for (const parentComponentId in flattenedView.childComponentIdMap) {
    const arr = flattenedView.childComponentIdMap[parentComponentId]
    const parentComp = componentMap[parentComponentId]
    for (let item of arr) {
      if (isString(item)) {
        item = { id: item }
      }
      const comp = componentMap[item.id]
      if (item.path) {
        set(parentComp, item.path, comp)
      } else {
        if (!parentComp.children) {
          parentComp.children = []
        }
        parentComp.children.push(comp)
      }
    }
  }
  const schema: E2ESchema = {
    ...restServerSchema,
    view,
    ...(linkage ? { linkage } : {}),
    ...(Object.keys(data).length ? { data } : {}),
  }
  for (const ed of dfsGenExpressionDetailByE2ESchema(schema, {
    iifeActions: false,
  })) {
    const expr = normalizeExpression(ed.expression)
    switch (expr.type) {
      case 'api': {
        const currentExpr = expr as E2EServerSchemaExpressionAPI
        if (!isUndefined(currentExpr.transform)) {
          expr.transform = normalizeWithDegradedJSONataValueExpression(
            currentExpr.transform,
          )
        }
        break
      }
      case 'apis': {
        const currentExpr = expr as E2EServerSchemaExpressionAPIs
        if (!isUndefined(currentExpr.transform)) {
          expr.transform = normalizeWithDegradedJSONataValueExpression(
            currentExpr.transform,
          )
        }
        set(schema, ed.path, {
          ...omit(expr, 'type', 'apiIds'),
          type: 'api',
          apiId: expr.apiIds,
        } satisfies E2ESchemaExpressionAPI)
        break
      }
    }
  }
  return schema
}

/**
 * 标准化【加上降级的 JSONata 表达式的值的表达式】
 * @param expression 表达式
 */
export function normalizeWithDegradedJSONataValueExpression(
  expression: E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue,
): E2ESchemaNormalizedExpression {
  if (expression.type) {
    return expression as E2ESchemaNormalizedExpression
  }
  const newExpr: E2ESchemaExpressionDegradedJSONata = {
    type: 'degraded-jsonata',
    value: expression as E2ESchemaExpressionDegradedJSONata['value'],
  }
  return newExpr
}

/**
 * 非标准【加上降级的 JSONata 表达式的值的表达式】
 * @param expression 表达式
 */
export function unnormalizeWithDegradedJSONataValueExpression(
  expression: E2ESchemaExpression,
): E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue {
  const expr = normalizeExpression(expression)
  if (expr.type === 'degraded-jsonata') {
    return expr.value
  }
  return expr
}
