import { BindIIFEAction } from '@ad/e2e-schema'

/**
 * 获取绑定立即执行（前端动作）动作的标签
 * @param action 动作
 */
export function getBindIIFEActionLabel(action: BindIIFEAction): string {
  switch (action.type) {
    case 'open-url': {
      return `打开链接：${action.url}${
        action.inPlace ? '，从代码里的 output 中获取' : ''
      }`
    }
    case 'exec-external-fn': {
      return `执行外部函数：${action.fnPath}，参数：${action.arg0 || '无'}${
        action.arg0IsPath ? '，从代码里的 output 中获取' : ''
      }`
    }
    case 'exec-effect': {
      return `执行副作用，参数：${action.arg0 || '无'}${
        action.arg0IsPath ? '，从代码里的 output 中获取' : ''
      }`
    }
  }
}
