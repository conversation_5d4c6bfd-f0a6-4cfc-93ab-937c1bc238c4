import { E2EServerSchema } from '@ad/e2e-schema'
import { sortBy } from 'lodash'

/**
 * 对端到端后端 Schema 的组件按 ID 进行排序，返回新的结构，不会修改入参
 * @param serverSchema 端到端后端 Schema
 */
export function sortE2EServerSchemaComponentsById(
  serverSchema: E2EServerSchema,
): E2EServerSchema {
  return {
    ...serverSchema,
    flattenedView: {
      ...serverSchema.flattenedView,
      components: sortBy(serverSchema.flattenedView.components, 'id'),
    },
  }
}
