import { validateE2ESchema, validateE2EServerSchema } from '../validate'

describe('validate', () => {
  it('validateE2EServerSchema', () => {
    expect(validateE2EServerSchema({})).toBe(false)
    expect(
      validateE2EServerSchema({
        schemaVersion: 'adsf',
      }),
    ).toBe(false)
    expect(
      validateE2EServerSchema({
        flattenedView: {
          rootComponentId: 'r1',
          components: [],
          childComponentIdMap: {},
        },
      }),
    ).toBe(false)
    expect(
      validateE2EServerSchema({
        schemaVersion: 'adsf',
        flattenedView: {
          rootComponentId: 'r1',
          components: [],
          childComponentIdMap: {},
        },
      }),
    ).toBe(true)
  })

  it('validateE2ESchema', () => {
    expect(validateE2ESchema({})).toBe(false)
    expect(
      validateE2ESchema({
        schemaVersion: 'adsf',
      }),
    ).toBe(false)
    expect(
      validateE2ESchema({
        view: {
          type: 't',
          id: 'i',
          name: 'n',
        },
      }),
    ).toBe(false)
    expect(
      validateE2ESchema({
        schemaVersion: 'adsf',
        view: {
          type: 't',
          id: 'i',
          name: 'n',
        },
      }),
    ).toBe(true)
  })
})
