import { cloneDeep } from 'lodash'
import { CanalImplementsField, CanalRootComponent<PERSON>ield } from '../constants'
import {
  b2fE2ESchema,
  f2bE2ESchema,
  sortE2EServerSchemaComponentsById,
} from '../convert'
import { schemaExampleChart } from './schema-examples/schema-example-chart'
import { schemaExampleDetail } from './schema-examples/schema-example-detail'
import { schemaExampleForm } from './schema-examples/schema-example-form'
import { schemaExampleMatrix } from './schema-examples/schema-example-matrix'
import { schemaExampleTable } from './schema-examples/schema-example-table'
import { serverSchemaExampleChart } from './schema-examples/server-schema-example-chart'
import { serverSchemaExampleDetail } from './schema-examples/server-schema-example-detail'
import { serverSchemaExampleForm } from './schema-examples/server-schema-example-form'
import { serverSchemaExampleMatrix } from './schema-examples/server-schema-example-matrix'
import { serverSchemaExampleTable } from './schema-examples/server-schema-example-table'

describe('convert', () => {
  it('example form', () => {
    let serverSchema = cloneDeep(serverSchemaExampleForm)
    const schema = b2fE2ESchema(serverSchema)
    expect(serverSchema).toEqual(serverSchemaExampleForm)
    expect(schema).toEqual(schemaExampleForm)
    serverSchema = f2bE2ESchema(schema)
    expect(schema).toEqual(schemaExampleForm)
    expect(sortE2EServerSchemaComponentsById(serverSchema)).toEqual(
      sortE2EServerSchemaComponentsById(serverSchemaExampleForm),
    )
  })

  it('example table', () => {
    let serverSchema = cloneDeep(serverSchemaExampleTable)
    const schema = b2fE2ESchema(serverSchema)
    expect(serverSchema).toEqual(serverSchemaExampleTable)
    expect(schema).toEqual(schemaExampleTable)
    serverSchema = f2bE2ESchema(schema)
    expect(schema).toEqual(schemaExampleTable)
    expect(sortE2EServerSchemaComponentsById(serverSchema)).toEqual(
      sortE2EServerSchemaComponentsById(serverSchemaExampleTable),
    )
  })

  it('example chart', () => {
    let serverSchema = cloneDeep(serverSchemaExampleChart)
    const schema = b2fE2ESchema(serverSchema)
    expect(serverSchema).toEqual(serverSchemaExampleChart)
    expect(schema).toEqual(schemaExampleChart)
    serverSchema = f2bE2ESchema(schema)
    expect(schema).toEqual(schemaExampleChart)
    expect(sortE2EServerSchemaComponentsById(serverSchema)).toEqual(
      sortE2EServerSchemaComponentsById(serverSchemaExampleChart),
    )
  })

  it('example detail', () => {
    let serverSchema = cloneDeep(serverSchemaExampleDetail)
    const schema = b2fE2ESchema(serverSchema)
    expect(serverSchema).toEqual(serverSchemaExampleDetail)
    expect(schema).toEqual(schemaExampleDetail)
    serverSchema = f2bE2ESchema(schema)
    expect(schema).toEqual(schemaExampleDetail)
    expect(sortE2EServerSchemaComponentsById(serverSchema)).toEqual(
      sortE2EServerSchemaComponentsById(serverSchemaExampleDetail),
    )
  })

  it('example matrix', () => {
    let serverSchema = cloneDeep(serverSchemaExampleMatrix)
    const schema = b2fE2ESchema(serverSchema)
    expect(serverSchema).toEqual(serverSchemaExampleMatrix)
    expect(schema).toEqual(schemaExampleMatrix)
    serverSchema = f2bE2ESchema(schema)
    expect(schema).toEqual(schemaExampleMatrix)
    expect(sortE2EServerSchemaComponentsById(serverSchema)).toEqual(
      sortE2EServerSchemaComponentsById(serverSchemaExampleMatrix),
    )
  })

  it('value to data', () => {
    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              props: {
                a: 3,
                value: {
                  type: 'static',
                  value: {
                    j: 44,
                  },
                },
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
        },
        type: 'Root',
      },
      data: {
        root: {
          j: 44,
        },
      },
    })
    expect(
      b2fE2ESchema(
        {
          schemaVersion: '0.0.1',
          flattenedView: {
            components: [
              {
                type: 'Root',
                id: 'root',
                name: 'r',
                props: {
                  a: 3,
                  value: {
                    type: 'static',
                    value: {
                      j: 44,
                    },
                  },
                },
              },
            ],
            rootComponentId: 'root',
            childComponentIdMap: {},
          },
        },
        { keepValueProp: true },
      ),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
          value: {
            type: 'static',
            value: {
              j: 44,
            },
          },
        },
        type: 'Root',
      },
    })

    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              props: {
                a: 3,
                value: null,
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
        },
        type: 'Root',
      },
    })
    expect(
      b2fE2ESchema(
        {
          schemaVersion: '0.0.1',
          flattenedView: {
            components: [
              {
                type: 'Root',
                id: 'root',
                name: 'r',
                props: {
                  a: 3,
                  value: null,
                },
              },
            ],
            rootComponentId: 'root',
            childComponentIdMap: {},
          },
        },
        { keepValueProp: true },
      ),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
          value: null,
        },
        type: 'Root',
      },
    })
  })

  it('component if', () => {
    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              props: {
                a: 3,
                [CanalImplementsField.IF]: true,
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
        },
        type: 'Root',
        if: true,
      },
    })
    expect(
      b2fE2ESchema(
        {
          schemaVersion: '0.0.1',
          flattenedView: {
            components: [
              {
                type: 'Root',
                id: 'root',
                name: 'r',
                props: {
                  a: 3,
                  [CanalImplementsField.IF]: true,
                },
              },
            ],
            rootComponentId: 'root',
            childComponentIdMap: {},
          },
        },
        { keepIfProp: true },
      ),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
          [CanalImplementsField.IF]: true,
        },
        type: 'Root',
      },
    })
  })

  it('component for', () => {
    expect(
      f2bE2ESchema({
        schemaVersion: '0.0.1',
        view: {
          id: 'root',
          name: 'r',
          props: {
            a: 3,
            [CanalImplementsField.FOR]: {
              type: 'static',
              value: {
                items: [1, 2, 3],
              },
            },
          },
          type: 'Root',
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      flattenedView: {
        components: [
          {
            type: 'Root',
            id: 'root',
            name: 'r',
            props: {
              a: 3,
            },
            backFor: {
              items: [1, 2, 3],
            },
          },
        ],
        rootComponentId: 'root',
        childComponentIdMap: {},
      },
    })
    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              props: {
                a: 3,
              },
              backFor: {
                items: [1, 2, 3],
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
          [CanalImplementsField.FOR]: {
            type: 'static',
            value: {
              items: [1, 2, 3],
            },
          },
        },
        type: 'Root',
      },
    })

    expect(
      f2bE2ESchema({
        schemaVersion: '0.0.1',
        view: {
          id: 'root',
          name: 'r',
          props: {
            a: 3,
            [CanalImplementsField.FOR]: {
              type: 'static',
              value: {
                items: [
                  {
                    value: 'a',
                    label: 'A',
                  },
                  {
                    value: 'b',
                    label: 'B',
                  },
                ],
                key: 'value',
              },
            },
          },
          type: 'Root',
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      flattenedView: {
        components: [
          {
            type: 'Root',
            id: 'root',
            name: 'r',
            props: {
              a: 3,
            },
            backFor: {
              items: [
                {
                  value: 'a',
                  label: 'A',
                },
                {
                  value: 'b',
                  label: 'B',
                },
              ],
              key: 'value',
            },
          },
        ],
        rootComponentId: 'root',
        childComponentIdMap: {},
      },
    })
    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              props: {
                a: 3,
              },
              backFor: {
                items: [
                  {
                    value: 'a',
                    label: 'A',
                  },
                  {
                    value: 'b',
                    label: 'B',
                  },
                ],
                key: 'value',
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
          [CanalImplementsField.FOR]: {
            type: 'static',
            value: {
              items: [
                {
                  value: 'a',
                  label: 'A',
                },
                {
                  value: 'b',
                  label: 'B',
                },
              ],
              key: 'value',
            },
          },
        },
        type: 'Root',
      },
    })

    expect(
      f2bE2ESchema({
        schemaVersion: '0.0.1',
        view: {
          id: 'root',
          name: 'r',
          props: {
            a: 3,
            [CanalImplementsField.FOR]: {
              type: 'object',
              value: {
                items: {
                  type: 'api',
                  apiId: 'PrZdG3MiPJF1cmsbEoXPD',
                  defaultValue: {
                    type: 'static',
                    value: null,
                  },
                  transform: {
                    type: 'js',
                    code: '...',
                    codeES: 'ctx => ctx.response.data.items',
                  },
                },
                key: {
                  type: 'api',
                  apiId: 'PrZdG3MiPJF1cmsbEoXPD',
                  defaultValue: {
                    type: 'static',
                    value: null,
                  },
                  transform: {
                    type: 'js',
                    code: '...',
                    codeES: 'ctx => ctx.response.data.key',
                  },
                },
              },
            },
          },
          type: 'Root',
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      flattenedView: {
        components: [
          {
            type: 'Root',
            id: 'root',
            name: 'r',
            props: {
              a: 3,
            },
            backFor: {
              items: {
                type: 'api',
                apiId: 'PrZdG3MiPJF1cmsbEoXPD',
                defaultValue: {
                  type: 'static',
                  value: null,
                },
                transform: {
                  type: 'js',
                  code: '...',
                  codeES: 'ctx => ctx.response.data.items',
                },
              },
              key: {
                type: 'api',
                apiId: 'PrZdG3MiPJF1cmsbEoXPD',
                defaultValue: {
                  type: 'static',
                  value: null,
                },
                transform: {
                  type: 'js',
                  code: '...',
                  codeES: 'ctx => ctx.response.data.key',
                },
              },
            },
          },
        ],
        rootComponentId: 'root',
        childComponentIdMap: {},
      },
    })
    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              props: {
                a: 3,
              },
              backFor: {
                items: {
                  type: 'api',
                  apiId: 'PrZdG3MiPJF1cmsbEoXPD',
                  defaultValue: {
                    type: 'static',
                    value: null,
                  },
                  transform: {
                    type: 'js',
                    code: '...',
                    codeES: 'ctx => ctx.response.data.items',
                  },
                },
                key: {
                  type: 'api',
                  apiId: 'PrZdG3MiPJF1cmsbEoXPD',
                  defaultValue: {
                    type: 'static',
                    value: null,
                  },
                  transform: {
                    type: 'js',
                    code: '...',
                    codeES: 'ctx => ctx.response.data.key',
                  },
                },
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
          [CanalImplementsField.FOR]: {
            type: 'object',
            value: {
              items: {
                type: 'api',
                apiId: 'PrZdG3MiPJF1cmsbEoXPD',
                defaultValue: {
                  type: 'static',
                  value: null,
                },
                transform: {
                  type: 'js',
                  code: '...',
                  codeES: 'ctx => ctx.response.data.items',
                },
              },
              key: {
                type: 'api',
                apiId: 'PrZdG3MiPJF1cmsbEoXPD',
                defaultValue: {
                  type: 'static',
                  value: null,
                },
                transform: {
                  type: 'js',
                  code: '...',
                  codeES: 'ctx => ctx.response.data.key',
                },
              },
            },
          },
        },
        type: 'Root',
      },
    })
  })

  it('module apis', () => {
    expect(
      f2bE2ESchema({
        schemaVersion: '0.0.1',
        view: {
          id: 'root',
          name: 'r',
          type: 'Root',
          apis: [
            {
              id: 'aaa',
              service: 'ad.b.c',
              method: 'ma',
            },
          ],
        },
        apis: [
          {
            id: 'bbb',
            service: 'ad.b.c',
            method: 'mb',
          },
        ],
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      flattenedView: {
        components: [
          {
            type: 'Root',
            id: 'root',
            name: 'r',
          },
        ],
        rootComponentId: 'root',
        childComponentIdMap: {},
      },
      apis: {
        aaa: {
          id: 'aaa',
          service: 'ad.b.c',
          method: 'ma',
        },
        bbb: {
          id: 'bbb',
          service: 'ad.b.c',
          method: 'mb',
        },
      },
    })

    expect(
      f2bE2ESchema({
        schemaVersion: '0.0.1',
        view: {
          id: 'root',
          name: 'r',
          type: 'Root',
          apis: [],
          children: [
            {
              id: 'vv',
              name: 'v',
              type: 'View',
              apis: [],
            },
          ],
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      flattenedView: {
        components: [
          {
            type: 'Root',
            id: 'root',
            name: 'r',
          },
          {
            id: 'vv',
            name: 'v',
            type: 'View',
          },
        ],
        rootComponentId: 'root',
        childComponentIdMap: {
          root: ['vv'],
        },
      },
    })

    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              apis: {
                aaa: {
                  id: 'aaa',
                  service: 'ad.b.c',
                  method: 'ma',
                },
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
        apis: {
          bbb: {
            id: 'bbb',
            service: 'ad.b.c',
            method: 'mb',
          },
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        type: 'Root',
        apis: [
          {
            id: 'aaa',
            service: 'ad.b.c',
            method: 'ma',
          },
          {
            id: 'bbb',
            service: 'ad.b.c',
            method: 'mb',
          },
        ],
      },
    })
  })

  it('api if', () => {
    expect(
      f2bE2ESchema({
        schemaVersion: '0.0.1',
        view: {
          id: 'root',
          name: 'r',
          type: 'Root',
          apis: [
            {
              id: 'aaa',
              service: 'ad.b.c',
              method: 'ma',
              if: {
                type: 'js',
                code: 'a.b',
                codeES: 'a.b',
              },
            },
          ],
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      flattenedView: {
        components: [
          {
            type: 'Root',
            id: 'root',
            name: 'r',
          },
        ],
        rootComponentId: 'root',
        childComponentIdMap: {},
      },
      apis: {
        aaa: {
          id: 'aaa',
          service: 'ad.b.c',
          method: 'ma',
          if: {
            type: 'js',
            code: 'a.b',
            codeES: 'a.b',
          },
        },
      },
    })
    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
        apis: {
          aaa: {
            id: 'aaa',
            service: 'ad.b.c',
            method: 'ma',
            if: {
              type: 'js',
              code: 'a.b',
              codeES: 'a.b',
            },
          },
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        type: 'Root',
        apis: [
          {
            id: 'aaa',
            service: 'ad.b.c',
            method: 'ma',
            if: {
              type: 'js',
              code: 'a.b',
              codeES: 'a.b',
            },
          },
        ],
      },
    })
  })

  it('api vs apis', () => {
    expect(
      f2bE2ESchema({
        schemaVersion: '0.0.1',
        view: {
          id: 'root',
          name: 'r',
          type: 'Root',
          props: {
            a: {
              type: 'api',
              apiId: 'aaa',
              transform: {
                type: 'js',
                code: 'a',
                codeES: 'a',
              },
            },
          },
          apis: [
            {
              id: 'aaa',
              service: 'ad.b.c',
              method: 'ma',
            },
          ],
        },
        iife: {
          type: 'api',
          apiId: 'aaa',
          transform: {
            type: 'js',
            code: 'x',
            codeES: 'xes',
          },
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      flattenedView: {
        components: [
          {
            type: 'Root',
            id: 'root',
            name: 'r',
            props: {
              a: {
                type: 'api',
                apiId: 'aaa',
                transform: {
                  type: 'js',
                  code: 'a',
                  codeES: 'a',
                },
              },
            },
          },
        ],
        rootComponentId: 'root',
        childComponentIdMap: {},
      },
      apis: {
        aaa: {
          id: 'aaa',
          service: 'ad.b.c',
          method: 'ma',
        },
      },
      iife: {
        type: 'api',
        apiId: 'aaa',
        transform: {
          type: 'js',
          code: 'x',
          codeES: 'xes',
        },
      },
    })
    expect(
      f2bE2ESchema({
        schemaVersion: '0.0.1',
        view: {
          id: 'root',
          name: 'r',
          type: 'Root',
          props: {
            a: {
              type: 'api',
              apiId: 'aaa',
              transform: {
                type: 'js',
                code: 'a',
                codeES: 'a',
              },
            },
            b: {
              type: 'api',
              apiId: ['aaa', 'aaa'],
              transform: {
                type: 'js',
                code: 'b',
                codeES: 'b',
              },
            },
          },
          apis: [
            {
              id: 'aaa',
              service: 'ad.b.c',
              method: 'ma',
            },
          ],
        },
        iife: {
          type: 'api',
          apiId: ['aaa', 'aaa'],
          transform: {
            type: 'js',
            code: 'x',
            codeES: 'xes',
          },
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      flattenedView: {
        components: [
          {
            type: 'Root',
            id: 'root',
            name: 'r',
            props: {
              a: {
                type: 'api',
                apiId: 'aaa',
                transform: {
                  type: 'js',
                  code: 'a',
                  codeES: 'a',
                },
              },
              b: {
                type: 'apis',
                apiIds: ['aaa', 'aaa'],
                transform: {
                  type: 'js',
                  code: 'b',
                  codeES: 'b',
                },
              },
            },
          },
        ],
        rootComponentId: 'root',
        childComponentIdMap: {},
      },
      apis: {
        aaa: {
          id: 'aaa',
          service: 'ad.b.c',
          method: 'ma',
        },
      },
      iife: {
        type: 'apis',
        apiIds: ['aaa', 'aaa'],
        transform: {
          type: 'js',
          code: 'x',
          codeES: 'xes',
        },
      },
    })
    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              props: {
                a: {
                  type: 'api',
                  apiId: 'aaa',
                  transform: {
                    type: 'js',
                    code: 'a',
                    codeES: 'a',
                  },
                },
                b: {
                  type: 'apis',
                  apiIds: ['aaa', 'aaa'],
                  transform: {
                    type: 'js',
                    code: 'b',
                    codeES: 'b',
                  },
                },
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
        apis: {
          aaa: {
            id: 'aaa',
            service: 'ad.b.c',
            method: 'ma',
          },
        },
        iife: {
          type: 'apis',
          apiIds: ['aaa', 'aaa'],
          transform: {
            type: 'js',
            code: 'x',
            codeES: 'xes',
          },
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        type: 'Root',
        props: {
          a: {
            type: 'api',
            apiId: 'aaa',
            transform: {
              type: 'js',
              code: 'a',
              codeES: 'a',
            },
          },
          b: {
            type: 'api',
            apiId: ['aaa', 'aaa'],
            transform: {
              type: 'js',
              code: 'b',
              codeES: 'b',
            },
          },
        },
        apis: [
          {
            id: 'aaa',
            service: 'ad.b.c',
            method: 'ma',
          },
        ],
      },
      iife: {
        type: 'api',
        apiId: ['aaa', 'aaa'],
        transform: {
          type: 'js',
          code: 'x',
          codeES: 'xes',
        },
      },
    })
  })

  it('back data', () => {
    expect(
      b2fE2ESchema({
        schemaVersion: '0.0.1',
        flattenedView: {
          components: [
            {
              type: 'Root',
              id: 'root',
              name: 'r',
              props: {
                a: 3,
                [CanalRootComponentField.BACK_DATA]: {
                  type: 'static',
                  value: {
                    a: 3,
                    b: 41,
                    root: 1,
                  },
                },
                value: 5,
              },
            },
          ],
          rootComponentId: 'root',
          childComponentIdMap: {},
        },
      }),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
        },
        type: 'Root',
      },
      data: {
        a: 3,
        b: 41,
        root: 5,
      },
    })
    expect(
      b2fE2ESchema(
        {
          schemaVersion: '0.0.1',
          flattenedView: {
            components: [
              {
                type: 'Root',
                id: 'root',
                name: 'r',
                props: {
                  a: 3,
                  [CanalRootComponentField.BACK_DATA]: {
                    type: 'static',
                    value: {
                      a: 3,
                      b: 41,
                      root: 1,
                    },
                  },
                  value: 5,
                },
              },
            ],
            rootComponentId: 'root',
            childComponentIdMap: {},
          },
        },
        { keepBackDataProp: true, keepValueProp: true },
      ),
    ).toEqual({
      schemaVersion: '0.0.1',
      view: {
        id: 'root',
        name: 'r',
        props: {
          a: 3,
          [CanalRootComponentField.BACK_DATA]: {
            type: 'static',
            value: {
              a: 3,
              b: 41,
              root: 1,
            },
          },
          value: 5,
        },
        type: 'Root',
      },
    })
  })
})
