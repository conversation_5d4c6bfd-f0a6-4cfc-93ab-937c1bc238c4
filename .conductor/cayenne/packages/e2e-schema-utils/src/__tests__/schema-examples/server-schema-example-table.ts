import { E2EServerSchema } from '@ad/e2e-schema'

export const serverSchemaExampleTable: E2EServerSchema = {
  schemaVersion: '0.0.1',
  // 打平的视图
  flattenedView: {
    // 根组件 ID
    rootComponentId: 'r1',
    // 所有组件
    components: [
      {
        id: 'r1',
        type: '@ad/e2e-components::Root',
        name: '根节点',
      },
      {
        id: 't1',
        type: '@ad/e2e-components::Table',
        name: '表格1',
        props: {
          data: [
            {
              id: '1',
              name: 'alice',
              age: '13',
              address: 'asdf',
              canRefund: false,
            },
            {
              id: '2',
              name: 'alice',
              age: '13',
              address: 'asdf',
              canRefund: true,
            },
          ],
          total: 1233,
          columns: {
            type: 'array',
            value: [
              {
                type: 'component',
              },
              {
                type: 'component',
              },
              {
                type: 'component',
              },
              {
                type: 'component',
              },
            ],
          },
          defaultActiveColumns: ['姓名', '年龄', '操作'],
        },
      },
      {
        id: 'c1',
        type: '@ad/e2e-components::TableColumn',
        name: '表格列1',
        props: {
          group: '基础信息',
          title: '姓名',
          dataIndex: 'name',
          fixed: true,
        },
      },
      {
        id: 'c2',
        type: '@ad/e2e-components::TableColumn',
        name: '表格列2',
        props: {
          group: '基础信息',
          title: '年龄',
          dataIndex: 'age',
          renderType: 'number',
        },
      },
      {
        id: 'c3',
        type: '@ad/e2e-components::TableColumn',
        name: '表格列3',
        props: {
          group: '基础信息',
          title: '地址',
          dataIndex: 'address',
        },
      },
      {
        id: 'c4',
        type: '@ad/e2e-components::TableColumn',
        name: '表格列4',
        props: {
          group: '交互',
          title: '操作',
        },
      },
      {
        id: 'f1',
        type: '@ad/e2e-components::Fragment',
        name: '片段1',
      },
      {
        id: 'f2',
        type: '@ad/e2e-components::Fragment',
        name: '片段2',
      },
      {
        id: 'b1',
        type: '@ad/e2e-components::Button',
        name: '按钮1',
        props: {
          text: '充值',
          // 点击打开链接
          onClick: {
            type: 'open-url',
            url: 'https://www.kuaishou.com/new-reco?a=1',
          },
        },
      },
      {
        id: 'b2',
        type: '@ad/e2e-components::Button',
        name: '按钮2',
        props: {
          text: '退款',
          disabled: true,
          onClick: {
            type: 'bind',
            // 这里调用宿主注入的函数，交由外部处理
            fn: { type: 'get-data', path: ['refund'] },
            args: ['asdf'],
          },
        },
      },
      {
        id: 'b3',
        type: '@ad/e2e-components::Button',
        name: '按钮3',
        props: {
          text: '充值',
          // 点击打开链接
          onClick: {
            type: 'open-url',
            url: 'https://www.kuaishou.com/new-reco?a=3',
          },
        },
      },
      {
        id: 'b4',
        type: '@ad/e2e-components::Button',
        name: '按钮4',
        props: {
          text: '退款',
          onClick: {
            type: 'bind',
            // 这里调用宿主注入的函数，交由外部处理
            fn: { type: 'get-data', path: ['refund'] },
            args: ['xxxxasf'],
          },
        },
      },
    ],
    // 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
    childComponentIdMap: {
      r1: ['t1'],
      t1: [
        {
          id: 'c1',
          path: ['props', 'columns', 'value', 0, 'value'],
        },
        {
          id: 'c2',
          path: ['props', 'columns', 'value', 1, 'value'],
        },
        {
          id: 'c3',
          path: ['props', 'columns', 'value', 2, 'value'],
        },
        {
          id: 'c4',
          path: ['props', 'columns', 'value', 3, 'value'],
        },
      ],
      c4: ['f1', 'f2'],
      f1: ['b1', 'b2'],
      f2: ['b3', 'b4'],
    },
  },
  data: {
    t1: {
      pageSize: 20,
      current: 1,
    },
  },
  linkage: {
    /**
     * 表格像表单控件那样，会产生数据
     *
     * 数据描述翻页相关的信息，翻页后，会自动刷新端到端 Schema
     */
    autoRefreshByComponent: ['t1'],
  },
}
