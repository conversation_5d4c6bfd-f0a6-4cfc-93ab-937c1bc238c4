import { E2EServerSchema } from '@ad/e2e-schema'

export const serverSchemaExampleForm: E2EServerSchema = {
  schemaVersion: '0.0.1',
  version: '123',
  // 打平的视图
  flattenedView: {
    // 根组件 ID
    rootComponentId: 'r1',
    // 所有组件
    components: [
      {
        id: 'r1',
        type: '@ad/e2e-components::Root',
        name: '根节点',
      },
      {
        id: 'p1',
        type: '@ad/e2e-components::Panel',
        name: '面板1',
        props: {
          title: '企业基本信息',
        },
      },
      {
        id: 'p2',
        type: '@ad/e2e-components::Panel',
        name: '面板2',
        props: {
          title: '管理员信息',
        },
      },
      {
        id: 'ft1',
        type: '@ad/e2e-components::FormItem',
        name: '表单项1',
        props: {
          title: '公司名称',
        },
      },
      {
        id: 'ft2',
        type: '@ad/e2e-components::FormItem',
        name: '表单项2',
        props: {
          title: '统一信用代码',
          // 校验信息通过刷新端到端 Schema 时，由后端给出
          validateStatus: 'error',
          help: '不能输入中文',
        },
      },
      {
        id: 'ft3',
        type: '@ad/e2e-components::FormItem',
        name: '表单项3',
        props: {
          title: '营业执照',
        },
      },
      {
        // 表单控件的数据不由 Form 组件维护，由运行时统一管理
        id: 'i1',
        type: '@ad/e2e-components::Input',
        name: '输入框1',
        props: {
          placeholder: '请输入',
        },
      },
      {
        // 表单控件的数据不由 Form 组件维护，由运行时统一管理
        id: 'i2',
        type: '@ad/e2e-components::Input',
        name: '输入框2',
        props: {
          placeholder: '请输入',
        },
      },
      {
        id: 'u1',
        type: '@ad/e2e-components::Uploader',
        name: '上传1',
        props: {
          title: '营业执照',
        },
      },
      {
        id: 'b1',
        type: '@ad/e2e-components::Button',
        name: '按钮1',
        props: {
          text: '提交',
          // 提交也统一走端到端 Schema 刷新，但可以指定刷新类型
          onClick: {
            type: 'refresh',
            refreshType: 'submit',
          },
        },
      },
    ],
    // 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
    childComponentIdMap: {
      r1: ['p1', 'p2'],
      p1: ['ft1', 'ft2', 'ft3'],
      p2: ['b1'],
      ft1: ['i1'],
      ft2: ['i2'],
      ft3: ['u1'],
    },
  },
  // （全局）数据（初始值），组件值也会按 ID 动态存入
  data: {
    i1: 'abc',
  },
  linkage: {
    // 通用（静态）参数
    commonParams: {
      a: 3,
      b: 4,
    },
    // 组件（产生的）数据参数
    componentDataParams: {
      // 通用组件产生的数据参数
      common: ['i1'],
      byRefreshType: {
        /**
         * submit 需要携带的组件值，最终携带的数据为:
         * {
         *   a: 3,
         *   b: 4,
         *   i1: 'asdf',
         *   u1: 'url1asdf',
         * }
         */
        submit: ['u1'],
      },
    },
    /**
     * i2 组件值变化时会自动刷新，最终携带的数据为（自动包含触发组件的数据）：
     * {
     *   a: 3,
     *   b: 4,
     *   i1: 'asdf',
     *   i2: 'xlkasdfj',
     * }
     */
    autoRefreshByComponent: ['i2'],
  },
}
