import { E2ESchema } from '@ad/e2e-schema'

export const schemaExampleMatrix: E2ESchema = {
  animations: [
    {
      animationProtocol: {
        type: 'static',
        value: {},
      },
      autoplay: false,
      componentId: 'casldkfj',
      delay: 1000,
      duration: 300,
      id: 'a1',
      onEnd: {
        trackId: 'track2',
        type: 'track',
      },
      repeatCount: -1,
      triggers: ['currentVideoEnd'],
    },
  ],
  componentCodes: {
    ['@ad/e2e-components::Root']: {
      code: {
        js: 'kwai://tk?bundleId=KwaishopCMallChannels&viewKey=channel_promotion&minBundleVersion=6',
      },
      exportIdentifier: 'Root',
    },
    ['@ad/e2e-components::Image']: {
      code: {
        js: 'kwai://tk?bundleId=KwaishopCMallChannels&viewKey=channel_promotion&minBundleVersion=6',
      },
      exportIdentifier: 'Image',
    },
    ['@ad/another-e2e-components::Card']: {
      code: {
        js: 'kwai://tk?bundleId=alsdfkj&viewKey=channel_promotion&minBundleVersion=6',
      },
      exportIdentifier: 'Card',
    },
  },
  schemaVersion: '0.0.1',
  tracks: [
    {
      eventOptions: {},
      eventType: 'CLICK',
      id: 'track1',
    },
    {
      eventOptions: {},
      eventType: 'SHOW',
      id: 'track2',
    },
  ],
  view: {
    children: [
      {
        id: 'baksjdf',
        name: '图片::jdf',
        props: {
          onClick: {
            trackId: 'track1',
            type: 'track',
          },
        },
        type: '@ad/e2e-components::Image',
      },
      {
        id: 'casldkfj',
        name: '卡片::kfj',
        type: '@ad/another-e2e-components::Card',
      },
    ],
    id: 'asfx',
    name: '根节点',
    type: '@ad/e2e-components::Root',
  },
}
