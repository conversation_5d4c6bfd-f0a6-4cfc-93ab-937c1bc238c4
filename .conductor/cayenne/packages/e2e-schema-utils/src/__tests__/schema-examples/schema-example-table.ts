import { E2ESchema } from '@ad/e2e-schema'

export const schemaExampleTable: E2ESchema = {
  linkage: {
    autoRefreshByComponent: ['t1'],
  },
  schemaVersion: '0.0.1',
  view: {
    children: [
      {
        id: 't1',
        name: '表格1',
        props: {
          columns: {
            type: 'array',
            value: [
              {
                type: 'component',
                value: {
                  id: 'c1',
                  name: '表格列1',
                  props: {
                    dataIndex: 'name',
                    fixed: true,
                    group: '基础信息',
                    title: '姓名',
                  },
                  type: '@ad/e2e-components::TableColumn',
                },
              },
              {
                type: 'component',
                value: {
                  id: 'c2',
                  name: '表格列2',
                  props: {
                    dataIndex: 'age',
                    group: '基础信息',
                    renderType: 'number',
                    title: '年龄',
                  },
                  type: '@ad/e2e-components::TableColumn',
                },
              },
              {
                type: 'component',
                value: {
                  id: 'c3',
                  name: '表格列3',
                  props: {
                    dataIndex: 'address',
                    group: '基础信息',
                    title: '地址',
                  },
                  type: '@ad/e2e-components::TableColumn',
                },
              },
              {
                type: 'component',
                value: {
                  id: 'c4',
                  name: '表格列4',
                  props: {
                    group: '交互',
                    title: '操作',
                  },
                  type: '@ad/e2e-components::TableColumn',
                  children: [
                    {
                      children: [
                        {
                          id: 'b1',
                          name: '按钮1',
                          props: {
                            onClick: {
                              type: 'open-url',
                              url: 'https://www.kuaishou.com/new-reco?a=1',
                            },
                            text: '充值',
                          },
                          type: '@ad/e2e-components::Button',
                        },
                        {
                          id: 'b2',
                          name: '按钮2',
                          props: {
                            disabled: true,
                            onClick: {
                              args: ['asdf'],
                              fn: {
                                path: ['refund'],
                                type: 'get-data',
                              },
                              type: 'bind',
                            },
                            text: '退款',
                          },
                          type: '@ad/e2e-components::Button',
                        },
                      ],
                      id: 'f1',
                      name: '片段1',
                      type: '@ad/e2e-components::Fragment',
                    },
                    {
                      children: [
                        {
                          id: 'b3',
                          name: '按钮3',
                          props: {
                            onClick: {
                              type: 'open-url',
                              url: 'https://www.kuaishou.com/new-reco?a=3',
                            },
                            text: '充值',
                          },
                          type: '@ad/e2e-components::Button',
                        },
                        {
                          id: 'b4',
                          name: '按钮4',
                          props: {
                            onClick: {
                              args: ['xxxxasf'],
                              fn: {
                                path: ['refund'],
                                type: 'get-data',
                              },
                              type: 'bind',
                            },
                            text: '退款',
                          },
                          type: '@ad/e2e-components::Button',
                        },
                      ],
                      id: 'f2',
                      name: '片段2',
                      type: '@ad/e2e-components::Fragment',
                    },
                  ],
                },
              },
            ],
          },
          data: [
            {
              address: 'asdf',
              age: '13',
              canRefund: false,
              id: '1',
              name: 'alice',
            },
            {
              address: 'asdf',
              age: '13',
              canRefund: true,
              id: '2',
              name: 'alice',
            },
          ],
          defaultActiveColumns: ['姓名', '年龄', '操作'],
          total: 1233,
        },
        type: '@ad/e2e-components::Table',
      },
    ],
    id: 'r1',
    name: '根节点',
    type: '@ad/e2e-components::Root',
  },
  data: {
    t1: {
      pageSize: 20,
      current: 1,
    },
  },
}
