import { E2EServerSchema } from '@ad/e2e-schema'

export const serverSchemaExampleMatrix: E2EServerSchema = {
  schemaVersion: '0.0.1',
  // 打平的视图
  flattenedView: {
    // 根组件 ID
    rootComponentId: 'asfx',
    // 所有组件
    components: [
      {
        id: 'asfx',
        type: '@ad/e2e-components::Root',
        name: '根节点',
      },
      {
        id: 'baksjdf',
        type: '@ad/e2e-components::Image',
        name: '图片::jdf',
        props: {
          // 点击图片埋点
          onClick: {
            type: 'track',
            trackId: 'track1',
          },
        },
      },
      {
        id: 'casldkfj',
        type: '@ad/another-e2e-components::Card',
        name: '卡片::kfj',
      },
    ],
    // 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
    childComponentIdMap: {
      asfx: ['baksjdf', 'casldkfj'],
    },
  },
  animations: [
    {
      id: 'a1',
      componentId: 'casldkfj',
      autoplay: false,
      // 触发事件，当前视频播放结束等
      triggers: ['currentVideoEnd'],
      // 时长
      duration: 300,
      // 延迟时间
      delay: 1000,
      repeatCount: -1,
      // 动画数据
      animationProtocol: {
        type: 'static',
        value: {
          // ...
        },
      },
      // 动画结束时埋点
      onEnd: {
        type: 'track',
        trackId: 'track2',
      },
    },
  ],
  tracks: [
    {
      id: 'track1',
      eventType: 'CLICK',
      eventOptions: {},
    },
    {
      id: 'track2',
      eventType: 'SHOW',
      eventOptions: {},
    },
  ],
  // 组件代码，组件类型 -> 代码定义，从物料协议里摘取
  componentCodes: {
    ['@ad/e2e-components::Root']: {
      code: {
        js: 'kwai://tk?bundleId=KwaishopCMallChannels&viewKey=channel_promotion&minBundleVersion=6',
      },
      exportIdentifier: 'Root',
    },
    ['@ad/e2e-components::Image']: {
      code: {
        js: 'kwai://tk?bundleId=KwaishopCMallChannels&viewKey=channel_promotion&minBundleVersion=6',
      },
      exportIdentifier: 'Image',
    },
    ['@ad/another-e2e-components::Card']: {
      code: {
        js: 'kwai://tk?bundleId=alsdfkj&viewKey=channel_promotion&minBundleVersion=6',
      },
      exportIdentifier: 'Card',
    },
  },
}
