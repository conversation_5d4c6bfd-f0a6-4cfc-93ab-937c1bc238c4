import { E2EServerSchema } from '@ad/e2e-schema'

export const serverSchemaExampleDetail: E2EServerSchema = {
  schemaVersion: '0.0.1',
  // 打平的视图
  flattenedView: {
    // 根组件 ID
    rootComponentId: 'r1',
    // 所有组件
    components: [
      {
        id: 'r1',
        type: '@ad/e2e-components::Root',
        name: '根节点',
      },
      {
        id: 'p1',
        type: '@ad/e2e-components::Panel',
        name: '面板1',
        props: {
          title: '什么有限公司',
          titleSuffix: {
            type: 'component',
          },
          subtitle: '数据更新时间',
          columnCount: 3,
          collapseAfterRows: 2,
        },
      },
      {
        id: 'p2',
        type: '@ad/e2e-components::Panel',
        name: '面板2',
        props: {
          title: '完成进度',
          subtitle: {
            type: 'api',
            apiId: 'aaa',
            transform: {
              x: 3,
            },
          },
          text: {
            defaultValue: {
              type: 'static',
              value: '文本333',
            },
            transform: {
              a: 3,
              b: {
                type: 'jsonata',
                code: '$.response.data.x',
              },
            },
            type: 'apis',
            apiIds: ['aaa', 'bbb'],
          },
          style: {
            type: 'object',
            value: {
              color: {
                type: 'api',
                apiId: 'bbb',
                transform: {
                  type: 'jsonata',
                  code: 'x.color',
                },
                defaultValue: 'green',
              },
              fontWeight: 400,
              fontSize: {
                type: 'api',
                apiId: 'bbb',
                transform: {
                  x: 3,
                  y: {
                    type: 'jsonata',
                    code: 'y',
                  },
                },
              },
            },
          },
        },
        apis: {
          aaa: {
            id: 'aaa',
            service: 'ad.b.c',
            method: 'ma',
          },
          bbb: {
            id: 'bbb',
            service: 'ad.b.c',
            method: 'mb',
            args: {
              a: 3,
              b: 4,
              c: {
                type: 'jsonata',
                code: 'userId',
              },
            },
          },
          cccc: {
            id: 'cccc',
            service: 'ad.b.c',
            method: 'mc',
            args: {
              type: 'jsonata',
              code: 'userId',
            },
          },
        },
      },
      {
        id: 'b1',
        type: '@ad/e2e-components::Button',
        name: '按钮1',
        props: {
          icon: 'StepBackwardOutlined',
          text: '切换主体',
          // 这里调用宿主注入的函数，交由外部处理
          onClick: { type: 'get-data', path: ['switchEntity'] },
        },
      },
      {
        id: 'di1',
        type: '@ad/e2e-components::DescItem',
        name: '描述项1',
        props: {
          title: '合同编号',
          text: '129471293847',
        },
      },
      {
        id: 'pie1',
        type: '@ad/e2e-components::Pie',
        name: '饼图1',
        props: {
          title: '消耗分布图',
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 1048, name: 'Search Engine' },
                { value: 735, name: 'Direct' },
                { value: 580, name: 'Email' },
                { value: 484, name: 'Union Ads' },
                { value: 300, name: 'Video Ads' },
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
            },
          ],
        },
      },
      {
        id: 'sc1',
        type: '@ad/another-e2e-components::StatisticCard',
        name: '指标卡1',
        props: {
          title: '总消耗',
          v: '23984712384',
        },
      },
    ],
    // 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
    childComponentIdMap: {
      r1: ['p1', 'p2'],
      p1: [
        'di1',
        {
          id: 'b1',
          path: ['props', 'titleSuffix', 'value'],
        },
      ],
      p2: ['sc1', 'pie1'],
    },
  },
}
