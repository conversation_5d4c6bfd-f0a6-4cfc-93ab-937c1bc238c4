import { E2ESchema } from '@ad/e2e-schema'

export const schemaExampleChart: E2ESchema = {
  data: {
    abc: 'a',
  },
  linkage: {
    autoRefreshByComponent: ['s1'],
  },
  schemaVersion: '0.0.1',
  tracks: [
    {
      eventOptions: {
        page: 'TEST_PAGE',
        type: 'enter',
      },
      eventType: 'PV',
      id: 'track1',
    },
  ],
  view: {
    children: [
      {
        children: [
          {
            id: 's1',
            name: '单选1',
            props: {
              options: [
                {
                  name: '选项一',
                  value: 'a',
                },
                {
                  name: '选项二',
                  value: 'b',
                },
              ],
            },
            type: '@ad/e2e-components::Select',
          },
        ],
        id: 'p1',
        name: '面板1',
        props: {
          columnCount: 1,
          title: '筛选',
        },
        type: '@ad/e2e-components::Panel',
      },
      {
        children: [
          {
            id: 'sc1',
            name: '指标卡1',
            props: {
              chartId: 1234,
              title: '总消耗',
              v: '23984712384',
            },
            type: '@ad/another-e2e-components::StatisticCard',
          },
          {
            id: 'pie1',
            name: '饼图1',
            props: {
              chartId: 5678,
              series: [
                {
                  data: [
                    {
                      name: 'Search Engine',
                      value: 1048,
                    },
                    {
                      name: 'Direct',
                      value: 735,
                    },
                    {
                      name: 'Email',
                      value: 580,
                    },
                    {
                      name: 'Union Ads',
                      value: 484,
                    },
                    {
                      name: 'Video Ads',
                      value: 300,
                    },
                  ],
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowColor: 'rgba(0, 0, 0, 0.5)',
                      shadowOffsetX: 0,
                    },
                  },
                  name: 'Access From',
                  radius: '50%',
                  type: 'pie',
                },
              ],
              title: '消耗分布图',
            },
            type: '@ad/e2e-components::Pie',
          },
        ],
        id: 'p2',
        name: '面板2',
        props: {
          columnCount: 2,
          title: '消耗核心指标',
        },
        type: '@ad/e2e-components::Panel',
      },
    ],
    id: 'r1',
    name: '根节点',
    props: {
      onMount: {
        trackId: 'track1',
        type: 'track',
      },
    },
    type: '@ad/e2e-components::Root',
  },
}
