import { E2EServerSchema } from '@ad/e2e-schema'

export const serverSchemaExampleChart: E2EServerSchema = {
  schemaVersion: '0.0.1',
  // 打平的视图
  flattenedView: {
    // 根组件 ID
    rootComponentId: 'r1',
    // 所有组件
    components: [
      {
        id: 'r1',
        type: '@ad/e2e-components::Root',
        name: '根节点',
        props: {
          // 挂载后执行埋点表达式
          onMount: {
            type: 'track',
            trackId: 'track1',
          },
        },
      },
      {
        id: 'p1',
        type: '@ad/e2e-components::Panel',
        name: '面板1',
        props: {
          title: '筛选',
          columnCount: 1,
        },
      },
      {
        id: 'p2',
        type: '@ad/e2e-components::Panel',
        name: '面板2',
        props: {
          title: '消耗核心指标',
          // 面板组件指定列数布局
          columnCount: 2,
        },
      },
      {
        id: 's1',
        type: '@ad/e2e-components::Select',
        name: '单选1',
        props: {
          options: [
            {
              name: '选项一',
              value: 'a',
            },
            {
              name: '选项二',
              value: 'b',
            },
          ],
        },
      },
      {
        id: 'sc1',
        type: '@ad/another-e2e-components::StatisticCard',
        name: '指标卡1',
        props: {
          chartId: 1234,
          title: '总消耗',
          v: '23984712384',
        },
      },
      {
        id: 'pie1',
        type: '@ad/e2e-components::Pie',
        name: '饼图1',
        props: {
          chartId: 5678,
          title: '消耗分布图',
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 1048, name: 'Search Engine' },
                { value: 735, name: 'Direct' },
                { value: 580, name: 'Email' },
                { value: 484, name: 'Union Ads' },
                { value: 300, name: 'Video Ads' },
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
            },
          ],
        },
      },
    ],
    // 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
    childComponentIdMap: {
      r1: ['p1', 'p2'],
      p1: ['s1'],
      p2: ['sc1', 'pie1'],
    },
  },
  // 数据初始值
  data: {
    abc: 'a',
  },
  tracks: [
    {
      id: 'track1',
      eventType: 'PV',
      eventOptions: {
        type: 'enter',
        page: 'TEST_PAGE',
      },
    },
  ],
  linkage: {
    autoRefreshByComponent: ['s1'],
  },
}
