import { E2ESchema } from '@ad/e2e-schema'

export const schemaExampleForm: E2ESchema = {
  data: {
    i1: 'abc',
  },
  linkage: {
    autoRefreshByComponent: ['i2'],
    commonParams: {
      a: 3,
      b: 4,
    },
    componentDataParams: {
      byRefreshType: {
        submit: ['u1'],
      },
      common: ['i1'],
    },
  },
  schemaVersion: '0.0.1',
  version: '123',
  view: {
    children: [
      {
        children: [
          {
            children: [
              {
                id: 'i1',
                name: '输入框1',
                props: {
                  placeholder: '请输入',
                },
                type: '@ad/e2e-components::Input',
              },
            ],
            id: 'ft1',
            name: '表单项1',
            props: {
              title: '公司名称',
            },
            type: '@ad/e2e-components::FormItem',
          },
          {
            children: [
              {
                id: 'i2',
                name: '输入框2',
                props: {
                  placeholder: '请输入',
                },
                type: '@ad/e2e-components::Input',
              },
            ],
            id: 'ft2',
            name: '表单项2',
            props: {
              help: '不能输入中文',
              title: '统一信用代码',
              validateStatus: 'error',
            },
            type: '@ad/e2e-components::FormItem',
          },
          {
            children: [
              {
                id: 'u1',
                name: '上传1',
                props: {
                  title: '营业执照',
                },
                type: '@ad/e2e-components::Uploader',
              },
            ],
            id: 'ft3',
            name: '表单项3',
            props: {
              title: '营业执照',
            },
            type: '@ad/e2e-components::FormItem',
          },
        ],
        id: 'p1',
        name: '面板1',
        props: {
          title: '企业基本信息',
        },
        type: '@ad/e2e-components::Panel',
      },
      {
        children: [
          {
            id: 'b1',
            name: '按钮1',
            props: {
              onClick: {
                refreshType: 'submit',
                type: 'refresh',
              },
              text: '提交',
            },
            type: '@ad/e2e-components::Button',
          },
        ],
        id: 'p2',
        name: '面板2',
        props: {
          title: '管理员信息',
        },
        type: '@ad/e2e-components::Panel',
      },
    ],
    id: 'r1',
    name: '根节点',
    type: '@ad/e2e-components::Root',
  },
}
