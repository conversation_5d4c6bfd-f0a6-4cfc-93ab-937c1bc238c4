import { E2ESchema } from '@ad/e2e-schema'

export const schemaExampleDetail: E2ESchema = {
  schemaVersion: '0.0.1',
  view: {
    children: [
      {
        children: [
          {
            id: 'di1',
            name: '描述项1',
            props: {
              text: '129471293847',
              title: '合同编号',
            },
            type: '@ad/e2e-components::DescItem',
          },
        ],
        id: 'p1',
        name: '面板1',
        props: {
          collapseAfterRows: 2,
          columnCount: 3,
          subtitle: '数据更新时间',
          title: '什么有限公司',
          titleSuffix: {
            type: 'component',
            value: {
              id: 'b1',
              name: '按钮1',
              props: {
                icon: 'StepBackwardOutlined',
                onClick: {
                  path: ['switchEntity'],
                  type: 'get-data',
                },
                text: '切换主体',
              },
              type: '@ad/e2e-components::Button',
            },
          },
        },
        type: '@ad/e2e-components::Panel',
      },
      {
        children: [
          {
            id: 'sc1',
            name: '指标卡1',
            props: {
              title: '总消耗',
              v: '23984712384',
            },
            type: '@ad/another-e2e-components::StatisticCard',
          },
          {
            id: 'pie1',
            name: '饼图1',
            props: {
              series: [
                {
                  data: [
                    {
                      name: 'Search Engine',
                      value: 1048,
                    },
                    {
                      name: 'Direct',
                      value: 735,
                    },
                    {
                      name: 'Email',
                      value: 580,
                    },
                    {
                      name: 'Union Ads',
                      value: 484,
                    },
                    {
                      name: 'Video Ads',
                      value: 300,
                    },
                  ],
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowColor: 'rgba(0, 0, 0, 0.5)',
                      shadowOffsetX: 0,
                    },
                  },
                  name: 'Access From',
                  radius: '50%',
                  type: 'pie',
                },
              ],
              title: '消耗分布图',
            },
            type: '@ad/e2e-components::Pie',
          },
        ],
        id: 'p2',
        name: '面板2',
        props: {
          title: '完成进度',
          subtitle: {
            type: 'api',
            apiId: 'aaa',
            transform: {
              type: 'degraded-jsonata',
              value: {
                x: 3,
              },
            },
          },
          text: {
            apiId: ['aaa', 'bbb'],
            defaultValue: {
              type: 'static',
              value: '文本333',
            },
            transform: {
              type: 'degraded-jsonata',
              value: {
                a: 3,
                b: {
                  code: '$.response.data.x',
                  type: 'jsonata',
                },
              },
            },
            type: 'api',
          },
          style: {
            type: 'object',
            value: {
              color: {
                type: 'api',
                apiId: 'bbb',
                transform: {
                  type: 'jsonata',
                  code: 'x.color',
                },
                defaultValue: 'green',
              },
              fontWeight: 400,
              fontSize: {
                type: 'api',
                apiId: 'bbb',
                transform: {
                  type: 'degraded-jsonata',
                  value: {
                    x: 3,
                    y: {
                      type: 'jsonata',
                      code: 'y',
                    },
                  },
                },
              },
            },
          },
        },
        apis: [
          {
            id: 'aaa',
            service: 'ad.b.c',
            method: 'ma',
          },
          {
            id: 'bbb',
            service: 'ad.b.c',
            method: 'mb',
            args: [
              {
                type: 'degraded-jsonata',
                value: {
                  a: 3,
                  b: 4,
                  c: {
                    type: 'jsonata',
                    code: 'userId',
                  },
                },
              },
            ],
          },
          {
            id: 'cccc',
            service: 'ad.b.c',
            method: 'mc',
            args: [
              {
                type: 'jsonata',
                code: 'userId',
              },
            ],
          },
        ],
        type: '@ad/e2e-components::Panel',
      },
    ],
    id: 'r1',
    name: '根节点',
    type: '@ad/e2e-components::Root',
  },
}
