import { E2ESchema, E2EServerSchema } from '@ad/e2e-schema'

/**
 * 校验端到端后端 Schema
 * @param serverSchema 不完全的端到端后端 Schema
 */
export function validateE2EServerSchema(
  serverSchema?: Partial<E2ESchema | E2EServerSchema>,
): serverSchema is E2EServerSchema {
  return !!(serverSchema?.schemaVersion && 'flattenedView' in serverSchema)
}

/**
 * 校验端到端 Schema
 * @param serverSchema 不完全的端到端 Schema
 */
export function validateE2ESchema(
  schema?: Partial<E2ESchema | E2EServerSchema>,
): schema is E2ESchema {
  return !!(schema?.schemaVersion && 'view' in schema)
}
