import {
  E2ESchemaExpression,
  E2ESchemaExpressionActions,
  E2ESchemaExpressionAPI,
  E2ESchemaExpressionBind,
  E2ESchemaExpressionJS,
} from '@ad/e2e-schema'
import { normalizeExpression } from '@kael/schema-utils'

/**
 * 是可绑定的表达式
 * @param expr 表达式
 */
export function isBindableExpression(
  expr?: E2ESchemaExpression | null,
): expr is E2ESchemaExpressionAPI | E2ESchemaExpressionJS {
  if (!expr) return false
  const ne = normalizeExpression(expr)
  return ne.type === 'api' || ne.type === 'js'
}

/**
 * 是接口表达式
 * @param expr 表达式
 */
export function isApiExpression(
  expr?: E2ESchemaExpression | null,
): expr is E2ESchemaExpressionAPI {
  if (!expr) return false
  const ne = normalizeExpression(expr)
  return ne.type === 'api'
}

/**
 * 是 JS 表达式
 * @param expr 表达式
 */
export function isJsExpression(
  expr?: E2ESchemaExpression | null,
): expr is E2ESchemaExpressionJS {
  if (!expr) return false
  const ne = normalizeExpression(expr)
  return ne.type === 'js'
}

/**
 * 是多操作表达式
 * @param expr 表达式
 */
export function isActionsExpression(
  expr?: E2ESchemaExpression | null,
): expr is E2ESchemaExpressionActions {
  if (!expr) return false
  const ne = normalizeExpression(expr)
  return ne.type === 'actions'
}

/**
 * 是绑定表达式
 * @param expr 表达式
 */
export function isBindExpression(
  expr?: E2ESchemaExpression | null,
): expr is E2ESchemaExpressionBind {
  if (!expr) return false
  const ne = normalizeExpression(expr)
  return ne.type === 'bind'
}
