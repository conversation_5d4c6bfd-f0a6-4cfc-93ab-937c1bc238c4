import {
  E2ESchemaComponent,
  E2ESchemaExpression,
  E2ESchemaNormalizedExpression,
} from '@ad/e2e-schema'
import {
  ARRAY_EXPRESSION_TYPE,
  OBJECT_EXPRESSION_TYPE,
  PropertyPath,
  STATIC_EXPRESSION_TYPE,
  normalizeExpression,
} from '@kael/schema-utils'
import { get } from 'lodash'

/**
 * 组件属性详情
 */
export interface ComponentPropDetail {
  /**
   * 表达式
   */
  expression?: E2ESchemaNormalizedExpression
  /**
   * JSON 路径
   */
  jsonPath: PropertyPath
  /**
   * 是否包装过的静态表达式
   */
  isWrappedStatic: boolean
}

/**
 * 获取组件属性详情，替代 getComponentPropExpression，有更多的信息
 * @param component 组件
 * @param logicPath 逻辑路径
 */
export function getComponentPropDetail(
  component: E2ESchemaComponent,
  logicPath: PropertyPath,
): ComponentPropDetail {
  const firstKey = logicPath[0]
  if (!logicPath.length || !component.props || !(firstKey in component.props)) {
    return {
      jsonPath: [],
      isWrappedStatic: false,
    }
  }
  const traverseRet = traverse(component.props[firstKey], logicPath.slice(1))
  return {
    ...traverseRet,
    jsonPath: ['props', firstKey, ...traverseRet.jsonPath],
  }

  function traverse(
    expr: E2ESchemaExpression,
    lp: PropertyPath,
  ): ComponentPropDetail {
    const normalizedExpr = normalizeExpression(expr)
    const isWrappedStatic = normalizedExpr !== expr
    if (!lp.length) {
      return {
        expression: normalizedExpr,
        jsonPath: [],
        isWrappedStatic,
      }
    }
    switch (normalizedExpr.type) {
      case STATIC_EXPRESSION_TYPE: {
        return {
          expression: {
            type: STATIC_EXPRESSION_TYPE,
            value: get(normalizedExpr.value, lp),
          },
          jsonPath: [...(isWrappedStatic ? [] : ['value']), ...lp],
          isWrappedStatic: true,
        }
      }
      case OBJECT_EXPRESSION_TYPE: {
        const key = lp[0]
        const ret = traverse(normalizedExpr.value[key], lp.slice(1))
        return {
          ...ret,
          jsonPath: ['value', key, ...ret.jsonPath],
        }
      }
      case ARRAY_EXPRESSION_TYPE: {
        const index = lp[0] as number
        const ret = traverse(normalizedExpr.value[index], lp.slice(1))
        return {
          ...ret,
          jsonPath: ['value', index, ...ret.jsonPath],
        }
      }
      default: {
        throw new Error(
          `getComponentPropDetail unknown expression type: ${normalizedExpr.type}`,
        )
      }
    }
  }
}
