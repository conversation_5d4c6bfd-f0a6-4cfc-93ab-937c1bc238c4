import { isArray } from 'lodash'

/**
 * 可数组化
 */
export type Arrayable<T> = T | T[]

/**
 * 反可数组化
 */
export type Unarrayable<T> = T extends Arrayable<infer V> ? V : unknown

/**
 * 可数组化转数组
 * @param v 可数组化的值
 */
export function arrayableToArray<T>(v: Arrayable<T> = []): T[] {
  return isArray(v) ? v : [v]
}

/**
 * 数组转可数组化
 * @param v 数组
 */
export function arrayToArrayable<T>(v: T[]): Arrayable<T> {
  return v.length === 1 ? v[0] : v
}
