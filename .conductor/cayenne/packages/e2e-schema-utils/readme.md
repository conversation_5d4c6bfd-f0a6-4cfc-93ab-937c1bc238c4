# e2e-schema-utils

端到端协议工具集，收拢一些共用的操作函数

# 使用举例

## f2bE2ESchema

端到端 Schema 转端到端后端 Schema

```ts
import { E2ESchema } from '@ad/e2e-schema'
import { f2bE2ESchema } from '@ad/e2e-schema-utils'

const schema: E2ESchema = {
  // ...
  view: {
    // ...
  },
}
const serverSchema = f2bE2ESchema(schema)
```

## b2fE2ESchema

端到端后端 Schema 转端到端 Schema

```ts
import { E2EServerSchema } from '@ad/e2e-schema'
import { b2fE2ESchema } from '@ad/e2e-schema-utils'

const serverSchema: E2EServerSchema = {
  // ...
  flattenedView: {
    // ...
  },
}
const schema = b2fE2ESchema(schema)
```

# 开发

## 脚本

| 脚本   | 说明                  |
| ------ | --------------------- |
| build  | 编译                  |
| start  | 开始开发              |
| test   | 运行单测              |
| test:w | 以 watch 模式运行单测 |
