import { FetchSchemaEnv } from '@ad/canal-react-runtime'
import { CommonRes } from '@ad/canal-shared'

/**
 * 获取模块详情请求
 */
export interface GetModuleDetailReq {
  /**
   * ID
   */
  id: string
  /**
   * 版本
   */
  version?: string
}

/**
 * 获取模块详情响应
 */
export type GetModuleDetailRes = CommonRes<{
  /**
   * 名称
   */
  name: string
  /**
   * （业务）域代码
   */
  businessDomainCode: string
  /**
   * 内容
   */
  content: string
}>

/**
 * 获取模块部署记录请求
 */
export interface GetModuleDeployReq {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 环境
   */
  env: FetchSchemaEnv
}
