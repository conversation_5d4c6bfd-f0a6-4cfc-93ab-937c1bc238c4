import { sleep } from '@ad/canal-shared'
import { E2EGlobalSchema } from '@ad/e2e-schema'
import type { IApi } from '@kmi/react'
import { isNumber } from 'lodash'
import { serverApis } from './server-apis'

/**
 * kmi 大运河配置
 */
interface KmiCanalConfig {
  /** 全局模块 ID，这里填了之后，loadGlobalModuleWithType 可以省略 */
  globalModuleId: string
  /** 全局模块版本 */
  globalModuleVersion?: number | undefined
  /** 大运河轮询间隔，用于拉取全局模块的内容，单位秒，默认：`30` */
  loogPollingInterval?: number | undefined
  /** 跳过服务器请求步骤，使用 any 作为全局前端模型的类型，默认：`false` */
  skipServer?: boolean | undefined
  /** 服务器（Node.js）环境，指 canal.staging.kuaishou.com 和 canal.corp.kuaishou.com，一般用不上 */
  nodeEnv?: 'production' | 'staging' | undefined
}

/**
 * 全局前端模型的文件名
 */
const FILE_NAME_GLOBAL_FRONT_MODEL =
  '__CANAL_SECRET_INTERNALS_DO_NOT_IMPORT_OR_YOU_WILL_BE_FIRED'

/**
 * 全局前端模型的 any 版本
 */
const ANY_GLOBAL_FRONT_MODEL = `export default {} as any`

export default (api: IApi): void => {
  api.describe({
    key: 'canal',
    config: {
      schema({ zod }) {
        return zod
          .object({
            globalModuleId: zod
              .string()
              .describe(
                '全局模块 ID，这里填了之后，loadGlobalModuleWithType 可以省略',
              ),
            globalModuleVersion: zod
              .number()
              .optional()
              .describe('全局模块版本'),
            loogPollingInterval: zod
              .number()
              .optional()
              .describe(
                '大运河轮询间隔，用于拉取全局模块的内容，单位秒，默认：`30`',
              ),
            skipServer: zod
              .boolean()
              .optional()
              .describe(
                '跳过服务器请求步骤，使用 any 作为全局前端模型的类型，默认：`false`',
              ),
            nodeEnv: zod
              .enum(['production', 'staging'])
              .optional()
              .describe(
                '服务器（Node.js）环境，指 canal.staging.kuaishou.com 和 canal.corp.kuaishou.com，一般用不上',
              ),
          })
          .describe('大运河配置')
      },
    },
    enableBy: api.EnableBy.config,
  })

  api.onGenerateFiles(async () => {
    const canalConfig = getKmiCanalConfig()
    await fetchGlobalFrontModel()
    if (api.name === 'dev') {
      loogPollingGlobalFrontModel()
    }
    api.writeTmpFile({
      path: 'index.ts',
      content: `import { loadGlobalModule, LoadGlobalModuleOptions } from '@ad/canal-react-runtime'
import GlobalFrontModel from './${FILE_NAME_GLOBAL_FRONT_MODEL}'

/**
 * 加载全局模块，带类型
 * @param options 选项
 */
export function loadGlobalModuleWithType(options: Partial<LoadGlobalModuleOptions>): Promise<GlobalModuleWithType> {
  return loadGlobalModule({
    moduleId: ${JSON.stringify(canalConfig.globalModuleId)},
    ...options,
  })
}

/**
 * 全局模型，带类型
 */
export interface GlobalModuleWithType {
  /**
   * 全局（前端）模型
   */
  globalModel: GlobalFrontModel
}

`,
    })
  })

  /**
   * 获取大运河配置
   */
  function getKmiCanalConfig(): KmiCanalConfig {
    return api.config.canal
  }

  /**
   * 上次保存的全局前端模型内容
   */
  let lastSavedGlobalFrontModel: string | undefined

  /**
   * 获取全局前端模型
   */
  async function fetchGlobalFrontModel(): Promise<void> {
    const canalConfig = getKmiCanalConfig()
    let content = ANY_GLOBAL_FRONT_MODEL
    if (!canalConfig.skipServer) {
      const res = await serverApis.getModuleDetail(
        {
          id: canalConfig.globalModuleId,
          version: isNumber(canalConfig.globalModuleVersion)
            ? `${canalConfig.globalModuleVersion}`
            : undefined,
        },
        {
          env: canalConfig.nodeEnv,
        },
      )
      const globalSchema: E2EGlobalSchema = JSON.parse(res.data.content)
      content =
        globalSchema.globalFrontModel?.codeTS ||
        globalSchema.globalFrontModel?.codeES ||
        ANY_GLOBAL_FRONT_MODEL
    }
    if (content === lastSavedGlobalFrontModel) {
      return
    }
    api.writeTmpFile({
      path: `${FILE_NAME_GLOBAL_FRONT_MODEL}.ts`,
      content: (lastSavedGlobalFrontModel = content),
    })
  }

  /**
   * 轮询获取全局前端模型
   */
  async function loogPollingGlobalFrontModel(): Promise<void> {
    const canalConfig = getKmiCanalConfig()
    if (canalConfig.skipServer) {
      return
    }
    const interval = Math.max(canalConfig.loogPollingInterval ?? 30, 5) * 1000
    for (;;) {
      await sleep(interval)
      await fetchGlobalFrontModel()
    }
  }
}
