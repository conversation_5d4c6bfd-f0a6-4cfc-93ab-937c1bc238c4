{"name": "@ad/kmi-plugin-canal", "version": "2.6.3", "description": "大运河 Kmi 插件", "repository": {"type": "git", "url": "*************************:ks-ad/ad-fe/grandcanal/canal.git"}, "license": "MIT", "author": "AD", "sideEffects": false, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && rollup --config rollup.config.ts --configPlugin typescript", "start": "rollup --config rollup.config.ts --configPlugin typescript --watch", "test": "jest --passWithNoTests", "test:w": "jest --watch"}, "dependencies": {"@ad/canal-react-runtime": "workspace:^", "@ad/canal-shared": "workspace:^", "@ad/e2e-schema": "workspace:^", "@ad/e2e-schema-utils": "workspace:^", "@kmi/react": "^2.0.28", "@ks/kof": "^1.0.3", "@ks/kof-fetch": "^1.0.3", "@types/lodash": "^4.14.199", "lodash": "^4.17.21"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.4", "@types/jest": "^29.5.5", "jest": "^29.7.0", "rollup": "^3.29.3", "rollup-plugin-exclude-dependencies-from-bundle": "1.1.23", "ts-jest": "^29.1.1"}}