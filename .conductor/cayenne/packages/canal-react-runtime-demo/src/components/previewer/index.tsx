import {
  AutoMergedConfig,
  CanalReactRuntime,
  CanalReactRuntimeFC,
  CanalReactRuntimeFCBaseProps,
  FetchSchema,
  disableAssertWeblog,
  disableCaptureException,
} from '@ad/canal-react-runtime'
import { isFunction } from 'lodash'
import { FC, useEffect, useMemo, useRef, useState } from 'react'
import { currentDemo } from '../../app'
import * as demoUI from '../demo-ui'

disableCaptureException()
disableAssertWeblog()

/**
 * 预览器属性
 */
export interface PreviewerProps {
  className?: string
  /**
   * 脚本
   */
  script: string
}

/**
 * 预览器
 */
export const Previewer: FC<PreviewerProps> = ({ className, script }) => {
  return (
    <div className={className}>
      <PreviewerInner script={script} />
    </div>
  )
}

/**
 * 预览器（内部）属性
 */
export interface PreviewerInnerProps {
  /**
   * 脚本
   */
  script: string
}

/**
 * 预览器（内部）
 */
export const PreviewerInner: FC<PreviewerInnerProps> = ({ script }) => {
  const refCanalReactRuntime = useRef<CanalReactRuntime>()
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(window as any).refCanalReactRuntime = refCanalReactRuntime
  const [fetchSchema, setFetchSchema] = useState<FetchSchema | null>(null)
  useEffect(() => {
    try {
      const fn = eval(script)
      if (isFunction(fn)) {
        setFetchSchema(() => fn)
      }
    } catch (err) {
      console.error('script eval err', err)
    }
  }, [script])
  const config = useMemo(
    () =>
      fetchSchema
        ? ({
            fetchSchema,
          } as AutoMergedConfig)
        : undefined,
    [fetchSchema],
  )

  return config ? (
    <CanalReactRuntimeFC
      key={fetchSchema?.toString()}
      schemaId="abc"
      config={config}
      defaultComponentLib={demoUI}
      refCanalReactRuntime={refCanalReactRuntime}
      FallbackComponent={RuntimeFallbackComponent}
      {...currentDemo.props}
    />
  ) : null
}

/**
 * 运行时回退组件
 */
const RuntimeFallbackComponent: CanalReactRuntimeFCBaseProps['FallbackComponent'] =
  ({ error }) => {
    return <div>渲染出错: {error.message}</div>
  }
