import Editor from '@monaco-editor/react'
import { debounce } from 'lodash'
import { FC, useMemo, useState } from 'react'
import styled from 'styled-components'
import { Previewer } from '../previewer'

/**
 * 可预览的编辑器属性
 */
export interface PreviewableEditorProps {
  className?: string
  /**
   * 脚本
   */
  script: string
}

/**
 * 可预览的编辑器
 */
export const PreviewableEditor: FC<PreviewableEditorProps> = ({
  className,
  script,
}) => {
  const [editingJs, setEditingJs] = useState(script)
  const handleEditorChange = useMemo(
    () =>
      debounce((value: string = '') => {
        setEditingJs(value)
      }, 500),
    [],
  )

  return (
    <Container className={className}>
      <EditorContainer>
        <Editor
          value={editingJs}
          onChange={handleEditorChange}
          language="javascript"
        />
      </EditorContainer>
      <StyledPreviewer script={editingJs} />
    </Container>
  )
}

const Container = styled.div`
  display: flex;
  height: 100%;
`

const EditorContainer = styled.div`
  flex-grow: 1;
  height: 100%;
`

const StyledPreviewer = styled(Previewer)`
  width: 400px;
  height: 100%;
  border: 1px solid;
  overflow: auto;
`
