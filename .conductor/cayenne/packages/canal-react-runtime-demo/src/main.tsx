import '@m-ui/react/dist/@m-ui/react.css'
import React from 'react'
import ReactD<PERSON> from 'react-dom/client'
import { createGlobalStyle } from 'styled-components'
import { App } from './app'

const GlobalStyle = createGlobalStyle`
  body {
    margin: 0;
    padding: 0;
  }

  * {
    box-sizing: border-box;
  }
`

// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <GlobalStyle />
    <App />
  </React.StrictMode>,
)
