import {
  AutoLoading,
  CanalReactRuntimeFCBaseProps,
  setCanalCdnDt,
} from '@ad/canal-react-runtime'
import { INTERNAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import { FC, ReactNode, useCallback } from 'react'
import styled from 'styled-components'
import { PreviewableEditor } from './components'

export const App: FC = () => {
  const handleDemoBtnClick = useCallback((demo: Demo, idx: number) => {
    void demo
    window.location.href = `${
      window.location.href.split('?')[0]
    }?demo-idx=${idx}`
  }, [])

  return (
    <Container>
      <Header>
        <SourceLink
          href="https://npm.corp.kuaishou.com/-/web/detail/@ad/canal-react-runtime"
          target="_blank"
          rel="noreferrer"
        >
          NPM→
        </SourceLink>
        {DEMOS.map((demo, idx) => (
          <button
            key={demo.name}
            onClick={handleDemoBtnClick.bind(null, demo, idx)}
          >
            {demo.name}
          </button>
        ))}
        {currentDemo.description}
      </Header>
      <StyledPreviewableEditor script={currentDemo.script} />
    </Container>
  )
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
`

const Header = styled.div`
  /* height: 300px; */
`

const SourceLink = styled.a`
  float: right;
`

const StyledPreviewableEditor = styled(PreviewableEditor)`
  height: 0;
  flex-grow: 1;
`

/**
 * Demo
 */
interface Demo {
  /**
   * 名称
   */
  name: string
  /**
   * 描述
   */
  description?: ReactNode
  /**
   * 脚本
   */
  script: string
  /**
   * 运行时属性
   */
  props?: Partial<CanalReactRuntimeFCBaseProps>
}

/**
 * 所有 Demo
 */
const DEMOS: Demo[] = [
  {
    name: 'Hello World',
    script: `async () => {
  // 模拟后端生成端到端 Schema
  return {
    schemaVersion: '0.1.0',
    view: {
      type: 'View',
      id: 'v1',
      name: 'v1n',
      children: [
        {
          type: 'Text',
          id: 't1',
          name: 't1n',
          props: {
            content: 'Hello World!',
          },
        },
      ],
    },
  }
}
`,
  },
  {
    name: 'linkage 联动',
    script: `async (options) => {
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 200))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: 'View',
      id: 'v1',
      name: 'v1n',
      children: [
        {
          type: 'View',
          id: 'v2',
          name: 'v2n',
          children: [
            {
              type: 'Checkbox',
              id: 'c1',
              name: 'c1n',
            }
          ]
        },
        {
          type: 'Text',
          id: 't1',
          name: 't1n',
          props: {
            content: \`参数: \${JSON.stringify({
              moduleId: options.schemaId,
              ...options,
              schemaId: undefined,
            })}\`,
          },
        },
        {
          type: 'Checkbox',
          id: 'c2',
          name: 'c2n',
        }
      ],
    },
    linkage: {
      autoRefreshByComponent: ['c1']
    },
    data: {
      c2: !Boolean(options.params['c1'])
    }
  }
}
`,
  },
  {
    name: '提交跳转',
    script: `async (options) => {
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 200))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: 'View',
      id: 'v1',
      name: 'v1n',
      children: [
        {
          type: 'Checkbox',
          id: 'c1',
          name: 'c1',
        },
        {
          type: 'Checkbox',
          id: 'c2',
          name: 'c2',
        },
        {
          type: 'Button',
          id: 'b1',
          name: 'b1',
          props: {
            content: '提交',
            onClick: {
              type: 'refresh',
              refreshType: 'submit',
            },
          }
        },
      ],
    },
    linkage: {
      componentDataParams: {
        byRefreshType: {
          submit: ['c1', 'c2']
        }
      }
    },
    iife: options.refreshType === 'submit' ? {
      type: 'open-url',
      url: \`https://www.kuaishou.com?a=\${JSON.stringify(Object.entries(options.params))}\`
    } : undefined
  }
}
`,
  },
  {
    name: '子模块',
    script: `async (options) => {
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 200))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: 'View',
      id: 'v1',
      name: 'v1n',
      children: [
        {
          type: 'Text',
          id: 't1',
          name: 't1n',
          props: {
            content: options.schemaId + ': ',
          },
        },
        {
          type: 'Text',
          id: 't2',
          name: 't2n',
          props: {
            content: JSON.stringify({
              moduleId: options.schemaId,
              ...options,
              schemaId: undefined,
            }),
          },
        },
        {
          type: '@ad/canal-components::Input',
          id: 'i1',
          name: 'i1n',
        },
        ...(options.schemaId === 'abc' ? [
          {
            type: '@ad/canal-components::Submodule',
            id: 's1',
            name: 's1n',
            props: {
              schemaId: 123,
              params: options.params.i1 ? {
                type: 'static',
                value: {
                  a: 1,
                  i1: options.params.i1
                },
              } : {
                type: 'static',
                value: {
                  a: 3,
                },
              },
            },
          },
        ] : []),
      ],
    },
    linkage: {
      autoRefreshByComponent: ['i1'],
    }
  }
}
`,
  },
  {
    name: '最大嵌套层数',
    script: `async (options) => {
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 200))
  const count = options.params.count || 0
  return {
    schemaVersion: '0.1.0',
    view: {
      type: 'View',
      id: 'v1',
      name: 'v1n',
      children: [
        {
          type: 'Text',
          id: 't1',
          name: 't1n',
          props: {
            content: \`运行时嵌套层数: \${count}，默认不应该超过 9 \`,
          },
        },
        {
          type: 'Text',
          id: 't2',
          name: 't2n',
          props: {
            content: JSON.stringify({
              moduleId: options.schemaId,
              ...options,
              schemaId: undefined,
            }),
          },
        },
        {
          type: '${INTERNAL_COMPONENT_LIB_NAME}::Submodule',
          id: 's1',
          name: 's1n',
          props: {
            schemaId: 'abc',
            params: {
              type: 'static',
              value: {
                count: (count) + 1,
              },
            },
          },
        },
      ],
    },
  }
}
`,
  },
  {
    name: '模块加载中',
    script: `async (options) => {
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 500))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: '@ad/canal-components::Container',
      id: 'c1',
      name: 'c1n',
      children: [
        {
          type: '@ad/canal-components::Text',
          id: 't1',
          name: 't1n',
          props: {
            text: '111111',
          },
        },
        {
          type: 'Checkbox',
          id: 'cb1',
          name: 'cb1n',
        },
        {
          type: '@ad/canal-components::Container',
          id: 'c2',
          name: 'c2n',
          children: [
            {
              type: '@ad/canal-components::Text',
              id: 't2',
              name: 't2n',
              props: {
                text: '222222',
              },
            },
          ],
        },
      ],
    },
    linkage: {
      autoRefreshByComponent: ['cb1']
    },
  }
}
`,
    props: {
      Loading: AutoLoading,
    },
  },
  {
    name: '容器加载中',
    script: `async (options) => {
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 500))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: '@ad/canal-components::Container',
      id: 'c1',
      name: 'c1n',
      children: [
        {
          type: '@ad/canal-components::Text',
          id: 't1',
          name: 't1n',
          props: {
            text: '111111',
          },
        },
        {
          type: 'Checkbox',
          id: 'cb1',
          name: 'cb1n',
        },
        {
          type: '@ad/canal-components::Container',
          id: 'c2',
          name: 'c2n',
          props: {
            enableLoading: true
          },
          children: [
            {
              type: '@ad/canal-components::Text',
              id: 't2',
              name: 't2n',
              props: {
                text: '222222',
              },
            },
          ],
        },
      ],
    },
    linkage: {
      autoRefreshByComponent: ['cb1']
    },
  }
}
`,
    props: {
      Loading: AutoLoading,
    },
  },
  {
    name: '表单',
    script: `async (options) => {
  console.log('options', options)
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 500))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: '@ad/canal-components::Form',
      id: 'f1',
      name: 'f1n',
      props: {
        onFinish: {
          type: 'refresh',
          refreshType: 'submit',
          params: {
            a: 3,
          },
        },
      },
      children: [
        {
          type: '@ad/canal-components::FormItem',
          id: 'ft1',
          name: 'ft1n',
          props: {
            label: 'adf',
            jsonRules: [{
              type: 'required',
              errMsg: 'eeeeee mm',
            }],
          },
          children: [
            {
              type: '@ad/canal-components::Checkbox',
              id: 'cb1',
              name: 'cb1n',
              props: {
                text: '测试',
              },
            },
          ],
        },
        {
          type: '@ad/canal-components::FormItem',
          id: 'ft2',
          name: 'ft2n',
          props: {
            label: 'adf222',
            jsonRules: [
              {
                type: 'required',
                errMsg: '必填33',
              },
              {
                type: 'reg-exp',
                expr: '^[abc]*$',
                errMsg: 'rrrr 只能 abc',
              },
            ],
            errMsgs: options.params.cb1 === true ? [
              '上面勾选，这里就报错',
              '而且报两条',
            ] : [],
          },
          children: [
            {
              type: '@ad/canal-components::Input',
              id: 'i1',
              name: 'i1n',
            },
          ],
        },
        {
          type: '@ad/canal-components::FormItem',
          id: 'ft3',
          name: 'ft3n',
          props: {
            label: '3333',
            jsonRules: [{
              type: 'required',
              errMsg: 'fasdf3333',
            }],
          },
          children: [
            {
              type: '@ad/canal-components::Input',
              id: 'i2',
              name: 'i2n',
            },
          ],
        },
        {
          type: '@ad/canal-components::Button',
          id: 'b1',
          name: 'b1n',
          props: {
            text: '提交',
            htmlType: 'submit',
          },
        },
      ],
    },
    linkage: {
      autoRefreshByComponent: ['cb1'],
      componentDataParams: {
        byRefreshType: {
          submit: ['cb1', 'i1', 'i2'],
        },
      },
    },
  }
}
`,
  },
  {
    name: '条件渲染',
    script: `async (options) => {
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 200))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: 'View',
      id: 'v1',
      name: 'v1n',
      children: [
        {
          type: 'Text',
          id: 't1',
          name: 't1n',
          props: {
            content: '11111',
          },
          if: options.refreshType !== 'submit',
        },
        {
          type: 'Text',
          id: 't2',
          name: 't2n',
          props: {
            content: '22222',
          },
          if: options.refreshType === 'submit',
        },
        {
          type: 'Button',
          id: 'b1',
          name: 'b1',
          props: {
            content: '提交',
            onClick: {
              type: 'refresh',
              refreshType: 'submit',
            },
          },
        },
      ],
    },
  }
}
`,
  },
  {
    name: '插槽',
    script: `async (options) => {
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 200))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: 'View',
      id: 'v1',
      name: 'v1n',
      children: [
        {
          type: 'View',
          id: 'v2',
          name: 'v2n',
          children: [
            {
              type: 'Checkbox',
              id: 'c1',
              name: 'c1n',
            }
          ]
        },
        {
          type: 'Text',
          id: 't1',
          name: 't1n',
          props: {
            content: {
              type: 'component',
              value:
              {
                type: 'Text',
                id: 't2',
                name: 't2n',
                props: {
                  content: \`参数: \${JSON.stringify({
                    moduleId: options.schemaId,
                    ...options,
                    schemaId: undefined,
                  })}\`,
                },
              },
            }
          },
        },
        {
          type: 'Checkbox',
          id: 'c2',
          name: 'c2n',
        }
      ],
    },
    linkage: {
      autoRefreshByComponent: ['c1']
    },
    data: {
      c2: !Boolean(options.params['c1'])
    }
  }
}
    `,
  },
  {
    name: '副作用',
    script: `async (options) => {
  console.log('options', options)
  // 模拟后端生成端到端 Schema
  await new Promise(r => setTimeout(r, 1000))
  return {
    schemaVersion: '0.1.0',
    view: {
      type: '@ad/canal-components::Form',
      id: 'f1',
      name: 'f1n',
      props: {
        onFinish: {
          type: 'refresh',
          refreshType: 'submit',
          params: {
            a: 3,
          },
        },
      },
      children: [
        {
          type: '@ad/canal-components::FormItem',
          id: 'ft1',
          name: 'ft1n',
          props: {
            label: 'adf',
          },
          children: [
            {
              type: '@ad/canal-components::Checkbox',
              id: 'cb1',
              name: 'cb1n',
              props: {
                text: '禁用副作用',
              },
            },
          ],
        },
        {
          type: '@ad/canal-components::FormItem',
          id: 'ft2',
          name: 'ft2n',
          props: {
            label: 'adf222',
          },
          children: [
            {
              type: '@ad/canal-components::Input',
              id: 'i1',
              name: 'i1n',
              effect: options.params.cb1 ? undefined : {
                type: 'js',
                code: ((value, prevValue, ctx) => {
                  console.log('effect', { value, prevValue, ctx })
                  ctx.setData({
                    i2: value.split('').reverse().join(''),
                  })
                }).toString(),
                codeES: ''
              },
            },
          ],
        },
        {
          type: '@ad/canal-components::FormItem',
          id: 'ft3',
          name: 'ft3n',
          props: {
            label: '3333',
          },
          children: [
            {
              type: '@ad/canal-components::Input',
              id: 'i2',
              name: 'i2n',
            },
          ],
        },
      ],
    },
    linkage: {
      autoRefreshByComponent: ['cb1', 'i1', 'i2'],
      componentDataParams: {
        common: ['cb1', 'i2'],
      },
    },
    data:
      options.params.i1 ? {
        i2: options.params.i1 + options.params.i1,
      } : options.params.i2 ? {
        i1: options.params.i2 + options.params.i2,
      } : {},
  }
}
`,
  },
  {
    name: 'CDN 容灾',
    script: `async () => {
  // 模拟后端生成端到端 Schema
  return {
    schemaVersion: '0.1.0',
    componentCodes: {
      ['@ad/canal-biz-components::Test']: {
        code: {
          js: 'data:,exports.default = () => "https://js-ad.a.yximgs.com/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.02657d05.js"',
        },
        version: '5',
      },
    },
    view: {
      type: 'View',
      id: 'v1',
      name: 'v1n',
      children: [
        {
          type: 'Text',
          id: 't1',
          name: 't1n',
          props: {
            content: 'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js',
          },
        },
        {
          type: 'View',
          id: 'v2',
          name: 'v2n',
          children: [
            {
              type: '@ad/canal-biz-components::Test',
              id: 'test0',
              name: 'n-test0'
            }
          ]
        }
      ],
    },
  }
}
`,
  },
]

export const currentDemo = ((): Demo => {
  const params = new URLSearchParams(window.location.search)
  const idx = Number(params.get('demo-idx')) || 0
  return DEMOS[idx]
})()

setCanalCdnDt({
  replaceDomains: ['p1.adkwai.com', 'https://js-ad.a.yximgs.com/kos/nlav12572'],
  cdnPublicPath: 'http://a.b.c',
})
