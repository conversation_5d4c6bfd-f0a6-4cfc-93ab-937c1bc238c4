{"name": "@ad/canal-react-runtime-demo", "version": "2.6.3", "private": true, "type": "module", "scripts": {"build": "tsc && vite build", "deploy:jinx": "cd ./dist && tar -zcf output.tgz ./* && curl 'https://jinx.corp.kuaishou.com/api/output' -F \"file=@output.tgz\" -F projectName=\"canal-runtime-demo\" -F branch=\"online\" -F email=\"`git config user.email`\" -F commit=\"demo 部署\"", "start": "vite"}, "dependencies": {"@ad/canal-react-components": "workspace:^", "@ad/canal-react-runtime": "workspace:^", "@ad/canal-shared": "workspace:^", "@ad/e2e-material-schema": "workspace:^", "@ad/e2e-material-schema-utils": "workspace:^", "@ad/e2e-schema": "workspace:^", "@ad/e2e-schema-utils": "workspace:^", "@m-ui/react": "^1.12.2", "@monaco-editor/react": "^4.5.2", "@types/lodash": "^4.14.199", "@types/react": "^18.2.23", "@types/react-dom": "^18.2.8", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^6.0.8"}, "devDependencies": {"@vitejs/plugin-react": "^4.1.0", "vite": "^4.4.5"}}