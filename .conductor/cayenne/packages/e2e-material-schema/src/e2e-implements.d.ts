import { CMSImplements } from '@kael/material-schema'
import { E2ECMSProp } from './e2e-prop'

/**
 * 端到端实现的功能
 */
export interface E2ECMSImplements extends CMSImplements {
  // 兼容 @kael/material-utils
  tip?: boolean
  link?: boolean
  reconfirm?: boolean
}

/**
 * 端到端实现的属性
 */
export type E2EImplementsProps = {
  [key in keyof E2ECMSImplements]: E2ECMSImplements[key] extends
    | boolean
    | undefined
    ? E2ECMSProp[]
    : never
}
