import { CMSFieldProp, CMSGroupProp, CMSSetter } from '@kael/material-schema'

/**
 * 端到端属性
 */
export type E2ECMSProp<S = CMSSetter> = E2ECMSGroupProp<S> | E2ECMSFieldProp<S>

/**
 * 端到端属性组
 */
export interface E2ECMSGroupProp<S = CMSSetter> extends CMSGroupProp<S> {
  /**
   * 组内属性
   */
  items: E2ECMSProp<S>[]
}

/**
 * 端到端字段属性
 */
export interface E2ECMSFieldProp<S = CMSSetter> extends CMSFieldProp<S> {
  /**
   * 升级
   */
  upgrade?: E2EPropUpgrade
  /**
   * 开启像素转视口宽度和像素转根元素字体大小宽度
   */
  px2vw?: boolean
  /**
   * alias px2vw
   */
  px2rem?: boolean
  /**
   * TS 类型声明
   */
  declaration?: string
}

/**
 * 端到端属性升级
 */
export type E2EPropUpgrade = E2EPropUpgradeOverwrite

/**
 * 端到端属性升级：覆盖
 */
export interface E2EPropUpgradeOverwrite {
  /**
   * 类型
   */
  type: 'overwrite'
  /**
   * （升级前）版本小于等于改值时，会使用新的默认值覆盖
   */
  versionLTE: string
}
