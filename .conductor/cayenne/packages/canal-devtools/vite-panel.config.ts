import { resolve } from 'path'
import { defineConfig, mergeConfig } from 'vite'
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'
import { replaceCodePlugin } from 'vite-plugin-replace'
import viteConfig from './vite.config'

export default mergeConfig(
  viteConfig,
  defineConfig({
    plugins: [
      cssInjectedByJsPlugin(),
      replaceCodePlugin({
        replacements: [
          {
            from: `document.createElement('script')`,
            to: `(() => { throw new Error('create script is not supported') })()`,
          },
        ],
      }),
    ],
    build: {
      minify: true,
      lib: {
        entry: resolve(__dirname, 'src/panel/index.ts'),
        formats: ['es'],
        fileName: 'panel',
      },
    },
  }),
)
