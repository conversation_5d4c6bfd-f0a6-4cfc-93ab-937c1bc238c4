import { defineConfig } from 'vite'
import checker from 'vite-plugin-checker'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    checker({
      typescript: true,
      eslint: {
        lintCommand: 'eslint "./src/**/*.{ts,tsx}"',
        useFlatConfig: true,
      },
    }),
  ],
  build: {
    target: 'es2022',
  },
  esbuild: {
    mangleProps: /^_[^_]*$/,
  },
  define: Object.fromEntries(
    ['NODE_ENV', 'CANAL_ORIGIN'].map((key) => [
      `process.env.${key}`,
      process.env[key] === undefined
        ? undefined
        : JSON.stringify(process.env[key]),
    ]),
  ),
})
