import { resolve } from 'path'
import { defineConfig, mergeConfig } from 'vite'
import viteConfig from './vite.config'

export default mergeConfig(
  viteConfig,
  defineConfig({
    build: {
      lib: {
        entry: resolve(__dirname, 'src/content-isolated/index.ts'),
        formats: ['iife'],
        name: '__canalDevtoolsContentIsolated',
        fileName: 'content-isolated',
      },
    },
  }),
)
