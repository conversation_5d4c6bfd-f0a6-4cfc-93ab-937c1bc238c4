{"name": "@ad/canal-devtools", "version": "2.6.3", "private": true, "description": "大运河开发者工具", "type": "module", "scripts": {"build": "rm -rf ./dist && rm -f ./dist.zip && cp -r ./src/static ./dist && pnpm run \"/build:.*/\" && pnpm run zip", "build:content-isolated": "vite build --emptyOutDir=false -c vite-content-isolated.config.ts", "build:content-main": "vite build --emptyOutDir=false -c vite-content-main.config.ts", "build:devtools": "vite build --emptyOutDir=false -c vite-devtools.config.ts", "build:panel": "vite build --emptyOutDir=false -c vite-panel.config.ts", "start": "pnpm run \"/start:.*/\"", "start:content-isolated": "NODE_ENV=development vite build -w --emptyOutDir=false  -c vite-content-isolated.config.ts", "start:content-main": "NODE_ENV=development vite build -w --emptyOutDir=false  -c vite-content-main.config.ts", "start:devtools": "NODE_ENV=development vite build -w --emptyOutDir=false  -c vite-devtools.config.ts", "start:panel": "NODE_ENV=development vite build -w --emptyOutDir=false  -c vite-panel.config.ts", "zip": "zip -r dist.zip ./dist"}, "dependencies": {"@ad/canal-react-runtime": "workspace:^", "@ad/canal-shared": "workspace:^", "@ad/canal-shared-ui": "workspace:^", "@ad/e2e-schema": "workspace:^", "@ad/e2e-schema-utils": "workspace:^", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@kael/runtime": "1.0.0-rc.190", "@kael/schema-utils": "1.0.0-rc.190", "@kael/shared": "1.0.0-rc.190", "@ks/kof": "^1.0.3", "@ks/kof-fetch": "^1.0.3", "@monaco-editor/react": "^4.5.2", "@mui/icons-material": "^6.4.3", "@mui/material": "^6.4.3", "@mui/x-data-grid": "^7.27.0", "@mui/x-tree-view": "^7.25.0", "@remote-ui/rpc": "^1.4.5", "@types/lodash": "^4.14.199", "@types/react": "^18.2.23", "@types/react-dom": "^18.2.8", "chrome-types": "^0.1.324", "dayjs": "^1.11.10", "json-edit-react": "^1.22.6", "lodash": "^4.17.21", "mobx": "^6.10.2", "mobx-react-lite": "^4.0.5", "monaco-editor": "^0.50.0", "nanoid": "^3.3.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-drag-sizing": "0.2.1", "react-use": "^17.4.0", "safe-stable-stringify": "^2.5.0", "styled-components": "^6.0.8", "vite-plugin-replace": "^0.1.1"}, "devDependencies": {"@vitejs/plugin-react": "^4.1.0", "vite": "^4.4.5", "vite-plugin-checker": "^0.7.2", "vite-plugin-css-injected-by-js": "^3.5.2"}}