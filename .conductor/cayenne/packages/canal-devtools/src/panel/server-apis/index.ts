import { FetchSchemaEnv } from '@ad/canal-react-runtime'
import { PLATFORM_ORIGIN, PLATFORM_ORIGIN_STAGING } from '@ad/canal-shared'
import { Kof, Middleware } from '@ks/kof'
import { KofFetchOptionsExt, kofFetch } from '@ks/kof-fetch'
import {
  GetAllDataSourcesReq,
  GetAllDataSourcesRes,
  GetModuleDeployReq,
  GetModuleDeployRes,
  GetModuleDetailReq,
  GetModuleDetailRes,
} from './types'

/**
 * 通过环境获取源
 * @param env 环境
 */
function getOriginByEnv(env?: FetchSchemaEnv): string {
  return env === 'staging' ? PLATFORM_ORIGIN_STAGING : PLATFORM_ORIGIN
}

/**
 * 通过环境修改 URL
 */
const urlByEnv: Middleware<{ env: FetchSchemaEnv } & KofFetchOptionsExt> = (
  ctx,
  next,
) => {
  const { env } = ctx.options
  ctx.options.urlBase = getOriginByEnv(env)
  return next()
}

/**
 * Kof 应用
 */
const app = new Kof().use(urlByEnv).use(kofFetch)

/**
 * 服务器接口，免登录
 */
export const serverApis = app.apis({
  /**
   * 获取模块详情
   */
  getModuleDetail: app.options<GetModuleDetailReq, GetModuleDetailRes>({
    urlPath: '/rest/canal/module/detail',
  }),
  /**
   * 获取模块部署记录
   */
  getModuleDeploy: app.options<GetModuleDeployReq, GetModuleDeployRes>({
    urlPath: '/rest/canal/deploy/module',
  }),
  /**
   * 获取所有数据源
   */
  getAllDataSources: app.options<GetAllDataSourcesReq, GetAllDataSourcesRes>({
    urlPath: '/rest/canal/data-source-config/all',
  }),
})
