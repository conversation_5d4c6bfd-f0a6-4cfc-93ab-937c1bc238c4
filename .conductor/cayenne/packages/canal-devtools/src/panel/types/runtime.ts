import { CanalReactRuntime } from '@ad/canal-react-runtime'
import { RuntimeTreeNode } from '../../types/content-panel-apis'

/**
 * 运行时树节点详情
 */
export interface RuntimeTreeNodeDetail {
  /**
   * 父运行时 ID
   */
  parentRuntimeId: string | null
  /**
   * 父模块 ID
   */
  parentModuleId: string | null
  /**
   * 节点
   */
  node: RuntimeTreeNode
}

/**
 * 大运河 React 组件
 */
export type CanalReactComponent = CanalReactRuntime['container']['view']

/**
 * 大运河 React 组件实例树节点
 */
export interface CanalReactComponentInstanceTreeNode {
  /**
   * 实例
   */
  instance: CanalReactComponent
  /**
   * 子节点
   */
  children: CanalReactComponentInstanceTreeNode[]
}
