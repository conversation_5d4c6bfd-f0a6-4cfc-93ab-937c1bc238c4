// chrome.devtools.network.onRequestFinished.addListener 通过 json2ts 的结果，chrome-types 不太完善

export interface HarLog extends chrome.devtools.network.Request {
  cache: unknown
  connection: string
  pageref: string
  request: Request
  response: Response
  serverIPAddress: string
  /**
   * 开始时间，形如：2025-02-18T08:32:43.232Z
   */
  startedDateTime: string
  /**
   * 耗时
   */
  time: number
}

export interface Request {
  method: string
  url: string
  httpVersion: string
  headers: Header[]
  queryString: Header[]
  cookies: Cookie[]
  headersSize: number
  bodySize: number
  postData?: PostData
}

export interface Header {
  name: string
  value: string
}

export interface Cookie {
  name: string
  value: string
  path: string
  domain: string
  expires: string
  httpOnly: boolean
  secure: boolean
  sameSite?: string
}

export interface PostData {
  mimeType: string
  text?: string
}

export interface Response {
  status: number
  statusText: string
  httpVersion: string
  headers: Header[]
  cookies: Cookie[]
  content: Content
  redirectURL: string
  headersSize: number
  bodySize: number
}

export interface Content {
  size: number
  mimeType: string
}
