import { useLatestFn } from '@ad/canal-shared-ui'
import { Alert } from '@mui/material'
import { observer } from 'mobx-react-lite'
import React, { FC } from 'react'
import styled, { createGlobalStyle } from 'styled-components'
import { mainStore } from '../stores/main-store'
import { ModuleSection } from './components/module-section'
import { TreeSelect } from './components/tree-select'

// antd 和 m-ui 在 panel 页面下，Select 有问题，没办法收起，所以这里先用 Material UI

/**
 * 应用
 */
export const App: FC = observer(() => {
  const { runtimeTree, selectedRuntimeNode, runtimeTreeItems } = mainStore
  const handleTreeSelectChange = useLatestFn((id: string) => {
    mainStore.selectRuntime(id)
  })
  return (
    <>
      <GlobalStyle />
      <Container>
        {runtimeTree.length ? (
          <>
            <TreeSelect
              items={runtimeTreeItems}
              label="模块"
              value={selectedRuntimeNode?.runtimeId}
              onChange={handleTreeSelectChange}
            />
            {selectedRuntimeNode && <StyledModuleSection />}
          </>
        ) : (
          <Alert severity="info">页面里没有大运河模块</Alert>
        )}
      </Container>
    </>
  )
})

const GlobalStyle = createGlobalStyle`
  body {
    margin: 0;
    padding: 0;
  }

  * {
    box-sizing: border-box;
  }
`

const Container = styled.div`
  height: 100vh;
  padding-top: 8px;
  display: flex;
  flex-direction: column;
`

const StyledModuleSection = styled(ModuleSection)`
  height: 0;
  flex: auto;
`
