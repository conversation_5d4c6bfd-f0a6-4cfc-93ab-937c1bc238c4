import { PLATFORM_ORIGIN } from '@ad/canal-shared'
import { StyleProps, useLatestFn } from '@ad/canal-shared-ui'
import { Button, Tab, Tabs, Tooltip } from '@mui/material'
import { observer } from 'mobx-react-lite'
import React, { FC, useState } from 'react'
import styled from 'styled-components'
import { mainStore } from '../../stores/main-store'
import { ModuleBasics } from './module-basics'
import { ModuleComponentTree } from './module-component-tree'
import { ModuleRefreshRecords } from './module-refresh-records'

/**
 * 模块部分属性
 */
export interface ModuleSectionProps extends StyleProps {}

/**
 * 模块部分
 */
export const ModuleSection: FC<ModuleSectionProps> = observer((props) => {
  const { selectedRuntimeNode } = mainStore
  const [tabsValue, setTabsValue] = useState(0)
  const handleTabsChange = useLatestFn(
    (ev: React.SyntheticEvent, newValue: number) => {
      void ev
      setTabsValue(newValue)
    },
  )
  const handleTabsExtraClick = useLatestFn(() => {
    open(
      `${PLATFORM_ORIGIN}/open-designer?moduleId=${selectedRuntimeNode?.moduleId}`,
    )
  })
  return (
    <Container {...props}>
      <div className="tabs-wrapper">
        <Tabs value={tabsValue} onChange={handleTabsChange}>
          <Tab label="基础" />
          <Tab label="组件树" />
          <Tab label="刷新记录" />
        </Tabs>
        <Tooltip title="最新的版本">
          <Button
            className="tabs-extra"
            variant="text"
            onClick={handleTabsExtraClick}
          >
            打开设计器
          </Button>
        </Tooltip>
      </div>
      <div className="module-section-content">
        {tabsValue === 0 && <ModuleBasics />}
        {tabsValue === 1 && <ModuleComponentTree />}
        {tabsValue === 2 && <ModuleRefreshRecords />}
      </div>
    </Container>
  )
})

const Container = styled.div`
  display: flex;
  flex-direction: column;

  .tabs-wrapper {
    display: flex;

    .MuiTabs-root {
      flex: auto;
    }

    .tabs-extra {
    }
  }

  .module-section-content {
    height: 0;
    flex: auto;
    overflow-y: auto;
  }
`
