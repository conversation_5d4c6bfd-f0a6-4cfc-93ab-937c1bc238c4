import React, { FC, memo } from 'react'
import styled from 'styled-components'
import { formatDateTime } from '../../../utils/time'
import { ModuleRequest } from '../../types/request'
import { DetailItem } from './detail-item'

/**
 * 刷新记录基础属性
 */
export interface RrBasicsProps {
  /**
   * 模块请求
   */
  mr: ModuleRequest
}

/**
 * 刷新记录基础
 */
export const RrBasics: FC<RrBasicsProps> = memo(({ mr }) => {
  return (
    <Container>
      <DetailItem label="类型" value={mr.req.refreshType} />
      <DetailItem label="时间" value={formatDateTime(mr.startTime)} />
      <DetailItem label="状态码" value={mr.res.result} />
      <DetailItem label="错误消息" value={mr.res.message} />
      <DetailItem label="整体耗时" value={`${mr.time | 0} ms`} />
      <DetailItem
        label="内部渲染耗时"
        value={mr.res.data && `${mr.res.data.renderInnerTimeCost} ms`}
      />
    </Container>
  )
})

const Container = styled.div`
  padding-left: 4px;
`
