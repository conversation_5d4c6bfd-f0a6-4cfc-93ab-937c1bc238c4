import Editor from '@monaco-editor/react'
import { Divider, Typography } from '@mui/material'
import React, { FC, Fragment, memo } from 'react'
import styled from 'styled-components'
import { stringifyRuntimeJson } from '../../../utils/json'
import { ModuleRequest } from '../../types/request'
import { DetailItem } from './detail-item'
import { MonacoJsonEditor } from './monaco-json-editor'

/**
 * 刷新记录业务请求属性
 */
export interface RrBizReqsProps {
  /**
   * 模块请求
   */
  mr: ModuleRequest
}

/**
 * 刷新记录业务请求
 */
export const RrBizReqs: FC<RrBizReqsProps> = memo(({ mr }) => {
  return (
    <div>
      {mr.res.data?.httpDataList.map((bizHttp, index) => {
        return (
          <Fragment key={`${bizHttp.request.url} ${index}`}>
            {!!index && <ItemDivider />}
            <Item>
              <Typography className="item-title">
                {bizHttp.request.url}
              </Typography>
              <DetailItem
                label="curl"
                value={
                  <Editor
                    language="bash"
                    value={bizHttp.curl}
                    height={200}
                    options={{
                      tabSize: 2,
                      wordWrap: 'on',
                      readOnly: true,
                    }}
                  />
                }
                collapsed={false}
              />
              <DetailItem
                label="请求"
                value={
                  <MonacoJsonEditor
                    value={stringifyRuntimeJson(bizHttp.request)}
                    readOnly
                    height={300}
                  />
                }
                collapsed={false}
              />
              <DetailItem
                label="响应"
                value={
                  <MonacoJsonEditor
                    value={stringifyRuntimeJson(bizHttp.response)}
                    readOnly
                    height={300}
                  />
                }
                collapsed={false}
              />
            </Item>
          </Fragment>
        )
      })}
    </div>
  )
})

const ItemDivider = styled(Divider)`
  &&& {
    margin: 16px 0;
  }
`

const Item = styled.div`
  padding-left: 4px;

  .item-title {
    font-weight: bold;
    word-break: break-all;
  }
`
