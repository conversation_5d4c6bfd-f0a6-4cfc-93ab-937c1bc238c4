import { useLatestFn, ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Box, Popover, TextField } from '@mui/material'
import { RichTreeView } from '@mui/x-tree-view/RichTreeView'
import React, { FC, memo, useMemo, useState } from 'react'
import styled from 'styled-components'

/**
 * 树条目
 */
export interface TreeItem {
  /**
   * ID
   */
  id: string
  /**
   * 标签
   */
  label: string
  /**
   * 子条目
   */
  children?: TreeItem[]
}

/**
 * 生成所有树条目
 * @param items 树条目
 */
export function* genAllTreeItems(items: TreeItem[]): Generator<TreeItem> {
  for (const item of items) {
    yield item
    if (item.children) {
      yield* genAllTreeItems(item.children)
    }
  }
}

/**
 * 树选择属性
 */
export interface TreeSelectProps extends ValueOnChangeProps<string> {
  /**
   * 标签
   */
  label: string
  /**
   * 条目
   */
  items: TreeItem[]
}

/**
 * 树选择
 */
export const TreeSelect: FC<TreeSelectProps> = memo(
  ({ label, items, value, onChange }: TreeSelectProps) => {
    const allIds = useMemo(
      () => Array.from(genAllTreeItems(items), (item) => item.id),
      [items],
    )
    const [expandedItems, setExpandedItems] = useState(allIds)
    const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null)
    const currentLabel = useMemo(() => {
      let ret = ''
      for (const item of genAllTreeItems(items)) {
        if (item.id === value) {
          ret = item.label
          break
        }
      }
      return ret
    }, [items, value])
    const handleTextFieldClick = useLatestFn(
      (ev: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        setAnchorEl(ev.currentTarget)
      },
    )
    const handlePopoverClose = useLatestFn(() => {
      setAnchorEl(null)
    })
    const handleTreeItemClick = useLatestFn(
      (ev: React.MouseEvent<Element, MouseEvent>, itemId) => {
        const target = ev.target
        let text = ''
        if (target instanceof HTMLElement) {
          text = target.innerText
        }
        // console.log('handleTreeItemClick', { ev, target, text, itemId })
        if (!text || !itemId) return
        onChange?.(itemId)
        setAnchorEl(null)
      },
    )
    const handleExpandedItemsChange = useLatestFn(
      (ev: React.SyntheticEvent, newItems: string[]) => {
        void ev
        setExpandedItems(newItems)
      },
    )
    return (
      <Box>
        <TextField
          label={label}
          fullWidth
          slotProps={{
            input: {
              readOnly: true,
            },
          }}
          value={currentLabel}
          onClick={handleTextFieldClick}
        />
        <StyledPopover
          open={!!anchorEl}
          anchorEl={anchorEl}
          onClose={handlePopoverClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
        >
          <RichTreeView
            items={items}
            selectedItems={value || null}
            onItemClick={handleTreeItemClick}
            expandedItems={expandedItems}
            onExpandedItemsChange={handleExpandedItemsChange}
            expansionTrigger="iconContainer"
          />
        </StyledPopover>
      </Box>
    )
  },
)

const StyledPopover = styled(Popover)`
  .MuiPaper-root {
    width: 100%;
    max-height: 500px;
    overflow-y: scroll;
  }
`
