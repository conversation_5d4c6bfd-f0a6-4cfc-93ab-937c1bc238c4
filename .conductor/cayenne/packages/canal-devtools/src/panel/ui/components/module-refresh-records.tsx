import { useLatestFn } from '@ad/canal-shared-ui'
import {
  <PERSON><PERSON>,
  Divider,
  List,
  ListItemButton,
  ListItemText,
  ListSubheader,
  Tab,
  Tabs,
} from '@mui/material'
import { observer } from 'mobx-react-lite'
import React, { FC, Fragment, useEffect, useState } from 'react'
import styled from 'styled-components'
import { formatTime } from '../../../utils/time'
import { mainStore } from '../../stores/main-store'
import { ModuleRequest } from '../../types/request'
import { RrBasics } from './rr-basics'
import { RrBizReqs } from './rr-biz-reqs'
import { RrLogs } from './rr-logs'
import { RrReqBody } from './rr-req-body'
import { RrResBody } from './rr-res-body'

/**
 * 模块刷新记录
 */
export const ModuleRefreshRecords: FC = observer(() => {
  const { selectedModuleRequests } = mainStore
  const [selectedMr, setSelectedMr] = useState<ModuleRequest | null>(null)
  const [tabsValue, setTabsValue] = useState(0)
  const handleTabsChange = useLatestFn(
    (ev: React.SyntheticEvent, newValue: number) => {
      void ev
      setTabsValue(newValue)
    },
  )
  const handleListItemClick = useLatestFn((mr: ModuleRequest) => {
    setSelectedMr(mr)
  })
  useEffect(() => {
    if (selectedMr && !selectedModuleRequests.find((mr) => mr === selectedMr)) {
      setSelectedMr(null)
    }
  }, [selectedModuleRequests, selectedMr])
  return (
    <Container>
      <List subheader={<ListSubheader>刷新类型</ListSubheader>}>
        {selectedModuleRequests.map((mr, index) => (
          <Fragment key={`${mr.runtimeId} ${mr.req.version}`}>
            {!!index && <Divider />}
            <ListItemButton
              selected={selectedMr === mr}
              onClick={handleListItemClick.bind(null, mr)}
            >
              <ListItemText>
                {mr.req.refreshType}
                <br />
                {formatTime(mr.startTime)}
                <br />
                {mr.time | 0} ms
              </ListItemText>
            </ListItemButton>
          </Fragment>
        ))}
      </List>
      <div className="rr-container">
        {selectedMr ? (
          <div className="rr-detail">
            <Tabs value={tabsValue} onChange={handleTabsChange}>
              <Tab label="基础" />
              <Tab label="日志" />
              <Tab label="请求体" />
              <Tab label="响应体" />
              <Tab label="业务请求" />
            </Tabs>
            <div className="rr-content">
              {tabsValue === 0 && <RrBasics mr={selectedMr} />}
              {tabsValue === 1 && <RrLogs mr={selectedMr} />}
              {tabsValue === 2 && <RrReqBody mr={selectedMr} />}
              {tabsValue === 3 && <RrResBody mr={selectedMr} />}
              {tabsValue === 4 && <RrBizReqs mr={selectedMr} />}
            </div>
          </div>
        ) : (
          <Alert severity="info">请选择刷新记录查看详情</Alert>
        )}
      </div>
    </Container>
  )
})

const Container = styled.div`
  height: 100%;
  display: flex;

  > .MuiList-root {
    height: 100%;
    width: 150px;
    position: relative;
    overflow-y: auto;

    > ul {
      padding: 0;
    }
  }

  .rr-container {
    height: 100%;
    width: 0;
    flex: auto;
  }

  .rr-detail {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .rr-content {
    height: 0;
    flex: auto;
    overflow-y: auto;
  }
`
