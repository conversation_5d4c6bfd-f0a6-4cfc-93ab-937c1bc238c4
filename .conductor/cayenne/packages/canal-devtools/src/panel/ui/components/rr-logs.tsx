import { Link } from '@mui/material'
import React, { FC, memo } from 'react'
import styled from 'styled-components'
import { stringifyRuntimeJson } from '../../../utils/json'
import { ModuleRequest } from '../../types/request'
import { DetailItem } from './detail-item'
import { MonacoJsonEditor } from './monaco-json-editor'

/**
 * 刷新记录日志属性
 */
export interface RrLogsProps {
  /**
   * 模块请求
   */
  mr: ModuleRequest
}

/**
 * 刷新记录日志
 */
export const RrLogs: FC<RrLogsProps> = memo(({ mr }) => {
  return (
    <Container>
      <DetailItem label="Ktrace" value={mr.res.ktrace} />
      {mr.res.data && (
        <>
          <DetailItem
            label="Trace url"
            value={
              <LinkEllipsis
                href={mr.res.data.traceUrl}
                rel="noopener"
                target="_blank"
              >
                {mr.res.data.traceUrl}
              </LinkEllipsis>
            }
          />
          <DetailItem
            label="表达式日志"
            value={
              <MonacoJsonEditor
                value={stringifyRuntimeJson(mr.res.data.logs)}
                readOnly
                height={200}
              />
            }
            collapsed={false}
          />
          <DetailItem
            label="接口参数表达式报错"
            value={
              <MonacoJsonEditor
                value={stringifyRuntimeJson(mr.res.data.schemaError.argsError)}
                readOnly
                height={200}
              />
            }
            collapsed={false}
          />
          <DetailItem
            label="渲染组件属性表达式报错"
            value={
              <MonacoJsonEditor
                value={stringifyRuntimeJson(
                  mr.res.data.schemaError.renderError,
                )}
                readOnly
                height={200}
              />
            }
            collapsed={false}
          />
          <DetailItem
            label="接口报错"
            value={
              <MonacoJsonEditor
                value={stringifyRuntimeJson(mr.res.data.schemaError.apiError)}
                readOnly
                height={200}
              />
            }
            collapsed={false}
          />
        </>
      )}
    </Container>
  )
})

const Container = styled.div`
  padding-left: 4px;
`

const LinkEllipsis = styled(Link)`
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`
