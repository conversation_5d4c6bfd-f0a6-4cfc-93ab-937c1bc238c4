import { observer } from 'mobx-react-lite'
import React, { FC } from 'react'
import { mainStore } from '../../stores/main-store'
import { DetailItem } from './detail-item'
import { MonacoJsonEditor } from './monaco-json-editor'

/**
 * 模块基础
 */
export const ModuleBasics: FC = observer(() => {
  const {
    selectedModuleMeta,
    selectedRuntimeNode,
    selectedRuntimeNodeDetail,
    selectedRuntimeState,
  } = mainStore
  // console.log('ModuleBasics render', selectedRuntimeState, Date.now())
  if (!selectedRuntimeState) {
    // JsonEditor 有个问题，短时间内（比如几十毫秒）刷新，会导致更新不上
    return null
  }
  return (
    <div>
      <DetailItem label="模块名称" value={selectedModuleMeta?.name} />
      <DetailItem label="模块 ID" value={selectedRuntimeNode?.moduleId} />
      <DetailItem
        label="父模块 ID"
        value={selectedRuntimeNodeDetail?.parentModuleId || 'null'}
      />
      <DetailItem label="环境" value={selectedRuntimeNode?.env} />
      <DetailItem
        label="参数"
        value={
          <MonacoJsonEditor
            value={selectedRuntimeState?.params}
            readOnly
            height={200}
          />
        }
        collapsed={false}
      />
      <DetailItem
        label="初始数据"
        value={
          <MonacoJsonEditor
            value={selectedRuntimeState?.initData}
            readOnly
            height={200}
          />
        }
        collapsed={false}
      />
      <DetailItem
        label="实时数据"
        value={
          <MonacoJsonEditor
            value={selectedRuntimeState?.data}
            readOnly
            height={300}
          />
        }
        collapsed={false}
      />
    </div>
  )
})
