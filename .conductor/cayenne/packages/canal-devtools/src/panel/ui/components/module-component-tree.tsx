import { useLatestFn } from '@ad/canal-shared-ui'
import { BugReport } from '@mui/icons-material'
import { Alert, IconButton, Snackbar, Tooltip } from '@mui/material'
import { RichTreeView } from '@mui/x-tree-view'
import { JsonEditor } from 'json-edit-react'
import { observer } from 'mobx-react-lite'
import React, { FC, useMemo, useState } from 'react'
import { useUpdateEffect } from 'react-use'
import styled from 'styled-components'
import { contentForPanelRpcApis } from '../../bridge'
import { mainStore } from '../../stores/main-store'
import { Resize } from './resize'
import { genAllTreeItems } from './tree-select'

/**
 * 模块组件树
 */
export const ModuleComponentTree: FC = observer(() => {
  const { componentTreeItems, selectedComponentId, selectedComponentProps } =
    mainStore
  const allComponentIds = useMemo(
    () => Array.from(genAllTreeItems(componentTreeItems), (item) => item.id),
    [componentTreeItems],
  )
  const [width, setWidth] = useState(innerWidth >> 1)
  const [msg, setMsg] = useState('')
  useUpdateEffect(() => {
    setMsg('')
  }, [selectedComponentProps])
  const handleSelectedComponentIdChange = useLatestFn(
    (ev: unknown, id: string | null) => {
      void ev
      mainStore.selectComponent(id)
    },
  )
  const handleSaveBtnClick = useLatestFn(async () => {
    const { selectedRuntimeId: runtimeId, selectedComponentId: componentId } =
      mainStore
    if (!runtimeId || !componentId) return
    const key = await contentForPanelRpcApis.savePropsAsGlobalVariable(
      runtimeId,
      componentId,
    )
    if (!key) return
    setMsg(`临时变量: ${key}`)
  })
  const handleMsgClose = useLatestFn(() => setMsg(''))
  return (
    <Container>
      <LeftContainer
        border="right"
        width={width}
        minWidth="30%"
        maxWidth="70%"
        onWidthChangeEnd={setWidth}
      >
        <div className="left-container-content">
          {!!allComponentIds.length && (
            <RichTreeView
              defaultExpandedItems={allComponentIds}
              items={componentTreeItems}
              expansionTrigger="iconContainer"
              selectedItems={selectedComponentId}
              onSelectedItemsChange={handleSelectedComponentIdChange}
            />
          )}
        </div>
      </LeftContainer>
      <RightContainer>
        {selectedComponentProps ? (
          <>
            <Tooltip title="保存为临时变量">
              <IconButton onClick={handleSaveBtnClick}>
                <BugReport />
              </IconButton>
            </Tooltip>
            <Snackbar
              anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
              open={!!msg}
              message={msg}
              onClose={handleMsgClose}
            />
            <JsonEditor
              data={selectedComponentProps}
              restrictEdit
              restrictDelete
              restrictAdd
              restrictDrag
              enableClipboard={false}
            />
          </>
        ) : (
          <Alert severity="info">请选择组件查看属性</Alert>
        )}
      </RightContainer>
    </Container>
  )
})

const Container = styled.div`
  height: 100%;
  display: flex;
`

const LeftContainer = styled(Resize)`
  height: 100%;
  border-right: 1px solid rgba(5, 5, 5, 0.06);
  flex: none;

  .left-container-content {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
`

const RightContainer = styled.div`
  height: 100%;
  flex: auto;
  overflow: auto;
`
