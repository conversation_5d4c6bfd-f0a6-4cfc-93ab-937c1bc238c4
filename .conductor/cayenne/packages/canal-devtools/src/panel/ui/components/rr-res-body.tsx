import React, { FC, memo } from 'react'
import { stringifyRuntimeJson } from '../../../utils/json'
import { ModuleRequest } from '../../types/request'
import { MonacoJsonEditor } from './monaco-json-editor'

/**
 * 刷新记录响应体属性
 */
export interface RrResBodyProps {
  /**
   * 模块请求
   */
  mr: ModuleRequest
}

/**
 * 刷新记录响应体
 */
export const RrResBody: FC<RrResBodyProps> = memo(({ mr }) => {
  return (
    <MonacoJsonEditor
      value={stringifyRuntimeJson(mr.res)}
      readOnly
      height="100%"
    />
  )
})
