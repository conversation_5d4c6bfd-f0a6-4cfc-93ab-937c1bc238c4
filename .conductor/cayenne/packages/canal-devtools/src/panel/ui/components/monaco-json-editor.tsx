import Editor from '@monaco-editor/react'
import React, { useMemo, type ComponentProps, type FC } from 'react'

/**
 * JSON 编辑器属性
 */
export interface JsonEditorProps extends ComponentProps<typeof Editor> {
  /**
   * 只读
   */
  readOnly?: boolean
}

/**
 * JSON 编辑器
 */
export const MonacoJsonEditor: FC<JsonEditorProps> = ({
  readOnly,
  options,
  ...restProps
}) => {
  const finalOptions = useMemo(
    () =>
      ({
        tabSize: 2,
        wordWrap: 'on',
        readOnly,
        ...options,
      }) as const,
    [options, readOnly],
  )
  return (
    <Editor
      language="json"
      height="150px"
      options={finalOptions}
      {...restProps}
    />
  )
}
