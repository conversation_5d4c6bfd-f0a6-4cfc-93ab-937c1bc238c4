import { Typography } from '@mui/material'
import { observer } from 'mobx-react-lite'
import React, { FC, ReactNode } from 'react'
import styled, { css } from 'styled-components'

/**
 * 详情条目属性
 */
export interface DetailItemProps {
  /**
   * 标签
   */
  label: string
  /**
   * 值
   */
  value: ReactNode
  /**
   * 是否折叠，默认：true
   */
  collapsed?: boolean
}

/**
 * 详情条目
 */
export const DetailItem: FC<DetailItemProps> = observer(
  ({ label, value, collapsed = true }) => {
    return (
      <Container $collapsed={collapsed}>
        <Typography
          variant="body2"
          fontWeight="bold"
          className="detail-item-label"
        >
          {label}:
        </Typography>
        <Typography
          component="div"
          variant="body2"
          className="detail-item-value"
        >
          {value}
        </Typography>
      </Container>
    )
  },
)

const Container = styled.div<{ $collapsed: boolean }>`
  margin: 8px 0;

  .detail-item-label {
    margin-right: 8px;
    flex: none;
  }

  ${
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    ({ $collapsed }) =>
      $collapsed
        ? css`
            display: flex;

            .detail-item-value {
              width: 0;
              flex: auto;
            }
          `
        : ''
  }
`
