import React, { FC, memo } from 'react'
import { stringifyRuntimeJson } from '../../../utils/json'
import { ModuleRequest } from '../../types/request'
import { MonacoJsonEditor } from './monaco-json-editor'

/**
 * 刷新记录请求体属性
 */
export interface RrReqBodyProps {
  /**
   * 模块请求
   */
  mr: ModuleRequest
}

/**
 * 刷新记录请求体
 */
export const RrReqBody: FC<RrReqBodyProps> = memo(({ mr }) => {
  return (
    <MonacoJsonEditor
      value={stringifyRuntimeJson(mr.req)}
      readOnly
      height="100%"
    />
  )
})
