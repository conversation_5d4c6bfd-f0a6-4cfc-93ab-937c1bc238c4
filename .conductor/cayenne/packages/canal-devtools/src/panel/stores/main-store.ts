import {
  CommonResOfJava,
  FetchSchemaEnv,
  FetchSchemaReq,
  FetchSchemaRes,
} from '@ad/canal-react-runtime'
import { safeParseJson } from '@ad/canal-shared'
import { E2ESchema } from '@ad/e2e-schema'
import { b2fE2ESchema, validateE2EServerSchema } from '@ad/e2e-schema-utils'
import {
  ComponentDetailOfSchema,
  dfsGenComponentDetailBySchema,
} from '@kael/schema-utils'
import dayjs from 'dayjs'
import { isArray, isNull, isObject, isString } from 'lodash'
import { action, computed, makeObservable, observable, runInAction } from 'mobx'
import { nanoid } from 'nanoid'
import {
  RuntimeComponentTreeNode,
  RuntimeState,
  RuntimeTreeNode,
} from '../../types/content-panel-apis'
import { getComponentIdFromRuntime } from '../../utils/schema'
import { contentForPanelRpcApis } from '../bridge'
import { serverApis } from '../server-apis'
import { DataSource } from '../server-apis/types'
import { HarLog } from '../types/har'
import { ModuleMeta } from '../types/module'
import { ModuleRequest } from '../types/request'
import { RuntimeTreeNodeDetail } from '../types/runtime'
import { TreeItem } from '../ui/components/tree-select'

/**
 * 主仓库
 */
export class MainStore {
  /**
   * 面板 ID
   */
  private _panelId = nanoid()

  /**
   * 运行时树
   */
  private _runtimeTree: RuntimeTreeNode[] = []

  /**
   * 运行时树
   */
  public get runtimeTree(): RuntimeTreeNode[] {
    return this._runtimeTree
  }

  /**
   * 模块 ID -> 模块元数据
   */
  private _moduleMetasByModuleId: Record<string, ModuleMeta | undefined> = {}

  /**
   * 选中的运行时节点详情
   */
  private _selectedRuntimeNodeDetail: RuntimeTreeNodeDetail | null = null

  /**
   * 选中的运行时节点详情
   */
  public get selectedRuntimeNodeDetail(): RuntimeTreeNodeDetail | null {
    return this._selectedRuntimeNodeDetail
  }

  /**
   * 选中的运行时节点
   */
  public get selectedRuntimeNode(): RuntimeTreeNode | null {
    return this._selectedRuntimeNodeDetail?.node || null
  }

  /**
   * 选中的运行时 ID
   */
  public get selectedRuntimeId(): string | null {
    return this.selectedRuntimeNode?.runtimeId || null
  }

  /**
   * 选中的（运行时对应的）模块元数据
   */
  public get selectedModuleMeta(): ModuleMeta | null {
    const moduleId = this.selectedRuntimeNode?.moduleId
    return (moduleId && this._moduleMetasByModuleId[moduleId]) || null
  }

  /**
   * 选中的（运行时对应的）设计器 Schema
   */
  private _selectedDesignerSchema: E2ESchema | null = null

  /**
   * 选中的（运行时对应的）设计器 Schema
   */
  public get selectedDesignerSchema(): E2ESchema | null {
    return this._selectedDesignerSchema
  }

  /**
   * 选中的（运行时对应的）数据源
   */
  private _selectedDataSources: DataSource[] = []

  /**
   * 选中的（运行时对应的）数据源
   */
  public get selectedDataSources(): DataSource[] {
    return this._selectedDataSources
  }

  /**
   * 选中的（运行时对应的）状态
   */
  private _selectedRuntimeState: RuntimeState | null = null

  /**
   * 选中的（运行时对应的）状态
   */
  public get selectedRuntimeState(): RuntimeState | null {
    return this._selectedRuntimeState
  }

  /**
   * 选中的组件 ID
   */
  private _selectedComponentId: string | null = null

  /**
   * 选中的组件 ID
   */
  public get selectedComponentId(): string | null {
    return this._selectedComponentId
  }

  /**
   * 选中的组件属性
   */
  private _selectedComponentProps: Record<string, unknown> | null = null

  /**
   * 选中的组件属性
   */
  public get selectedComponentProps(): Record<string, unknown> | null {
    return this._selectedComponentProps
  }

  /**
   * 运行时 ID -> 模块请求
   */
  private _moduleRequestsByRuntimeId: Record<
    string,
    ModuleRequest[] | undefined
  > = {}

  /**
   * 选中的（运行时对应的）模块请求
   */
  public get selectedModuleRequests(): ModuleRequest[] {
    const runtimeId = this.selectedRuntimeId
    return (runtimeId && this._moduleRequestsByRuntimeId[runtimeId]) || []
  }

  /**
   * 运行时树条目
   */
  public get runtimeTreeItems(): TreeItem[] {
    const traverse = (node: RuntimeTreeNode): TreeItem => {
      let label = node.moduleId
      const name = this._moduleMetasByModuleId[node.moduleId]?.name
      if (name) {
        label = `${name} (${label})`
      }
      return {
        id: node.runtimeId,
        label,
        children: node.children.map(traverse),
      }
    }
    return this._runtimeTree.map(traverse)
  }

  /**
   * 组件树条目
   */
  public get componentTreeItems(): TreeItem[] {
    const componentTree = this._selectedRuntimeState?.componentTree
    if (!componentTree) return []
    const cdm: Record<string, ComponentDetailOfSchema<E2ESchema> | undefined> =
      {}
    if (this._selectedDesignerSchema) {
      for (const cd of dfsGenComponentDetailBySchema(
        this._selectedDesignerSchema,
      )) {
        cdm[cd.component.id] = cd
      }
    }
    return [traverse(componentTree)]

    function traverse(node: RuntimeComponentTreeNode): TreeItem {
      const cd = cdm[getComponentIdFromRuntime(node.componentId)]
      return {
        id: node.componentId,
        label: cd?.component.name || node.componentId,
        children: node.children.map(traverse),
      }
    }
  }

  public constructor() {
    makeObservable<
      MainStore,
      | '_runtimeTree'
      | '_moduleMetasByModuleId'
      | '_selectedRuntimeNodeDetail'
      | '_selectedDesignerSchema'
      | '_selectedDataSources'
      | '_selectedRuntimeState'
      | '_selectedComponentId'
      | '_selectedComponentProps'
      | '_moduleRequestsByRuntimeId'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _runtimeTree: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _moduleMetasByModuleId: observable.shallow,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _selectedRuntimeNodeDetail: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _selectedDesignerSchema: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _selectedDataSources: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _selectedRuntimeState: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _selectedComponentId: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _selectedComponentProps: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _moduleRequestsByRuntimeId: observable.shallow,
      runtimeTreeItems: computed,
      componentTreeItems: computed,
      selectRuntime: action,
      updateRuntimeTree: action,
      updateRuntimeState: action,
    })
    this._listenRequests()
    if (process.env.NODE_ENV === 'development') {
      Object.assign(window, {
        debugMainStore: this,
      })
    }
  }

  /**
   * 监听请求
   */
  private _listenRequests(): void {
    chrome.devtools.network.getHAR((har) => {
      for (const harlog of har.entries as HarLog[]) {
        this._onRequest(harlog)
      }
    })
    chrome.devtools.network.onRequestFinished.addListener((r) => {
      this._onRequest(r as HarLog)
    })
  }

  /**
   * 请求事件
   * @param harlog HAR 日志
   */
  private _onRequest(harlog: HarLog): void {
    const {
      request: { method, postData },
      response,
    } = harlog
    if (method !== 'POST') {
      return
    }
    if (
      !postData ||
      postData.mimeType !== 'application/json' ||
      !postData.text ||
      response.status !== 200 ||
      response.content.mimeType !== 'application/json'
    ) {
      return
    }
    const req = safeParseJson<FetchSchemaReq>(postData.text)
    if (
      !isObject(req) ||
      !isString(req.runtimeId) ||
      !isString(req.moduleId) ||
      !isString(req.refreshType) ||
      !isObject(req.params) ||
      !isString(req.env)
    ) {
      return
    }
    harlog.getContent((content) => {
      const res = safeParseJson<CommonResOfJava<FetchSchemaRes | null>>(content)
      if (
        !isObject(res) ||
        (!isNull(res.data) && !isObject(res.data)) ||
        (isObject(res.data) &&
          (!res.data.schemaError ||
            !res.data.traceUrl ||
            !res.data.httpDataList ||
            !validateE2EServerSchema(res.data)))
      ) {
        return
      }
      const runtimeId = `${req.runtimeId}`
      runInAction(() => {
        this._moduleRequestsByRuntimeId[runtimeId] = [
          ...(this._moduleRequestsByRuntimeId[runtimeId] || []),
          {
            runtimeId,
            startTime: +dayjs(harlog.startedDateTime),
            time: harlog.time,
            req,
            res,
          },
        ]
      })
    })
  }

  /**
   * 生成运行时节点详情，用于遍历
   */
  private *_genRuntimeNodeDetails(): Generator<
    RuntimeTreeNodeDetail,
    void,
    unknown
  > {
    for (const node of this._runtimeTree) {
      yield* generate(node, null, null)
    }

    function* generate(
      node: RuntimeTreeNode,
      parentRuntimeId: string | null,
      parentModuleId: string | null,
    ): Generator<RuntimeTreeNodeDetail, void, unknown> {
      yield { parentRuntimeId, parentModuleId, node }
      for (const child of node.children) {
        yield* generate(child, node.runtimeId, node.moduleId)
      }
    }
  }

  /**
   * 加载模块元数据
   * @param moduleId 模块 ID
   * @param env 环境
   */
  private async _loadModuleMeta(
    moduleId: string,
    env: FetchSchemaEnv,
  ): Promise<void> {
    if (this._moduleMetasByModuleId[moduleId]) {
      return
    }
    const res = await serverApis.getModuleDetail(
      {
        id: moduleId,
      },
      {
        env,
      },
    )
    if (isObject(res.data)) {
      runInAction(() => {
        this._moduleMetasByModuleId[moduleId] = {
          name: res.data.name,
          domainCode: res.data.businessDomainCode,
        }
      })
    }
  }

  /**
   * 加载所有模块元数据
   */
  private _loadAllModuleMetas(): void {
    for (const { node } of this._genRuntimeNodeDetails()) {
      this._loadModuleMeta(node.moduleId, node.env)
    }
  }

  /**
   * 加载设计器 Schema
   */
  private async _loadDesignerSchema(): Promise<void> {
    if (!this.selectedRuntimeNode) return
    const loadingNode = this.selectedRuntimeNode
    runInAction(() => {
      this._selectedDesignerSchema = null
    })
    const res = await serverApis.getModuleDeploy(
      {
        moduleId: loadingNode.moduleId,
        env: loadingNode.env,
      },
      {
        env: loadingNode.env,
      },
    )
    if (loadingNode === this.selectedRuntimeNode && isObject(res.data)) {
      runInAction(() => {
        this._selectedDesignerSchema = b2fE2ESchema(
          JSON.parse(res.data.content),
        )
      })
    }
  }

  /**
   * 加载数据源
   */
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  private async _loadDataSources(): Promise<void> {
    if (!this.selectedRuntimeNode) return
    const loadingNode = this.selectedRuntimeNode
    runInAction(() => {
      this._selectedDataSources = []
    })
    await this._loadModuleMeta(loadingNode.moduleId, loadingNode.env)
    const moduleMeta = this._moduleMetasByModuleId[loadingNode.moduleId]
    if (!moduleMeta) return
    const res = await serverApis.getAllDataSources(
      {
        domainCode: moduleMeta.domainCode,
      },
      {
        env: loadingNode.env,
      },
    )
    if (loadingNode === this.selectedRuntimeNode && isArray(res.data)) {
      runInAction(() => {
        this._selectedDataSources = res.data
      })
    }
  }

  /**
   * 自动选择运行时
   */
  private _autoSelectRuntime(): void {
    if (this.selectedRuntimeId) {
      // 已有选择有效
      for (const nodeDetail of this._genRuntimeNodeDetails()) {
        if (nodeDetail.node.runtimeId === this.selectedRuntimeId) {
          return
        }
      }
    }
    // 没有选择的时候，选择第一个
    if (this._runtimeTree.length) {
      this.selectRuntime(this._runtimeTree[0].runtimeId)
    }
  }

  /**
   * 选择运行时
   * @param runtimeId 运行时 ID
   */
  public selectRuntime(runtimeId: string | null): void {
    if (runtimeId === this.selectedRuntimeId) {
      return
    }
    this.selectComponent(null)
    if (!runtimeId) {
      this._selectedRuntimeNodeDetail = null
      return
    }
    if (this.selectedRuntimeNode?.runtimeId === runtimeId) {
      return
    }
    for (const nodeDetail of this._genRuntimeNodeDetails()) {
      if (nodeDetail.node.runtimeId === runtimeId) {
        this._selectedRuntimeNodeDetail = nodeDetail
        this._loadDesignerSchema()
        // 暂时没用到，先不加载
        // this._loadDataSources()
        this._selectedRuntimeState = null
        contentForPanelRpcApis.listenRuntimeState(nodeDetail.node.runtimeId)
        break
      }
    }
  }

  /**
   * 内容脚本加载完成
   */
  public async afterContentScriptLoaded(): Promise<void> {
    // console.log('MainStore::afterContentScriptLoaded')
    runInAction(() => {
      this._moduleRequestsByRuntimeId = {}
    })
    await contentForPanelRpcApis.listenRuntimeTree(this._panelId)
  }

  /**
   * 更新运行时树
   * @param panelId 面板 ID
   * @param tree 运行时树
   */
  public updateRuntimeTree(panelId: string, tree: RuntimeTreeNode[]): void {
    console.log('MainStore::updateRuntimeTree', {
      panelId,
      tree,
      ['this._panelId']: this._panelId,
    })
    if (panelId !== this._panelId) {
      console.log('MainStore::updateRuntimeTree drop')
      return
    }
    this._runtimeTree = tree
    this._loadAllModuleMetas()
    this._autoSelectRuntime()
  }

  /**
   * 更新运行时状态
   * @param panelId 面板 ID
   * @param runtimeId 运行时 ID
   * @param state 运行时状态
   */
  public updateRuntimeState(
    panelId: string,
    runtimeId: string,
    state: RuntimeState | null,
  ): void {
    console.log('MainStore::updateRuntimeState', {
      panelId,
      runtimeId,
      state,
      ['this._panelId']: this._panelId,
      ['this.selectedRuntimeNode?.runtimeId']:
        this.selectedRuntimeNode?.runtimeId,
    })
    if (
      panelId !== this._panelId ||
      this.selectedRuntimeNode?.runtimeId !== runtimeId
    ) {
      console.log('MainStore::updateRuntimeState drop')
      return
    }
    this._selectedRuntimeState = state
  }

  /**
   * 选择组件
   * @param componentId 组件 ID
   */
  public async selectComponent(componentId: string | null): Promise<void> {
    if (componentId === this._selectedComponentId) {
      return
    }
    runInAction(() => {
      this._selectedComponentId = componentId
      this._selectedComponentProps = null
    })
    const runtimeId = this.selectedRuntimeId
    if (runtimeId && componentId) {
      const newProps = await contentForPanelRpcApis.getPropsForPreview(
        runtimeId,
        componentId,
      )
      if (
        runtimeId === this.selectedRuntimeId &&
        componentId === this._selectedComponentId
      ) {
        runInAction(() => {
          this._selectedComponentProps = newProps
        })
      }
    }
  }
}

/**
 * 单例：主仓库
 */
export const mainStore = new MainStore()
