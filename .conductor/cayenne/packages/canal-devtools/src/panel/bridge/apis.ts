import { MaybePromiseApis } from '@kael/shared'
import {
  ContentForPanelRpcApis,
  PanelForContentRpcApis,
} from '../../types/content-panel-apis'
import { createEazyMsgApis, evalEazyMsgApis } from '../../utils/rpc'
import { mainStore } from '../stores/main-store'

/**
 * Content scripts 为 panel.html 提供的 RPC 接口
 */
export const contentForPanelRpcApis = createEazyMsgApis<ContentForPanelRpcApis>(
  (msg) => chrome.tabs.sendMessage(chrome.devtools.inspectedWindow.tabId, msg),
)

const exposeApis: MaybePromiseApis<PanelForContentRpcApis> = {
  updateRuntimeTree(...args) {
    return mainStore.updateRuntimeTree(...args)
  },
  updateRuntimeState(...args) {
    return mainStore.updateRuntimeState(...args)
  },
}
chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  void sender
  evalEazyMsgApis(exposeApis, msg, sendResponse)
  return true
})
