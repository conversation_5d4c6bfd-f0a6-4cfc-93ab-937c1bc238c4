import { sleep } from '@ad/canal-shared'
import { mainStore } from '../stores/main-store'
;(async (): Promise<void> => {
  let isInited = false
  for (;;) {
    try {
      const checkRet = await chrome.scripting.executeScript({
        target: {
          tabId: chrome.devtools.inspectedWindow.tabId,
        },
        world: 'MAIN',
        func: () => {
          return (
            // // 有运行时
            // !!window.__canalRuntimes &&
            // 没有注入过
            !window.__canalContentScriptType
          )
        },
      })
      if (checkRet[0]?.result) {
        // 需要注入
        await Promise.all([
          chrome.scripting.executeScript({
            target: {
              tabId: chrome.devtools.inspectedWindow.tabId,
            },
            world: 'ISOLATED',
            files: ['content-isolated.iife.js'],
          }),
          chrome.scripting.executeScript({
            target: {
              tabId: chrome.devtools.inspectedWindow.tabId,
            },
            world: 'MAIN',
            files: ['content-main.iife.js'],
          }),
        ])
        await mainStore.afterContentScriptLoaded()
      } else if (!isInited) {
        // 在 devtools 里刷新之类的场景，panel 页面重新加载了，但是主页面没有
        isInited = true
        await mainStore.afterContentScriptLoaded()
      }
    } catch (err) {
      console.error('注入脚本错误', err)
    }
    await sleep(300)
  }
})()
