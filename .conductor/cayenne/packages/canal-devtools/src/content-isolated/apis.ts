import { createQuickEncoder } from '@ad/canal-shared'
import { type MaybePromiseApis } from '@kael/shared'
import { createEndpoint } from '@remote-ui/rpc'
import {
  CUSTOM_EVENT_TYPE_ISOLATED_TO_MAIN,
  CUSTOM_EVENT_TYPE_MAIN_TO_ISOLATED,
} from '../constants'
import {
  ContentIsolatedForContentMainRpcApis,
  ContentMainForContentIsolatedRpcApis,
} from '../types/content-main-content-isolated-apis'
import {
  ContentForPanelRpcApis,
  PanelForContentRpcApis,
} from '../types/content-panel-apis'
import {
  createEazyMsgApis,
  evalEazyMsgApis,
  fromCustomEvent,
} from '../utils/rpc'

/**
 * content-main 为 content-isolated 提供的端点
 */
export const contentMainForContentIsolatedEndpoint =
  createEndpoint<ContentMainForContentIsolatedRpcApis>(
    fromCustomEvent(
      CUSTOM_EVENT_TYPE_ISOLATED_TO_MAIN,
      CUSTOM_EVENT_TYPE_MAIN_TO_ISOLATED,
    ),
    { createEncoder: createQuickEncoder },
  )
const contentMainForContentIsolatedExposedApis: MaybePromiseApis<ContentIsolatedForContentMainRpcApis> =
  {
    updateRuntimeTree(...args) {
      return panelForContentRpcApis.updateRuntimeTree(...args)
    },
    updateRuntimeState(...args) {
      return panelForContentRpcApis.updateRuntimeState(...args)
    },
  }
contentMainForContentIsolatedEndpoint.expose(
  contentMainForContentIsolatedExposedApis,
)

/**
 * panel.html 为 Content scripts 提供的 RPC 接口
 */
export const panelForContentRpcApis = createEazyMsgApis<PanelForContentRpcApis>(
  chrome.runtime.sendMessage,
)
const panelForContentExposedApis: MaybePromiseApis<ContentForPanelRpcApis> = {
  listenRuntimeTree:
    contentMainForContentIsolatedEndpoint.call.listenRuntimeTree,
  listenRuntimeState:
    contentMainForContentIsolatedEndpoint.call.listenRuntimeState,
  getPropsForPreview:
    contentMainForContentIsolatedEndpoint.call.getPropsForPreview,
  savePropsAsGlobalVariable:
    contentMainForContentIsolatedEndpoint.call.savePropsAsGlobalVariable,
}
chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  void sender
  evalEazyMsgApis(panelForContentExposedApis, msg, sendResponse)
  return true
})
