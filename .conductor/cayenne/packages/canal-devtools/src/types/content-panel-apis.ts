import { FetchSchemaEnv } from '@ad/canal-react-runtime'

/**
 * Content scripts 为 panel.html 提供的 RPC 接口
 */
export interface ContentForPanelRpcApis {
  /**
   * 监听运行时树
   * @param panelId 面板 ID
   */
  listenRuntimeTree(panelId: string): void
  /**
   * 监听运行时状态
   * @param runtimeId 运行时 ID
   * @returns 是否成功，如果运行时已经丢失，则失败
   */
  listenRuntimeState(runtimeId: string): boolean
  /**
   * 获取属性，用于预览
   * @param runtimeId 运行时 ID
   * @param componentId 组件 ID
   */
  getPropsForPreview(
    runtimeId: string,
    componentId: string,
  ): Record<string, unknown>
  /**
   * 属性作为全局变量保存
   * @param runtimeId 运行时 ID
   * @param componentId 组件 ID
   */
  savePropsAsGlobalVariable(runtimeId: string, componentId: string): string
}

/**
 * panel.html 为 Content scripts 提供的 RPC 接口
 */
export interface PanelForContentRpcApis {
  /**
   * 更新运行时树
   * @param panelId 面板 ID
   * @param tree 运行时树
   */
  updateRuntimeTree(panelId: string, tree: RuntimeTreeNode[]): void
  /**
   * 更新运行时状态
   * @param panelId 面板 ID
   * @param runtimeId 运行时 ID
   * @param state 状态，null 表示运行时已经卸载
   */
  updateRuntimeState(
    panelId: string,
    runtimeId: string,
    state: RuntimeState | null,
  ): void
}

/**
 * 运行时树节点
 */
export interface RuntimeTreeNode {
  /**
   * 运行时 ID
   */
  runtimeId: string
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 环境
   */
  env: FetchSchemaEnv
  /**
   * 子节点
   */
  children: this[]
}

/**
 * 运行时状态
 */
export interface RuntimeState {
  /**
   * 参数，JSON 字符串
   */
  params: string
  /**
   * 初始数据，JSON 字符串
   */
  initData: string
  /**
   * 实时数据，JSON 字符串
   */
  data: string
  /**
   * 组件树
   */
  componentTree: RuntimeComponentTreeNode
}

/**
 * 运行时组件树
 */
export interface RuntimeComponentTreeNode {
  /**
   * 组件 ID
   */
  componentId: string
  /**
   * 子节点
   */
  children: RuntimeComponentTreeNode[]
}

// /**
//  * 可预览属性值，和表达式的结构类似
//  */
// export type PreviewablePropValue =
//   | StaticPreviewablePropValue
//   | ObjectPreviewablePropValue
//   | ArrayPreviewablePropValue
//   | FnPreviewablePropValue
//   | JsxPreviewablePropValue

// /**
//  * 静态可预览属性值
//  */
// export interface StaticPreviewablePropValue {
//   /**
//    * 类型
//    */
//   type: 'static'
//   /**
//    * 值
//    */
//   value: unknown
// }

// /**
//  * 对象可预览属性值
//  */
// export interface ObjectPreviewablePropValue {
//   /**
//    * 类型
//    */
//   type: 'object'
//   /**
//    * 值
//    */
//   value: Record<string, PreviewablePropValue>
// }

// /**
//  * 数组可预览属性值
//  */
// export interface ArrayPreviewablePropValue {
//   /**
//    * 类型
//    */
//   type: 'array'
//   /**
//    * 值
//    */
//   value: PreviewablePropValue[]
// }

// /**
//  * 函数可预览属性值
//  */
// export interface FnPreviewablePropValue {
//   /**
//    * 类型
//    */
//   type: 'fn'
//   /**
//    * 函数
//    */
//   name: string
// }

// /**
//  * JSX 可预览属性值
//  */
// export interface JsxPreviewablePropValue {
//   /**
//    * 类型
//    */
//   type: 'jsx'
//   /**
//    * （根）元素名
//    */
//   elementName: string
// }
