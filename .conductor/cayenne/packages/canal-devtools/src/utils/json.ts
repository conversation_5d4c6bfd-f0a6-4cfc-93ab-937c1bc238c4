import { safeStringifyJson } from '@ad/canal-shared'
import { get, isArray, isObject, isPlainObject, toString } from 'lodash'
import { isValidElement } from 'react'
import { configure } from 'safe-stable-stringify'

const stringifyOfStringifyRuntimeJson = configure({ maximumDepth: 9 })

/**
 * 字符串化运行时 JSON
 * @param value 值
 * @param defaultStr 转换失败时返回的字符串
 */
export function stringifyRuntimeJson(
  value: unknown,
  defaultStr?: string,
): string {
  return safeStringifyJson(value, defaultStr, {
    space: 2,
    replacer(...[, v]) {
      if (isValidElement(v)) {
        return '[ReactElement]'
      }
      if (!isArray(v) && isObject(v) && !isPlainObject(v)) {
        const name = get(Object.getPrototypeOf(v), 'constructor.name')
        return name ? `[${name}]` : toString(v)
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return v as any
    },
    stringify: stringifyOfStringifyRuntimeJson,
  })
}
