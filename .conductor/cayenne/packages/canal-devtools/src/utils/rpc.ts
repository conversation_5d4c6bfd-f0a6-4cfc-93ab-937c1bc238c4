import { AnyFn } from '@ad/canal-shared'
import { MaybePromiseApis } from '@kael/shared'
import { MaybePromise, MessageEndpoint } from '@remote-ui/rpc'

/**
 * 创建简单消息接口发送消息
 */
export interface CreateEazyMsgApisPostMessage {
  (msg: CreateEazyMsgApisReqMessage): MaybePromise<CreateEazyMsgApisResMessage>
}

/**
 * 创建简单消息接口请求消息
 */
export interface CreateEazyMsgApisReqMessage<Args = unknown[]> {
  /**
   * 类型
   */
  type: string
  /**
   * 参数
   */
  args: Args
}

/**
 * 创建简单消息接口响应消息
 */
export interface CreateEazyMsgApisResMessage<ReturnType = unknown> {
  /**
   * 错误
   */
  err?: Error
  /**
   * 数据
   */
  data?: ReturnType
}

/**
 * 创建简单消息接口
 * @param postMessage 发送消息
 */
export function createEazyMsgApis<T>(
  postMessage: CreateEazyMsgApisPostMessage,
): MaybePromiseApis<T> {
  const m = new Map()
  return new Proxy({} as MaybePromiseApis<T>, {
    get(o, k): unknown {
      void o
      let fn = m.get(k)
      if (!fn) {
        m.set(
          k,
          (fn = async (...args: unknown[]): Promise<unknown> => {
            const res = await postMessage({
              type: k as string,
              args,
            })
            if (res.err) {
              throw res.err
            }
            return res.data
          }),
        )
      }
      return fn
    },
  })
}

/**
 * 求值简单消息接口
 * @param exposeApis 暴露接口
 * @param msg 消息
 * @param sendResponse 发送响应
 */
export function evalEazyMsgApis(
  exposeApis: Record<string, AnyFn>,
  msg: CreateEazyMsgApisReqMessage,
  sendResponse: (msg: CreateEazyMsgApisResMessage) => void,
): void {
  const { type, args } = msg
  ;(async (): Promise<void> => {
    const res: CreateEazyMsgApisResMessage = {}
    try {
      res.data = await exposeApis[type](...args)
    } catch (err) {
      res.err = err as Error
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ;(sendResponse as any)(res)
  })()
}

/**
 * 从自定义事件（创建消息端点）
 * @param postEventName 发送事件名
 * @param listenEventName 监听事件名
 */
export function fromCustomEvent(
  postEventName: string,
  listenEventName: string,
): MessageEndpoint {
  const listenerMap = new WeakMap<
    (ev: MessageEvent) => void,
    (ev: Event) => void
  >()
  const ep: MessageEndpoint = {
    postMessage(message) {
      window.dispatchEvent(
        new CustomEvent(postEventName, {
          detail: message,
        }),
      )
    },
    addEventListener(event, listener) {
      const wrappedListener = (ev: Event): void => {
        const e = new MessageEvent('MessageEvent', {
          data: (ev as CustomEvent).detail,
        })
        listener(e)
      }
      listenerMap.set(listener, wrappedListener)
      void event
      window.addEventListener(listenEventName, wrappedListener)
    },
    removeEventListener(event, listener) {
      const wrappedListener = listenerMap.get(listener)
      if (wrappedListener == null) return
      listenerMap.delete(listener)
      void event
      window.removeEventListener(listenEventName, wrappedListener)
    },
  }
  return ep
}
