import { createQuickEncoder } from '@ad/canal-shared'
import { type MaybePromiseApis } from '@kael/shared'
import { createEndpoint } from '@remote-ui/rpc'
import {
  CUSTOM_EVENT_TYPE_ISOLATED_TO_MAIN,
  CUSTOM_EVENT_TYPE_MAIN_TO_ISOLATED,
} from '../constants'
import {
  ContentIsolatedForContentMainRpcApis,
  ContentMainForContentIsolatedRpcApis,
} from '../types/content-main-content-isolated-apis'
import { fromCustomEvent } from '../utils/rpc'
import { mainStore } from './stores/main-store'

/**
 * content-isolated 为 content-main 提供的端点
 */
export const contentIsolatedForContentMainEndpoint =
  createEndpoint<ContentIsolatedForContentMainRpcApis>(
    fromCustomEvent(
      CUSTOM_EVENT_TYPE_MAIN_TO_ISOLATED,
      CUSTOM_EVENT_TYPE_ISOLATED_TO_MAIN,
    ),
    { createEncoder: createQuickEncoder },
  )
const contentIsolatedForContentMainExposedApis: MaybePromiseApis<ContentMainForContentIsolatedRpcApis> =
  {
    listenRuntimeTree: mainStore.listenRuntimeTree.bind(mainStore),
    listenRuntimeState: mainStore.listenRuntimeState.bind(mainStore),
    getPropsForPreview: mainStore.getPropsForPreview.bind(mainStore),
    savePropsAsGlobalVariable:
      mainStore.savePropsAsGlobalVariable.bind(mainStore),
  }
contentIsolatedForContentMainEndpoint.expose(
  contentIsolatedForContentMainExposedApis,
)
