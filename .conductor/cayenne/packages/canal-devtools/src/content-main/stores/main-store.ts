import {
  assignOrProduce,
  CanalReactRuntime,
  getCachedComponentProps,
  getCanalRuntimes,
} from '@ad/canal-react-runtime'
import { genJsonValues, safeParseJson, sleep } from '@ad/canal-shared'
import { ComponentController } from '@kael/runtime'
import {
  get,
  isArray,
  isEqual,
  isPlainObject,
  once,
  uniqueId,
  values,
} from 'lodash'
import { isValidElement } from 'react'
import {
  CanalReactComponent,
  CanalReactComponentInstanceTreeNode,
} from '../../panel/types/runtime'
import {
  ContentForPanelRpcApis,
  RuntimeComponentTreeNode,
  RuntimeState,
  RuntimeTreeNode,
} from '../../types/content-panel-apis'
import { stringifyRuntimeJson } from '../../utils/json'
import { contentIsolatedForContentMainEndpoint } from '../apis'

/**
 * 主仓库
 */
export class MainStore implements ContentForPanelRpcApis {
  /**
   * 面板 ID
   */
  private _panelId: string | null = null

  /**
   * 运行时 ID
   */
  private _runtimeId: string | null = null

  /**
   * 监听运行时树
   * @param panelId 面板 ID
   */
  public listenRuntimeTree(panelId: string): void {
    console.log('MainStore::listenRuntimeTree', panelId)
    if (panelId !== this._panelId) {
      this._runtimeId = null
    }
    this._panelId = panelId
    this._startRuntimeTreeLoop()
  }

  /**
   * 开始运行时树循环
   */
  private _startRuntimeTreeLoop = once(async () => {
    let lastData: [string | null, RuntimeTreeNode[]] | undefined
    for (;;) {
      try {
        const tree = this._getRuntimeTree()
        const panelId = this._panelId
        const newData: typeof lastData = [panelId, tree]
        if (!isEqual(lastData, newData)) {
          if (panelId) {
            await contentIsolatedForContentMainEndpoint.call.updateRuntimeTree(
              panelId,
              tree,
            )
          }
          lastData = newData
        }
      } catch (err) {
        console.error('_startRuntimeTreeLoop err', err)
      }
      await sleep(300)
    }
  })

  /**
   * 生成所有运行时
   */
  private *_genAllRuntimes(): Generator<CanalReactRuntime> {
    for (const runtime of getCanalRuntimes()) {
      yield* runtime.container.genAllRuntimes() as Generator<CanalReactRuntime>
    }
  }

  /**
   * 获取运行时
   * @param runtimeId 运行时 ID
   */
  private _getRuntime(runtimeId?: string | null): CanalReactRuntime | null {
    if (!runtimeId) return null
    for (const runtime of this._genAllRuntimes()) {
      if (runtime.container.runtimeId === runtimeId) {
        return runtime
      }
    }
    return null
  }

  /**
   * 获取运行时树
   */
  private _getRuntimeTree(): RuntimeTreeNode[] {
    return [...getCanalRuntimes()].map(traverse)

    function traverse(runtime: CanalReactRuntime): RuntimeTreeNode {
      const {
        constructorOptions: { config: { schemaId, env = 'production' } = {} },
        container: { runtimeId, childModuleCtxs },
      } = runtime
      return {
        runtimeId,
        moduleId: `${schemaId}`,
        env,
        children: values(childModuleCtxs).map((ctx) =>
          traverse(ctx.runtime as CanalReactRuntime),
        ),
      }
    }
  }

  /**
   * 监听运行时状态
   * @param runtimeId 运行时 ID
   */
  public listenRuntimeState(runtimeId: string): boolean {
    console.log('MainStore::listenRuntimeState', runtimeId)
    this._runtimeId = runtimeId
    this._startRuntimeStateLoop()
    return true
  }

  /**
   * 开始运行时状态循环
   */
  private _startRuntimeStateLoop = once(async () => {
    let lastData:
      | [string | null, string | null, RuntimeState | null]
      | undefined
    for (;;) {
      try {
        const state = this._getRuntimeState()
        const panelId = this._panelId
        const runtimeId = this._runtimeId
        const newData: typeof lastData = [panelId, runtimeId, state]
        if (!isEqual(lastData, newData)) {
          if (panelId && runtimeId) {
            await contentIsolatedForContentMainEndpoint.call.updateRuntimeState(
              panelId,
              runtimeId,
              state,
            )
          }
          lastData = newData
        }
      } catch (err) {
        console.error('_startRuntimeStateLoop err', err)
      }
      await sleep(300)
    }
  })

  /**
   * 获取运行时状态
   */
  private _getRuntimeState(): RuntimeState | null {
    const runtime = this._getRuntime(this._runtimeId)
    if (!runtime) return null
    const {
      container: { initParams, schema, data },
      constructorOptions: { config = {} },
    } = runtime
    const initData = assignOrProduce({ ...schema.data }, config.data)
    return {
      params: stringifyRuntimeJson(initParams),
      initData: stringifyRuntimeJson(initData),
      data: stringifyRuntimeJson(data),
      componentTree: this._getRuntimeComponentTree(runtime),
    }
  }

  /**
   * 获取运行时组件树
   * @param view 运行时视图
   */
  private _getRuntimeComponentTree(
    runtime: CanalReactRuntime,
  ): RuntimeComponentTreeNode {
    return traverse(
      this._getCanalReactComponentInstanceTree(runtime.container.runtimeId),
    )
    function traverse(
      node: CanalReactComponentInstanceTreeNode,
    ): RuntimeComponentTreeNode {
      return {
        componentId: node.instance.id,
        children: node.children.map(traverse),
      }
    }
  }

  /**
   * 获取大运河 React 组件实例树
   * @param runtimeId 运行时 ID
   */
  private _getCanalReactComponentInstanceTree(
    runtimeId: string,
  ): CanalReactComponentInstanceTreeNode
  private _getCanalReactComponentInstanceTree(
    runtimeId?: string | null,
  ): CanalReactComponentInstanceTreeNode | null
  private _getCanalReactComponentInstanceTree(
    runtimeId?: string | null,
  ): CanalReactComponentInstanceTreeNode | null {
    const runtime = this._getRuntime(runtimeId)
    if (!runtime) return null
    return traverse(runtime.container.view)
    function traverse(
      comp: CanalReactComponent,
    ): CanalReactComponentInstanceTreeNode {
      const children = isArray(comp.children) ? comp.children.map(traverse) : []
      for (const detail of genJsonValues(getCachedComponentProps(comp), {
        filter({ value }) {
          const isEle = isValidElement(value)
          if (isEle) {
            const componentController = get(
              value,
              'props.componentController',
            ) as unknown
            if (
              componentController &&
              get(componentController, 'runtime') === runtime
            ) {
              children.push(
                ...// eslint-disable-next-line @typescript-eslint/no-explicit-any
                (
                  (componentController as ComponentController)
                    .instances as unknown as CanalReactComponent[]
                ).map(traverse),
              )
            }
          }
          return isArray(value) || (isPlainObject(value) && !isEle)
        },
      })) {
        void detail
      }
      return {
        instance: comp,
        children,
      }
    }
  }

  /**
   * 获取属性，用于预览
   * @param runtimeId 运行时 ID
   * @param componentId 组件 ID
   */
  public getPropsForPreview(
    runtimeId: string,
    componentId: string,
  ): Record<string, unknown> {
    return safeParseJson(
      stringifyRuntimeJson(this._getProps(runtimeId, componentId), '{}'),
      {},
    ) as Record<string, unknown>
  }

  /**
   * 获取属性
   * @param runtimeId 运行时 ID
   * @param componentId 组件 ID
   */
  private _getProps(
    runtimeId: string,
    componentId: string,
  ): Record<string, unknown> {
    for (const comp of this._genAllCanalReactComponents(runtimeId)) {
      if (comp.id === componentId) {
        return getCachedComponentProps(comp)
      }
    }
    return {}
  }

  /**
   * 生成所有大运河 React 组件
   */
  private *_genAllCanalReactComponents(
    runtimeId?: string | null,
  ): Generator<CanalReactComponent> {
    const node = this._getCanalReactComponentInstanceTree(runtimeId)
    if (!node) return
    yield* traverse(node)

    function* traverse(
      n: CanalReactComponentInstanceTreeNode,
    ): Generator<CanalReactComponent> {
      yield n.instance
      for (const child of n.children) {
        yield* traverse(child)
      }
    }
  }

  /**
   * 属性作为全局变量保存
   * @param runtimeId 运行时 ID
   * @param componentId 组件 ID
   */
  public savePropsAsGlobalVariable(
    runtimeId: string,
    componentId: string,
  ): string {
    const props = this._getProps(runtimeId, componentId)
    return this._saveAsGlobalVariable(props)
  }

  /**
   * 保存为全局变量
   * @param value 值
   */
  private _saveAsGlobalVariable(value: unknown): string {
    let key: string
    for (;;) {
      key = uniqueId('canalTmp')
      if (!(key in window)) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ;(window as any)[key] = value
        break
      }
    }
    return key
  }
}

/**
 * 单例：主仓库
 */
export const mainStore = new MainStore()
