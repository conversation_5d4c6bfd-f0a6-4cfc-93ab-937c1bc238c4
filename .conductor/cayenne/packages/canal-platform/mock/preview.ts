/* eslint-disable @typescript-eslint/explicit-function-return-type */
/* eslint-disable @typescript-eslint/naming-convention */
export default {
  'POST /rest/ad/canal/preview/schema': (req, res) => {
    res.json({
      data: {
        schemaVersion: '0.0.1',
        componentCodes: {
          '@ad/canal-biz-components::17': {
            code: {
              js: 'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.6/dist/button/index.069f5f93.js',
            },
            version: '3',
          },
        },
        view: {
          type: '@ad/canal-components::Root',
          id: 'root',
          name: '根组件',
          children: [
            {
              id: 'comp_2vz9_sAERCgby2FVmHGm6',
              type: '@ad/canal-components::Container',
              name: '容器::Gm6',
              props: {
                style: {
                  type: 'static',
                  value: {
                    marginPadding: {},
                  },
                },
              },
              children: [
                {
                  id: 'comp_xjwI2tEDwnhXjUU-nUvbt',
                  type: '@ad/canal-components::Text',
                  name: '文本::vbt',
                  props: {
                    text: '文本sssss',
                    style: {
                      type: 'static',
                      value: {
                        marginPadding: {},
                      },
                    },
                  },
                },
                {
                  id: 'comp_LAJDew4lLD9aZJQveWp72',
                  type: '@ad/canal-components::Text',
                  name: '文本::p72',
                  props: {
                    text: '文本',
                    style: {
                      type: 'static',
                      value: {
                        marginPadding: {},
                      },
                    },
                  },
                },
                {
                  id: 'comp_IUUuAdCWD01A7QK8Wx64H',
                  type: '@ad/canal-biz-components::17',
                  name: '按钮::64H',
                  props: {
                    text: 'sdfx',
                    style: {
                      type: 'static',
                      value: {
                        marginPadding: {},
                      },
                    },
                  },
                },
              ],
            },
            {
              id: 'comp_hGwq8XVBfA9aMhcYuN5C3',
              type: '@ad/canal-components::Text',
              name: '文本::5C3',
              props: {
                text: '文本33333',
              },
            },
            {
              id: 'comp_eVblJ6Ymd2VTp4PPqvYB8',
              type: '@ad/canal-components::Button',
              name: '按钮::YB8',
              props: {
                text: '查询',
                style: {
                  type: 'static',
                  value: {},
                },
                onClick: {
                  type: 'refresh',
                  refreshType: 'submit',
                  params: {
                    a: 3312,
                  },
                },
              },
            },
            {
              id: 'comp_0iFpkHT$qb6zPKplEjm1u',
              type: '@ad/canal-components::Container',
              name: '容器::m1u',
              children: [
                {
                  id: 'comp_8MQkSNbiQcMHKLVl7fBxX',
                  type: '@ad/canal-components::Text',
                  name: '文本::BxX',
                  children: [],
                  props: {
                    text: JSON.stringify(req.body.rawSchema),
                  },
                },
              ],
            },
          ],
        },
        linkage: {
          autoRefreshByComponent: [
            'comp_xjwI2tEDwnhXjUU-nUvbt',
            'comp_LAJDew4lLD9aZJQveWp72',
            'comp_hGwq8XVBfA9aMhcYuN5C3',
          ],
          componentDataParams: {
            common: [],
            byRefreshType: {
              auto: ['comp_2vz9_sAERCgby2FVmHGm6'],
            },
          },
          commonParams: {
            a: 34234,
          },
        },
      },
    })
  },
}
