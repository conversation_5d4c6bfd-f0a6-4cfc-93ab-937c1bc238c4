import mockjs from 'mockjs'

export default {
  ['GET /rest/canal/change/list']: mockjs.mock({
    status: 200,
    message: '成功',
    data: {
      list: [
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: 0,
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: 0,
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
        {
          changeId: 'ADInfraDataDiag_' + '@integer(10)',
          changeName: '@string',
          type: '@integer(0, 1)',
          createUser: '@string',
          publishTime: 1698031744826,
          createTime: 1698031744826,
          status: '@integer(0, 4)',
          isOnline: '@integer(0, 1)',
          teamId: '@string',
        },
      ],
      pageNo: '@integer(1, 10)',
      pageSize: 10,
      totalCount: '@integer(1, 1000)',
    },
  }),
  ['POST /rest/canal/change/close/test']: mockjs.mock({
    status: 200,
    message: '成功',
  }),
  ['GET /rest/canal/change/detail/test']: mockjs.mock({
    status: 200,
    message: '成功',
    data: {
      changeId: 'ADInfraDataDiag_' + '@integer(10)',
      changeName: '@string(30)',
      type: '@integer(0, 1)',
      createUser: '@string',
      publishTime: 1698031744826,
      createTime: 1698031744826,
      status: '@integer(0, 4)',
      isOnline: '@integer(0, 1)',
      teamId: '@string',
      developer: ['yuanzhaohao', 'liming10'],
    },
  }),
  ['GET /rest/canal/change/module/list']: mockjs.mock({
    status: 200,
    message: '成功',
    data: {
      list: [
        {
          id: '@integer(0, 10000)',
          name: '@string(40)',
          version: '@integer(0, 40)',
          updateUser: '@string',
          updateTime: 1698031744826,
        },
        {
          id: '@integer(0, 10000)',
          name: '@string(40)',
          version: '@integer(0, 40)',
          updateUser: '@string',
          updateTime: 1698031744826,
        },
        {
          id: '@integer(0, 10000)',
          name: '@string(40)',
          version: '@integer(0, 40)',
          updateUser: '@string',
          updateTime: 1698031744826,
        },
        {
          id: '@integer(0, 10000)',
          name: '@string(40)',
          version: '@integer(0, 40)',
          updateUser: '@string',
          updateTime: 1698031744826,
        },
      ],
    },
  }),
}
