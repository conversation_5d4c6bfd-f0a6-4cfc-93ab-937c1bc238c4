/* eslint-disable @typescript-eslint/naming-convention */
export default {
  ['GET /rest/canal/material-platform/search']: {
    result: 1,
    msg: '',
    data: [
      {
        id: 1401,
        name: 'AnchorWrapper',
        displayName: '锚点',
        imgs: 'https://p2-ad.adbkwai.com/kos/proj-drow/ZHJvdw/kcdn/cdn-kcdn111910/chrome-plugin-upload/2024-03-12/1710245802269.3b0007a396124ace.png',
        version: '0.2.0-alpha.0',
        description: '自动收集页面锚点，生成目录',
        createAt: 1711615629992,
        updateAt: 1711615629992,
        materialId: 80,
        packageName: '@ks-material/components',
        packageType: 'multiple',
      },
      {
        id: 234,
        name: 'Buss',
        displayName: '撒旦法分',
        imgs: 'https://p2-ad.adbkwai.com/kos/proj-drow/ZHJvdw/kcdn/cdn-kcdn111910/chrome-plugin-upload/2024-03-12/1710245802269.3b0007a396124ace.png33333',
        version: '1.0.3',
        description: '自动收集页面锚点，生成目录asdfasdf',
        createAt: 1711615629111,
        updateAt: 1711615629111,
        materialId: 4234,
        packageName: '@ks-material/components',
        packageType: 'multiple',
      },
    ],
  },
  ['GET /rest/canal/material-platform/get']: {
    result: 1,
    msg: '',
    data: {
      id: 1403,
      materialId: 80,
      createAt: 1711615629992,
      updateAt: 1711615629992,
      version: '0.2.0-alpha.0',
      name: 'BasicDetail',
      type: 'component',
      packageDescription: null,
      displayName: '基础详情组件',
      description: '展示基本的详情内容',
      imgs: 'https://p3-ad.adbkwai.com/kos/proj-drow/ZHJvdw/kcdn/cdn-kcdn111910/chrome-plugin-upload/2024-03-12/1710245894676.2077290eb0c9f648.png',
      umdUrls: ['https://cdn.js,https://cdn.css'],
      dependencies: {
        'react-dom': '^18.2.0',
        classnames: '^2.5.1',
      },
      devDependencies: {
        'prettier-plugin-organize-imports': '^3.1.0',
      },
      peerDependencies: {
        react: '^18.0.0',
        'react-dom': '^18.2.0',
      },
      props: [
        {
          type: 'string',
          comments: '步骤条类型，有 default、navigation、dotted三种',
          optional: false,
          identifier: 'type',
          defaultValue: '',
        },
      ],

      versionList: [
        '0.2.0-alpha.0',
        '0.1.16-alpha.18',
        '0.1.16-alpha.17',
        '0.1.16-alpha.16',
        '0.1.16-alpha.13',
      ],

      schema: {
        schemaVersion: '0.0.1',
        type: 'Button',
        name: '按钮',
        icon: './icon.png',
        props: [
          {
            type: 'group',
            name: '内容',
            items: [
              {
                type: 'group',
                name: '常用',
                path: [],
                items: [
                  {
                    name: '文本',
                    type: 'field',
                    path: ['text'],
                    defaultValue: '文本',
                    setter: 'String',
                  },
                ],
              },
            ],
          },
        ],
        code: { code: { js: './index.tsx' }, exportIdentifier: 'Button' },
        implements: {
          style: true,
        },
      },
    },
  },
}
