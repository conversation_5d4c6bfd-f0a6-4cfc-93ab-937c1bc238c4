/* eslint-disable @typescript-eslint/explicit-function-return-type */
/* eslint-disable @typescript-eslint/naming-convention */
export default {
  'GET /rest/canal/module/detail': {
    data: {
      name: '模块名1',
      type: 1,
      containerType: 0,
      businessDomainCode: 'businessDomainCode',
      canEdit: true,
      id: 'adf',
      version: 3,
      changeId: 'ad.productivityffb24b12-e44a-4f47-8b97-5471b015a7fa',
      content: JSON.stringify({
        schemaVersion: '0.0.1',
        componentCodes: {
          '@ad/canal-biz-components::todo': {
            code: {
              js: 'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js',
            },
            version: '5',
          },
          '@ad/canal-biz-components::17': {
            code: {
              js: 'https://p1.adkwai.com/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.02657d05.js',
            },
            version: '18',
          },
        },
        iife: {
          transform: {
            type: 'js',
            code: 'function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports.__main = __main;\nexports.output = exports["default"] = void 0;\nfunction __main() {\n  var _this = this;\n  var actions = JSON.parse("[{\\"type\\":\\"exec-effect\\",\\"expr\\":{\\"type\\":\\"js\\",\\"code\\":\\"(function() {\\\\n  var exports = {}, module = {}\\\\n  module.exports = exports\\\\n  ;(function() {\\\\n    \\\\\\"use strict\\\\\\";\\\\n\\\\nObject.defineProperty(exports, \\\\\\"__esModule\\\\\\", {\\\\n  value: true\\\\n});\\\\nexports[\\\\\\"default\\\\\\"] = void 0;\\\\n/**\\\\n* @param {Container} ctx 上下文\\\\n* @param {any} arg 参数\\\\n*/\\\\nvar _default = exports[\\\\\\"default\\\\\\"] = function _default(ctx, arg) {\\\\n  console.log(\'模块级前端动作副作用\', ctx, arg);\\\\n};\\\\n  })()\\\\n  return module.exports.default\\\\n})()\\",\\"codeES\\":\\"/**\\\\n* @param {Container} ctx 上下文\\\\n* @param {any} arg 参数\\\\n*/\\\\nexport default (ctx, arg) => {\\\\n  console.log(\'模块级前端动作副作用\', ctx, arg)\\\\n}\\\\n\\"},\\"arg0\\":\\"sdf\\",\\"arg0IsPath\\":false},{\\"type\\":\\"exec-external-fn\\",\\"fnPath\\":\\"customFn\\",\\"arg0\\":\\"\\",\\"arg0IsPath\\":false},{\\"type\\":\\"exec-effect\\",\\"expr\\":{\\"type\\":\\"js\\",\\"code\\":\\"(function() {\\\\n  var exports = {}, module = {}\\\\n  module.exports = exports\\\\n  ;(function() {\\\\n    \\\\\\"use strict\\\\\\";\\\\n\\\\nObject.defineProperty(exports, \\\\\\"__esModule\\\\\\", {\\\\n  value: true\\\\n});\\\\nexports[\\\\\\"default\\\\\\"] = void 0;\\\\n/**\\\\n* @param {Container} ctx 上下文\\\\n* @param {any} arg 参数\\\\n*/\\\\nvar _default = exports[\\\\\\"default\\\\\\"] = function _default(ctx, arg) {\\\\n  console.log(\'模块级前端动作副作用2222\', ctx, arg);\\\\n};\\\\n  })()\\\\n  return module.exports.default\\\\n})()\\",\\"codeES\\":\\"/**\\\\n* @param {Container} ctx 上下文\\\\n* @param {any} arg 参数\\\\n*/\\\\nexport default (ctx, arg) => {\\\\n  console.log(\'模块级前端动作副作用2222\', ctx, arg)\\\\n}\\\\n\\"},\\"arg0\\":\\"fff\\",\\"arg0IsPath\\":true}]");\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  var actionIdxes = this["default"].apply(this, args);\n  if (typeof actionIdxes === \'number\') {\n    actionIdxes = [actionIdxes];\n  }\n  var getByOutput = function getByOutput(v, isPath) {\n    return isPath ? _this.output[v] : v;\n  };\n  var actionsExpr = {\n    type: \'actions\',\n    fns: actions.map(function (action) {\n      switch (action.type) {\n        case \'open-url\':\n          {\n            return {\n              type: \'open-url\',\n              url: getByOutput(action.url, action.urlIsPath),\n              inPlace: action.inPlace\n            };\n          }\n        case \'exec-external-fn\':\n          {\n            return {\n              type: \'bind\',\n              fn: {\n                type: \'get-data\',\n                path: action.fnPath\n              },\n              args: [{\n                type: \'static\',\n                value: getByOutput(action.arg0, action.arg0IsPath)\n              }]\n            };\n          }\n        case \'exec-effect\':\n          {\n            return {\n              type: \'bind\',\n              fn: {\n                type: \'js\',\n                code: action.expr.code\n              },\n              args: [{\n                type: \'js\',\n                code: \'ctx\'\n              }, {\n                type: \'static\',\n                value: getByOutput(action.arg0, action.arg0IsPath)\n              }]\n            };\n          }\n      }\n    })\n  };\n  actionsExpr.fns = actionIdxes.map(function (idx) {\n    return actionsExpr.fns[idx];\n  });\n  return actionsExpr;\n}\n/**\n* @param {Ctx} ctx 上下文\n* @returns {number | number[]} 动作编号\n*/\nvar _default = exports["default"] = function _default(ctx) {\n  ctx.fff = 234;\n  if (ctx.response.data.x) {\n    return 0;\n  }\n  return [1, 2];\n};\n/**\n * 输出，用于动作列表里按键值取用，可以在上面函数里修改\n */\nvar output = exports.output = {};\n  })()\n  return module.exports.__main.apply(module.exports, mainArg0.args)\n}',
            codeES:
              '/**\n* @param {Ctx} ctx 上下文\n* @returns {number | number[]} 动作编号\n*/\nexport default (ctx) => {\n  ctx.fff = 234\n  if (ctx.response.data.x) {\n    return 0\n  }\n  return [1, 2]\n}\n\n/**\n * 输出，用于动作列表里按键值取用，可以在上面函数里修改\n */\nexport const output = {}\n',
          },
          type: 'apis',
          apiIds: ['2yvo3bFtGWJeFe0buNMf2', 'MKR2DsvWBw5Xxo_JDK3bn'],
        },
        tracks: [
          {
            id: 'OAS_AfD4XQo-698Sr2gJO',
            eventType: 'CLICK',
            eventOptions: {
              action: 'CLICK_ACTION',
              params: {
                paramA: 3333,
              },
            },
            canRepeat: false,
          },
          {
            id: '08MENxBAr0zpQdndoF9ly',
            eventType: 'PV',
            eventOptions: {
              page: 'EDITOR',
              params: {
                project: '${git 工程}',
                path: '${工程内的相对路径}',
                scene: '${场景}',
              },
            },
            canRepeat: true,
          },
        ],
        model: {
          code: '(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = exports.Eabc = void 0;\nfunction _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }\nfunction _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }\nvar Eabc = exports.Eabc = /*#__PURE__*/function (Eabc) {\n  Eabc[Eabc["Ax"] = 0] = "Ax";\n  Eabc[Eabc["Bjjjj"] = 1] = "Bjjjj";\n  return Eabc;\n}({});\n/**\n * 前端模型\n */\nvar FrontModel = exports["default"] = /*#__PURE__*/_createClass(\n/**\n * @param ctx 上下文\n */\nfunction FrontModel(ctx) {\n  _classCallCheck(this, FrontModel);\n  /**\n   * 撒旦法\n   */\n  _defineProperty(this, "abc", Eabc);\n  /**\n   * 撒的副科级\n   */\n  _defineProperty(this, "xyz", 234);\n  this.ctx = ctx;\n  console.log(\'FrontModel init\', ctx);\n});\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)',
          codeTS:
            "export enum Eabc {\n  Ax,\n  Bjjjj\n}\n\n/**\n * 前端模型\n */\nexport default class FrontModel {\n  /**\n   * 撒旦法\n   */\n  public abc = Eabc\n\n  /**\n   * 撒的副科级\n   */\n  public xyz = 234\n\n  /**\n   * @param ctx 上下文\n   */\n  public constructor(private ctx: Container) {\n    console.log('FrontModel init', ctx)\n  }\n}\n",
        },
        linkage: {
          autoRefreshByComponent: [
            'comp_m9fs3fQscfEEib8gEC0Gx',
            'comp_DdZC6mr0N3SWtMn0fHINi',
          ],
          componentDataParams: {
            common: ['comp_DdZC6mr0N3SWtMn0fHINi'],
            byRefreshType: {
              auto: ['comp_m9fs3fQscfEEib8gEC0Gx'],
            },
          },
        },
        flattenedView: {
          rootComponentId: 'root',
          components: [
            {
              type: '@ad/canal-components::Root',
              id: 'root',
              name: '根组件',
              props: {
                style: {
                  type: 'static',
                  value: {},
                },
              },
            },
            {
              id: 'comp_m9fs3fQscfEEib8gEC0Gx',
              type: '@ad/canal-components::Container',
              name: '容器',
              props: {
                enableLoading: true,
                style: {
                  type: 'static',
                  value: {
                    display: 'flex',
                  },
                },
              },
            },
            {
              id: 'comp_Bymdn0403$iCE7Jm70qfx',
              type: '@ad/canal-components::Text',
              name: '文本',
              props: {
                text: {
                  defaultValue: {
                    type: 'static',
                    value: '文本333',
                  },
                  transform: {
                    a: 3,
                    b: {
                      type: 'jsonata',
                      code: '$.response.data.x',
                    },
                  },
                  type: 'apis',
                  apiIds: [
                    '2yvo3bFtGWJeFe0buNMf2',
                    'FF3emRysI5QJc5nxCJJo2',
                    'MKR2DsvWBw5Xxo_JDK3bn',
                  ],
                },
                style: {
                  type: 'object',
                  value: {
                    height: '',
                    fontSize: '16px',
                    fontWeight: 700,
                    width: {
                      type: 'api',
                      apiId: 'FF3emRysI5QJc5nxCJJo2',
                      defaultValue: {
                        type: 'static',
                        value: null,
                      },
                      transform: {
                        a: 3,
                        b: {
                          type: 'jsonata',
                          code: '$.response.data.x',
                        },
                      },
                    },
                  },
                },
              },
              apis: {
                FF3emRysI5QJc5nxCJJo2: {
                  id: 'FF3emRysI5QJc5nxCJJo2',
                  service: 'ad.test.cc',
                  method: 'testAbc',
                },
              },
            },
            {
              id: 'comp_H3chYOt8DcTtIuQXN1JZL',
              type: '@ad/canal-components::Button',
              name: '按钮::JZL',
              props: {
                text: '按钮123',
                onClick: {
                  type: 'actions',
                  fns: [
                    {
                      type: 'refresh',
                      refreshType: 'submit',
                      params: {
                        a: 3,
                      },
                    },
                    {
                      type: 'track',
                      trackId: '08MENxBAr0zpQdndoF9ly',
                    },
                  ],
                },
                type: 'primary',
              },
            },
            {
              id: 'comp_kLhJQuM$WCD$Y9ks24wS6',
              type: '@ad/canal-components::Container',
              name: '子容器1',
            },
            {
              id: 'comp_PxcFo6IeXkmrdU6FcHA3x',
              type: '@ad/canal-components::Text',
              name: '文本::A3x',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_SR5u8SqywR7dljsoIjRqD',
              type: '@ad/canal-components::Container',
              name: '子容器2',
            },
            {
              id: 'comp_gFxoZTQ_CgW9JKmsnJtdS',
              type: '@ad/canal-components::Text',
              name: '文本::tdS',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_DdZC6mr0N3SWtMn0fHINi',
              type: '@ad/canal-components::Submodule',
              name: '子模块',
              props: {
                schemaId: '34234',
                params: {
                  type: 'static',
                  value: {
                    a: 3,
                    b: 4,
                    c: [
                      {
                        a: 3,
                        b: 4,
                      },
                      {
                        a: 3,
                        b: 4,
                      },
                      {
                        a: 3,
                        b: 4,
                      },
                      {
                        a: 3,
                        b: 4,
                      },
                      {
                        a: 3,
                        b: 4,
                        c: [
                          {
                            a: 3,
                            b: 4,
                          },
                          {
                            a: 3,
                            b: 4,
                          },
                          {
                            a: 3,
                            b: 4,
                          },
                          {
                            a: 3,
                            b: 4,
                          },
                        ],
                      },
                    ],
                  },
                },
              },
              apis: {
                AOqc5GmvnE5L__R637dzn: {
                  id: 'AOqc5GmvnE5L__R637dzn',
                  service: 'https://ad-diag.corp.kuaishou.com',
                  method: '/rest/dt/data/query',
                },
                ZTENxyDto5M7byxBJpmcf: {
                  id: 'ZTENxyDto5M7byxBJpmcf',
                  service: 'https://ad-diag.corp.kuaishou.com',
                  method: '/rest/dt/data/query111',
                  args: {
                    a: 3111,
                    b: {
                      type: 'jsonata',
                      code: '$.request.params.x',
                    },
                  },
                },
                'csCt5ckmSWOqcE6wlmPi-': {
                  id: 'csCt5ckmSWOqcE6wlmPi-',
                  service: 'ad.test.cc',
                  method: 'testAbc',
                  args: {
                    type: 'js',
                    code: 'function main() {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports["default"] = function _default(ctx) {\n  return {\n    a: 333323,\n    b: ctx.request.params.x\n  };\n};\n  })()\n  return module.exports.default.apply(null, arguments)\n}',
                    codeES:
                      '/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return {\n    a: 333323,\n    b: ctx.request.params.x,\n  }\n}\n',
                  },
                },
              },
            },
            {
              id: 'comp_T4ceWR$_G1h7UObzSsmWI',
              type: '@ad/canal-biz-components::todo',
              name: '按钮::mWI',
              props: {
                text: {
                  type: 'js',
                  code: '(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports.default = void 0;\nvar _lodash = _interopRequireDefault(require("lodash"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n/**\n* @param ctx 上下文\n*/\nvar _default = ctx => {\n  return `${_lodash.default.range(3)}|||${ctx.initParams?.abc}`;\n};\nexports.default = _default;\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)(ctx.runtime.container)',
                  defaultValue: {
                    type: 'static',
                    value: '文本',
                  },
                  codeTS:
                    "import _ from 'lodash'\n\n/**\n* @param ctx 上下文\n*/\nexport default (ctx: Container) => {\n  return `${_.range(3)}|||${ctx.initParams?.abc}`\n}\n",
                },
                style: {
                  type: 'static',
                  value: {
                    marginPadding: {},
                  },
                },
              },
            },
            {
              id: 'comp_wOqCPhSCVnDplr$i5V4nY',
              type: '@ad/canal-biz-components::todo',
              name: '按钮::4nY',
              props: {
                text: {
                  type: 'api',
                  apiId: 'tATZr08Zem00Wbr6mrAp6',
                  defaultValue: {
                    type: 'static',
                    value: '文本',
                  },
                  transform: {
                    a: {
                      x: 3,
                    },
                  },
                },
                style: {
                  type: 'object',
                  value: {
                    marginPadding: {
                      type: 'static',
                      value: {},
                    },
                    width: {
                      type: 'api',
                      apiId: 'ZV-4QLZwEXm0RxZDtFsUF',
                      defaultValue: {
                        type: 'static',
                      },
                      transform: {
                        type: 'js',
                        code: 'function main() {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports["default"] = function _default(ctx) {\n  return {\n    a: 3234234,\n    b: ctx.response.data.x\n  };\n};\n  })()\n  return module.exports.default.apply(null, arguments)\n}',
                        codeES:
                          '/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return {\n    a: 3234234,\n    b: ctx.response.data.x,\n  }\n}\n',
                      },
                    },
                  },
                },
                value: {
                  type: 'api',
                  apiId: 'tATZr08Zem00Wbr6mrAp6',
                  transform: {
                    type: 'jsonata',
                    code: '$.request.params.comp_5JzNCZUtNM5oE4I78yzJ-.selected',
                  },
                  defaultValue: {
                    type: 'static',
                  },
                },
              },
              apis: {
                tATZr08Zem00Wbr6mrAp6: {
                  id: 'tATZr08Zem00Wbr6mrAp6',
                  service: 'https://ad-diag.corp.kuaishou.com',
                  method: '/rest/dt/data/query',
                },
                'ZV-4QLZwEXm0RxZDtFsUF': {
                  id: 'ZV-4QLZwEXm0RxZDtFsUF',
                  service: 'ad.test.cc',
                  method: 'testAbc',
                  args: {
                    a: 3,
                  },
                },
              },
            },
            {
              id: 'comp_Nm1g3lk1Hyu8l_r$KWUVR',
              type: '@ad/canal-biz-components::17',
              name: '卡片::UVR',
              props: {
                size: 'default',
                title: {
                  type: 'array',
                  value: [
                    {
                      type: 'component',
                    },
                    {
                      type: 'component',
                    },
                  ],
                },
                extra: {
                  type: 'array',
                  value: [
                    {
                      type: 'component',
                    },
                    {
                      type: 'component',
                    },
                    {
                      type: 'component',
                    },
                    {
                      type: 'component',
                    },
                    {
                      type: 'component',
                    },
                    {
                      type: 'component',
                    },
                    {
                      type: 'component',
                    },
                  ],
                },
              },
            },
            {
              id: 'comp_aFY7EIgZ6jqF2DnPo8WPT',
              type: '@ad/canal-components::Text',
              name: '文本::WPT',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_$fimP3mzzuo9G9saZa3kP',
              type: '@ad/canal-components::Text',
              name: '文本::3kP',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_g5yhRMMP34L9bFvAvCUwi',
              type: '@ad/canal-components::Text',
              name: '文本::Uwi',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_R5R2B9ipFBhjNzF5R8nPI',
              type: '@ad/canal-components::Text',
              name: '文本::nPI',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_0CVSERKu5EOxbaOOsANCx',
              type: '@ad/canal-components::Text',
              name: '文本::NCx',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_apZX3Uk6q7FK9M_k44UEs',
              type: '@ad/canal-components::Text',
              name: '文本::UEs',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_bixjXxyTXNp8emaAYC$mq',
              type: '@ad/canal-components::Text',
              name: '文本::$mq',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_JYmQqFEBrK$sE04bi0pX5',
              type: '@ad/canal-components::Text',
              name: '文本::pX5',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_HCKQixsc8HW9eXThbEOY2',
              type: '@ad/canal-components::Text',
              name: '文本::OY2',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_7aVc8W8a7rpqwYDj8bdbr',
              type: '@ad/canal-components::Text',
              name: '文本::dbr',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_gf8PFa2j6jSXdYnIblS8b',
              type: '@ad/canal-components::Text',
              name: '文本::S8b',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_Qy7cjnTY9RfwT9Qco8PZD',
              type: '@ad/canal-components::Form',
              name: '表单::PZD',
            },
            {
              type: '@ad/canal-components::FormItem',
              name: '表单项::5oX',
              props: {
                label: '标签123',
                jsonRules: [
                  {
                    type: 'required',
                    errMsg: '必填234234',
                  },
                ],
              },
              id: 'comp_1J_gtNAakShkJFKc9q5oX',
            },
            {
              type: '@ad/canal-components::Checkbox',
              name: '多选框::eZD',
              props: {
                text: '显示',
              },
              id: 'comp_9Vw4cpj24TGifAsw0ceZD',
            },
            {
              type: '@ad/canal-components::FormItem',
              name: '表单项::Hxk',
              props: {
                label: '标签',
                jsonRules: [
                  {
                    type: 'required',
                    errMsg: '必填',
                  },
                ],
              },
              id: 'comp_y0JGLlmOlCLn1FVVC9Hxk',
            },
            {
              type: '@ad/canal-components::Input',
              name: '输入框::1L1',
              id: 'iii',
            },
            {
              type: '@ad/canal-components::Button',
              name: '提交按钮::qRx',
              props: {
                text: '提交',
                style: {
                  type: 'static',
                  value: {
                    margin: '50px',
                  },
                },
                type: 'primary',
                htmlType: 'submit',
              },
              id: 'comp_65zyXPbfQkA_5XAHhtqRx',
            },
            {
              id: 'comp_hjaSmcLdI2hwSxFYeNiuy',
              type: '@ad/canal-components::Table',
              name: '表格::iuy',
              props: {
                dataSource: [
                  {
                    key: '1',
                    name: 'John Brown',
                    age: 32,
                    address: 'New York No. 1 Lake Park',
                  },
                  {
                    key: '2',
                    name: 'Jim Green',
                    age: 42,
                    address: 'London No. 1 Lake Park',
                  },
                  {
                    key: '3',
                    name: 'Joe Black',
                    age: 32,
                    address: 'Sidney No. 1 Lake Park',
                  },
                ],
                emptyText: '暂无数据',
                pagination: {
                  type: 'static',
                  value: {
                    pageSizeOptions: ['10', '20', '50', '100'],
                  },
                },
              },
            },
            {
              type: '@ad/canal-components::TableColumn',
              name: '表格列名称::VVV',
              props: {
                title: '名称',
                dataIndex: 'name',
              },
              id: 'comp_SRSvvCtT1vEvz9D6cMVVV',
            },
            {
              type: '@ad/canal-components::TableColumn',
              name: '表格列年龄::ue7',
              props: {
                title: '年龄',
                dataIndex: 'age',
              },
              id: 'comp_742R_tpENWEPEMwshRue7',
            },
            {
              type: '@ad/canal-components::TableColumn',
              name: '表格列操作::oqD',
              props: {
                title: '操作',
                dataIndex: 'op',
              },
              id: 'comp_ddcvSKhX$IE$hJUiFQoqD',
            },
            {
              type: '@ad/canal-components::TableCell',
              name: '表格单元格::4jA',
              props: {
                recordKey: '1',
              },
              id: 'comp_Xkama7SnDC2M3JASKi4jA',
            },
            {
              type: '@ad/canal-components::Button',
              name: '查询按钮::dH$',
              props: {
                text: '查询',
              },
              id: 'comp_ScDXECN5xMSjk8u7Z1dH$',
            },
            {
              type: '@ad/canal-components::TableCell',
              name: '表格单元格::58Q',
              props: {
                recordKey: '2',
              },
              id: 'comp_uf8ZcyPoQSh7Qxu1Uo58Q',
            },
            {
              type: '@ad/canal-components::Button',
              name: '查询按钮::jZ9',
              props: {
                text: '查询',
              },
              id: 'comp_$3FAblvlDwfwgX35DcjZ9',
            },
            {
              type: '@ad/canal-components::TableCell',
              name: '表格单元格::xmA',
              props: {
                recordKey: '3',
              },
              id: 'comp_meoEAXk2bmKOZ9TkglxmA',
            },
            {
              type: '@ad/canal-components::Button',
              name: '查询按钮::aJz',
              props: {
                text: '查询',
              },
              id: 'comp_cPjnYAPUTok2Lv1u9naJz',
            },
            {
              id: 'comp_Ek08xtc5gFAlnfC2$QuqT',
              type: '@ad/canal-biz-components::17',
              name: '卡片::uqT',
              props: {
                size: 'default',
                title: {
                  type: 'array',
                  value: [
                    {
                      type: 'component',
                    },
                  ],
                },
                extra: {
                  type: 'array',
                  value: [
                    {
                      type: 'component',
                    },
                  ],
                },
              },
            },
            {
              id: 'comp_nJpY11WH4SHiX7dMB$jZg',
              type: '@ad/canal-components::Button',
              name: '按钮::jZg',
              props: {
                text: '按钮123',
                onClick: {
                  type: 'actions',
                  fns: [
                    {
                      type: 'refresh',
                      refreshType: 'submit',
                      params: {
                        a: 3,
                      },
                    },
                    {
                      type: 'track',
                      trackId: '08MENxBAr0zpQdndoF9ly',
                    },
                  ],
                },
                type: 'primary',
              },
            },
            {
              id: 'comp_q4f9M2RlKV6JWQ1VBm3Hi',
              type: '@ad/canal-components::Text',
              name: '文本::3Hi',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_hXB0_pWP3ky55$SqlIGlI',
              type: '@ad/canal-components::Text',
              name: '文本::GlI',
              props: {
                text: '文本',
              },
            },
            {
              id: 'comp_gZSCCJtBA3gnmVONSlE50',
              type: '@ad/canal-components::Text',
              name: '文本::E50',
              props: {
                text: {
                  defaultValue: {
                    type: 'static',
                    value: '文本333',
                  },
                  transform: {
                    a: 3,
                    b: {
                      type: 'jsonata',
                      code: '$.response.data.x',
                    },
                  },
                  type: 'apis',
                  apiIds: [
                    '2yvo3bFtGWJeFe0buNMf2',
                    'pvRY5iTwp9pYkBgyfeoEQ',
                    'MKR2DsvWBw5Xxo_JDK3bn',
                  ],
                },
                style: {
                  type: 'object',
                  value: {
                    height: '',
                    fontSize: '16px',
                    fontWeight: 700,
                    width: {
                      type: 'api',
                      apiId: 'pvRY5iTwp9pYkBgyfeoEQ',
                      defaultValue: {
                        type: 'static',
                        value: null,
                      },
                      transform: {
                        a: 3,
                        b: {
                          type: 'jsonata',
                          code: '$.response.data.x',
                        },
                      },
                    },
                  },
                },
              },
              apis: {
                pvRY5iTwp9pYkBgyfeoEQ: {
                  id: 'pvRY5iTwp9pYkBgyfeoEQ',
                  service: 'ad.test.cc',
                  method: 'testAbc',
                },
              },
            },
          ],
          childComponentIdMap: {
            comp_m9fs3fQscfEEib8gEC0Gx: [
              'comp_Bymdn0403$iCE7Jm70qfx',
              'comp_H3chYOt8DcTtIuQXN1JZL',
              'comp_kLhJQuM$WCD$Y9ks24wS6',
              'comp_SR5u8SqywR7dljsoIjRqD',
            ],
            comp_kLhJQuM$WCD$Y9ks24wS6: ['comp_PxcFo6IeXkmrdU6FcHA3x'],
            comp_SR5u8SqywR7dljsoIjRqD: ['comp_gFxoZTQ_CgW9JKmsnJtdS'],
            root: [
              'comp_m9fs3fQscfEEib8gEC0Gx',
              'comp_DdZC6mr0N3SWtMn0fHINi',
              'comp_T4ceWR$_G1h7UObzSsmWI',
              'comp_wOqCPhSCVnDplr$i5V4nY',
              'comp_Nm1g3lk1Hyu8l_r$KWUVR',
              'comp_7aVc8W8a7rpqwYDj8bdbr',
              'comp_gf8PFa2j6jSXdYnIblS8b',
              'comp_Qy7cjnTY9RfwT9Qco8PZD',
              'comp_hjaSmcLdI2hwSxFYeNiuy',
              'comp_Ek08xtc5gFAlnfC2$QuqT',
            ],
            comp_Nm1g3lk1Hyu8l_r$KWUVR: [
              {
                id: 'comp_aFY7EIgZ6jqF2DnPo8WPT',
                path: ['props', 'title', 'value', 0, 'value'],
              },
              {
                id: 'comp_$fimP3mzzuo9G9saZa3kP',
                path: ['props', 'title', 'value', 1, 'value'],
              },
              {
                id: 'comp_g5yhRMMP34L9bFvAvCUwi',
                path: ['props', 'extra', 'value', 0, 'value'],
              },
              {
                id: 'comp_R5R2B9ipFBhjNzF5R8nPI',
                path: ['props', 'extra', 'value', 1, 'value'],
              },
              {
                id: 'comp_0CVSERKu5EOxbaOOsANCx',
                path: ['props', 'extra', 'value', 2, 'value'],
              },
              {
                id: 'comp_apZX3Uk6q7FK9M_k44UEs',
                path: ['props', 'extra', 'value', 3, 'value'],
              },
              {
                id: 'comp_bixjXxyTXNp8emaAYC$mq',
                path: ['props', 'extra', 'value', 4, 'value'],
              },
              {
                id: 'comp_JYmQqFEBrK$sE04bi0pX5',
                path: ['props', 'extra', 'value', 5, 'value'],
              },
              {
                id: 'comp_HCKQixsc8HW9eXThbEOY2',
                path: ['props', 'extra', 'value', 6, 'value'],
              },
            ],
            comp_1J_gtNAakShkJFKc9q5oX: ['comp_9Vw4cpj24TGifAsw0ceZD'],
            comp_Qy7cjnTY9RfwT9Qco8PZD: [
              'comp_1J_gtNAakShkJFKc9q5oX',
              'comp_y0JGLlmOlCLn1FVVC9Hxk',
              'comp_65zyXPbfQkA_5XAHhtqRx',
            ],
            comp_y0JGLlmOlCLn1FVVC9Hxk: ['iii'],
            comp_hjaSmcLdI2hwSxFYeNiuy: [
              'comp_SRSvvCtT1vEvz9D6cMVVV',
              'comp_742R_tpENWEPEMwshRue7',
              'comp_ddcvSKhX$IE$hJUiFQoqD',
            ],
            comp_Xkama7SnDC2M3JASKi4jA: ['comp_ScDXECN5xMSjk8u7Z1dH$'],
            comp_ddcvSKhX$IE$hJUiFQoqD: [
              'comp_Xkama7SnDC2M3JASKi4jA',
              'comp_uf8ZcyPoQSh7Qxu1Uo58Q',
              'comp_meoEAXk2bmKOZ9TkglxmA',
            ],
            comp_uf8ZcyPoQSh7Qxu1Uo58Q: ['comp_$3FAblvlDwfwgX35DcjZ9'],
            comp_meoEAXk2bmKOZ9TkglxmA: ['comp_cPjnYAPUTok2Lv1u9naJz'],
            comp_Ek08xtc5gFAlnfC2$QuqT: [
              'comp_nJpY11WH4SHiX7dMB$jZg',
              'comp_q4f9M2RlKV6JWQ1VBm3Hi',
              {
                id: 'comp_hXB0_pWP3ky55$SqlIGlI',
                path: ['props', 'title', 'value', 0, 'value'],
              },
              {
                id: 'comp_gZSCCJtBA3gnmVONSlE50',
                path: ['props', 'extra', 'value', 0, 'value'],
              },
            ],
          },
        },
        apis: {
          '2yvo3bFtGWJeFe0buNMf2': {
            id: '2yvo3bFtGWJeFe0buNMf2',
            service: 'https://ad-diag.corp.kuaishou.com',
            method: '/rest/dt/data/query',
            args: {
              type: 'js',
              code: 'function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports["default"] = function _default(ctx) {\n  return {\n    a: 3,\n    b: ctx.request.params.x\n  };\n};\n  })()\n  return module.exports.default.apply(null, mainArg0.args)\n}',
              codeES:
                '/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return {\n    a: 3,\n    b: ctx.request.params.x,\n  }\n}\n',
            },
          },
          MKR2DsvWBw5Xxo_JDK3bn: {
            id: 'MKR2DsvWBw5Xxo_JDK3bn',
            service: 'ad.test.cc',
            method: 'testAbc',
          },
        },
      }),
    },
  },
  'GET /rest/canal/module/global-detail': {
    result: 1,
    msg: '请求成功',
    data: {
      id: '1ebd5c02-7480-49a3-a20e-94dd9542f1a8',
      name: 'ggx345',
      type: 100,
      containerType: 0,
      businessDomainCode: 'ad.productivity',
      createUser: 'lishuihua',
      updateUser: 'lishuihua',
      createTime: 1736999147177,
      globalModuleLock: null,
      version: '88',
      content:
        '{"globalBackModel":{"code":"function main(mainArg0) {\\n  const exports = {}, module = {}\\n  module.exports = exports\\n  ;(function() {\\n    \\"use strict\\";\\n\\nfunction _typeof(o) { \\"@babel/helpers - typeof\\"; return _typeof = \\"function\\" == typeof Symbol && \\"symbol\\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \\"function\\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \\"symbol\\" : typeof o; }, _typeof(o); }\\nObject.defineProperty(exports, \\"__esModule\\", {\\n  value: true\\n});\\nexports[\\"default\\"] = void 0;\\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\\"value\\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \\"prototype\\", { writable: false }); return Constructor; }\\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \\"string\\"); return \\"symbol\\" == _typeof(i) ? i : String(i); }\\nfunction _toPrimitive(t, r) { if (\\"object\\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \\"default\\"); if (\\"object\\" != _typeof(i)) return i; throw new TypeError(\\"@@toPrimitive must return a primitive value.\\"); } return (\\"string\\" === r ? String : Number)(t); }\\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\\"Cannot call a class as a function\\"); } }\\n/**\\n * 全局后端模型\\n */\\nvar GlobalBackModel = exports[\\"default\\"] = /*#__PURE__*/_createClass(function GlobalBackModel(ctx) {\\n  _classCallCheck(this, GlobalBackModel);\\n  this.ctx = ctx;\\n  console.log(\'GlobalBackModel2 init\', ctx);\\n});\\n  })()\\n  const M = module.exports.default\\n  return new M(...mainArg0.args)\\n}","codeES":"/**\\n * 全局后端模型\\n */\\nexport default class GlobalBackModel {\\n  constructor(ctx) {\\n    this.ctx = ctx\\n    console.log(\'GlobalBackModel2 init\', ctx)\\n  }\\n}\\n"},"globalFrontModel":{"code":"(function(deps) {\\n  var exports = {}, module = {}\\n  module.exports = exports\\n  ;(function() {\\n    \\"use strict\\";\\n\\nObject.defineProperty(exports, \\"__esModule\\", {\\n  value: true\\n});\\nexports[\\"default\\"] = void 0;\\nfunction _typeof(o) { \\"@babel/helpers - typeof\\"; return _typeof = \\"function\\" == typeof Symbol && \\"symbol\\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \\"function\\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \\"symbol\\" : typeof o; }, _typeof(o); }\\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\\"value\\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \\"prototype\\", { writable: false }); return Constructor; }\\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\\"Cannot call a class as a function\\"); } }\\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \\"string\\"); return \\"symbol\\" == _typeof(i) ? i : String(i); }\\nfunction _toPrimitive(t, r) { if (\\"object\\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \\"default\\"); if (\\"object\\" != _typeof(i)) return i; throw new TypeError(\\"@@toPrimitive must return a primitive value.\\"); } return (\\"string\\" === r ? String : Number)(t); }\\n/**\\n * 全局前端模型\\n */\\nvar GlobalFrontModel = exports[\\"default\\"] = /*#__PURE__*/_createClass(function GlobalFrontModel(ctx) {\\n  _classCallCheck(this, GlobalFrontModel);\\n  _defineProperty(this, \\"abc\\", \'ddd3-fff343\');\\n  this.ctx = ctx;\\n  console.log(\'GlobalFrontModel init d3\', ctx);\\n  console.log(\'testcreatebykjz\');\\n});\\n  })()\\n  return module.exports.default\\n  function require(name) {\\n    return deps[name]\\n  }\\n})(ctx.runtime.container.dependencies)","codeTS":"/**\\n * 全局前端模型\\n */\\nexport default class GlobalFrontModel {\\n  abc = \'ddd3-fff343\' as const\\n\\n  public constructor(private ctx: Container) {\\n    console.log(\'GlobalFrontModel init d3\', ctx)\\n    console.log(\'testcreatebykjz\')\\n  }\\n}\\n"}}',
      canEdit: true,
      changeId: 'ad.productivity_1701849745805_315741',
      updateTime: 1749462625930,
    },
    traceId: '646a43a4-5e85-4875-bc7f-0735c731e11d',
  },
  'POST /rest/canal/module/update': {
    msg: '请求成功',
    result: 1,
  },
  'POST /rest/canal/module/getPermission': (req, res) => {
    res.json({
      result: 1,
      msg: '请求成功',
      data: {
        editingRoom: {
          user: {
            userName: '李水华',
            userCode: 'lishuihua',
            avatar:
              'https://static.yximgs.com/udata/pkg/KS-IS-AVATAR-PRODUCTION/44101/E9238B9C4F2FA4F7DB9B545FBEA4BD88',
            department: '商业化技术部',
          },
          roomId: req.body.roomId,
        },
      },
      traceId: 'c1299c1a-1ec2-45ff-b232-51ddbe8f04d5',
    })
  },
  'POST /rest/canal/module/listByChangeId': {
    result: 1,
    msg: '请求成功',
    data: {
      list: [
        {
          id: '1ebd5c02-7480-49a3-a20e-94dd9542f1a8',
          moduleId: '1ebd5c02-7480-49a3-a20e-94dd9542f1a8',
          changeId: 'ad.productivityffb24b12-e44a-4f47-8b97-5471b015a7fa',
          moduleVersion: '59',
          checkoutModuleVersion: '57',
          rollbackFrom: '57',
          updateUser: 'pengzhijian',
          updateTime: 1749011149335,
          version: '59',
          name: 'ggx345',
          type: 100,
        },
      ],
    },
    traceId: '8c0a001a-3f69-4e77-9f06-bea98c499ea5',
  },
}
