export default {
  ['GET /rest/canal/expression-template']: {
    result: 1,
    msg: '请求成功',
    data: [
      {
        id: '10',
        name: 'FFFF<PERSON>',
        expression:
          '/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return {\n    a: 3,\n    b: ctx.response.data.x234234234,\n  }\n}\n',
        domainCode: 'ad.productivity',
        expressionType: 1,
        createdAt: 1704682930232,
        updatedAt: 1704682925973,
        creator: 'lishuihua',
        updater: 'lishuihua',
      },
      {
        id: '11',
        name: 'sd',
        expression:
          '/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx23232) => {\n  return {\n    a: 3,\n    b: ctx.response.data.x234234233333334,\n  }\n}\n',
        domainCode: 'ad.productivity',
        expressionType: 1,
        createdAt: 1704684178841,
        updatedAt: 1704684145870,
        creator: 'lishuihua',
        updater: 'lishuihua',
      },
    ],
    traceId: 'cdcae3b6-53cb-45a0-87f0-6bc458401ba9',
  },
}
