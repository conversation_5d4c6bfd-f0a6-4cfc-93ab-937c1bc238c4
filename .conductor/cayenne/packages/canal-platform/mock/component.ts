/* eslint-disable @typescript-eslint/naming-convention */
export default {
  ['POST /rest/canal/component/list']: {
    result: 1,
    msg: '',
    data: {
      list: [
        {
          /** 示例图url */
          coverUrl:
            'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.6/dist/button/icon.png',
          /** 关联物料中心组件id */
          associatedComponentId: null,
          /** 关联物料中心组件版本 */
          associatedComponentVersion: 'todo',
          /** 资源url */
          resourceUrl:
            'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.6/dist/button/index.069f5f93.js',
          /** 属性配置 */
          propsConfig: `
    {
      "props": [
        {
          "type": "group",
          "name": "内容",
          "items": [
            {
              "type": "group",
              "name": "常用",
              "path": [],
              "items": [
                {
                  "name": "文本",
                  "path": ["text"],
                  "defaultValue": "文本",
                  "setter": "String"
                },
                {
                  "name": "nnn",
                  "path": ["nnn"],
                  "defaultValue": "nnn",
                  "setter": "String"
                }
              ]
            }
          ]
        },
        { "type": "group", "name": "样式", "items": [] },
        {
          "type": "group",
          "name": "交互",
          "items": [
            {
              "type": "group",
              "name": "事件",
              "items": [
                {
                  "name": "点击",
                  "path": ["onClick"],
                  "setter": "Action"
                }
              ]
            }
          ]
        }
      ],
      "implements": {
        "style": true
      }
    }
    `,
          /** 事件key */
          eventKey: 'todo',
          /** 组件名称 */
          name: '按钮',
          /** 组件类型：web-0，H5-1，RN-2，Native-3 */
          type: 0,
          /** 业务域code */
          businessDomainCode: 'todo',
          /** 组件描述 */
          descs: 'descs',
          /** 是否容器组件 */
          isContainer: false,
          /** 组件id */
          id: 'todo',
          /** 组件版本 */
          version: 6,
        },
        {
          id: '17',
          name: '卡片',
          type: 0,
          descs: '卡片',
          businessDomainCode: 'ad.infra.luopan-inner',
          isContainer: true,
          createUser: 'wanglintao',
          updateUser: 'wanglintao',
          createTime: 1709803590778,
          coverUrl:
            'https://cdnfile.corp.kuaishou.com/kc/files/a/adCanal/card.be9570ee2388c8c5.png',
          associatedComponentId: null,
          associatedComponentVersion: null,
          propsConfig:
            '{\n  "props": [\n    {\n      "type": "group",\n      "name": "内容",\n      "items": [\n        {\n          "type": "group",\n          "name": "常用",\n          "path": [],\n          "items": [\n            {\n              "name": "尺寸",\n              "type": "field",\n              "path": [\n                "size"\n              ],\n              "defaultValue": "default",\n              "setter": {\n                "type": "Enum",\n                "props": {\n                  "options": [\n                    {\n                      "label": "常规",\n                      "value": "default"\n                    },\n                    {\n                      "label": "小",\n                      "value": "small"\n                    }\n                  ]\n                }\n              }\n            },\n            {\n              "name": "边框",\n              "type": "field",\n              "path": [\n                "bordered"\n              ],\n              "setter": {\n                "type": "Boolean"\n              }\n            },\n            {\n              "name": "内部卡片",\n              "type": "field",\n              "path": [\n                "type"\n              ],\n              "setter": {\n                "type": "Boolean"\n              }\n            }\n          ]\n        },\n        {\n          "type": "group",\n          "name": "卡片标题",\n          "path": [],\n          "items": [\n            {\n              "name": "卡片标题",\n              "type": "field",\n              "path": [\n                "title"\n              ],\n              "setter": {\n                "type": "Slot"\n              }\n            }\n          ]\n        },\n        {\n          "type": "group",\n          "name": "卡片右侧",\n          "path": [],\n          "items": [\n            {\n              "name": "卡片右侧",\n              "type": "field",\n              "path": [\n                "extra"\n              ],\n              "setter": {\n                "type": "Slot"\n              }\n            }\n          ]\n        }\n      ]\n    },\n    {\n      "type": "group",\n      "name": "样式",\n      "items": []\n    },\n    {\n      "type": "group",\n      "name": "渲染",\n      "items": []\n    }\n  ],\n  "implements": {\n    "container": true,\n    "style": true,\n    "condition": true\n  }\n}',
          resourceUrl:
            'https://p1.adkwai.com/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.02657d05.js',
          eventKey: null,
          version: '18',
        },
      ],
      pageInfo: {
        total: 1,
        pageNum: 1,
        pageSize: 10,
      },
    },
  },
  ['GET /rest/canal/component/detail']: {
    result: 1,
    msg: '',
    data: {
      /** 示例图url */
      coverUrl:
        'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.5/dist/button/icon.png',
      /** 关联物料中心组件id */
      associatedComponentId: '[127,"LineChart"]',
      /** 关联物料中心组件版本 */
      associatedComponentVersion: '0.0.1-alpha.4',
      /** 资源url */
      resourceUrl:
        'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js',
      /** 属性配置 */
      propsConfig: `
  {
    "props": [
      {
        "type": "group",
        "name": "内容",
        "items": [
          {
            "type": "group",
            "name": "常用",
            "path": [],
            "items": [
              {
                "name": "文本 old",
                "path": ["text"],
                "defaultValue": "文本 oo",
                "setter": "String"
              }
            ]
          }
        ]
      },
      { "type": "group", "name": "样式", "items": [] },
      {
        "type": "group",
        "name": "交互",
        "items": [
          {
            "type": "group",
            "name": "事件",
            "items": [
              {
                "name": "点击",
                "path": ["onClick"],
                "setter": "Action"
              }
            ]
          }
        ]
      }
    ],
    "implements": {
      "style": true
    }
  }
  `,
      /** 事件key */
      eventKey: 'todo',
      /** 组件名称 */
      name: '按钮5',
      /** 组件类型：web-0，H5-1，RN-2，Native-3 */
      type: 0,
      /** 业务域code */
      businessDomainCode: 'todo',
      /** 组件描述 */
      descs: 'descs',
      /** 是否容器组件 */
      isContainer: true,
      /** 组件id */
      id: 'todo',
      /** 组件版本 */
      version: 5,
    },
  },
  ['GET /rest/canal/component/structure/version/info']: {
    result: 1,
    msg: '',
    data: {
      '119': [
        {
          id: '141',
          associatedComponentVersion: '0.0.3',
          associatedComponentName: 'Module',
          version: '9',
          name: '模块',
        },
        {
          id: '140',
          associatedComponentVersion: '0.0.3',
          associatedComponentName: 'Field',
          version: '10',
          name: '字段',
        },
      ],
      '127': [
        {
          id: '320',
          associatedComponentVersion: '0.1.4',
          associatedComponentName: 'ChooseWhen',
          version: '4',
          name: '选择展示',
        },
      ],
    },
  },
}
