import { DESIGNER_QUERY } from '@/pages/designer/constants'
import type { CollaborativeModel } from '@/pages/designer/types'
import { getSimpleDetailBusinessDomain } from '@/services/backend/business_domain'
import type { Room } from '@/services/backend/models'
import { cancelPermission, getPermission } from '@/services/backend/module'
import { userStore } from '@/stores/user-store'
import type { Promisable } from '@ad/canal-shared'
import { sleep } from '@kael/schema-utils'
import { makeObservable, observable, runInAction } from 'mobx'
import { nanoid } from 'nanoid'

export interface CoeditStoreOptions {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 模块版本
   */
  moduleVersion: number
  /**
   * 模式
   */
  mode?: 'debug' | 'readonly'
  /**
   * 可保存
   */
  savable(): boolean
  /**
   * 初始 Schema 是脏的，就是初始化之后，是否有其他人又修改过
   */
  initSchemaIsDirty(): Promisable<boolean>
}

/**
 * 协同编辑仓库
 */
export class CoeditStore {
  /**
   * （当前）房间 ID
   */
  private readonly _roomId = nanoid()

  /**
   * 当前房间是否需要授予权限
   */
  private _grant = true

  /**
   * 当前房间是否需要授予权限
   */
  public get grant(): boolean {
    return this._grant
  }

  /**
   * 编辑中的房间
   */
  private _editingRoom: Room | null = null

  /**
   * 编辑中的房间
   */
  public get editingRoom(): Room | null {
    return this._editingRoom
  }

  /**
   * 是否已经初始化
   */
  private _isInited = false

  /**
   * 是否已经初始化
   */
  public get isInited(): boolean {
    return this._isInited
  }

  /**
   * 退出编辑信息
   */
  private _quitEditingMsg: string | null = null

  /**
   * 退出编辑信息
   */
  public get quitEditingMsg(): string | null {
    return this._quitEditingMsg
  }

  /**
   * 是调试模式
   */
  private _isDebugMode = false

  /**
   * 是只读模式
   */
  private _isReadonlyMode = false

  /**
   * 正在长轮询
   */
  private _isLoogPolling = false

  /**
   * 协作模式, 初始化值为单人模式
   */
  private _collaborativeModel: CollaborativeModel = 'single'

  /**
   * 在写作模式下的merge后的reload
   */
  private _isReloadAfterCollaborativMerge = false

  public set isReloadAfterCollaborativMerge(value: boolean) {
    this._isReloadAfterCollaborativMerge = value
  }

  public get isReloadAfterCollaborativMerge(): boolean {
    return this._isReloadAfterCollaborativMerge
  }

  /**
   * 协作模式获取
   */
  public get collaborativeModel(): CollaborativeModel {
    return this._collaborativeModel
  }
  /**
   * 初始化协作模式
   */
  private async _initCollaborativeModel(): Promise<void> {
    const {
      data: { collaborativeModel },
    } = await getSimpleDetailBusinessDomain({ code: DESIGNER_QUERY.domainCode })
    collaborativeModel && (this._collaborativeModel = collaborativeModel)
  }

  /**
   * 是否当前的房间在编辑
   */
  public get isCurrentRoomEditing(): boolean {
    return this._isDebugMode || this._roomId === this._editingRoom?.roomId
  }

  /**
   * 是否当前用户在编辑
   */
  public get isCurrentUserEditing(): boolean {
    return this._editingRoom?.user.userCode === userStore.user?.userCode
  }

  public constructor(private _options: CoeditStoreOptions) {
    this._isDebugMode = _options.mode === 'debug'
    this._isReadonlyMode = _options.mode === 'readonly'
    this._isLoogPolling = !this._isDebugMode

    makeObservable<
      CoeditStore,
      | '_grant'
      | '_editingRoom'
      | '_isInited'
      | '_quitEditingMsg'
      | '_collaborativeModel'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _grant: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _editingRoom: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isInited: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _quitEditingMsg: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _collaborativeModel: observable.ref,
    })
    this._loogPollingEditingRoom()
    this._initCollaborativeModel()

    window.addEventListener('beforeunload', (ev) => {
      if (
        _options.savable() &&
        !this._isDebugMode &&
        !this._isReadonlyMode &&
        !this._isReloadAfterCollaborativMerge
      ) {
        ev.preventDefault()
        return
      }
    })
    window.addEventListener('unload', () => {
      this._isLoogPolling = false
      if (this.isCurrentRoomEditing) {
        fetch('/rest/canal/module/cancelPermission', {
          method: 'POST',
          headers: {
            ['Content-Type']: 'application/json',
          },
          body: JSON.stringify({
            id: this._options.moduleId,
            version: this._options.moduleVersion,
            roomId: this._roomId,
          }),
          keepalive: true,
        })
      }
    })

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(window as any).debugCoeditStore = this
    }
  }

  /**
   * 长轮询编辑中的房间，5 秒一次
   */
  private async _loogPollingEditingRoom(): Promise<void> {
    for (;;) {
      if (this._isLoogPolling) {
        try {
          await this._updateEditingRoom()
        } catch (err) {
          console.error('_updateEditingRoom err', err)
        }
      }
      await sleep(5000)
    }
  }

  /**
   * 更新编辑中的房间
   */
  private async _updateEditingRoom(): Promise<void> {
    const ret = await getPermission({
      id: this._options.moduleId,
      version: this._options.moduleVersion,
      grant: this._grant,
      roomId: this._roomId,
    })
    const room = (ret.data.editingRoom || null) as Room | null
    // 自动取得权限时，如果设计器 Schema 跟线上不一致，则自动退出
    if (
      this._isInited &&
      !this.isCurrentRoomEditing &&
      room &&
      this._roomId === room.roomId &&
      (await this._options.initSchemaIsDirty())
    ) {
      await this.quitEditing('有其他用户保存过，请刷新页面获取最新模块内容')
    } else {
      runInAction(() => {
        this._editingRoom = room
        this._isInited = true
      })
    }
  }

  /**
   * 退出编辑
   */
  public async quitEditing(
    quitEditingMsg = '您已退出编辑，刷新页面可以重新获得权限',
  ): Promise<void> {
    runInAction(() => {
      this._grant = false
      this._quitEditingMsg = quitEditingMsg
    })
    this._isLoogPolling = false
    try {
      await cancelPermission({
        id: this._options.moduleId,
        version: this._options.moduleVersion,
        roomId: this._roomId,
      })
      await this._updateEditingRoom()
    } finally {
      this._isLoogPolling = true
    }
  }
}
