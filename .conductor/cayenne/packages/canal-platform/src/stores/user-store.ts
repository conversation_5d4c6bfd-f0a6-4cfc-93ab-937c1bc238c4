import type { User } from '@/services/backend/models'
import { userInfoUsingGet } from '@/services/backend/user'
import { makeObservable, observable, runInAction } from 'mobx'

/**
 * 用户仓库
 */
export class UserStore {
  /**
   * 等待用户
   */
  public readonly waitUser: Promise<User>

  /**
   * （当前）用户
   */
  private _user: User | null = null

  /**
   * （当前）用户
   */
  public get user(): User | null {
    return this._user
  }

  public constructor() {
    this.waitUser = this._initUser()
    makeObservable<UserStore, '_user'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _user: observable.ref,
    })

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(window as any).debugUserStore = this
    }
  }

  /**
   * 初始化用户
   */
  private async _initUser(): Promise<User> {
    const ret = await userInfoUsingGet()
    runInAction(() => {
      this._user = ret.data
    })
    return ret.data
  }
}

/**
 * 单例：用户仓库
 */
export const userStore = new UserStore()
