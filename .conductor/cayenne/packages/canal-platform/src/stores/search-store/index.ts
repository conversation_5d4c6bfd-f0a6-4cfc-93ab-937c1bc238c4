import { createQuickEncoder } from '@ad/canal-shared'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import type { E2ESchema } from '@ad/e2e-schema'
import type { MaybePromiseApis } from '@kael/shared'
import { createEndpoint, fromWebWorker } from '@remote-ui/rpc'
import { action, makeObservable, observable } from 'mobx'
import { CorsWorker as Worker } from './cors-worker'
import { RichSearchTask } from './rich-search-task'
import type {
  MainForSearchWorkerRpcApis,
  SearchResult,
  SearchWorkerForMainRpcApis,
} from './types'

export * from './types'
export * from './utils'

/**
 * 搜索仓库
 */
export abstract class SearchStore {
  /**
   * 关键字
   */
  protected $keyword = ''

  /**
   * 关键字
   */
  public get keyword(): typeof this.$keyword {
    return this.$keyword
  }

  /**
   * 当前任务
   */
  protected $currentTask: RichSearchTask | null = null

  /**
   * 当前任务
   */
  public get currentTask(): typeof this.$currentTask {
    return this.$currentTask
  }

  /**
   * RPC 终端
   */
  protected readonly $rpcEndpoint = createEndpoint<SearchWorkerForMainRpcApis>(
    fromWebWorker(
      new Worker(new URL('./worker/index.ts', import.meta.url)).getWorker(),
    ),
    { createEncoder: createQuickEncoder },
  )

  public constructor() {
    makeObservable<SearchStore, '$keyword' | '$currentTask'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      $keyword: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      $currentTask: observable.ref,
      setKeyword: action,
      search: action,
      cancelSearch: action,
    })
    const apis: MaybePromiseApis<MainForSearchWorkerRpcApis> = {
      getSchema: this.handleGetSchema.bind(this),
      sendSearchResult: this.handleSendSearchResult.bind(this),
      getComponentMaterialSchema:
        this.handleGetComponentMaterialSchema.bind(this),
    }
    this.$rpcEndpoint.expose(apis)
  }

  /**
   * 设置关键词
   * @param keyword 关键词
   */
  public setKeyword(keyword: string): void {
    this.$keyword = keyword
  }

  /**
   * 搜索
   * @param keyword 关键词
   * @param moduleIds 模块 ID
   * @param matchCase 匹配大小写，默认: false
   */
  public search(keyword: string, moduleIds: string[], matchCase = false): void {
    const currentTask = (this.$currentTask = new RichSearchTask(
      keyword,
      moduleIds,
      matchCase,
    ))
    this.$rpcEndpoint.call.search(currentTask.toJson())
  }

  /**
   * 取消
   */
  public cancelSearch(): void {
    const { currentTask } = this
    if (!currentTask) return
    this.$currentTask = null
    this.$rpcEndpoint.call.cancelSearch(currentTask.id)
  }

  /**
   * 获取 Schema
   * @param moduleId 模块 ID
   */
  protected abstract handleGetSchema(
    moduleId: string,
  ): Promise<E2ESchema | null>

  /**
   * 处理【获取组件物料 Schema】
   * @param moduleId 模块 ID
   * @param type 组件类型
   * @param version 版本
   */
  protected abstract handleGetComponentMaterialSchema(
    moduleId: string,
    type: string,
    version?: string,
  ): Promise<E2ERemoteComponentMaterialSchema | null>

  /**
   * 处理【发送搜索结果】
   * @param result 搜索结果
   */
  protected handleSendSearchResult(result: SearchResult): void {
    // console.log('SearchStore::handleSendSearchResult', { result })
    this.$currentTask?.addResult(result)
  }
}
