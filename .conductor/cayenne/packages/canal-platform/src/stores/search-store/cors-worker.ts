/**
 * 跨域 Worker
 */
export class CorsWorker {
  // 'https://cdnfile.corp.kuaishou.com/kc/files/a/adCanal/platform/staging/3843.ada659e9.async.js'
  public constructor(private _url: URL) {}

  /**
   * 获取 Worker
   */
  public getWorker(): Worker {
    const fileName = this._url.toString().match(/[^/]*$/)?.[0]
    const url =
      this._url.origin === location.origin || !fileName
        ? this._url
        : `${location.origin}/${fileName}`
    return new Worker(url)
  }
}
