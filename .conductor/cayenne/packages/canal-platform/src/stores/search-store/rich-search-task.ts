import { pick, uniqueId } from 'lodash'
import { action, makeObservable, observable } from 'mobx'
import {
  SearchTaskStatus,
  type SearchResult,
  type SearchResultItem,
  type SearchTask,
} from './types'

/**
 * 充血搜索任务
 */
export class RichSearchTask implements SearchTask {
  /**
   * ID
   */
  public readonly id = uniqueId('s')

  /**
   * 状态
   */
  public status = SearchTaskStatus.RUNNING

  /**
   * 结果条目
   */
  private _resultItems: SearchResultItem[] = []

  /**
   * 结果条目
   */
  public get resultItems(): typeof this._resultItems {
    return this._resultItems
  }

  public constructor(
    /**
     * 关键词
     */
    public readonly keyword: string,
    /**
     * 模块 ID
     */
    public readonly moduleIds: string[],
    /**
     * 匹配大小写
     */
    public readonly matchCase: boolean,
  ) {
    makeObservable<RichSearchTask, '_resultItems'>(this, {
      status: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _resultItems: observable.ref,
      addResult: action,
    })
  }

  /**
   * 转 JSON
   */
  public toJson(): SearchTask {
    return pick(this, 'id', 'keyword', 'moduleIds', 'matchCase')
  }

  /**
   * 添加结果
   * @param result 搜索结果
   */
  public addResult(result: SearchResult): void {
    if (this.id !== result.taskId) return
    this.status = result.taskStatus
    if (
      result.taskStatus !== SearchTaskStatus.CANCELED ||
      result.items.length
    ) {
      this._resultItems = [...this._resultItems, ...result.items]
    }
  }
}
