import {
  decodeJSExpr2BindIIFEFormValues,
  encodeBindIIFEFormValues2JSExpr,
  type BindIIFEFormValues,
} from '@/pages/designer/extensions/property/components/bind-iife-drawer/utils'
import {
  compileBackJsExpression,
  compileBackModel,
  compileFrontJsExpression,
  compileFrontModel,
} from '@/utils/babel/combos'
import { isJsonPrimitive, type AnyObject } from '@ad/canal-shared'
import {
  type E2ESchema,
  type E2ESchemaComponent,
  type E2ESchemaExpressionJS,
  type Linkage,
} from '@ad/e2e-schema'
import {
  INNER_JSON_PATH_IIFE_ACTIONS,
  dfsGenApiRelatedExpressionDetailByE2ESchema,
  dfsGenExpressionDetailByE2ESchema,
  e2eSchemaTraverseOptions,
  isApiExpression,
  isJsExpression,
} from '@ad/e2e-schema-utils'
import {
  dfsGenComponentDetailBySchema,
  dfsGenExpressionDetailBySchemaExpression,
  type ExpressionDetailOfSchema,
  type PropertyPath,
} from '@kael/schema-utils'
import {
  cloneDeep,
  difference,
  entries,
  get,
  isArray,
  isBoolean,
  isEqual,
  isNumber,
  isObject,
  isString,
  last,
  set,
  values,
} from 'lodash'
import type { SearchResultItem } from './types'

/**
 * 搜索结果条目模块分组
 */
export interface SearchResultItemModuleGroup {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 条目
   */
  items: SearchResultItemCategoryGroup[]
}

/**
 * 搜索结果条目分类分组
 */
export interface SearchResultItemCategoryGroup {
  /**
   * 分类
   */
  category: string
  /**
   * 条目
   */
  items: SearchResultItem[]
}

/**
 * 搜索结果条目分组
 * @param items 条目
 */
export function groupSearchResultItems(
  items: SearchResultItem[],
): SearchResultItemModuleGroup[] {
  const moduleGroupObjMap: Record<
    string,
    Record<string, SearchResultItem[] | undefined> | undefined
  > = {}
  for (const item of items) {
    const { moduleId, category } = item
    let categoryGroupObjMap = moduleGroupObjMap[moduleId]
    if (!categoryGroupObjMap) {
      categoryGroupObjMap = moduleGroupObjMap[moduleId] = {}
    }
    let ts = categoryGroupObjMap[category]
    if (!ts) {
      ts = categoryGroupObjMap[category] = []
    }
    ts.push(item)
  }
  return entries(moduleGroupObjMap).map(([moduleId, categoryGroupObjMap]) => ({
    moduleId,
    items: entries(categoryGroupObjMap).map(([category, ts]) => ({
      category,
      items: ts || [],
    })),
  }))
}

/**
 * 通过搜索结果条目替换端到端 Schema
 * @param schema 端到端 Schema
 * @param items 搜索结果条目
 * @param keyword 关键词
 * @param replaceText 替换文本
 */
export async function replaceSchemaBySearchResultItems(
  schema: E2ESchema,
  items: SearchResultItem[],
  keyword: string,
  replaceText: string,
): Promise<E2ESchema> {
  schema = cloneDeep(schema)
  // 反向遍历，避免下标混乱
  items = [...items].reverse()
  // 特殊情况处理：iife
  await replaceSchemaIIFE(schema, items, keyword, replaceText)
  /**
   * 所有组件集合
   */
  const allComponentSet = new Set(
    Array.from(dfsGenComponentDetailBySchema(schema), (cd) => cd.component),
  )
  /**
   * 组件替换 ID 表
   */
  const componentReplaceIdsMap = new Map<E2ESchemaComponent, [string, string]>()
  /**
   * 所有后端表达式集合
   */
  const allBackJsExpressionSet = new Set(
    Array.from(
      genBackJsExpressionDetailExcludeIIFE(schema),
      (ed) => ed.expression as E2ESchemaExpressionJS,
    ),
  )
  /**
   * 所有前端表达式集合
   */
  const allFrontJsExpressionSet = new Set(
    difference(
      Array.from(
        genJsExpressionDetailExcludeIIFE(schema),
        (ed) => ed.expression as E2ESchemaExpressionJS,
      ),
      [...allBackJsExpressionSet],
    ),
  )
  /**
   * 已访问的后端表达式集合
   */
  const visitedBackJsExpressionSet = new Set<E2ESchemaExpressionJS>()
  /**
   * 已访问的前端表达式集合
   */
  const visitedFrontJsExpressionSet = new Set<E2ESchemaExpressionJS>()
  for (const item of items) {
    const { jsonPath, isJsonKey, textIndex } = item.anchor
    // 特殊情况处理：iife
    if (isIIFEJsonPath(jsonPath)) {
      continue
    }
    const obj = get(schema, jsonPath.slice(0, -1))
    const key = last(jsonPath)
    // JS 表达式收集
    if (
      (['codeES', 'codeTS'] as (string | number | undefined)[]).includes(key)
    ) {
      if (isJsExpression(obj)) {
        if (allBackJsExpressionSet.has(obj)) {
          visitedBackJsExpressionSet.add(obj)
        } else if (allFrontJsExpressionSet.has(obj)) {
          visitedFrontJsExpressionSet.add(obj)
        }
      }
    }
    // 组件替换 ID 收集
    const isReplaceId = key === 'id' && allComponentSet.has(obj)
    let replaceIds = componentReplaceIdsMap.get(obj)
    if (isReplaceId && !replaceIds) {
      componentReplaceIdsMap.set(obj, (replaceIds = [obj.id, '']))
    }
    // 一般情况处理
    // console.log('before replaceJson', {
    //   v: get(schema, jsonPath),
    //   json: schema,
    //   jsonPath,
    //   isJsonKey,
    //   textIndex,
    //   keyword,
    //   replaceText,
    // })
    replaceJson({
      json: schema,
      jsonPath,
      isJsonKey,
      textIndex,
      keyword,
      replaceText,
    })
    // console.log('after replaceJson', {
    //   v: get(schema, jsonPath),
    // })
    // 组件替换 ID 收集
    if (replaceIds) {
      replaceIds[1] = obj.id
    }
  }
  // 编译模型
  if (
    items.some((item) => {
      const { jsonPath } = item.anchor
      return isFrontModelJsonPath(jsonPath)
    })
  ) {
    await compileFrontModel(schema.model)
  }
  if (
    items.some((item) => {
      const { jsonPath } = item.anchor
      return isBackModelJsonPath(jsonPath)
    })
  ) {
    await compileBackModel(schema.backModel)
  }
  // 编译后端表达式
  for (const jsExpression of visitedBackJsExpressionSet) {
    await compileBackJsExpression(jsExpression)
  }
  // 编译前端表达式
  for (const jsExpression of visitedFrontJsExpressionSet) {
    await compileFrontJsExpression(jsExpression)
  }
  // 替换组件 ID
  for (const replaceIds of componentReplaceIdsMap.values()) {
    replaceLinkageComponentId(schema.linkage, ...replaceIds)
  }
  return schema
}

/**
 * 是立即执行的函数表达式 JSON 路径
 * @param jsonPath JSON 路径
 */
function isIIFEJsonPath(jsonPath: PropertyPath): boolean {
  return jsonPath[0] === 'iife'
}

/**
 * 是立即执行的函数表达式动作 JSON 路径
 * @param jsonPath JSON 路径
 */
function isIIFEActionsJsonPath(jsonPath: PropertyPath): boolean {
  return isEqual(
    jsonPath.slice(0, INNER_JSON_PATH_IIFE_ACTIONS.length),
    INNER_JSON_PATH_IIFE_ACTIONS,
  )
}

/**
 * 是前端模型 JSON 路径
 * @param jsonPath JSON 路径
 */
function isFrontModelJsonPath(jsonPath: PropertyPath): boolean {
  return jsonPath[0] === 'model'
}

/**
 * 是后端模型 JSON 路径
 * @param jsonPath JSON 路径
 */
function isBackModelJsonPath(jsonPath: PropertyPath): boolean {
  return jsonPath[0] === 'backModel'
}

/**
 * 替换 Schema 立即执行的函数表达式，原地生效
 * @param schema 端到端 Schema
 * @param items 搜索结果条目
 * @param keyword 关键词
 * @param replaceText 替换文本
 */
async function replaceSchemaIIFE(
  schema: E2ESchema,
  items: SearchResultItem[],
  keyword: string,
  replaceText: string,
): Promise<void> {
  const { iife } = schema
  if (!iife) return
  items = items.filter((item) => {
    const { jsonPath } = item.anchor
    return isIIFEJsonPath(jsonPath)
  })
  if (!items.length) return
  // 非动作处理
  for (const item of items) {
    const { jsonPath, isJsonKey, textIndex } = item.anchor
    if (!isIIFEActionsJsonPath(jsonPath)) {
      replaceJson({
        json: schema,
        jsonPath,
        isJsonKey,
        textIndex,
        keyword,
        replaceText,
      })
    }
  }
  // 动作处理
  if (isApiExpression(iife)) {
    if (isJsExpression(iife.transform)) {
      const iifeFormValues = decodeJSExpr2BindIIFEFormValues(
        iife.transform,
      ) as BindIIFEFormValues
      for (const item of items) {
        const { jsonPath, isJsonKey, textIndex } = item.anchor
        if (isIIFEActionsJsonPath(jsonPath)) {
          replaceJson({
            json: iifeFormValues.actions,
            jsonPath: jsonPath.slice(INNER_JSON_PATH_IIFE_ACTIONS.length),
            isJsonKey,
            textIndex,
            keyword,
            replaceText,
          })
        }
      }
      for (const action of iifeFormValues.actions) {
        if (action.type === 'exec-effect') {
          await compileFrontJsExpression(action.expr)
        }
      }
      iife.transform = await encodeBindIIFEFormValues2JSExpr(iifeFormValues)
    }
  }
}

/**
 * 替换 JSON 选项
 */
interface ReplaceJsonOptions {
  /**
   * JSON
   */
  json: AnyObject
  /**
   * JSON 路径
   */
  jsonPath: PropertyPath
  /**
   * 是 JSON 键值
   */
  isJsonKey: boolean
  /**
   * 文本下标，命中文本在搜索文本中的下标
   */
  textIndex: number
  /**
   * 关键词
   */
  keyword: string
  /**
   * 替换文本
   */
  replaceText: string
}

/**
 * 替换 JSON （的内容），原地生效
 * @param options 选项
 */
function replaceJson({
  json,
  jsonPath,
  isJsonKey,
  textIndex,
  keyword,
  replaceText,
}: ReplaceJsonOptions): void {
  const matchKeyword = (text: string): boolean => {
    return text.slice(textIndex, textIndex + keyword.length) === keyword
  }
  const replaceKeyword = (text: string): string => {
    return (
      text.slice(0, textIndex) +
      replaceText +
      text.slice(textIndex + keyword.length)
    )
  }
  if (isJsonKey) {
    const key = last(jsonPath)
    if (!isString(key) || !matchKeyword(key)) {
      return
    }
    const obj: AnyObject = get(json, jsonPath.slice(0, -1))
    if (isArray(obj) || !isObject(obj)) {
      return
    }
    const newKey = replaceKeyword(key)
    obj[newKey] = obj[key]
    delete obj[key]
  } else {
    const value = get(json, jsonPath)
    const valueStr = `${value}`
    if (!isJsonPrimitive(value) || !matchKeyword(valueStr)) {
      return
    }
    let newValue = value
    if (isBoolean(newValue)) {
      if (
        ['true', 'false'].includes(keyword) &&
        ['true', 'false'].includes(replaceText)
      ) {
        newValue = replaceText === 'true'
      } else {
        newValue = valueStr
      }
    } else if (isNumber(newValue)) {
      const newString = replaceKeyword(valueStr)
      const newNumber = Number(newString)
      if (newString === String(newNumber)) {
        newValue = newNumber
      } else {
        newValue = newString
      }
    } else {
      newValue = replaceKeyword(valueStr)
    }
    set(json, jsonPath, newValue)
  }
}

/**
 * 获取后端 JS 表达式路径，不含 iife
 * @param schema 端到端 Schema
 */
function* genBackJsExpressionDetailExcludeIIFE(
  schema: E2ESchema,
): Generator<ExpressionDetailOfSchema<E2ESchema>> {
  for (const ed of dfsGenApiRelatedExpressionDetailByE2ESchema(schema)) {
    if (isJsExpression(ed.expression)) {
      yield ed
    }
  }
  for (const ed of dfsGenExpressionDetailByE2ESchema(schema, { iife: false })) {
    if (isApiExpression(ed.expression)) {
      for (const d of dfsGenExpressionDetailBySchemaExpression(
        ed.expression,
        ed.componentIdPath,
        ed.path,
        e2eSchemaTraverseOptions,
      )) {
        if (isJsExpression(d.expression)) {
          yield d
        }
      }
    }
  }
}

/**
 * 获取 JS 表达式路径，不含 iife
 * @param schema 端到端 Schema
 */
function* genJsExpressionDetailExcludeIIFE(
  schema: E2ESchema,
): Generator<ExpressionDetailOfSchema<E2ESchema>> {
  for (const ed of dfsGenExpressionDetailByE2ESchema(schema, { iife: false })) {
    if (isJsExpression(ed.expression)) {
      yield ed
    }
  }
}

/**
 * 替换联动里的组件 ID
 * @param linkage 联动
 * @param sourceId 源组件 ID
 * @param targetId 目标组件 ID
 */
export function replaceLinkageComponentId(
  linkage: Linkage | undefined,
  sourceId: string,
  targetId: string,
): void {
  if (!linkage) return
  const { componentDataParams, autoRefreshByComponent } = linkage
  for (const arr of [
    autoRefreshByComponent,
    componentDataParams?.common,
    ...values(componentDataParams?.byRefreshType),
  ]) {
    if (!arr) continue
    for (let i = 0; i < arr.length; i++) {
      if (arr[i] === sourceId) {
        arr[i] = targetId
      }
    }
  }
}
