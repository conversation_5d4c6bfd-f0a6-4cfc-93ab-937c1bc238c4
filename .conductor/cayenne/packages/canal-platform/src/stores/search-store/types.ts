import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import type { E2ESchema } from '@ad/e2e-schema'
import type { PropertyPath } from '@kael/schema-utils'

/**
 * 搜索 Worker 为主页面提供的 RPC 接口
 */
export interface SearchWorkerForMainRpcApis {
  /**
   * 搜索，自动取消上一次搜索
   * @param task 任务
   */
  search(task: SearchTask): void
  /**
   * 取消搜索
   * @param taskId 任务 ID
   */
  cancelSearch(taskId: string): void
  /**
   * 删除 Schema 缓存
   * @param moduleIds 模块 ID
   */
  deleteSchemaCache(moduleIds: string[]): void
}

/**
 * 搜索任务
 */
export interface SearchTask {
  /**
   * ID
   */
  id: string
  /**
   * 关键词
   */
  keyword: string
  /**
   * 模块 ID
   */
  moduleIds: string[]
  /**
   * 匹配大小写
   */
  matchCase: boolean
}

/**
 * 主页面为搜索 Worker 提供的 RPC 接口
 */
export interface MainForSearchWorkerRpcApis {
  /**
   * 获取 Schema
   * @param moduleId 模块 ID
   */
  getSchema(moduleId: string): E2ESchema | null
  /**
   * 获取组件物料 Schema
   * @param moduleId 模块 ID
   * @param type 组件类型
   * @param version 版本
   */
  getComponentMaterialSchema(
    moduleId: string,
    type: string,
    version?: string,
  ): E2ERemoteComponentMaterialSchema | null
  /**
   * 发送搜索结果
   * @param result 搜索结果
   */
  sendSearchResult(result: SearchResult): void
}

/**
 * 搜索结果
 */
export interface SearchResult {
  /**
   * 任务 ID
   */
  taskId: string
  /**
   * 搜索任务状态
   */
  taskStatus: SearchTaskStatus
  /**
   * 结果条目
   */
  items: SearchResultItem[]
}

/**
 * 搜索任务状态
 */
export enum SearchTaskStatus {
  /**
   * 运行中
   */
  RUNNING = 'RUNNING',
  /**
   * 已完成
   */
  FINISHED = 'FINISHED',
  /**
   * 已取消
   */
  CANCELED = 'CANCELED',
}

/**
 * 搜索结果条目
 */
export interface SearchResultItem {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 分类
   */
  category: string
  /**
   * 内容
   */
  content: SearchContentItem[]
  /**
   * 锚点
   */
  anchor: SearchAnchor
}

/**
 * 搜索内容条目
 */
export interface SearchContentItem {
  /**
   * 是否高亮
   */
  isHighlight: boolean
  /**
   * 文本
   */
  text: string
}

/**
 * 搜索锚点
 */
export type SearchAnchor = ComponentSearchAnchor | LinkageSearchAnchor

/**
 * 搜索锚点：基础部分
 */
export interface SearchAnchorBase {
  /**
   * JSON 路径
   */
  jsonPath: PropertyPath
  /**
   * 是 JSON 键值
   */
  isJsonKey: boolean
  /**
   * 文本下标，命中文本在搜索文本中的下标
   */
  textIndex: number
}

/**
 * 组件搜索锚点
 */
export type ComponentSearchAnchor =
  | ComponentPropTypeSearchAnchor
  | ComponentPropPathSearchAnchor

/**
 * 组件搜索锚点：基础部分
 */
export interface ComponentSearchAnchorBase extends SearchAnchorBase {
  /**
   * 类型
   */
  type: 'component'
  /**
   * 组件 ID
   */
  componentId: string
}

/**
 * 组件属性类型搜索锚点
 */
export type ComponentPropTypeSearchAnchor =
  | ComponentPropTypeOtherSearchAnchor
  | ComponentPropTypeAPISearchAnchor

/**
 * 组件属性类型其他搜索锚点
 */
export interface ComponentPropTypeOtherSearchAnchor
  extends ComponentSearchAnchorBase {
  /**
   * 属性类型
   */
  propType:
    | 'id'
    | 'name'
    | 'value'
    | 'effect'
    | 'track'
    | 'iife'
    | 'data'
    | 'frontModel'
    | 'backModel'
}

/**
 * 组件属性类型接口搜索锚点
 */
export interface ComponentPropTypeAPISearchAnchor
  extends ComponentSearchAnchorBase {
  /**
   * 属性类型
   */
  propType: 'api'
  /**
   * 接口 ID
   */
  apiId: string
}

/**
 * 组件属性路径搜索锚点
 */
export interface ComponentPropPathSearchAnchor
  extends ComponentSearchAnchorBase {
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称路径
   */
  propNamePath: string[]
}

/**
 * 联动搜索锚点
 */
export interface LinkageSearchAnchor extends SearchAnchorBase {
  /**
   * 类型
   */
  type: 'linkage'
}
