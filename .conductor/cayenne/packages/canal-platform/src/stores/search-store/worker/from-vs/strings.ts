/**
 * See http://en.wikipedia.org/wiki/Surrogate_pair
 */
export function isHighSurrogate(charCode: number): boolean {
  return 0xd800 <= charCode && charCode <= 0xdbff
}

/**
 * See http://en.wikipedia.org/wiki/Surrogate_pair
 */
export function isLowSurrogate(charCode: number): boolean {
  return 0xdc00 <= charCode && charCode <= 0xdfff
}

/**
 * See http://en.wikipedia.org/wiki/Surrogate_pair
 */
export function computeCodePoint(
  highSurrogate: number,
  lowSurrogate: number,
): number {
  return ((highSurrogate - 0xd800) << 10) + (lowSurrogate - 0xdc00) + 0x10000
}

/**
 * get the code point that begins at offset `offset`
 */
export function getNextCodePoint(
  str: string,
  len: number,
  offset: number,
): number {
  const charCode = str.charCodeAt(offset)
  if (isHighSurrogate(charCode) && offset + 1 < len) {
    const nextCharCode = str.charCodeAt(offset + 1)
    if (isLowSurrogate(nextCharCode)) {
      return computeCodePoint(charCode, nextCharCode)
    }
  }
  return charCode
}

/**
 * Escapes regular expression characters in a given string
 */
export function escapeRegExpCharacters(value: string): string {
  // eslint-disable-next-line no-useless-escape
  return value.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g, '\\$&')
}

export interface RegExpOptions {
  matchCase?: boolean
  wholeWord?: boolean
  multiline?: boolean
  global?: boolean
  unicode?: boolean
}

export function createRegExp(
  searchString: string,
  isRegex: boolean,
  options: RegExpOptions = {},
): RegExp {
  if (!searchString) {
    throw new Error('Cannot create regex from empty string')
  }
  if (!isRegex) {
    searchString = escapeRegExpCharacters(searchString)
  }
  if (options.wholeWord) {
    if (!/\B/.test(searchString.charAt(0))) {
      searchString = '\\b' + searchString
    }
    if (!/\B/.test(searchString.charAt(searchString.length - 1))) {
      searchString = searchString + '\\b'
    }
  }
  let modifiers = ''
  if (options.global) {
    modifiers += 'g'
  }
  if (!options.matchCase) {
    modifiers += 'i'
  }
  if (options.multiline) {
    modifiers += 'm'
  }
  if (options.unicode) {
    modifiers += 'u'
  }

  return new RegExp(searchString, modifiers)
}
