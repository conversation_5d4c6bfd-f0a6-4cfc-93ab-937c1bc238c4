/* eslint-disable @typescript-eslint/explicit-member-accessibility */
/* eslint-disable @typescript-eslint/naming-convention */
import { CharCode } from './char-code'
import * as strings from './strings'
import {
  WordCharacterClass,
  WordCharacterClassifier,
} from './word-character-classifier'

function leftIsWordBounday(
  wordSeparators: WordCharacterClassifier,
  text: string,
  _textLength: number,
  matchStartIndex: number,
  matchLength: number,
): boolean {
  if (matchStartIndex === 0) {
    // Match starts at start of string
    return true
  }

  const charBefore = text.charCodeAt(matchStartIndex - 1)
  if (wordSeparators.get(charBefore) !== WordCharacterClass.Regular) {
    // The character before the match is a word separator
    return true
  }

  if (
    charBefore === CharCode.CarriageReturn ||
    charBefore === CharCode.LineFeed
  ) {
    // The character before the match is line break or carriage return.
    return true
  }

  if (matchLength > 0) {
    const firstCharInMatch = text.charCodeAt(matchStartIndex)
    if (wordSeparators.get(firstCharInMatch) !== WordCharacterClass.Regular) {
      // The first character inside the match is a word separator
      return true
    }
  }

  return false
}

function rightIsWordBounday(
  wordSeparators: WordCharacterClassifier,
  text: string,
  textLength: number,
  matchStartIndex: number,
  matchLength: number,
): boolean {
  if (matchStartIndex + matchLength === textLength) {
    // Match ends at end of string
    return true
  }

  const charAfter = text.charCodeAt(matchStartIndex + matchLength)
  if (wordSeparators.get(charAfter) !== WordCharacterClass.Regular) {
    // The character after the match is a word separator
    return true
  }

  if (
    charAfter === CharCode.CarriageReturn ||
    charAfter === CharCode.LineFeed
  ) {
    // The character after the match is line break or carriage return.
    return true
  }

  if (matchLength > 0) {
    const lastCharInMatch = text.charCodeAt(matchStartIndex + matchLength - 1)
    if (wordSeparators.get(lastCharInMatch) !== WordCharacterClass.Regular) {
      // The last character in the match is a word separator
      return true
    }
  }

  return false
}

function isValidMatch(
  wordSeparators: WordCharacterClassifier,
  text: string,
  textLength: number,
  matchStartIndex: number,
  matchLength: number,
): boolean {
  return (
    leftIsWordBounday(
      wordSeparators,
      text,
      textLength,
      matchStartIndex,
      matchLength,
    ) &&
    rightIsWordBounday(
      wordSeparators,
      text,
      textLength,
      matchStartIndex,
      matchLength,
    )
  )
}

export class Searcher {
  public readonly _wordSeparators: WordCharacterClassifier | null
  private readonly _searchRegex: RegExp
  private _prevMatchStartIndex: number
  private _prevMatchLength: number

  constructor(
    wordSeparators: WordCharacterClassifier | null,
    searchRegex: RegExp,
  ) {
    this._wordSeparators = wordSeparators
    this._searchRegex = searchRegex
    this._prevMatchStartIndex = -1
    this._prevMatchLength = 0
  }

  public reset(lastIndex: number): void {
    this._searchRegex.lastIndex = lastIndex
    this._prevMatchStartIndex = -1
    this._prevMatchLength = 0
  }

  public next(text: string): RegExpExecArray | null {
    const textLength = text.length

    let m: RegExpExecArray | null
    do {
      if (this._prevMatchStartIndex + this._prevMatchLength === textLength) {
        // Reached the end of the line
        return null
      }

      m = this._searchRegex.exec(text)
      if (!m) {
        return null
      }

      const matchStartIndex = m.index
      const matchLength = m[0].length
      if (
        matchStartIndex === this._prevMatchStartIndex &&
        matchLength === this._prevMatchLength
      ) {
        if (matchLength === 0) {
          // the search result is an empty string and won't advance `regex.lastIndex`, so `regex.exec` will stuck here
          // we attempt to recover from that by advancing by two if surrogate pair found and by one otherwise
          if (
            strings.getNextCodePoint(
              text,
              textLength,
              this._searchRegex.lastIndex,
            ) > 0xffff
          ) {
            this._searchRegex.lastIndex += 2
          } else {
            this._searchRegex.lastIndex += 1
          }
          continue
        }
        // Exit early if the regex matches the same range twice
        return null
      }
      this._prevMatchStartIndex = matchStartIndex
      this._prevMatchLength = matchLength

      if (
        !this._wordSeparators ||
        isValidMatch(
          this._wordSeparators,
          text,
          textLength,
          matchStartIndex,
          matchLength,
        )
      ) {
        return m
      }
    } while (m)

    return null
  }
}
