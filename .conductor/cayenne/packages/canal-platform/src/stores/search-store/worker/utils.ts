import { first, last } from 'lodash'
import type { SearchContentItem } from '../types'
import { Searcher } from './from-vs/searcher'

/**
 * 默认的最大的非高亮文本长度
 */
const DEFAULT_MAX_NORMAL_TEXT_LENGTH = 100

/**
 * 搜索内容详情
 */
export interface SearchContentDetail {
  /**
   * 内容
   */
  content: SearchContentItem[]
  /**
   * 文本下标，命中文本在搜索文本中的下标
   */
  textIndex: number
}

/**
 * 生成搜索详情
 */
export function* genSearchContentDetails(
  ...args: Parameters<typeof genSearchAll>
): Generator<SearchContentDetail> {
  for (const m of genSearchAll(...args)) {
    const d = searchRegArrayToContentDetail(m)
    if (d) yield d
  }
}

/**
 * 搜索所有
 * @param searcher 搜索器
 * @param text 文本
 */
function* genSearchAll(
  searcher: Searcher,
  text: string,
): Generator<RegExpExecArray> {
  let m: RegExpExecArray | null
  searcher.reset(0)
  while ((m = searcher.next(text))) {
    yield m
  }
}

/**
 * 搜索正则结果转搜索内容详情
 * @param m 正则结果
 * @param options 选项
 */
function searchRegArrayToContentDetail(
  m: RegExpExecArray,
  {
    maxNormalTextLength = DEFAULT_MAX_NORMAL_TEXT_LENGTH,
  }: {
    /**
     * 最大的非高亮文本长度，默认 100
     */
    maxNormalTextLength?: number
  } = {},
): null | SearchContentDetail {
  const matchStr = m[0]
  if (!matchStr) {
    return null
  }
  return {
    content: normalizeSearchContentItems(
      [
        {
          isHighlight: false,
          text: m.input.slice(
            Math.max(0, m.index - maxNormalTextLength),
            m.index,
          ),
        },
        {
          isHighlight: true,
          text: matchStr,
        },
        {
          isHighlight: false,
          text: m.input.slice(
            m.index + matchStr.length,
            Math.min(
              m.input.length,
              m.index + matchStr.length + maxNormalTextLength,
            ),
          ),
        },
      ],
      { maxNormalTextLength },
    ),
    textIndex: m.index,
  }
}

/**
 * 标准化条目内容，合并相邻的同类内容
 * @param items 条目内容数组
 * @param options 选项
 */
export function normalizeSearchContentItems(
  items: SearchContentItem[],
  {
    maxNormalTextLength = DEFAULT_MAX_NORMAL_TEXT_LENGTH,
  }: {
    /**
     * 最大的非高亮文本长度，默认 100
     */
    maxNormalTextLength?: number
  } = {},
): SearchContentItem[] {
  if (!items.length) {
    return items
  }

  // 合并相邻的同类文本
  {
    let curIdx = 0
    for (let i = 1; i < items.length; i++) {
      if (items[curIdx].isHighlight === items[i].isHighlight) {
        items[curIdx].text += items[i].text
      } else {
        items[++curIdx] = items[i]
      }
    }
    items.splice(curIdx + 1, items.length - (curIdx + 1))
  }

  // 非高亮文本截断
  {
    // 第一个非高亮文本去掉左侧多余字符
    const firstItem = first(items)
    if (firstItem?.isHighlight === false) {
      firstItem.text = firstItem.text.slice(-maxNormalTextLength)
      firstItem.text = last(firstItem.text.split('\n')) || ''
    }
    // 最后一个非高亮文本去掉右侧多余字符
    const lastItem = last(items)
    if (lastItem?.isHighlight === false) {
      lastItem.text = lastItem.text.slice(0, maxNormalTextLength)
      lastItem.text = first(lastItem.text.split('\n')) || ''
    }
  }

  return items
}
