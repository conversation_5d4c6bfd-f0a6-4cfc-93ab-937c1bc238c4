import { createQuickEncoder } from '@ad/canal-shared'
import { type MaybePromiseApis } from '@kael/shared'
import { createEndpoint, fromWebWorker } from '@remote-ui/rpc'
import { memoize } from 'lodash'
import {
  type MainForSearchWorkerRpcApis,
  type SearchWorkerForMainRpcApis,
} from '../types'
import { WorkerSearchTask } from './worker-search-task'

/**
 * （正在运行的）任务对照表，id -> 搜索任务
 */
export const taskMap = new Map<string, WorkerSearchTask>()

/**
 * RPC 终端
 */
export const rpcEndpoint = createEndpoint<MainForSearchWorkerRpcApis>(
  fromWebWorker(self as unknown as Worker),
  { createEncoder: createQuickEncoder },
)
const apis: MaybePromiseApis<SearchWorkerForMainRpcApis> = {
  search(task) {
    // console.log('SearchStore worker search', { task })
    const searchTask = new WorkerSearchTask(task)
    taskMap.set(task.id, searchTask)
    searchTask.search()
  },
  cancelSearch(taskId) {
    // console.log('SearchStore worker cancelSearch', { taskId })
    taskMap.get(taskId)?.cancelSearch()
  },
  deleteSchemaCache(moduleIds) {
    // console.log('SearchStore worker deleteSchemaCache', { moduleIds })
    for (const moduleId of moduleIds) {
      memoGetSchema.cache.delete(moduleId)
    }
  },
}
rpcEndpoint.expose(apis)

/**
 * 缓存获取 Schema
 */
export const memoGetSchema = memoize(rpcEndpoint.call.getSchema)

/**
 * 缓存获取组件物料 Schema
 */
export const memoGetComponentMaterialSchema = memoize(
  rpcEndpoint.call.getComponentMaterialSchema,
  (...args) => JSON.stringify(args),
)
