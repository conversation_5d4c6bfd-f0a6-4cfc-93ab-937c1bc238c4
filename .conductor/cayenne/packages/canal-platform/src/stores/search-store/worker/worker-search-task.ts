import { isJsonPrimitive, sleep, type AnyObject } from '@ad/canal-shared'
import type { E2ECMSProp } from '@ad/e2e-material-schema'
import type {
  E2EAPI,
  E2ESchema,
  E2ESchemaExpression,
  Model,
  Track,
} from '@ad/e2e-schema'
import {
  CanalRootComponentField,
  INNER_JSON_PATH_IIFE_ACTIONS,
  getBindIIFEActionsFromIIFE,
  getComponentPropDetail,
} from '@ad/e2e-schema-utils'
import {
  dfsGenComponentDetailBySchema,
  getComponentPropExpression,
  normalizeExpression,
  type ComponentDetailOfSchema,
  type PropertyPath,
} from '@kael/schema-utils'
import { isArray, isEqual, isObject, isUndefined } from 'lodash'
import {
  memoGetComponentMaterialSchema,
  memoGetSchema,
  rpcEndpoint,
  taskMap,
} from '.'
import {
  SearchTaskStatus,
  type SearchAnchor,
  type SearchResultItem,
  type SearchTask,
} from '../types'
import { Searcher } from './from-vs/searcher'
import { createRegExp } from './from-vs/strings'
import { genSearchContentDetails, normalizeSearchContentItems } from './utils'

/**
 * 最小的批量结果条目数
 */
const MIN_BATCH_RESULT_ITEMS_COUNT = 500

/**
 * 最小的批量发送时长
 */
const MIN_BATCH_SEND_DURATION = 300

/**
 * 搜索接口选项
 */
interface SearchApiOptions {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
  /**
   * 组件详情
   */
  cd: ComponentDetailOfSchema<E2ESchema>
  /**
   * JSON 路径
   */
  jsonPath: PropertyPath
  /**
   * 接口
   */
  api: E2EAPI
}

/**
 * 搜索埋点选项
 */
interface SearchTrackOptions {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
  /**
   * 组件详情
   */
  cd: ComponentDetailOfSchema<E2ESchema>
  /**
   * JSON 路径
   */
  jsonPath: PropertyPath
  /**
   * 埋点
   */
  track: Track
}

/**
 * 搜索模型选项
 */
interface SearchModelOptions {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
  /**
   * 组件详情
   */
  cd: ComponentDetailOfSchema<E2ESchema>
  /**
   * JSON 路径
   */
  jsonPath: PropertyPath
  /**
   * 属性类型
   */
  propType: 'frontModel' | 'backModel'
  /**
   * 模型
   */
  model?: Model
}

/**
 * 搜索表达式选项
 */
interface SearchExpressionOptions {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
  /**
   * 分类
   */
  category: string
  /**
   * 锚点
   */
  anchor: SearchAnchor
  /**
   * 表达式
   */
  expression?: E2ESchemaExpression
  /**
   * 前缀文本
   */
  prefixText?: string
}

/**
 * 搜索 JSON 选项
 */
interface SearchJsonOptions {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
  /**
   * 分类
   */
  category: string
  /**
   * 锚点
   */
  anchor: SearchAnchor
  /**
   * JSON
   */
  json?: unknown
  /**
   * 前缀文本
   */
  prefixText?: string
}

/**
 * 搜索文本选项
 */
interface SearchTextOptions {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
  /**
   * 分类
   */
  category: string
  /**
   * 锚点
   */
  anchor: SearchAnchor
  /**
   * 文本
   */
  text?: string
  /**
   * 前缀文本
   */
  prefixText?: string
}

/**
 * Worker 搜索任务
 */
export class WorkerSearchTask implements SearchTask {
  /**
   * ID
   */
  public readonly id: string

  /**
   * 关键词
   */
  public readonly keyword: string

  /**
   * 模块 ID
   */
  public readonly moduleIds: string[]

  /**
   * 匹配大小写
   */
  public readonly matchCase: boolean

  /**
   * 状态
   */
  public status = SearchTaskStatus.RUNNING

  /**
   * 正在运行
   */
  private get _isRunning(): boolean {
    return this.status === SearchTaskStatus.RUNNING
  }

  /**
   * 任务是否被取消了
   */
  private get _isCanceled(): boolean {
    return this.status === SearchTaskStatus.CANCELED
  }

  /**
   * 结果条目缓存
   */
  private _resultItemsCache: SearchResultItem[] = []

  /**
   * 上次批量发送的时间戳
   */
  private _lastBatchSendTimestamp = Date.now()

  /**
   * vscode 的搜索器
   */
  private readonly _searcher: Searcher

  public constructor(task: Required<SearchTask>) {
    this.id = task.id
    this.keyword = task.keyword
    this.moduleIds = task.moduleIds
    this.matchCase = task.matchCase
    this._searcher = new Searcher(
      null,
      createRegExp(task.keyword, false, {
        matchCase: this.matchCase,
        wholeWord: false,
        multiline: true,
        global: true,
        unicode: true,
      }),
    )
  }

  /**
   * 搜索
   */
  public async search(): Promise<void> {
    for (const moduleId of this.moduleIds) {
      await this._searchModule(moduleId)
      if (this._isCanceled) return
    }
    await this._finish()
  }

  /**
   * 取消搜索
   */
  public cancelSearch(): void {
    this.status = SearchTaskStatus.CANCELED
    rpcEndpoint.call.sendSearchResult({
      taskId: this.id,
      taskStatus: this.status,
      items: [],
    })
    taskMap.delete(this.id)
  }

  /**
   * 搜索模块
   * @param moduleId 模块 ID
   */
  private async _searchModule(moduleId: string): Promise<void> {
    // console.log('WorkerSearchTask::_searchModule', {
    //   moduleId,
    // })
    const schema = await memoGetSchema(moduleId)
    if (!schema || this._isCanceled) return
    await this._searchComponents(moduleId, schema)
    if (this._isCanceled) return
    await this._searchLinkage(moduleId, schema)
  }

  /**
   * 搜索所有组件
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   */
  private async _searchComponents(
    moduleId: string,
    schema: E2ESchema,
  ): Promise<void> {
    for (const cd of dfsGenComponentDetailBySchema(schema)) {
      await this._searchComponent(moduleId, schema, cd)
      if (this._isCanceled) return
    }
  }

  /**
   * 搜索组件
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchComponent(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    // if (moduleId === 'e10de42c-8001-4cfa-813c-0b860db251f2') {
    //   console.log('debug _searchComponent', cd)
    // }
    if (schema.view.id === cd.component.id) {
      await this._searchRootComponent(moduleId, schema, cd)
    } else {
      await this._searchOtherComponent(moduleId, schema, cd)
    }
  }

  /**
   * 搜索根组件
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchRootComponent(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    const { apis: schemaApis = [], tracks: schemaTracks = [] } = schema
    const {
      component: { apis: componentApis = [] },
    } = cd
    for (let i = 0; i < schemaApis.length; i++) {
      const api = schemaApis[i]
      await this._searchApi({
        moduleId,
        schema,
        cd,
        jsonPath: ['apis', i],
        api,
      })
      if (this._isCanceled) return
    }
    for (let i = 0; i < componentApis.length; i++) {
      const api = componentApis[i]
      await this._searchApi({
        moduleId,
        schema,
        cd,
        jsonPath: [...cd.path, 'apis', i],
        api,
      })
      if (this._isCanceled) return
    }
    for (let i = 0; i < schemaTracks.length; i++) {
      const track = schemaTracks[i]
      await this._searchTrack({
        moduleId,
        schema,
        cd,
        jsonPath: ['tracks', i],
        track,
      })
      if (this._isCanceled) return
    }
    await this._searchIIFE(moduleId, schema, cd)
    if (this._isCanceled) return
    await this._searchData(moduleId, schema, cd)
    if (this._isCanceled) return
    await this._searchModel({
      moduleId,
      schema,
      cd,
      jsonPath: ['model'],
      propType: 'frontModel',
      model: schema.model,
    })
    if (this._isCanceled) return
    await this._searchModel({
      moduleId,
      schema,
      cd,
      jsonPath: ['backModel'],
      propType: 'backModel',
      model: schema.backModel,
    })
    if (this._isCanceled) return
    await this._searchProps(moduleId, schema, cd)
  }

  /**
   * 搜索其他组件
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchOtherComponent(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    const {
      component: { apis: componentApis = [] },
    } = cd
    await this._searchComponentBaseInfo(moduleId, schema, cd)
    if (this._isCanceled) return
    for (let i = 0; i < componentApis.length; i++) {
      const api = componentApis[i]
      await this._searchApi({
        moduleId,
        schema,
        cd,
        jsonPath: [...cd.path, 'apis', i],
        api,
      })
      if (this._isCanceled) return
    }
    await this._searchValue(moduleId, schema, cd)
    if (this._isCanceled) return
    await this._searchEffect(moduleId, schema, cd)
    if (this._isCanceled) return
    await this._searchProps(moduleId, schema, cd)
  }

  /**
   * 搜索接口
   * @param options 选项
   */
  private async _searchApi({
    moduleId,
    schema,
    cd,
    jsonPath,
    api,
  }: SearchApiOptions): Promise<void> {
    const { component } = cd
    const { args = [] } = api
    const category = `${component.name} 接口`
    const anchorBase = {
      isJsonKey: false,
      textIndex: 0,
      type: 'component',
      componentId: component.id,
      propType: 'api',
      apiId: api.id,
    } as const
    await this._searchText({
      moduleId,
      schema,
      category,
      anchor: {
        ...anchorBase,
        jsonPath: [...jsonPath, 'name'],
      },
      text: api.name,
    })
    if (this._isCanceled) return
    for (let i = 0; i < args.length; i++) {
      const arg = args[i]
      await this._searchExpression({
        moduleId,
        schema,
        category,
        anchor: {
          ...anchorBase,
          jsonPath: [...jsonPath, 'args', i],
        },
        expression: arg,
      })
      if (this._isCanceled) return
    }
    await this._searchExpression({
      moduleId,
      schema,
      category,
      anchor: {
        ...anchorBase,
        jsonPath: [...jsonPath, 'if'],
      },
      expression: api.if,
    })
    await this._searchJson({
      moduleId,
      schema,
      category,
      anchor: {
        ...anchorBase,
        jsonPath: [...jsonPath, 'mockRes'],
      },
      json: api.mockRes,
    })
  }

  /**
   * 搜索埋点
   * @param options 选项
   */
  private async _searchTrack({
    moduleId,
    schema,
    cd,
    jsonPath,
    track,
  }: SearchTrackOptions): Promise<void> {
    const { component } = cd
    const category = `${component.name} 埋点`
    await this._searchJson({
      moduleId,
      schema,
      category,
      anchor: {
        jsonPath: [...jsonPath, 'eventOptions'],
        isJsonKey: false,
        textIndex: 0,
        type: 'component',
        componentId: component.id,
        propType: 'track',
      },
      json: track.eventOptions,
    })
  }

  /**
   * 搜索立即执行的函数表达式，前端动作
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchIIFE(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    const { iife } = schema
    const { component } = cd
    const category = `${component.name} 动作`
    const anchorBase = {
      isJsonKey: false,
      textIndex: 0,
      type: 'component',
      componentId: component.id,
      propType: 'iife',
    } as const
    await this._searchExpression({
      moduleId,
      schema,
      category,
      anchor: {
        ...anchorBase,
        jsonPath: ['iife'],
      },
      expression: iife,
    })
    if (this._isCanceled) return
    if (!iife) return
    const actions = getBindIIFEActionsFromIIFE(iife)
    for (let i = 0; i < actions.length; i++) {
      const action = actions[i]
      switch (action.type) {
        case 'open-url': {
          await this._searchText({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchorBase,
              // 因为解析了字符串，这里在替换时，需要单独额外处理
              jsonPath: [...INNER_JSON_PATH_IIFE_ACTIONS, i, 'url'],
            },
            text: action.url,
          })
          break
        }
        case 'exec-external-fn': {
          await this._searchText({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchorBase,
              // 因为解析了字符串，这里在替换时，需要单独额外处理
              jsonPath: [...INNER_JSON_PATH_IIFE_ACTIONS, i, 'fnPath'],
            },
            text: action.fnPath,
          })
          if (this._isCanceled) return
          await this._searchText({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchorBase,
              // 因为解析了字符串，这里在替换时，需要单独额外处理
              jsonPath: [...INNER_JSON_PATH_IIFE_ACTIONS, i, 'arg0'],
            },
            text: action.arg0,
          })
          break
        }
        case 'exec-effect': {
          await this._searchExpression({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchorBase,
              // 因为解析了字符串，这里在替换时，需要单独额外处理
              jsonPath: [...INNER_JSON_PATH_IIFE_ACTIONS, i, 'expr'],
            },
            expression: action.expr,
          })
          if (this._isCanceled) return
          await this._searchText({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchorBase,
              // 因为解析了字符串，这里在替换时，需要单独额外处理
              jsonPath: [...INNER_JSON_PATH_IIFE_ACTIONS, i, 'arg0'],
            },
            text: action.arg0,
          })
          break
        }
      }
    }
  }

  /**
   * 搜索数据
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchData(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    const { component } = cd
    const category = `${component.name} 数据`
    const backDataExpr = getComponentPropExpression<E2ESchema>(component, [
      CanalRootComponentField.BACK_DATA,
    ])
    await this._searchExpression({
      moduleId,
      schema,
      category,
      anchor: {
        jsonPath: [...cd.path, 'props', CanalRootComponentField.BACK_DATA],
        isJsonKey: false,
        textIndex: 0,
        type: 'component',
        componentId: component.id,
        propType: 'data',
      },
      expression: backDataExpr,
    })
  }

  /**
   * 搜索模型
   * @param options 选项
   */
  private async _searchModel({
    moduleId,
    schema,
    cd,
    jsonPath,
    propType,
    model,
  }: SearchModelOptions): Promise<void> {
    if (!model) return
    const { component } = cd
    const category = `${component.name} ${
      propType === 'frontModel' ? '前端' : '后端'
    }模型`
    const anchorBase = {
      isJsonKey: false,
      textIndex: 0,
      type: 'component',
      componentId: component.id,
      propType,
    } as const
    if (model.codeES) {
      await this._searchText({
        moduleId,
        schema,
        category,
        anchor: {
          ...anchorBase,
          jsonPath: [...jsonPath, 'codeES'],
        },
        text: model.codeES,
      })
    }
    if (model.codeTS) {
      await this._searchText({
        moduleId,
        schema,
        category,
        anchor: {
          ...anchorBase,
          jsonPath: [...jsonPath, 'codeTS'],
        },
        text: model.codeTS,
      })
    }
  }

  /**
   * 搜索属性
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchProps(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    const { component } = cd
    const materialSchema = await memoGetComponentMaterialSchema(
      moduleId,
      component.type,
      schema.componentCodes?.[component.type]?.version,
    )
    // if (cd.component.id === 'table') {
    //   console.log('debug _searchProps', { cd, materialSchema })
    // }
    if (this._isCanceled) return
    if (!materialSchema) return
    const searchProp = async (
      prop: E2ECMSProp,
      parentPropPath: string[],
      parentPropNamePath: string[],
    ): Promise<void> => {
      const propPath = [...parentPropPath, ...(prop.path || [])]
      const propNamePath = [...parentPropNamePath, `${prop.name}`]
      if (prop.type === 'group') {
        for (const item of prop.items) {
          await searchProp(item, propPath, propNamePath)
          if (this._isCanceled) return
        }
      } else {
        if (isEqual(propPath, ['value']) || isEqual(propPath, ['onChange'])) {
          return
        }
        const propDetail = getComponentPropDetail(component, propPath)
        if (!propDetail.expression) return
        const optionsBase = {
          moduleId,
          schema,
          category: `${component.name} 属性 ${prop.name}`,
          anchor: {
            jsonPath: [...cd.path, ...propDetail.jsonPath],
            isJsonKey: false,
            textIndex: 0,
            type: 'component' as const,
            componentId: component.id,
            propPath,
            propNamePath,
          },
        }
        if (
          propDetail.isWrappedStatic &&
          propDetail.expression.type === 'static'
        ) {
          await this._searchJson({
            ...optionsBase,
            json: propDetail.expression.value,
          })
        } else {
          await this._searchExpression({
            ...optionsBase,
            expression: propDetail.expression,
          })
        }
      }
    }
    for (const prop of materialSchema.props || []) {
      await searchProp(prop, [], [])
      if (this._isCanceled) return
    }
  }

  /**
   * 搜索组件基础信息
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchComponentBaseInfo(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    const { component } = cd
    await this._searchText({
      moduleId,
      schema,
      category: component.name,
      anchor: {
        jsonPath: [...cd.path, 'id'],
        isJsonKey: false,
        textIndex: 0,
        type: 'component',
        componentId: component.id,
        propType: 'id',
      },
      text: component.id,
    })
    if (this._isCanceled) return
    await this._searchText({
      moduleId,
      schema,
      category: component.name,
      anchor: {
        jsonPath: [...cd.path, 'name'],
        isJsonKey: false,
        textIndex: 0,
        type: 'component',
        componentId: component.id,
        propType: 'name',
      },
      text: component.name,
    })
  }

  /**
   * 搜索组件值
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchValue(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    const { component } = cd
    const category = `${component.name} 值`
    const propExpr = getComponentPropExpression<E2ESchema>(component, ['value'])
    await this._searchExpression({
      moduleId,
      schema,
      category,
      anchor: {
        jsonPath: [...cd.path, 'props', 'value'],
        isJsonKey: false,
        textIndex: 0,
        type: 'component',
        componentId: component.id,
        propType: 'value',
      },
      expression: propExpr,
    })
  }

  /**
   * 搜索副作用
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   * @param cd 组件详情
   */
  private async _searchEffect(
    moduleId: string,
    schema: E2ESchema,
    cd: ComponentDetailOfSchema<E2ESchema>,
  ): Promise<void> {
    const { component } = cd
    const category = `${component.name} 副作用`
    await this._searchExpression({
      moduleId,
      schema,
      category,
      anchor: {
        jsonPath: [...cd.path, 'effect'],
        isJsonKey: false,
        textIndex: 0,
        type: 'component',
        componentId: component.id,
        propType: 'effect',
      },
      expression: component.effect,
    })
  }

  /**
   * 搜索联动
   * @param moduleId 模块 ID
   * @param schema 端到端 Schema
   */
  private async _searchLinkage(
    moduleId: string,
    schema: E2ESchema,
  ): Promise<void> {
    const commonParams = schema.linkage?.commonParams
    if (!commonParams) return
    const category = `联动`
    await this._searchJson({
      moduleId,
      schema,
      category,
      anchor: {
        jsonPath: ['linkage', 'commonParams'],
        isJsonKey: false,
        textIndex: 0,
        type: 'linkage',
      },
      json: commonParams,
      prefixText: '静态数据 ',
    })
  }

  /**
   * 搜索表达式
   * @param options 选项
   */
  private async _searchExpression({
    moduleId,
    schema,
    category,
    anchor,
    expression,
    prefixText,
  }: SearchExpressionOptions): Promise<void> {
    if (isUndefined(expression)) return
    const ne = normalizeExpression(expression)
    switch (ne.type) {
      case 'static': {
        await this._searchJson({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath:
              ne === expression
                ? [...anchor.jsonPath, 'value']
                : anchor.jsonPath,
          },
          json: ne.value,
          prefixText,
        })
        break
      }
      case 'array': {
        for (let i = 0; i < ne.value.length; i++) {
          const item = ne.value[i]
          await this._searchExpression({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchor,
              jsonPath: [...anchor.jsonPath, 'value', i],
            },
            expression: item,
            prefixText,
          })
          if (this._isCanceled) return
        }
        break
      }
      case 'object': {
        for (const key in ne.value) {
          const item = ne.value[key]
          await this._searchExpression({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchor,
              jsonPath: [...anchor.jsonPath, 'value', key],
            },
            expression: item,
            prefixText,
          })
          if (this._isCanceled) return
        }
        break
      }
      case 'component': {
        // noop
        break
      }
      case 'refresh': {
        await this._searchJson({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'params'],
          },
          json: ne.params,
          prefixText,
        })
        break
      }
      case 'track': {
        await this._searchText({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'trackId'],
          },
          text: ne.trackId,
          prefixText,
        })
        break
      }
      case 'open-url': {
        await this._searchExpression({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'url'],
          },
          expression: ne.url,
          prefixText,
        })
        break
      }
      case 'actions': {
        for (let i = 0; i < ne.fns.length; i++) {
          const fn = ne.fns[i]
          await this._searchExpression({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchor,
              jsonPath: [...anchor.jsonPath, 'fns', i],
            },
            expression: fn,
            prefixText,
          })
          if (this._isCanceled) return
        }
        break
      }
      case 'get-data': {
        // noop
        break
      }
      case 'bind': {
        await this._searchExpression({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'fn'],
          },
          expression: ne.fn,
          prefixText,
        })
        if (this._isCanceled) return
        await this._searchExpression({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'this'],
          },
          expression: ne.this,
          prefixText,
        })
        if (this._isCanceled) return
        const args = ne.args || []
        for (let i = 0; i < args.length; i++) {
          const arg = args[i]
          await this._searchExpression({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchor,
              jsonPath: [...anchor.jsonPath, 'args', i],
            },
            expression: arg,
            prefixText,
          })
          if (this._isCanceled) return
        }
        break
      }
      case 'api': {
        await this._searchExpression({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'transform'],
          },
          expression: ne.transform,
          prefixText,
        })
        if (this._isCanceled) return
        await this._searchExpression({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'defaultValue'],
          },
          expression: ne.defaultValue,
          prefixText,
        })
        break
      }
      case 'apis': {
        await this._searchExpression({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'transform'],
          },
          expression: ne.transform,
          prefixText,
        })
        if (this._isCanceled) return
        await this._searchExpression({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'defaultValue'],
          },
          expression: ne.defaultValue,
          prefixText,
        })
        break
      }
      case 'jsonata': {
        await this._searchText({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'code'],
          },
          text: ne.code,
          prefixText,
        })
        break
      }
      case 'js': {
        if (ne.codeES) {
          await this._searchText({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchor,
              jsonPath: [...anchor.jsonPath, 'codeES'],
            },
            text: ne.codeES,
            prefixText,
          })
        }
        if (ne.codeTS) {
          await this._searchText({
            moduleId,
            schema,
            category,
            anchor: {
              ...anchor,
              jsonPath: [...anchor.jsonPath, 'codeTS'],
            },
            text: ne.codeTS,
            prefixText,
          })
        }
        if (this._isCanceled) return
        await this._searchExpression({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'defaultValue'],
          },
          expression: ne.defaultValue,
          prefixText,
        })
        break
      }
      case 'degraded-jsonata': {
        await this._searchJson({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath: [...anchor.jsonPath, 'value'],
          },
          json: ne.value,
          prefixText,
        })
        break
      }
    }
  }

  /**
   * 搜索 JSON
   * @param options 选项
   */
  private async _searchJson({
    moduleId,
    schema,
    category,
    anchor,
    json,
    prefixText,
  }: SearchJsonOptions): Promise<void> {
    // json 来自 Schema，不存在循环引用
    const traverse = async (
      jsonPath: PropertyPath,
      value?: unknown,
    ): Promise<void> => {
      if (isObject(value)) {
        if (isArray(value)) {
          for (let i = 0; i < value.length; i++) {
            await traverse([...jsonPath, i], value[i])
            if (this._isCanceled) return
          }
        } else {
          for (const key in value) {
            const nextJsonPath = [...jsonPath, key]
            await this._searchText({
              moduleId,
              schema,
              category,
              anchor: {
                ...anchor,
                jsonPath: nextJsonPath,
                isJsonKey: true,
              },
              text: key,
              prefixText,
            })
            if (this._isCanceled) return
            await traverse(nextJsonPath, (value as AnyObject)[key])
            if (this._isCanceled) return
          }
        }
      } else if (isJsonPrimitive(value)) {
        await this._searchText({
          moduleId,
          schema,
          category,
          anchor: {
            ...anchor,
            jsonPath,
          },
          text: `${value}`,
          prefixText,
        })
      }
    }
    await traverse(anchor.jsonPath, json)
  }

  /**
   * 搜索文本
   * @param options 选项
   */
  private async _searchText({
    moduleId,
    schema,
    category,
    anchor,
    text,
    prefixText,
  }: SearchTextOptions): Promise<void> {
    void schema
    if (!text) return
    for (const detail of genSearchContentDetails(this._searcher, text)) {
      if (prefixText) {
        detail.content = normalizeSearchContentItems([
          {
            isHighlight: false,
            text: prefixText,
          },
          ...detail.content,
        ])
      }
      await this._sendResultItem({
        moduleId,
        category,
        content: detail.content,
        anchor: {
          ...anchor,
          textIndex: detail.textIndex,
        },
      })
      if (this._isCanceled) return
    }
  }

  /**
   * 发送结果条目
   * @param item 条目
   */
  private async _sendResultItem(item: SearchResultItem): Promise<void> {
    this._resultItemsCache.push(item)
    await this._batchSendResultItems()
  }

  /**
   * 完成任务
   */
  private async _finish(): Promise<void> {
    this.status = SearchTaskStatus.FINISHED
    await this._batchSendResultItems()
    taskMap.delete(this.id)
  }

  /**
   * 批量发送结果条目
   */
  private async _batchSendResultItems(): Promise<void> {
    if (this._isCanceled) return
    const now = Date.now()
    /**
     * 小于最小的批量结果条目数
     */
    const isLtMinBatchResultItemsCount =
      this._resultItemsCache.length < MIN_BATCH_RESULT_ITEMS_COUNT
    const durationDelta =
      MIN_BATCH_SEND_DURATION - (now - this._lastBatchSendTimestamp)
    /**
     * 小于最小的批量发送时长
     */
    const isLtMinBatchSendDuration = durationDelta > 0
    if (
      this._isRunning &&
      isLtMinBatchResultItemsCount &&
      isLtMinBatchSendDuration
    ) {
      // 运行过程中，条目不够多，时间也不够长，就不发送
      return
    }
    if (this._isRunning && isLtMinBatchSendDuration) {
      // 运行过程中，如果不是因为时间到了发送的话，就再等一会儿
      await sleep(durationDelta)
    }
    rpcEndpoint.call.sendSearchResult({
      taskId: this.id,
      taskStatus: this.status,
      items: this._resultItemsCache,
    })
    this._resultItemsCache = []
    this._lastBatchSendTimestamp = now
  }
}
