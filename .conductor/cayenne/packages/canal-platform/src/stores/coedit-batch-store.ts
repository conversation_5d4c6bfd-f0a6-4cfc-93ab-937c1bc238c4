import {
  e2eSchemaToModuleContent,
  getModuleSchema,
} from '@/pages/designer/tools'
import { cancelPermission, getPermission } from '@/services/backend/module'
import { saveAndDeployModule } from '@/utils/apis'
import { sleep } from '@ad/canal-shared'
import type { E2ESchema } from '@ad/e2e-schema'
import type { MaybePromise, WithoutPromise } from '@kael/shared'
import { isEqual } from 'lodash'
import { nanoid } from 'nanoid'

/**
 * 协同编辑模块
 */
export interface CoeditModule {
  /**
   * ID
   */
  id: string
  /**
   * 版本
   */
  version: number
  /**
   * 名称
   */
  name: string
  /**
   * 搭建 Schema
   */
  schema: E2ESchema
}

/**
 * 协同编辑批量仓库选项
 */
export interface CoeditBatchStoreOptions {
  /**
   * （业务）域代码
   */
  domainCode: string
  /**
   * 协同编辑模块
   */
  modules: CoeditModule[]
  /**
   * 编辑 Schema
   * @param module 协同编辑模块
   */
  editSchema(module: CoeditModule): MaybePromise<E2ESchema>
}

/**
 * 协同编辑批量仓库
 */
export class CoeditBatchStore {
  /**
   * （当前）房间 ID
   */
  private readonly _roomId = nanoid()

  /**
   * 正在长轮询
   */
  private _isLoogPolling = false

  public constructor(public readonly options: CoeditBatchStoreOptions) {}

  /**
   * 运行
   */
  public async run(): Promise<void> {
    try {
      await this._startEditing()
      this._startLoogPolling() // 并行长轮询
      await this._dirtyCheck()
      await this._editAndSave()
    } finally {
      await this._quitEditing()
    }
  }

  /**
   * 开始编辑
   */
  private async _startEditing(): Promise<void> {
    const permissions = await this._getAllPermissions()
    const noPermissionModuleNames: string[] = []
    for (let i = 0; i < permissions.length; i++) {
      const {
        data: { editingRoom },
      } = permissions[i]
      const module = this.options.modules[i]
      if (editingRoom.roomId !== this._roomId) {
        const { user } = editingRoom
        noPermissionModuleNames.push(
          `${module.name} [${module.id}] [${user.userName}: ${user.userCode}]`,
        )
      }
    }
    if (noPermissionModuleNames.length) {
      throw new Error(
        `以下模块正在被其他人编辑，请在对方退出编辑后重试，或挨个更新其他模块：${noPermissionModuleNames.join(
          '、',
        )}`,
      )
    }
  }

  /**
   * 获取所有（模块）权限
   */
  private async _getAllPermissions(): Promise<
    WithoutPromise<ReturnType<typeof getPermission>>[]
  > {
    try {
      return await Promise.all(
        this.options.modules.map(({ id, version }) =>
          getPermission({
            id,
            version,
            grant: true,
            roomId: this._roomId,
          }),
        ),
      )
    } catch (err) {
      console.error('CoeditBatchStore::_getAllPermissions err', err)
      throw new Error(`获取模块权限报错，请稍后再试`)
    }
  }

  /**
   * 开始长轮询
   */
  private async _startLoogPolling(): Promise<void> {
    for (;;) {
      await sleep(5000)
      if (!this._isLoogPolling) {
        break
      }
      await this._getAllPermissions()
    }
  }

  /**
   * 结束长轮询
   */
  private _stopLoogPolling(): void {
    this._isLoogPolling = false
  }

  /**
   * 脏检查
   */
  private async _dirtyCheck(): Promise<void> {
    const dirtyModuleNames: string[] = []
    const moduleLatestSchemas = await Promise.all(
      this.options.modules.map((module) =>
        getModuleSchema(module.id, module.version),
      ),
    )
    for (let i = 0; i < moduleLatestSchemas.length; i++) {
      const moduleLatestSchema = moduleLatestSchemas[i]
      const module = this.options.modules[i]
      if (!isEqual(moduleLatestSchema, module.schema)) {
        dirtyModuleNames.push(`${module.name} [${module.id}]`)
      }
    }
    if (dirtyModuleNames.length) {
      throw new Error(
        `以下模块有更新，请先重新搜索，并关注搜索结果的变化，再替换：${dirtyModuleNames.join(
          '、',
        )}`,
      )
    }
  }

  /**
   * 编辑和保存
   */
  private async _editAndSave(): Promise<void> {
    await Promise.all(
      this.options.modules.map(async (module) => {
        const schema = await this.options.editSchema(module)
        await saveAndDeployModule({
          content: e2eSchemaToModuleContent(schema),
          domainCode: this.options.domainCode,
          moduleId: module.id,
          moduleVersion: module.version,
          workload: 0,
        })
      }),
    )
  }

  /**
   * 退出编辑，结束轮训 & 取消权限
   */
  private async _quitEditing(): Promise<void> {
    this._stopLoogPolling()
    await Promise.all(
      this.options.modules.map(({ id, version }) =>
        cancelPermission({
          id,
          version,
          roomId: this._roomId,
        }),
      ),
    )
  }
}
