import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import { useCallback, type FC, type ReactNode } from 'react'
import styled, { css } from 'styled-components'

/**
 * 图标多选框属性
 */
export interface IconCheckboxProps extends ValueOnChangeProps<boolean> {
  children?: ReactNode
}

/**
 * 图标多选框
 */
export const IconCheckbox: FC<IconCheckboxProps> = ({
  value = false,
  onChange,
  ...restProps
}) => {
  const handleClick = useCallback(() => {
    onChange?.(!value)
  }, [onChange, value])
  return <Container $isActive={value} {...restProps} onClick={handleClick} />
}

const Container = styled.div<{ $isActive: boolean }>`
  display: inline-block;
  cursor: pointer;
  ${
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    (props) =>
      props.$isActive
        ? null
        : css`
            opacity: 0.3;
          `
  }
`
