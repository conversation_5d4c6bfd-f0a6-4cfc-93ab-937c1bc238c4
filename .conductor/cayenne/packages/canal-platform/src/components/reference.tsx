import { dateFormat } from '@/pages/change/const'
import { type ModuleDetail } from '@/services/backend/models'
import { getModuleList, ModuleStatus } from '@/services/backend/module'
import { createDesignerUrl } from '@/utils'
import { SystemAttentionCircleFill } from '@m-ui/icons'
import {
  Button,
  Col,
  Drawer,
  List,
  Row,
  Skeleton,
  Tag,
  Tooltip,
  Typography,
} from '@m-ui/react'
import qs from 'query-string'
import React, { useEffect, useState } from 'react'

interface ReferenceProps {
  componentId?: string
  domainCode: string
  name: string
  dataSourceId?: string
  upgradeComponent?: (partProps: {
    currentVersion: number
    moduleId: string
    moduleVersion: number
    callback: (moduleId: string) => void
  }) => JSX.Element
}

interface ModuleOnline extends ModuleDetail {
  /** content 模版内容 */
  status: number
  moduleId: string
  moduleName: string
  changeId: string
  changeName: string
  changeStatus: number
  components: string
}

const Reference: React.FC<ReferenceProps> = (props) => {
  const [visible, setVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [pageInfo, setPageInfo] = useState<{
    pageNum: number
    pageSize: number
  }>({
    pageNum: 1,
    pageSize: 10,
  })
  const [total, setTotal] = useState<number>(0)
  const [list, setList] = useState<ModuleOnline[]>([])

  const handleOpen = (): void => {
    setVisible(true)
  }

  const handleClose = (): void => {
    setVisible(false)
  }

  /**
   * 页码变动
   * @param pagination
   */
  const onPageChange = (page: number, pageSize: number): void => {
    setPageInfo({
      pageNum: page,
      pageSize: pageSize,
    })
  }

  const fetchReferenceList = async (): Promise<void> => {
    setLoading(true)
    const response = await getModuleList({
      domainCode: props.domainCode,
      /** 组件id */
      componentId: props.componentId,
      dataSourceId: props.dataSourceId,
      /** 页码 */
      pageNum: pageInfo.pageNum,
      /** 分页大小 */
      pageSize: pageInfo.pageSize,
    })
    setLoading(false)
    if (response.result === 1) {
      if (Array.isArray(response.data?.list)) {
        setList(response.data.list)
        setTotal(response.data.total)
      }
    }
  }
  useEffect(() => {
    if (visible) {
      fetchReferenceList()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, pageInfo])

  return (
    <div>
      <Button type="link" onClick={handleOpen} size="small">
        被引用信息
      </Button>
      <Drawer
        title={`引用【${props.name}】的模块共${total}个`}
        visible={visible}
        onClose={handleClose}
        width={1400}
      >
        <List
          className="module_list"
          loading={loading}
          itemLayout="horizontal"
          dataSource={list}
          pagination={{
            pageSize: pageInfo.pageSize,
            total: total,
            showSizeChanger: true,
            current: pageInfo.pageNum,
            onChange: onPageChange,
          }}
          renderItem={(item): JSX.Element => {
            const componentInfo = item.components
              ? JSON.parse(item.components)[
                  '@ad/canal-biz-components::' + props.componentId
                ]
              : null
            return (
              <List.Item
                actions={[
                  <Button
                    type="link"
                    href={createDesignerUrl(item.type, {
                      moduleId: `${item.moduleId}`,
                      moduleVersion: `${item.version}`,
                      domainCode: props.domainCode,
                    })}
                    target="_blank"
                  >
                    查看模块
                  </Button>,
                ]}
              >
                <Skeleton title={false} loading={loading} active>
                  <Row style={{ width: '100%' }}>
                    <Col span={7}>
                      <Typography.Text copyable>
                        {item.moduleId}
                      </Typography.Text>
                      <div>名称：{item.moduleName}</div>
                    </Col>
                    <Col span={2}>模块版本：{item.version}</Col>
                    {!!props.componentId && !!componentInfo && (
                      <Col span={3}>
                        组件版本：
                        {componentInfo?.version}
                        <Tooltip
                          title={String(componentInfo?.code?.js)}
                          color={'green'}
                          overlayInnerStyle={{ width: '400px' }}
                        >
                          <span
                            style={{
                              padding: '0 4px',
                              cursor: 'pointer',
                              color: '#52c41a',
                            }}
                          >
                            <SystemAttentionCircleFill />
                          </span>
                          {}
                        </Tooltip>
                        {item.status === ModuleStatus.NotOnline &&
                          props.upgradeComponent?.({
                            currentVersion: componentInfo?.version,
                            moduleId: item.moduleId,
                            moduleVersion: item.version,
                            callback: (moduleId: string) => {
                              console.log(moduleId)
                              fetchReferenceList()
                            },
                          })}
                      </Col>
                    )}

                    <Col span={2}>
                      {item.status === ModuleStatus.Online ? (
                        <Tag color="green">线上模块</Tag>
                      ) : (
                        <Tag color="blue">开发中</Tag>
                      )}
                    </Col>
                    <Col span={5}>
                      <Tooltip placement="top" title={'点击查看对应的变更'}>
                        <Button
                          type="link"
                          href={`/change/detail?${qs.stringify({
                            changeId: item.changeId,
                            domainCode: props.domainCode,
                          })}`}
                          target="_blank"
                          style={{ whiteSpace: 'pre-wrap' }}
                        >
                          变更：
                          {item.changeName || ''}
                        </Button>
                      </Tooltip>
                    </Col>
                    <Col>
                      <div>更新人：{item.updateUser}</div>
                      <div>更新时间：{dateFormat(item.updateTime)}</div>
                    </Col>
                  </Row>
                </Skeleton>
              </List.Item>
            )
          }}
        />
      </Drawer>
    </div>
  )
}

export default Reference
