import { Env, KOncall } from '@ks-koncall/web-agent'
import { QuestionCircleOutlined } from '@m-ui/icons'
import { Tooltip } from '@m-ui/react'
import React, { useState } from 'react'
import styled from 'styled-components'

// Initialize KOncall instance
const kOncall = new KOncall({
  env: Env.Prod,
})

interface KoncallHelpProps {
  /**
   * 要提问的问题文本
   */
  question: string
  /**
   * 提示文本，显示在tooltip中
   */
  tooltipTitle?: string
  /**
   * 自定义样式
   */
  style?: React.CSSProperties
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 图标大小
   */
  iconSize?: number
  /**
   * 图标颜色
   */
  iconColor?: string
}

/**
 * KoncallHelp 组件
 *
 * 显示一个问号图标，鼠标悬停时显示提示文本，点击时打开koncall并提问指定问题
 */
export const KoncallHelp: React.FC<KoncallHelpProps> = ({
  question,
  tooltipTitle = '点击咨询小助手',
  style,
  className = '',
  iconSize = 16,
  iconColor = '#1296db',
}) => {
  const [isHovered, setIsHovered] = useState(false)

  const handleClick = (event: React.MouseEvent): void => {
    event.preventDefault()
    event.stopPropagation()
    // 打开koncall并提问指定问题
    kOncall.chat({
      robotId: 196,
      input:
        '你好，你现在的角色是大运河的开发小助手，请向我说明大运河中' +
        question +
        '的用法，有更多的图片或者代码片段、配置建议更好，目的是为了帮助使用者更好的使用大运河平台',
      autoSend: '1',
    })
  }

  return (
    <Tooltip title={tooltipTitle}>
      <HelpIconWrapper
        className={`koncall-help-icon ${className}`}
        style={style}
        onClick={handleClick}
        onMouseEnter={(): void => setIsHovered(true)}
        onMouseLeave={(): void => setIsHovered(false)}
      >
        <QuestionCircleOutlined
          style={{
            fontSize: iconSize,
            color: isHovered ? '#0e86ca' : iconColor,
            transition: 'color 0.2s ease',
          }}
        />
      </HelpIconWrapper>
    </Tooltip>
  )
}

const HelpIconWrapper = styled.span`
  align-items: center;
  cursor: pointer;
  margin-left: 4px;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
`

export default KoncallHelp
