import type { CSSProperties, FC, ReactNode } from 'react'
import styled from 'styled-components'

/**
 * 控件标签属性
 */
export interface ControlLabelProps {
  /**
   * css 类型
   */
  className?: string
  /**
   * 样式
   */
  style?: CSSProperties
  children?: ReactNode
}

/**
 * 控件标签
 */
export const ControlLabel: FC<ControlLabelProps> = (props) => {
  return <Container {...props} />
}

const Container = styled.div`
  margin: 5px 0;
`
