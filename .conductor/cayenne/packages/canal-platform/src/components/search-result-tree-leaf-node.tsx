import { ReverseEllipsisText } from '@/components/reverse-ellipsis-text'
import { type SearchResultItem } from '@/stores/search-store'
import { useLatestFn } from '@ad/canal-shared-ui'
import { type FC, type ReactNode } from 'react'
import styled from 'styled-components'
import { SearchResultTreeNonLeafNode } from './search-result-tree-non-leaf-node'

/**
 * 搜索结果树叶节点属性
 */
export interface SearchResultTreeLeafNodeProps {
  /**
   * 条目数据
   */
  item: SearchResultItem
  /**
   * 点击事件
   */
  onClick?(item: SearchResultItem): void
  /**
   * 操作
   */
  actions?: ReactNode
  /**
   * 替换文本
   */
  replaceText?: string
}

/**
 * 搜索结果树叶节点
 */
export const SearchResultTreeLeafNode: FC<SearchResultTreeLeafNodeProps> = ({
  item,
  onClick,
  actions,
  replaceText,
}) => {
  const handleClick = useLatestFn(() => {
    onClick?.(item)
  })
  return (
    <StyledSearchResultTreeNonLeafNode
      onClick={handleClick}
      title={item.content.map(({ isHighlight, text }, index) => {
        if (isHighlight) {
          const replacable = replaceText && replaceText !== text
          return (
            <span key={index} className="text highlight">
              {replacable ? (
                <>
                  <span className="removed">{text}</span>
                  <span className="inserted">{replaceText}</span>
                </>
              ) : (
                <span className="keyword">{text}</span>
              )}
            </span>
          )
        } else {
          const Comp = index ? 'span' : ReverseEllipsisText
          return (
            <Comp key={index} className="text">
              {text}
            </Comp>
          )
        }
      })}
      actions={actions}
    />
  )
}

const StyledSearchResultTreeNonLeafNode = styled(SearchResultTreeNonLeafNode)`
  // SearchResultTreeNonLeafNode 内的
  .title {
    display: flex;
    width: 0;
    flex: auto;
  }

  .text {
    font-weight: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre;
    max-width: 100%;
  }

  .highlight {
    flex: none;

    & > span {
      display: inline-block;
    }

    .keyword {
      background-color: rgba(234, 92, 0, 0.33);
    }

    .removed {
      background-color: rgba(255, 0, 0, 0.4);
      text-decoration: line-through;
    }

    .inserted {
      background-color: rgba(156, 204, 44, 0.2);
    }
  }
`
