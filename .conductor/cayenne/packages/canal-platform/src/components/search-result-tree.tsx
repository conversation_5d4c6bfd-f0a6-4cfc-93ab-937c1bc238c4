import { memo, type FC } from 'react'
import { useSize } from 'react-use'
import styled from 'styled-components'
import {
  DefaultExpandedTree,
  type DefaultExpandedTreeProps,
} from './default-expanded-tree'

/**
 * 搜索结果树属性
 */
export interface SearchResultTreeProps extends DefaultExpandedTreeProps {}

/**
 * 搜索结果树
 */
export const SearchResultTree: FC<SearchResultTreeProps> = memo((props) => {
  const [sizedTree] = useSize(
    ({ height }) => {
      return (
        <Container>
          <DefaultExpandedTree height={height} {...props} />
        </Container>
      )
    },
    { height: 1 },
  )
  return sizedTree
})

const Container = styled.div`
  height: 100%;

  .ant-tree-treenode {
    display: flex;
    width: 100%;

    .ant-tree-switcher-noop {
      display: none;
    }

    .ant-tree-node-content-wrapper {
      width: 0;
      padding: 0;
      overflow: hidden;
      flex-grow: 1;
      cursor: default;
    }
  }
`
