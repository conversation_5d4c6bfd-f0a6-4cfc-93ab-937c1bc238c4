import IMG_AI_ICON from '@/assets/ai-icon.png'
import styled, { css, keyframes } from 'styled-components'

/**
 * 移动背景关键帧
 */
const moveBgKeyframes = keyframes` 
  0% {
    background-position: -150% -80%;
  }
  25% {
    background-position: 110% 170%;
  }
  50% {
    background-position: -120% 40%;
  }
  75% {
    background-position: 50% -80%;
  }
  100%  {
    background-position: -150% -80%;
  }
`

/**
 * AI 动画图标
 */
export const AiAnimationIcon = styled.div<{
  /**
   * 激活，动画更快
   */
  $isActive?: boolean
}>`
  display: inline-block;
  width: 32px;
  height: 32px;
  cursor: pointer;

  background: radial-gradient(
    circle,
    #fa6400 0%,
    #fa6400 10%,
    #00eef4 40%,
    #7539be 60%,
    #d519d2 70%,
    #fa6400 100%
  );
  background-size: 400% 400%;
  mask-image: url(${IMG_AI_ICON});
  mask-repeat: no-repeat;
  mask-size: contain;
  mask-position: center;
  animation: 15s ${moveBgKeyframes} infinite;

  ${
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    ({ $isActive }) =>
      $isActive &&
      css`
        animation: 3s ${moveBgKeyframes} infinite;
        cursor: auto;
      `
  }
`
