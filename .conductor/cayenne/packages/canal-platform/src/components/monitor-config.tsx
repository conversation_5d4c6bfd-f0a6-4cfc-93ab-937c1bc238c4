import {
  getSimpleDetailBusinessDomain,
  setSingle,
} from '@/services/backend/business_domain'
import { useSearchParams } from '@kmi/react'
import { Card, Space, message } from '@m-ui/react'
import React, { useEffect, useState } from 'react'
import StringArrayEdit from './string-array-edit'

const MonitorConfig: React.FC = () => {
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode') || ''
  const [radarIds, setRadarIds] = useState<string[]>([])
  const [schemaDomains, setSchemaDomains] = useState<string[]>([])
  useEffect(() => {
    getSimpleDetailBusinessDomain({
      code: domainCode,
    }).then((res) => {
      if (res.result === 1) {
        setRadarIds(JSON.parse(res.data?.radarProjects || '[]'))
        setSchemaDomains(JSON.parse(res.data?.schemaDomains || '[]'))
      }
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const setCallback = (value: string[], type: string): Promise<void> => {
    return setSingle({
      domainCode: domainCode,
      value: JSON.stringify(value),
      key: type,
    }).then((res) => {
      if (res.result === 1) {
        message.success('更新成功')
      }
    })
  }
  return (
    <Space direction="vertical">
      <Card title="雷达项目ID">
        <StringArrayEdit
          value={radarIds}
          onChange={(value): void => {
            setRadarIds(value)
            setCallback(value, 'radarProjects')
          }}
        />
      </Card>
      <Card title="请求schema域名">
        <StringArrayEdit
          value={schemaDomains}
          onChange={(value): void => {
            setSchemaDomains(value)
            setCallback(value, 'schemaDomains')
          }}
        />
      </Card>
    </Space>
  )
}

export default MonitorConfig
