import { getDomainRouteConfig } from '@/services/backend/business_domain'
import { history, useLocation, useModel, useSearchParams } from '@kmi/react'
import { Layout, Menu } from '@m-ui/react'
import { useEffect, useState, type FC } from 'react'
import styled from 'styled-components'
import routes from '../../../config/routes'

type sideMenuType = {
  label: string
  key: string
  icon: string
  path: string
}
const CanalSider: FC = () => {
  const location = useLocation()
  const [searchParams] = useSearchParams()
  const { initialState } = useModel('@@initialState')
  const { domainInfo } = initialState as any
  const domainCode = searchParams.get('domainCode') || ''
  const [currentPath, setCurrentPath] = useState(location.pathname)
  const [sideMenu, setSideMenu] = useState<sideMenuType[]>([])

  useEffect(() => {
    async function fetchRouteConfig(): Promise<void> {
      const config = (await getDomainRouteConfig())?.data || {}
      const newRoutes: sideMenuType[] = []
      routes
        .filter((item) => !item.hideInMenu)
        .forEach((item) => {
          // 需要隐藏
          if (
            item.hideConfigKey &&
            !!config?.[domainCode]?.[item.hideConfigKey]
          ) {
            return
          }
          if (item.showConfigKey) {
            if (!config?.[domainCode]?.[item.showConfigKey]) {
              return
            }
          }

          newRoutes.push({
            label: item.name || '',
            key: item.path || '',
            icon: item.icon || '',
            path: item.path || '',
          })
        })
      setSideMenu(newRoutes)
    }
    fetchRouteConfig()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleMenuClick = ({ item, key, keyPath, domEvent }) => {
    setCurrentPath(key)
    const target = sideMenu.find((menuItem) => menuItem.key === key)
    !!target &&
      history.push(target.path + '?domainCode=' + domainInfo.domainCode)
  }

  return (
    <Sider>
      <Layout.Sider width={100} theme="light">
        <Menu
          mode="inline"
          selectedKeys={[currentPath]}
          items={sideMenu}
          onClick={handleMenuClick}
        />
      </Layout.Sider>
    </Sider>
  )
}

export default CanalSider

const Sider = styled.div`
  background: #fff;
`
