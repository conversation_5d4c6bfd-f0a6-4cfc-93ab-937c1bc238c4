import { Tooltip } from '@m-ui/react'
import type { ComponentProps, FC } from 'react'
import styled from 'styled-components'

type InnerLongTooltipProps = ComponentProps<typeof Tooltip> & {
  className?: string
}

const InnerLongTooltip: FC<InnerLongTooltipProps> = (props) => {
  return <Tooltip {...props} overlayClassName={props.className} />
}

/**
 * 长的文字提示
 */
export const LongTooltip = styled(InnerLongTooltip)`
  max-width: 550px;
`
