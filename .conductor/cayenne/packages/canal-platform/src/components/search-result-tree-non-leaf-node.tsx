import type { StyleProps } from '@ad/canal-shared-ui'
import { type FC, type ReactNode } from 'react'
import styled, { css } from 'styled-components'

/**
 * 搜索结果树非叶节点属性
 */
export interface SearchResultTreeNonLeafNodeProps extends StyleProps {
  /**
   * 标题
   */
  title: ReactNode
  /**
   * 点击事件
   */
  onClick?(): void
  /**
   * 操作
   */
  actions?: ReactNode
}

/**
 * 搜索结果树非叶节点
 */
export const SearchResultTreeNonLeafNode: FC<
  SearchResultTreeNonLeafNodeProps
> = ({ className, style, title, onClick, actions }) => {
  return (
    <Container className={className} style={style} onClick={onClick}>
      <div className="title">{title}</div>
      {actions && <div className="actions">{actions}</div>}
    </Container>
  )
}

const Container = styled.div`
  display: flex;
  justify-content: space-between;
  ${
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    ({ onClick }) =>
      onClick &&
      css`
        cursor: pointer;
      `
  }

  .title {
    font-weight: bold;
  }

  .actions {
    display: none;
    flex: none;
  }

  &:hover {
    .actions {
      display: block;
    }
  }
`
