import { SystemQuestionmarkCircleLine } from '@m-ui/icons'
import { Tooltip } from '@m-ui/react'
import type { FC, ReactNode } from 'react'
import styled from 'styled-components'
import { VerticalCenterDiv } from './center-div'
import { KoncallHelp } from './koncall-help'

/**
 * 问号提示属性
 */
export interface QuestionTipProps {
  /**
   * 文本
   */
  text: ReactNode
  /**
   * 提示
   */
  tip: ReactNode
}

/**
 * 问号提示
 */
export const QuestionTip: FC<QuestionTipProps> = ({ text, tip }) => {
  return (
    <VerticalCenterDiv>
      <Tooltip title={tip}>{text}</Tooltip>
      {typeof text === 'string' ? (
        <KoncallHelp question={'编辑器中的' + text} />
      ) : (
        <QuestionIcon />
      )}
    </VerticalCenterDiv>
  )
}

/**
 * 问号提示图标属性
 */
export interface QuestionTipIconProps {
  /**
   * 提示
   */
  tip: ReactNode
}

/**
 * 问号提示图标
 */
export const QuestionTipIcon: FC<QuestionTipIconProps> = ({ tip }) => {
  return (
    <Tooltip title={tip}>
      <QuestionIcon />
    </Tooltip>
  )
}

const QuestionIcon = styled(SystemQuestionmarkCircleLine)`
  margin-left: 4px;
  color: #8c8c8c;
  cursor: help;
`
