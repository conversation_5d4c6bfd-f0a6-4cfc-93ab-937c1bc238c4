// 初始化一个react组件
import { SharePCmonitorLine } from '@m-ui/icons'
import { Button } from '@m-ui/react'
import React from 'react'

interface MonitorProps {
  domain?: string
  projectId?: string
}

const FrontMonitor: React.FC<MonitorProps> = (props) => {
  const jumpToServerMonitor = (): void => {
    const serverMonitor = `https://tianwen.corp.kuaishou.com/dashboard/23335/detail?currentEnv=prod&var-stage=PROD&var-pattern_url_domain=${
      props.domain || ''
    }&var-project_name=&var-project_id=${
      props.projectId || ''
    }&_filter=%257B%2522varibales%2522%253A%257B%257D%257D`
    window.open(serverMonitor, '_blank')
  }
  return (
    <>
      <Button
        onClick={jumpToServerMonitor}
        type="link"
        style={{ color: '#5E30B5' }}
      >
        <SharePCmonitorLine />
        天问前端监控
      </Button>
    </>
  )
}

export default FrontMonitor
