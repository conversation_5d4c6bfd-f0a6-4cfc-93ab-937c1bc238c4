import { reverseString } from '@ad/canal-shared'
import { useMemo, type FC } from 'react'
import styled from 'styled-components'

/**
 * 反向省略文本的属性
 */
export type ReverseEllipsisTextProps = JSX.IntrinsicElements['span'] & {
  children: string
}

/**
 * 括号组
 */
const BRACE_PAIRS = [
  // eslint-disable-next-line no-useless-escape
  [/[\[\]]/g, '[]'],
  [/[<>]/g, '<>'],
  // eslint-disable-next-line no-useless-escape
  [/[\(\)]/g, '()'],
  // eslint-disable-next-line no-useless-escape
  [/[\{\}]/g, '{}'],
  [/[【】]/g, '【】'],
  [/[《》]/g, '《》'],
  [/[（）]/g, '（）'],
  [/[「」]/g, '「」'],
] as const

/**
 * 反向省略文本
 */
export const ReverseEllipsisText: FC<ReverseEllipsisTextProps> = ({
  children,
  ...restProps
}) => {
  const finalText = useMemo(() => {
    let ret = reverseString(children)
    for (const [reg, pair] of BRACE_PAIRS) {
      ret = ret.replace(reg, (match: string) => {
        const idx = pair.indexOf(match)
        return pair[+!idx]
      })
    }
    return ret
  }, [children])
  return <Container {...restProps}>{finalText}</Container>
}

const Container = styled.span`
  direction: rtl;
  unicode-bidi: bidi-override;
`
