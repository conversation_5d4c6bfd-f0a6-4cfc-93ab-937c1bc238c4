import { DeleteOutlined, PlusOutlined } from '@m-ui/icons'
import { Button, Input, Space, message } from '@m-ui/react'
import React, { useEffect, useState } from 'react'

interface StringArrayEditProps {
  value: string[]
  onChange: (value: string[]) => void
}

const StringArrayEdit: React.FC<StringArrayEditProps> = ({
  value,
  onChange,
}) => {
  const [inputs, setInputs] = useState(value.map((item) => ({ value: item })))
  useEffect(() => {
    setInputs(value.map((item) => ({ value: item })))
  }, [value])

  const handleInputChange = (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    setInputs((prevInputs) => {
      const newInputs = [...prevInputs]
      newInputs[index].value = e.target.value
      return newInputs
    })
  }

  const handleAddInput = (): void => {
    setInputs((prevInputs) => [...prevInputs, { value: '' }])
  }

  const handleDeleteInput = (index: number): void => {
    setInputs((prevInputs) => prevInputs.filter((_, i) => i !== index))
  }

  const handleSubmit = (): void => {
    const newValue = inputs.map((input) => input.value)
    onChange(newValue)
    message.success('提交成功')
  }

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      {inputs.map((input, index) => (
        <Space key={index} align="center">
          <Input
            value={input.value}
            onChange={(e): void => handleInputChange(index, e)}
          />
          <Button
            type="link"
            icon={<DeleteOutlined />}
            onClick={(): void => handleDeleteInput(index)}
          />
        </Space>
      ))}
      <Button type="dashed" icon={<PlusOutlined />} onClick={handleAddInput}>
        添加
      </Button>
      <Button type="primary" onClick={handleSubmit}>
        提交
      </Button>
    </Space>
  )
}

export default StringArrayEdit
