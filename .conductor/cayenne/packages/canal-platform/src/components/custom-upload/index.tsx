import { getUploadToken } from '@/services/backend/upload'
import { request } from '@kmi/react'
import { SystemUploadLine } from '@m-ui/icons'
import { Button, Image, Upload, message } from '@m-ui/react'
import { type FC } from 'react'
import styled from 'styled-components'

interface CustomUploadProps {
  value?: string
  onChange?: (value: string) => void
  btnText?: string
}

const kcdnOrigin =
  process.env.KMI_ENV === 'online'
    ? 'https://kcdn.corp.kuaishou.com'
    : 'https://kcdnconsole.staging.kuaishou.com'

let CANAL_KCDN_TOKEN = ''

getUploadToken().then((res) => {
  if (res.result === 1) {
    console.log(res)
    CANAL_KCDN_TOKEN = res.data
  }
})

const CustomUpload: FC<CustomUploadProps> = (props) => {
  const { value, onChange, btnText } = props

  const handleUpload = async (file: File) => {
    const resp = await request(
      `${kcdnOrigin}/api/kcdn/v1/service/npmUpload/single?token=${CANAL_KCDN_TOKEN}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        data: {
          pid: 'adCanal',
          filename: file.name,
          allowHash: true,
          file,
        },
      },
    )
    if (resp?.data?.cdnUrl) {
      onChange && onChange(resp.data.cdnUrl)
    } else {
      message.error(resp.message || '上传失败，请重试')
    }
    return false
  }

  return (
    <Container>
      {value ? <Image width={200} src={value} /> : null}
      <Upload showUploadList={false} beforeUpload={handleUpload}>
        <Button icon={<SystemUploadLine />}>{btnText ?? '上传图片'}</Button>
      </Upload>
    </Container>
  )
}

export default CustomUpload

const Container = styled.div`
  display: flex;
  flex-direction: column;
  .ant-image {
    margin-bottom: 16px;
  }
`
