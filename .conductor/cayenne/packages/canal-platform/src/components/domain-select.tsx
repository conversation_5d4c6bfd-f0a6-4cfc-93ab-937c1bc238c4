import { getAllBusinessDomain } from '@/services/backend/business_domain'
import type { BusinessDomain } from '@/services/backend/models'
import { Alert, Modal, Select, Space } from '@m-ui/react'
import React, { useEffect, useState } from 'react'
interface DomainSelectProps {
  onSelect: (value: string) => void
  visible: boolean
  onClose: () => void
  msg?: string
}

const DomainSelect: React.FC<DomainSelectProps> = ({
  onSelect,
  visible,
  onClose,
  msg,
}) => {
  const [options, setOptions] = useState<BusinessDomain[]>([])
  const [value, setValue] = useState('')

  useEffect(() => {
    const fetchOptions = async (): Promise<void> => {
      const list = await getAllBusinessDomain({ type: 'All' })
      if (list.result === 1) {
        setOptions(list.data)
      }
    }
    fetchOptions()
  }, [])

  const handleSelect = (selectedValue: string): void => {
    setValue(selectedValue)
  }

  const handleConfirm = (): void => {
    onSelect(value)
    onClose()
  }

  return (
    <Modal
      title="选择目标业务域"
      visible={visible}
      onCancel={onClose}
      onOk={handleConfirm}
    >
      <Space direction="vertical">
        {!!msg && <Alert message={msg} type="info" />}
        <Select
          value={value}
          onChange={handleSelect}
          style={{ width: '100%' }}
          optionFilterProp="children"
          showSearch
          filterOption={(input, option): boolean =>
            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {options.map((option) => (
            <Select.Option key={option.domainCode} value={option.domainCode}>
              {option.domainName}
            </Select.Option>
          ))}
        </Select>
      </Space>
    </Modal>
  )
}

export default DomainSelect
