import { MODULE_TYPE_TEXT, ModuleType } from '@ad/canal-shared'
import { Tag } from '@m-ui/react'
import { memo, type FC } from 'react'

/**
 * 模块类型标签属性
 */
export interface ModuleTypeTagProps {
  /**
   * 类型
   */
  type: ModuleType | number
}

/**
 * 模块类型标签
 */
export const ModuleTypeTag: FC<ModuleTypeTagProps> = memo(({ type }) => {
  const text = MODULE_TYPE_TEXT[type as ModuleType]
  if (type === ModuleType.GLOBAL) {
    return <Tag color="red">{text}</Tag>
  }
  return <div>{text}</div>
})
