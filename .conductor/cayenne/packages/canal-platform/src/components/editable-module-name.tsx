import { updateModuleMetaUsingPost } from '@/services/backend/module'
import { Typography } from '@m-ui/react'
import { memo, useCallback, useState, type FC } from 'react'
import { useUpdateEffect } from 'react-use'

/**
 * 可编辑的模块名称属性
 */
export interface EditableModuleNameProps {
  /**
   * UUID
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 名称变化后事件
   * @param newName 新的名称
   */
  onNameChanged?: (newName: string) => void
}

/**
 * 可编辑的模块名称
 */
export const EditableModuleName: FC<EditableModuleNameProps> = memo(
  ({ id, name, onNameChanged }) => {
    const [finalName, setFinalName] = useState(name)
    useUpdateEffect(() => {
      setFinalName(name)
    }, [name])
    const handleEditableChange = useCallback(
      async (newName: string) => {
        setFinalName(newName)
        await updateModuleMetaUsingPost({
          id,
          name: newName,
        })
        onNameChanged?.(newName)
      },
      [id, onNameChanged],
    )
    return (
      <Typography.Paragraph
        editable={{
          onChange: handleEditableChange,
        }}
      >
        {finalName}
      </Typography.Paragraph>
    )
  },
)
