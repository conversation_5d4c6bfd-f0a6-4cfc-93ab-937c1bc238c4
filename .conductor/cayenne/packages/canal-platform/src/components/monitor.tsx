// 初始化一个react组件
import { SharePCmonitorLine } from '@m-ui/icons'
import { Button } from '@m-ui/react'
import React from 'react'

interface MonitorProps {
  moduleId?: string
  domain?: string
  service?: string
}
export const jumpToServerMonitor = (
  service: string | undefined,
  domain: string | undefined,
  moduleId: string | undefined,
): string => {
  return `https://tianwen.corp.kuaishou.com/dashboard/18128/detail?currentEnv=prod&var-stage=PROD&var-service=${
    service || ''
  }&var-domain=${domain || ''}&var-module_id=${
    moduleId || ''
  }&var-api_version=$all&_filter=%257B%2522varibales%2522%253A%257B%257D%252C%2522time%2522%253A%257B%2522from%2522%253A%2522now-3h%2522%252C%2522to%2522%253A%2522now%2522%257D%257D&activeTab=1`
}

const ServerMonitor: React.FC<MonitorProps> = (props) => {
  const jump = (): void => {
    window.open(
      jumpToServerMonitor(props.service, props.domain, props.moduleId),
      '_blank',
    )
  }
  return (
    <>
      <Button onClick={jump} type="link" style={{ color: '#389e0d' }}>
        <SharePCmonitorLine />
        服务端监控
      </Button>
    </>
  )
}

export default ServerMonitor
