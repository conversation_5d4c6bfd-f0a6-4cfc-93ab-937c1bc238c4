import { SystemQuestionmarkCircleLine } from '@m-ui/icons'
import { Button, Popover } from '@m-ui/react'
import React from 'react'
export const Config: Record<
  string,
  {
    txt: string
    docLink: string
  }
> = {
  frontLane: {
    txt: '请求header内加入frontLane的key',
    docLink:
      'https://docs.corp.kuaishou.com/k/home/<USER>/fcAC0TAUYsIOz_QYUt2E6Z6xL',
  },
  troubleshooting: {
    txt: '自助排障指南',
    docLink:
      'https://docs.corp.kuaishou.com/k/home/<USER>/fcACoxBrw_OT0zKvt_Veoohlf',
  },
  serverEnvConfig: {
    txt: '首次接入服务配置指南',
    docLink:
      'https://docs.corp.kuaishou.com/k/home/<USER>/fcABAO1M73Ol49Tjel6aShTkB',
  },
  grey: {
    txt: '灰度发布说明',
    docLink:
      'https://docs.corp.kuaishou.com/k/home/<USER>/fcAAA0SiQGIHjyIM1JTjjtwu3',
  },
}

interface Props {
  configKey: string
}
const HelpTip: React.FC<Props> = (props) => {
  const config = Config[props.configKey]
  if (!config) return
  return (
    <Popover
      title={config.txt}
      content={
        <Button href={config.docLink} type="link" target="_blank">
          点击查看使用文档
        </Button>
      }
    >
      <SystemQuestionmarkCircleLine
        style={{ padding: '0 4px', color: 'rgb(0, 117, 255)' }}
      />
    </Popover>
  )
}

export default HelpTip
