import IMG_LOGO from '@/assets/logo.png'
import { Breadcrumb } from '@m-ui/react'
import { memo, type FC } from 'react'
import styled from 'styled-components'
import { OpenLink } from './open-link'

/**
 * 设计器标题属性
 */
export interface DesignerTitleProps {
  /**
   * 模式
   */
  mode?: 'debug' | 'readonly'
  /**
   * （业务）域代码
   */
  domainCode: string
  /**
   * 变更 ID
   */
  changeId?: string
  /**
   * 标题
   */
  title?: string
}

/**
 * 设计器标题
 */
export const DesignerTitle: FC<DesignerTitleProps> = memo(
  ({ mode, domainCode, changeId, title }) => {
    const name = mode
      ? {
          readonly: '大运河只读模式',
          debug: '大运河调试模式',
        }[mode]
      : '大运河'
    return (
      <Breadcrumb>
        <Breadcrumb.Item>
          <Logo href={window.location.origin} $mode={mode}>
            <Img src={IMG_LOGO} />
            {name}
          </Logo>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <OpenLink href={`/change/list?domainCode=${domainCode}`}>
            变更列表
          </OpenLink>
        </Breadcrumb.Item>
        {changeId && (
          <Breadcrumb.Item>
            <OpenLink
              href={`/change/detail?changeId=${changeId}&domainCode=${domainCode}`}
            >
              变更详情
            </OpenLink>
          </Breadcrumb.Item>
        )}
        <Breadcrumb.Item>{title}</Breadcrumb.Item>
      </Breadcrumb>
    )
  },
)

const Logo = styled(OpenLink)<{
  /**
   * 模式
   */
  $mode?: 'debug' | 'readonly'
}>`
  &&& {
    display: inline-block;
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    background-color: ${
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      ({ $mode }) => ($mode ? '#ffed98' : '#e6f6ff')
    };
    color: #52aeff;
    text-align: center;
    border-radius: 4px;
    font-size: 16px;
  }
`

const Img = styled.img`
  height: 100%;
`
