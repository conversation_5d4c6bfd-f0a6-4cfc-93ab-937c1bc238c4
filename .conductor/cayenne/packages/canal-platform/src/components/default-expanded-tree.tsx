import { Tree } from '@m-ui/react'
import type { DataNode } from '@m-ui/react/lib/tree'
import { difference } from 'lodash'
import {
  useCallback,
  useMemo,
  useState,
  type ComponentProps,
  type FC,
} from 'react'

/**
 * 默认全部打开的树形控件属性
 */
export type DefaultExpandedTreeProps = Omit<
  ComponentProps<typeof Tree>,
  'defaultExpandedKeys' | 'expandedKeys' | 'onExpand'
>

/**
 * 默认全部打开的树形控件
 */
export const DefaultExpandedTree: FC<DefaultExpandedTreeProps> = (props) => {
  const { treeData } = props
  const allKeys = useMemo(() => {
    const keys: (string | number)[] = []
    ;(treeData as DataNode[] | undefined)?.forEach(traverse)
    return keys

    function traverse(node: DataNode): void {
      if (node.isLeaf) return
      keys.push(node.key)
      node.children?.forEach(traverse)
    }
  }, [treeData])
  const [nonExpandedKeys, setNonExpandedKeys] = useState<
    NonNullable<ComponentProps<typeof Tree>['expandedKeys']>
  >([])
  const expandedKeys = useMemo(
    () => difference(allKeys, nonExpandedKeys),
    [allKeys, nonExpandedKeys],
  )
  const handleExpand = useCallback(
    (
      newExpandedKeys: NonNullable<ComponentProps<typeof Tree>['expandedKeys']>,
    ) => {
      setNonExpandedKeys(difference(allKeys, newExpandedKeys))
    },
    [allKeys],
  )
  return <Tree {...props} expandedKeys={expandedKeys} onExpand={handleExpand} />
}
