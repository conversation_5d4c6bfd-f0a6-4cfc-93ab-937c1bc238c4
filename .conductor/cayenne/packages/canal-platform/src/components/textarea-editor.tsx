import { Form, Input } from '@m-ui/react'
import { isArray, isEqual, isObject, isString } from 'lodash'
import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  type CSSProperties,
  type ChangeEvent,
  type ComponentProps,
  type FC,
} from 'react'
import { useLatest } from 'react-use'
import styled from 'styled-components'

/**
 * 文本区编辑器，支持 tab 等
 */
export const TextAreaEditor: FC<ComponentProps<typeof Input.TextArea>> = (
  props,
) => {
  const { onKeyDown } = props
  const handleOnKeyDown = useCallback(
    (...args: Parameters<NonNullable<typeof onKeyDown>>) => {
      const [ev] = args
      // 兼容 tab
      if (ev.keyCode === 9 && ev.target instanceof HTMLTextAreaElement) {
        ev.preventDefault()
        document.execCommand('insertText', false, '  ')
      }
      return onKeyDown?.(...args)
    },
    [onKeyDown],
  )
  return <StyledTextArea {...props} onKeyDown={handleOnKeyDown} />
}

const StyledTextArea = styled(Input.TextArea)`
  &.ant-input {
    min-height: 0;
  }
`

/**
 * 文本区校验编辑器属性
 */
export interface TextAreaValidateEditorProps
  extends ComponentProps<typeof TextAreaEditor> {
  /**
   * 校验
   * @param value 当前的值
   */
  validate?: (value: string) => string | null | void
  /**
   * 校验失败事件
   * @param errMsg 错误信息
   */
  onValidateFailed?: (errMsg: string) => void
  /**
   * 是否需要更新
   * @param oldValue 旧的值
   * @param newValue 新的值
   */
  shouldUpdate?: (oldValue: string, newValue: string) => boolean
}

/**
 * 文本区校验编辑器
 */
export const TextAreaValidateEditor: FC<TextAreaValidateEditorProps> = ({
  value,
  onChange,
  validate,
  onValidateFailed,
  shouldUpdate,
  ...restProps
}) => {
  const refLatestShouldUpdate = useLatest(shouldUpdate)
  const [v, setV] = useState(value)
  const refLatestV = useLatest(v)
  const handleChange = useCallback(
    (e: ChangeEvent<HTMLTextAreaElement>) => {
      setV(e.target.value)
      const validateRet = validate?.(e.target.value)
      if (isString(validateRet)) {
        onValidateFailed?.(validateRet)
      } else {
        onChange?.(e)
      }
    },
    [onChange, onValidateFailed, validate],
  )
  useEffect(() => {
    if (
      refLatestShouldUpdate.current &&
      !refLatestShouldUpdate.current(
        refLatestV.current as string,
        value as string,
      )
    ) {
      return
    }
    setV(value)
  }, [refLatestShouldUpdate, refLatestV, value])
  return <TextAreaEditor {...restProps} value={v} onChange={handleChange} />
}

/**
 * 文本区 JSON 编辑器带表单项属性
 */
export interface TextAreaJsonEditorWithFormItemProps
  extends TextAreaValidateEditorProps {
  /**
   * 表单项标签
   */
  label?: string
  /**
   * 值
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  value?: any
  /**
   * 值变化
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange?: (value: any) => void
}

/**
 * 文本区 JSON 编辑器带表单项，目前必须为对象形式
 */
export const TextAreaJsonEditorWithFormItem: FC<
  TextAreaJsonEditorWithFormItemProps
> = ({ label, className, style, size, value, onChange, ...restProps }) => {
  const editorValue = useMemo(() => JSON.stringify(value, null, 2), [value])
  const handleEditorChange = useCallback(
    (e: ChangeEvent<HTMLTextAreaElement>) => {
      onChange?.(JSON.parse(e.target.value))
    },
    [onChange],
  )
  const [commonParamsHelp, setCommonParamsHelp] = useState('')
  const handleCommonParamsStrInputValidate = useCallback(
    (v: string): string | void => {
      try {
        const ret = JSON.parse(v)
        if (!isObject(ret) || isArray(ret)) {
          return '必须为对象形式'
        }
      } catch (err) {
        return (err as Error).message
      }
      setCommonParamsHelp('')
    },
    [],
  )
  const handleCommonParamsStrInputShouldValidateFailed = useCallback(
    (errMsg: string): void => {
      setCommonParamsHelp(errMsg)
    },
    [],
  )
  const handleCommonParamsStrInputShouldUpdate = useCallback(
    (oldValue: string, newValue: string): boolean => {
      try {
        if (isEqual(JSON.parse(oldValue), JSON.parse(newValue))) {
          return false
        }
      } catch (err) {
        // noop
      }
      return true
    },
    [],
  )
  const textAreaStyle: CSSProperties = useMemo(
    () =>
      size === 'small'
        ? {
            paddingTop: 2.5,
            paddingBottom: 2.5,
          }
        : {},
    [size],
  )
  return (
    <StyledItem
      className={className}
      style={style}
      label={label}
      help={commonParamsHelp}
      validateStatus={commonParamsHelp ? 'error' : ''}
    >
      <TextAreaValidateEditor
        autoSize
        validate={handleCommonParamsStrInputValidate}
        onValidateFailed={handleCommonParamsStrInputShouldValidateFailed}
        shouldUpdate={handleCommonParamsStrInputShouldUpdate}
        size={size}
        style={textAreaStyle}
        value={editorValue}
        onChange={handleEditorChange}
        {...restProps}
      />
    </StyledItem>
  )
}

const StyledItem = styled(Form.Item)`
  .ant-form-item-control-input {
    min-height: 0;
  }
`
