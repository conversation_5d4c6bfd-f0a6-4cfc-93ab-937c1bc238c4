import { Alert, Modal, Tabs, Typography } from '@m-ui/react'
import React from 'react'

export const CheckErrorMp = {
  dataSource: '数据源配置',
  component: '组件配置',
}

export interface ErrorTip {
  text: string
  content: string[]
}

export interface ContentItem {
  moduleId: string
  errorTips: ErrorTip[]
}

export interface CheckErrorItem {
  key: string
  content: ContentItem[]
}

interface CheckErrorModalProps {
  visible: boolean
  onOk: () => void
  onCancel: () => void
  checkErrorInfo: CheckErrorItem[]
}

const CheckErrorModal: React.FC<CheckErrorModalProps> = ({
  visible,
  onOk,
  onCancel,
  checkErrorInfo = [],
}) => {
  return (
    <Modal
      title="发布校验未通过"
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      width={1000}
    >
      <Tabs>
        {checkErrorInfo?.map((errorItem) => {
          return (
            <Tabs.TabPane
              tab={CheckErrorMp[errorItem.key as keyof typeof CheckErrorMp]}
              key={errorItem.key}
            >
              {errorItem.content.map((contentItem, contentIndex) => {
                return (
                  <React.Fragment key={`content-${contentIndex}`}>
                    <Typography.Title level={5} type="danger">
                      模块{contentItem.moduleId}
                    </Typography.Title>
                    {contentItem?.errorTips?.map((reason, reasonIndex) => {
                      return (
                        <div
                          style={{ marginBottom: 16 }}
                          key={`reason-${reasonIndex}`}
                        >
                          <Typography.Text mark>
                            {reason.text + ':'}
                          </Typography.Text>
                          {reason.content.map((item, itemIndex) => (
                            <div
                              style={{ margin: '8px 0' }}
                              key={`item-${itemIndex}`}
                            >
                              <Alert message={item} type="error" />
                            </div>
                          ))}
                        </div>
                      )
                    })}
                  </React.Fragment>
                )
              })}
            </Tabs.TabPane>
          )
        })}
      </Tabs>
    </Modal>
  )
}

export default CheckErrorModal
