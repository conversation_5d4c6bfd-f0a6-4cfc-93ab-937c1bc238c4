import { getSimpleDetailBusinessDomain } from '@/services/backend/business_domain'
import { history, useModel, useSearchParams } from '@kmi/react'
import { NormalFormsLine, SystemSearchLine } from '@m-ui/icons'
import { Button, Space, Tooltip } from '@m-ui/react'
import { useEffect, type FC } from 'react'
import styled from 'styled-components'
import ChatIcon from './chat'

const CanalHeader: FC = () => {
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode')
  const { initialState, setInitialState } = useModel('@@initialState')
  const { userInfo, domainInfo } = (initialState as any) || {}
  let domainName = domainInfo?.domainName

  const goHome = () => history.push('/')

  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      const res = await getSimpleDetailBusinessDomain({ code: domainCode })
      if (res.result === 1 && res.data) {
        setInitialState({
          ...(initialState || {}),
          ...{
            domainInfo: {
              domainCode: res.data.id,
              domainName: res.data.name,
            },
          },
        })
        domainName = res.data.name
      }
    }
    try {
      if (!domainCode) return
      if (domainCode === domainInfo?.domainCode) return
      fetchData()
    } catch (error) {
      console.log(error)
    }
  }, [])

  return (
    <Header>
      <div>
        <Title>
          <span style={{ cursor: 'pointer' }} onClick={goHome}>
            <img
              src="https://cdnfile.corp.kuaishou.com/kc/files/a/adCanal/images/canal-logo-light.d241e35897c57f50.png"
              width={64}
              style={{ marginTop: -4 }}
            />
            大运河
          </span>
          <TitleDesc>一站式端到端页面级解决方案</TitleDesc>
        </Title>
      </div>
      <HeaderRight>
        {window.location.pathname !== '/' && domainInfo?.domainCode ? (
          <CurrentDomain>
            <span style={{ color: '#8A9199', fontWeight: 400 }}>
              当前业务域：
            </span>
            {domainInfo.domainName}
          </CurrentDomain>
        ) : null}
        <Space>
          <ChatIcon />
          <Tooltip title="快速搜索模块信息">
            <Button
              type="dashed"
              shape="circle"
              icon={<SystemSearchLine />}
              href="/search"
              target="_blank"
            />
          </Tooltip>
          <a
            href="https://docs.corp.kuaishou.com/k/home/<USER>/fcADEk-9pPvm6i8RVVk2T9Yl-"
            target="_blank"
            style={{ paddingRight: '8px' }}
          >
            <NormalFormsLine />
            帮助文档
          </a>
          <UserInfo>
            <UserAvatar
              style={{
                backgroundImage: `url(${
                  userInfo?.avatar ||
                  'https://cdnfile.corp.kuaishou.com/kc/files/a/adCanal/images/canal-logo-light.d241e35897c57f50.png'
                })`,
              }}
            />
            {userInfo?.userName}
          </UserInfo>
        </Space>
      </HeaderRight>
    </Header>
  )
}

export default CanalHeader

const Header = styled.div`
  height: 64px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 0 24px 0 32px;
  box-shadow: 0 4px 16px 0 rgba(203, 206, 213, 0.2);
  display: flex;
  justify-content: space-between;
`

const Title = styled.span`
  font-size: 20px;
  font-weight: 500;
  color: #1d2126;
`

const TitleDesc = styled.span`
  display: inline-block;
  margin-left: 16px;
  padding-left: 16px;
  border-left: 1px solid #f3f3f3;
  color: #8c8c8c;
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
`

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
`

const CurrentDomain = styled.div`
  margin-right: 24px;
  font-weight: 500;
`

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  color: #283e59;
`

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  background-position: 50%;
  background-size: cover;
  border: 1px solid #fff;
  border-radius: 50%;
  margin-right: 4px;
`
