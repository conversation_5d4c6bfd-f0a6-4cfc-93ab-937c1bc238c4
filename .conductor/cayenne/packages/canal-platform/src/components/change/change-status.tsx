import { STATUS_MAP } from '@/pages/change/const'
import { Tag } from '@m-ui/react'

const ChangeStatus = ({
  status,
}: {
  status: number | undefined
}): JSX.Element => {
  if (typeof status === 'undefined') return <></>
  const config = STATUS_MAP[status]
  return (
    <Tag color={config?.color} style={{ color: config?.fontColor || '#fff' }}>
      {config.txt}
    </Tag>
  )
}
export default ChangeStatus
