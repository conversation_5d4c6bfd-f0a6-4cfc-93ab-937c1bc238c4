import { getAllProdDomainsByCode } from '@/services/backend/data_source'
import React, { useEffect, useState } from 'react'
import ServerMonitor from './monitor'

interface MonitorProps {
  domain?: string[]
  code: string
}

const DomainServerMonitor: React.FC<MonitorProps> = (props) => {
  const [monitorDomains, setMonitorDomains] = useState<string[]>([])
  useEffect(() => {
    if (!props.code) return
    getAllProdDomainsByCode({
      domainCode: props.code,
    }).then((res) => {
      if (res.result === 1) {
        if (Array.isArray(res.data)) {
          if (props.domain) {
            const combinedArray = [...res.data, ...(props.domain || [])]
            const uniqueArray = [...new Set(combinedArray)]
            setMonitorDomains(uniqueArray)
          }
        }
      }
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return <ServerMonitor domain={monitorDomains.join(',')} />
}

export default DomainServerMonitor
