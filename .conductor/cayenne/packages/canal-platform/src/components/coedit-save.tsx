import IMG_LOGO from '@/assets/logo.png'
import type { CoeditStore } from '@/stores/coedit-store'
import { Button, Popover, Tooltip } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, type FC, type ReactNode } from 'react'
import styled from 'styled-components'

/**
 * 协同编辑保存属性
 */
export interface CoeditSaveProps {
  /**
   * 协同编辑仓库
   */
  coeditStore: CoeditStore
  /**
   * 保存按钮
   */
  saveBtn: ReactNode
  /**
   * 可编辑
   */
  editable(): boolean
  /**
   * 是否是全局模块
   */
  isGlobalModule?: boolean
}

/**
 * 协同编辑保存
 */
export const CoeditSave: FC<CoeditSaveProps> = observer(
  ({ coeditStore, saveBtn, editable, isGlobalModule = false }) => {
    const handleQuitEditingBtnClick = useCallback(() => {
      coeditStore.quitEditing()
    }, [coeditStore])
    let avatar = (
      <Avatar src={coeditStore.editingRoom?.user.avatar || IMG_LOGO} />
    )

    if (coeditStore.isCurrentRoomEditing) {
      avatar = (
        <Popover
          content={
            <QuitEditingBtn type="link" onClick={handleQuitEditingBtnClick}>
              退出编辑
            </QuitEditingBtn>
          }
        >
          {avatar}
        </Popover>
      )
    }
    const editableValue = editable()
    return (
      <>
        {
          // 可编辑，并且当前用户在编辑
          editableValue &&
            (coeditStore.isCurrentRoomEditing ||
              (coeditStore.collaborativeModel === 'multiple' &&
                !isGlobalModule)) && (
              <Tooltip
                title={
                  coeditStore.collaborativeModel === 'multiple' &&
                  !isGlobalModule
                    ? '当前为多人同时可编辑模式'
                    : '当前为单人编辑模式'
                }
              >
                {saveBtn}
              </Tooltip>
            )
        }
        {avatar}
        {
          // 可编辑，其他人在编辑，单人模式
          editableValue &&
            (coeditStore.collaborativeModel === 'single' || isGlobalModule) &&
            coeditStore.editingRoom &&
            !coeditStore.isCurrentRoomEditing && (
              <Tooltip
                title={
                  coeditStore.isCurrentUserEditing
                    ? '您正在其他页面编辑'
                    : '可以联系对方退出编辑'
                }
              >
                <div>
                  {coeditStore.editingRoom.user.userName} (
                  {coeditStore.editingRoom.user.userCode}) 正在编辑...
                </div>
              </Tooltip>
            )
        }
        {
          // 可编辑，没有人在编辑
          editableValue &&
            !coeditStore.editingRoom &&
            coeditStore.quitEditingMsg && (
              <div>{coeditStore.quitEditingMsg}</div>
            )
        }
        {
          // 不可编辑
          !editableValue && <div>已发布的模块版本不可编辑</div>
        }
      </>
    )
  },
)

const Avatar = styled.img`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 12px;
`

const QuitEditingBtn = styled(Button)`
  padding: 0;
`
