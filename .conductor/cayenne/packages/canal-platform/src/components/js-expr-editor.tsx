import { JSEditor } from '@/components/js-editor'
import { getBigJsDtsFiles, getLodashDtsFiles } from '@/utils'
import { nanoid } from 'nanoid'
import {
  memo,
  useCallback,
  useEffect,
  useState,
  type ComponentProps,
  type FC,
} from 'react'

/**
 * JS 表达式编辑器属性
 */
export interface JSExprEditorProps extends ComponentProps<typeof JSEditor> {
  /**
   * 上下文类型定义
   */
  ctxDts?: string
}

/**
 * JS 表达式编辑器
 */
export const JSExprEditor: FC<JSExprEditorProps> = memo(
  ({ beforeMount, ctxDts, ...restProps }) => {
    const [monaco, setMonaco] = useState<
      Parameters<NonNullable<typeof beforeMount>>[0] | null
    >(null)
    const finalBeforeMount = useCallback(
      (...args: Parameters<NonNullable<typeof beforeMount>>) => {
        setMonaco(args[0])
        beforeMount?.(...args)
      },
      [beforeMount],
    )
    const { language } = restProps
    useEffect(() => {
      if (!monaco || !ctxDts) {
        return
      }
      // console.log('ctxDts', ctxDts)
      const lngTs = monaco.languages.typescript
      const disposer = (
        language === 'typescript'
          ? lngTs.typescriptDefaults
          : lngTs.javascriptDefaults
      ).addExtraLib(ctxDts, `ctx.${nanoid()}.d.ts`)
      return () => {
        disposer.dispose()
      }
    }, [ctxDts, monaco, language])
    useEffect(() => {
      if (!monaco) {
        return
      }
      const lngTs = monaco.languages.typescript
      const lngDefaults =
        language === 'typescript'
          ? lngTs.typescriptDefaults
          : lngTs.javascriptDefaults
      if (language === 'typescript') {
        lngDefaults.setCompilerOptions({
          target: monaco.languages.typescript.ScriptTarget.ES2016,
          allowNonTsExtensions: true,
          moduleResolution:
            monaco.languages.typescript.ModuleResolutionKind.NodeJs,
          module: monaco.languages.typescript.ModuleKind.CommonJS,
          noEmit: true,
          typeRoots: ['node_modules/@types'],
          allowSyntheticDefaultImports: true,
        })
      }
      const disposers: MonacoDisposable[] = []
      for (const [filename, content] of [
        ...getLodashDtsFiles(),
        ...getBigJsDtsFiles(),
      ]) {
        disposers.push(lngDefaults.addExtraLib(content, filename))
      }
      return () => {
        for (const d of disposers) {
          d.dispose()
        }
      }
    }, [language, monaco])
    return <JSEditor {...restProps} beforeMount={finalBeforeMount} />
  },
)

interface MonacoDisposable {
  dispose(): void
}
