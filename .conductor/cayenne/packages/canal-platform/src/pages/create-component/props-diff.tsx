import { Button, Modal, Tooltip } from '@m-ui/react'
import { DiffEditor } from '@monaco-editor/react'
import { useState } from 'react'

interface PropsDiffProps {
  originalJson: string
  getModifiedJson: () => string
  buttonProps?: {
    text?: string
    type?: 'default' | 'primary' | 'link'
    size?: 'small' | 'middle' | 'large'
  }
  modalProps?: {
    title?: string
    width?: number | string
    height?: number
  }
}

/**
 * JSON差异比较组件
 * 提供一个按钮，点击后打开Modal窗口展示两个JSON字符串之间的差异
 */
const PropsDiff: React.FC<PropsDiffProps> = ({
  originalJson,
  getModifiedJson,
  buttonProps = {},
  modalProps = {},
}) => {
  const [visible, setVisible] = useState(false)
  const [currentModifiedJson, setCurrentModifiedJson] = useState('')

  // 格式化JSON字符串
  const formatJson = (jsonStr: string): string => {
    try {
      // 如果传入的是字符串形式的JSON，先解析再格式化
      const parsed = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr
      return JSON.stringify(parsed, null, 2)
    } catch (error) {
      console.error('JSON格式化失败:', error)
      return jsonStr || ''
    }
  }

  const handleOpenDiff = (): void => {
    // 打开Modal时获取最新的表单值
    setCurrentModifiedJson(getModifiedJson())
    setVisible(true)
  }

  const handleCloseDiff = (): void => {
    setVisible(false)
  }

  const { text = 'Diff', type = 'link', size = 'middle' } = buttonProps

  const { title = 'JSON 差异比较', width = 1000, height = 600 } = modalProps

  return (
    <>
      <Tooltip title="更新组件后，可点击查看前后差异">
        <Button type={type} size={size} onClick={handleOpenDiff}>
          {text}
        </Button>
      </Tooltip>
      <Modal
        title={title}
        visible={visible}
        onCancel={handleCloseDiff}
        width={1200}
        footer={null}
        destroyOnClose
      >
        <div style={{ height: height }}>
          <DiffEditor
            height="100%"
            width="100%"
            original={formatJson(originalJson)}
            modified={formatJson(currentModifiedJson)}
            language="json"
            options={{
              renderSideBySide: true,
              readOnly: true,
              minimap: { enabled: false },
              folding: true,
              lineNumbers: 'on',
              scrollBeyondLastLine: false,
              automaticLayout: true,
            }}
          />
        </div>
      </Modal>
    </>
  )
}

export default PropsDiff
