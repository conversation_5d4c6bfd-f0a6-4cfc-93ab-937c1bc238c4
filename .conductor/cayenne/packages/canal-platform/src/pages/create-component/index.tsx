import CustomUpload from '@/components/custom-upload'
import {
  createComponentUsingPost,
  deleteComponentUsingPost,
  getComponentDetail,
  getStructureVersionInfo,
  updateComponentUsingPost,
} from '@/services/backend/component'
import type {
  CreateComponentDto,
  UpdateComponentDto,
} from '@/services/backend/models'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { getComponentGroupName } from '@ad/e2e-material-schema-utils'
import { type Component } from '@drow/sdk/lib/ui'
import { history, useSearchParams } from '@kmi/react'
import { SystemQuestionmarkCircleLine } from '@m-ui/icons'
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  Popconfirm,
  Row,
  Select,
  Switch,
  Tooltip,
  message,
} from '@m-ui/react'
import Editor from '@monaco-editor/react'
import { get, isArray, pick } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import styled from 'styled-components'
import {
  MPComponentSelect,
  type MPComponentDetail,
} from './mp-component-select'
import PropsDiff from './props-diff'

/**
 * 创建组件
 */
export const CreateComponent: FC = () => {
  const [searchParams] = useSearchParams()
  const domainId = searchParams.get('domainCode')
  const componentId = searchParams.get('componentId')
  const [form] = Form.useForm<CreateComponentFormValues>()
  const [submitting, setSubmitting] = useState(false)
  const [loading, setLoading] = useState(false)
  const [sortMpMaterialIds, setSortMpMaterialIds] = useState<number[]>([])
  const [originComponentValue, setOriginComponentValue] = useState<{
    componentName: string
    materialId: string
  }>(null)
  // PropsDiff组件内部管理modal状态，不需要在这里管理
  const [originPropsConfig, setOriginPropsConfig] = useState('')

  const handleFormFinish = useCallback(
    (values: CreateComponentFormValues) => {
      ;(async (): Promise<void> => {
        const params:
          | Omit<CreateComponentDto, 'businessDomainCode'>
          | Omit<UpdateComponentDto, 'id'> = {
          coverUrl: values.coverUrl,
          resourceUrl: values.resourceUrl,
          propsConfig: values.propsConfig,
          name: values.name,
          type: values.type,
          descs: values.descs,
          isContainer: values.isContainer,
          group: values.group,
        }
        if (values.mpComponentDetail) {
          params.associatedComponentId = JSON.stringify([
            values.mpComponentDetail.materialId,
            values.mpComponentDetail.componentName,
          ])
          params.associatedComponentVersion =
            values.mpComponentDetail.materialVersion
        }
        try {
          setSubmitting(true)
          if (componentId) {
            const resp = await updateComponentUsingPost({
              ...params,
              id: componentId,
            })
            if (resp?.result === 1) {
              message.success('保存成功')
              history.go(-1)
            } else {
              message.error(resp.msg || '保存失败')
            }
          } else if (domainId) {
            const resp = await createComponentUsingPost({
              ...params,
              businessDomainCode: domainId,
            })
            if (resp?.result === 1) {
              message.success('创建成功')
              history.go(-1)
            } else {
              message.error(resp.msg || '创建失败')
            }
          }
        } catch (error) {
          message.error(componentId ? '保存失败' : '创建失败')
        } finally {
          setSubmitting(false)
        }
      })()
    },
    [componentId, domainId],
  )
  const handleClickCancelBtn = (): void => {
    history.go(-1)
  }

  const handleClickDeleteBtn = useCallback(async () => {
    if (!componentId) return
    const fail = (): void => {
      message.error('删除失败')
    }
    try {
      const ret = await deleteComponentUsingPost({
        id: componentId,
      })
      if (ret.result === 1) {
        message.success('删除成功')
        history.go(-1)
      } else {
        fail()
      }
    } catch (err) {
      fail()
    }
  }, [componentId])
  useEffect(() => {
    if (componentId) {
      ;(async (): Promise<void> => {
        try {
          setLoading(true)
          const resp = await getComponentDetail({ id: componentId })
          if (resp.result === 1) {
            const {
              associatedComponentId,
              associatedComponentVersion,
              ...restData
            } = resp.data
            let mpComponentDetail: MPComponentDetail | undefined
            if (associatedComponentId && associatedComponentVersion) {
              const associatedComponentIdTmp = JSON.parse(associatedComponentId)
              mpComponentDetail = {
                materialId: associatedComponentIdTmp[0],
                materialVersion: associatedComponentVersion,
                componentName: associatedComponentIdTmp[1],
              }
              setOriginComponentValue({
                componentName: associatedComponentIdTmp[1],
                materialId: associatedComponentIdTmp[0],
              })
            }
            // 保存组件的 propsConfig
            setOriginPropsConfig(restData.propsConfig)
            form.setFieldsValue({ ...restData, mpComponentDetail })
          }
        } catch (err) {
          console.log('CreateComponent init err', err)
        } finally {
          setLoading(false)
        }
      })()
    }
  }, [componentId, form])
  const handleMPComponentSelectChange = useCallback(
    async (detail: Component | null) => {
      if (!detail) {
        return
      }

      if (!detail.schema || Object.keys(detail.schema).length === 0) {
        return
      }
      const schema = detail.schema as E2ERemoteComponentMaterialSchema
      let jsUrl = get(schema, 'code.code.js') as string | string[] | undefined
      if (isArray(jsUrl)) {
        jsUrl = jsUrl[0]
      }
      const exportIdentifier = get(schema, 'code.exportIdentifier') as
        | string
        | undefined
      form.setFieldsValue({
        mpComponentDetail: {
          materialId: detail.materialId,
          materialVersion: detail.version,
          componentName: detail.name,
          packageName: detail.packageName,
        },
        name: `${schema.name}`,
        type: form?.getFieldValue('type') || 0,
        descs: schema.description && `${schema.description}`,
        coverUrl: schema.icon || form?.getFieldValue('coverUrl'),
        resourceUrl: jsUrl,
        isContainer: !!schema.implements?.container,
        propsConfig: JSON.stringify(
          {
            ...pick(schema, 'props', 'implements'),
            ...(exportIdentifier
              ? {
                  code: {
                    exportIdentifier,
                  },
                }
              : {}),
          },
          null,
          2,
        ),
      })
      const group = getComponentGroupName(
        detail.lowCodeData.groups,
        schema.type,
      )
      if (group) {
        form.setFieldsValue({
          group,
        })
      }
    },
    [form],
  )

  useEffect(() => {
    if (domainId) {
      ;(async (): Promise<void> => {
        try {
          const res = await getStructureVersionInfo({
            domainCode: domainId,
          })
          if (res.result === 1 && res.data) {
            const materialIds = Object.keys(res.data)?.map((key) => {
              return Number(key)
            })
            setSortMpMaterialIds(materialIds)
          }
        } catch (err) {
          console.log('createComponent-getStructureVersionInfo init err', err)
        }
      })()
    }
  }, [domainId])

  return (
    <Card loading={loading}>
      <Form form={form} layout="vertical" onFinish={handleFormFinish}>
        <FormTitle>{componentId ? '编辑' : '新建'}组件</FormTitle>
        <Row>
          <Col span={8}>
            <Form.Item
              label="导入物料平台组件（自动填充后续属性）"
              // tooltip={
              //   <InlineMarkdownPreview source="默认搜索最新版本，如果需要搜索旧版本，可以添加 `@{版本号}`实现，比如：`button@0.0.1`" />
              // }
            >
              <Form.Item noStyle name="mpComponentDetail">
                <MPComponentSelect
                  sortMaterialIds={sortMpMaterialIds}
                  onChange={handleMPComponentSelectChange}
                  originalValue={originComponentValue}
                />
              </Form.Item>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="名称"
              name="name"
              rules={[{ required: true, message: '请填写组件名' }]}
            >
              <Input placeholder="请填写" style={{ width: '80%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="类型"
              name="type"
              rules={[{ required: true, message: '请选择组件类型' }]}
            >
              <Select placeholder="请选择" style={{ width: '80%' }}>
                <Select.Option value={0}>Web</Select.Option>
                <Select.Option value={1}>H5</Select.Option>
                <Select.Option value={2}>RN</Select.Option>
                <Select.Option value={3}>Native</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <Form.Item label="描述" name="descs">
              <Input placeholder="请填写" style={{ width: '80%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="示例图"
              name="coverUrl"
              rules={[{ required: true, message: '请上传组件示例图' }]}
            >
              <CustomUpload />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="JS URL"
              name="resourceUrl"
              rules={[{ required: true, message: '请填写 JS URL' }]}
            >
              <Input placeholder="请填写" />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <Form.Item label="组件分类" name="group">
              <Input placeholder="请填写" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="是否为容器组件"
              name="isContainer"
              valuePropName="checked"
              style={{ flexDirection: 'row', alignItems: 'center' }}
            >
              <Switch style={{ marginTop: -8, marginLeft: 12 }} />
            </Form.Item>
          </Col>
        </Row>
        <FormTitle>
          映射属性编辑
          <Tooltip
            title={
              <div>
                此处只填写
                <a
                  href="https://docs.corp.kuaishou.com/k/home/<USER>/fcAAs9eCu3svGS9h2TtMrKEX1#section=h.61c30i166shp"
                  rel="noopener"
                  target="_blank"
                >
                  组件物料 Schema{' '}
                </a>
                中的两个字段：props、implements
              </div>
            }
          >
            <TitleIconWrapper>
              <SystemQuestionmarkCircleLine />
            </TitleIconWrapper>
          </Tooltip>
          {!!componentId && !!originPropsConfig && (
            <PropsDiff
              originalJson={originPropsConfig}
              getModifiedJson={(): string =>
                form.getFieldValue('propsConfig') || ''
              }
              buttonProps={{
                text: 'diff',
                type: 'link',
              }}
              modalProps={{
                title: '组件配置对比',
                width: 1000,
              }}
            />
          )}
        </FormTitle>
        <EditorWrapper>
          <Form.Item noStyle name="propsConfig">
            <Editor
              height="300px"
              language="json"
              options={{
                minimap: {
                  enabled: false,
                },
                tabSize: 2,
                lineNumbersMinChars: 3,
                lineDecorationsWidth: 2,
              }}
            />
          </Form.Item>
        </EditorWrapper>
        <Footer>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 8 }}
            loading={submitting}
          >
            提交
          </Button>
          <Button onClick={handleClickCancelBtn}>取消</Button>
          {componentId ? (
            <Popconfirm title="确认删除？" onConfirm={handleClickDeleteBtn}>
              <DeleteBtn type="primary" danger>
                删除
              </DeleteBtn>
            </Popconfirm>
          ) : null}
        </Footer>
      </Form>
    </Card>
  )
}

export default CreateComponent

const FormTitle = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: #333840;
  line-height: 24px;
  margin-bottom: 16px;
`
const TitleIconWrapper = styled.span`
  margin-left: 4px;
`

const EditorWrapper = styled.div`
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px 0;
  width: 60%;
`

const Footer = styled.div`
  position: relative;
  margin-top: 24px;
  text-align: center;
`

const DeleteBtn = styled(Button)`
  position: absolute;
  right: 0;
`

/**
 * 创建组件表单值
 */
export interface CreateComponentFormValues {
  /**
   * 物流平台组件详情
   */
  mpComponentDetail?: MPComponentDetail
  /**
   * 名称
   */
  name: string
  /**
   * 类型
   */
  type: number
  /**
   * 描述
   */
  descs?: string
  /**
   * 示例图
   */
  coverUrl: string
  /**
   * JS URL
   */
  resourceUrl: string
  /**
   * 是否为容器组件
   */
  isContainer?: boolean
  /**
   * 映射属性编辑
   */
  propsConfig: string
  /**
   * 组件分类，即物料 Schema 里的 group
   */
  group?: string
}
