import { getMaterialDetail } from '@/services/material/material-platform'
import '@drow/sdk/dist/sdk.min.css'
import { DrowImport, DrowUpdate, type OkValues } from '@drow/sdk/lib/ui'
import { NormalExchangeLine, NormalRiseLine } from '@m-ui/icons'
import { Button, Popconfirm, message } from '@m-ui/react'
import { memo, useEffect, useState, type FC } from 'react'

/**
 * 物流平台组件详情
 */
export interface MPComponentDetail {
  /**
   * 物料 ID
   */
  materialId: number
  /**
   * 物料版本
   */
  materialVersion: string
  /**
   * 组件名称
   */
  componentName: string
  packageName?: string
}

/**
 * 物流平台组件选择器属性
 */
export interface MPComponentSelectProps {
  value: MPComponentDetail
  originalValue: {
    materialId: number | string
    componentName: string
  }
  onChange: (v) => void
  /** 排序 */
  sortMaterialIds: number[]
}

/**
 * 物流平台组件选择器
 */
export const MPComponentSelect: FC<MPComponentSelectProps> = memo((props) => {
  const [visible, setVisible] = useState(false)
  const [updateVisible, setUpdateVisible] = useState(false)

  const { sortMaterialIds = [], onChange, value, originalValue } = props
  const [packageName, setPackageName] = useState('')
  const { materialId = '', materialVersion, componentName } = value || {}

  const onImportOk = ({ components }: OkValues) => {
    const detail = components?.[0]
    setVisible(false)
    if (!detail.schema || Object.keys(detail.schema).length === 0) {
      message.error('组件 schema 为空, 导入失败。')
      return
    }
    onChange?.(detail)
  }

  const onUpdateOk = ({ components }: OkValues) => {
    const detail = components?.[0]
    setUpdateVisible(false)
    if (!detail.schema || Object.keys(detail.schema).length === 0) {
      message.error('组件 schema 为空, 更新失败。')
      return
    }
    onChange?.(components?.[0])
  }

  const getMPComponentDetail = async (mPMaterialId: number) => {
    const res = await getMaterialDetail({
      materialId: mPMaterialId,
    })
    const { data } = await res
    setPackageName(data?.packageName)
  }

  useEffect(() => {
    if (!materialId || packageName) {
      return
    }
    getMPComponentDetail(materialId)
  }, [materialId, packageName])

  return (
    <>
      <div>
        {materialId && componentName ? (
          <>
            <div
              style={{ marginBottom: '8px' }}
            >{`${value?.componentName}(${packageName}@${materialVersion})`}</div>
            <div>
              <Button
                icon={<NormalRiseLine />}
                style={{ paddingLeft: 0 }}
                onClick={() => setUpdateVisible(true)}
                type="link"
              >
                升级版本
              </Button>
              <Popconfirm
                title="确认替换组件？"
                onConfirm={() => setVisible(true)}
                okText="确认替换"
              >
                <Button icon={<NormalExchangeLine />} type="link">
                  替换组件
                </Button>
              </Popconfirm>
            </div>
          </>
        ) : (
          <Button type="primary" onClick={() => setVisible(true)}>
            + 导入组件
          </Button>
        )}
      </div>
      <DrowImport
        visible={visible}
        onCancel={() => setVisible(false)}
        onOk={onImportOk}
        sortMaterialIds={sortMaterialIds}
        type="component"
        mode="single"
        destroyOnClose
        excludeComponents={
          originalValue?.materialId
            ? [
                {
                  materialId: originalValue?.materialId,
                  componentName: originalValue?.componentName,
                },
              ]
            : []
        }
      />
      {materialId && (
        <DrowUpdate
          visible={updateVisible}
          onCancel={() => setUpdateVisible(false)}
          onOk={onUpdateOk}
          updateComponents={[
            {
              materialId,
              componentName,
              version: materialVersion,
              minVersion: materialVersion,
            },
          ]}
        />
      )}
    </>
  )
})
