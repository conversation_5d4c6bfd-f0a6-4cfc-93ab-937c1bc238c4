import { createPublishService } from '@/services/backend/change'
import { checkBeforePublishRequest } from '@/services/backend/publish'
import { checkRedirectToNewPlatform } from '@/utils/lingzhu'
import { history } from '@kmi/react'
import { Button, Tooltip, message } from '@m-ui/react'
import { useState } from 'react'
import CheckErrorModal from '../../components/check-error-modal'
import CreatePublishGrey from '../grey/create-publish-grey'
import { type IGreySubmitConfig } from '../grey/grey-const'

interface CreatePublishProps {
  disabled?: boolean
  domainCode: string
  changeId: string
  ignoreComponentCheck: boolean
}

export const CheckErrorMp = {
  dataSource: '数据源配置',
  component: '组件',
}

const CreatePublish: React.FC<CreatePublishProps> = ({
  domainCode,
  disabled,
  changeId,
  ignoreComponentCheck,
}) => {
  // 创建发布单loading态
  const [createPublishLoading, setCreatePublishLoading] = useState(false)
  /**
   * 灰度弹框
   */
  const [isGrayModalOpen, setIsGrayModalOpen] = useState(false)
  /**
   * 部署提示
   */
  const [deployTip, setDeployTip] = useState('')
  /**
   * 校验不通过弹框
   */
  const [checkErrorInfo, setCheckErrorInfo] = useState<
    {
      key: string
      content: {
        moduleId: string
        errorTips: {
          text: string
          content: string[]
        }[]
      }[]
    }[]
  >()
  const [checkErrorModalOpen, setCheckErrorModalOpen] = useState<boolean>(false)
  /**
   * 创建发布单
   */
  const createPublishRequest = async (
    greyConfig?: IGreySubmitConfig,
  ): Promise<void> => {
    setCreatePublishLoading(true)
    try {
      const res = await createPublishService({
        changeId,
        domainCode: domainCode,
        status: 0, //init
        type: 0, // 0: 发布单，1: 回滚单
        ...(greyConfig || {}),
      })
      if (res.result === 1) {
        message.success('创建发布单成功')
        history.push(`/change/publishManage?domainCode=${domainCode}`)
      } else {
        message.error(res.msg || '创建发布单失败')
      }
    } catch (e) {
      message.error('创建发布单失败')
    }
    setCreatePublishLoading(false)
  }
  const clearCheckErrorInfo = (): void => {
    setCheckErrorInfo([])
    setCheckErrorModalOpen(false)
  }

  /**
   * 发布前check
   */
  const checkBeforePublish = async (): Promise<void> => {
    if (checkRedirectToNewPlatform(changeId, domainCode)) return
    setCreatePublishLoading(true)
    const checkResult = await checkBeforePublishRequest({
      changeId,
      ignoreComponentCheck,
    })

    if (checkResult.result === 1 && checkResult.data) {
      if (checkResult.data.checkError) {
        setCheckErrorInfo(checkResult.data.checkError)
        setCheckErrorModalOpen(true)
        setCreatePublishLoading(false)
        return
      }
      if (checkResult.data.noHistory) {
        message.info(
          '本发布单所有模块均为首次发布，请直接使用宿主工程进行灰度即可，大运河为全量发布',
        )
        createPublishRequest()
      }
      if (checkResult.data.noHistory === false) {
        setIsGrayModalOpen(true)
      }
      if (
        Number(checkResult.data.moduleCount) > 1 &&
        checkResult.data.hasNoHistory
      ) {
        setDeployTip(
          '本次发布单有多个模块，并且有模块为首次上线，一般此类情况建议依赖宿主产品版本号进行发布，若使用百分比、白名单、泳道灰度方式，未命中灰度的用户将不能获取到新模块，请谨慎评估！！',
        )
      }
    } else {
      message.error(checkResult.msg || '请求失败')
    }
    setCreatePublishLoading(false)
  }
  return (
    <>
      <Tooltip title="在发布过PRT环境后，并且当前业务域没有正在未完结的发布单，才会允许创建上线单">
        <Button
          type="primary"
          loading={createPublishLoading}
          onClick={checkBeforePublish}
          disabled={disabled}
        >
          创建发布单
        </Button>
      </Tooltip>
      <CreatePublishGrey
        visible={isGrayModalOpen}
        onCancel={(): void => {
          setIsGrayModalOpen(false)
        }}
        onOk={(greyConfig: IGreySubmitConfig): void => {
          setIsGrayModalOpen(false)
          createPublishRequest(greyConfig)
        }}
        tip={deployTip}
        domainCode={domainCode}
      />
      <CheckErrorModal
        visible={checkErrorModalOpen}
        onOk={clearCheckErrorInfo}
        onCancel={clearCheckErrorInfo}
        checkErrorInfo={checkErrorInfo || []}
      />
    </>
  )
}

export default CreatePublish
