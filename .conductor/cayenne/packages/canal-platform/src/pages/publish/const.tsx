import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  MinusCircleOutlined,
  SyncOutlined,
} from '@m-ui/icons'

const PUBLISH_STATUS = {
  INIT: 0,
  ING: 1,
  FAIL: 2,
  FINISHED: 3,
  QUIT: 4,
  NO_NEED_FLOW: 5, //无需审批流程，待发布
  FLOWING: 6, //审批中
  FLOW_END: 7, // 审批结束待发布
  FLOW_FAIL: 8, //上线审批流程失败
}
const PUBLISH_STATUS_RENDER_CONFIG = {
  [PUBLISH_STATUS.INIT]: {
    txt: '待发布',
    icon: <ClockCircleOutlined />,
    status: 'default',
  },
  [PUBLISH_STATUS.ING]: {
    txt: '发布中',
    icon: <SyncOutlined spin />,
    color: 'processing',
  },
  [PUBLISH_STATUS.FAIL]: {
    txt: '发布失败',
    icon: <CloseCircleOutlined />,
    color: 'error',
  },
  [PUBLISH_STATUS.FINISHED]: {
    txt: '发布成功',
    icon: <CheckCircleOutlined />,
    color: 'success',
  },
  [PUBLISH_STATUS.QUIT]: {
    txt: '已退出',
    icon: <MinusCircleOutlined />,
    color: 'default',
  },
  [PUBLISH_STATUS.NO_NEED_FLOW]: {
    txt: '无需审批，已经跳过，待部署',
    color: 'processing',
  },
  [PUBLISH_STATUS.FLOW_FAIL]: {
    txt: '审批失败',
    color: 'processing',
  },
  [PUBLISH_STATUS.FLOWING]: {
    txt: '审批中',
    color: 'processing',
  },
  [PUBLISH_STATUS.FLOW_END]: {
    txt: '审批完成',
    color: 'processing',
  },
}

const PUBLISH_TYPE = {
  PUBLISH: 0,
  ROLLBACK: 1,
}

const PUBLISH_FLOW_STATUS_TEXT = {
  [PUBLISH_STATUS.NO_NEED_FLOW]: '无需审批，流程已经自动跳过',
  [PUBLISH_STATUS.FLOW_FAIL]: '审批流失败，请看审批详情了解详情',
  [PUBLISH_STATUS.FLOWING]:
    '审批已提交，请到审批详情页填写相关信息并提交，获取最新审批结果请点击【刷新】按钮',
  [PUBLISH_STATUS.FLOW_END]: '审批完成',
}

export {
  PUBLISH_FLOW_STATUS_TEXT,
  PUBLISH_STATUS,
  PUBLISH_STATUS_RENDER_CONFIG,
  PUBLISH_TYPE,
}
