import { type IGreyDeploy } from '../grey/grey-const'
export interface ChangePublish {
  id: number
  changeId: string
  domainCode: string
  status: number
  isOnline?: number
  type: number
  createUser: string
  updateUser: string
  createTime: number
  updateTime: number
  flowId?: string
  flowUrl?: string
  changeName?: string
  publishTime?: number
  greyId?: number
  rollbackFrom?: string
}

export interface NewestPublish extends ChangePublish {
  grey?: IGreyDeploy
}
