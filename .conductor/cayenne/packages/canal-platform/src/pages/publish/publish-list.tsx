import { dateFormat } from '@/pages/change/const'
import GreyHistoryList from '@/pages/grey/grey-history-list'
import {
  getCanalPublishList,
  rollBackCurrentService,
  rollBackService,
} from '@/services/backend/change'
import { useSearchParams } from '@kmi/react'
import {
  Badge,
  Button,
  Descriptions,
  Empty,
  Popconfirm,
  Space,
  Table,
  Tag,
  message,
} from '@m-ui/react'
import type { ColumnsType, TablePaginationConfig } from '@m-ui/react/es/table'
import type { ReactNode } from 'react'
import { useEffect, useState } from 'react'
import { PUBLISH_STATUS, PUBLISH_STATUS_RENDER_CONFIG } from './const'
import { type ChangePublish } from './model.type'
const locale = {
  emptyText: <Empty description={'发布单记录为空'} />,
}

const ChangePublishList = ({
  refreshCallback,
}: {
  refreshCallback: () => void
}): JSX.Element => {
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode')
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const [rollbackLoading, setRollbackLoading] = useState(false)
  const [publishList, setPublishList] = useState<ChangePublish[]>([])

  /**
   * 页码变动
   * @param pagination
   */
  const onPageChange = (pagination: TablePaginationConfig): void => {
    if (typeof pagination.current !== 'undefined') {
      setCurrentPage(pagination.current)
    }
  }
  const getPublishListFunc = async (): Promise<void> => {
    if (!domainCode) return
    setLoading(true)
    const res = await getCanalPublishList({
      domainCode,
      pageNum: currentPage,
      pageSize: 10,
    })
    if (res.result === 1 && Array.isArray(res.data?.list)) {
      setPublishList(res.data.list)
      if (res.data.pageInfo) {
        setTotal(res.data.pageInfo.total)
      }
    }
    setLoading(false)
  }

  const rollBack = async (publish: ChangePublish): Promise<void> => {
    setLoading(true)
    try {
      const res = await rollBackService({ id: Number(publish.id) })
      if (res.result === 1 && res.data) {
        message.success('创建回滚单成功，请进行回滚发布')
        setCurrentPage(1)
        if (typeof refreshCallback === 'function') {
          refreshCallback()
        }
      } else {
        message.error(res.msg || '创建回滚单失败')
      }
    } catch (e) {
      message.success('创建回滚单失败')
    }
    setLoading(false)
  }

  const rollBackCurrent = async (publish: ChangePublish): Promise<void> => {
    setRollbackLoading(true)
    try {
      const res = await rollBackCurrentService({ id: Number(publish.id) })
      if (res.result === 1 && res.data) {
        message.success('回滚成功')
      } else {
        message.error(res.msg || '回滚失败')
      }
    } catch (e) {
      console.error(e)
      message.error('回滚失败')
    } finally {
      setTimeout(() => {
        window.location.reload()
      }, 200)
    }
    setRollbackLoading(false)
  }

  const columns: ColumnsType<ChangePublish> = [
    {
      title: '发布单ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '变更',
      dataIndex: 'changeId',
      key: 'name',
      width: 400,
      render: (changeId, record): ReactNode => {
        return (
          <Descriptions title="" column={1}>
            <Descriptions.Item label="变更ID">
              <a
                href={`/change/detail?changeId=${changeId}&domainCode=${record.domainCode}`}
                target="_blank"
              >
                {changeId}
              </a>
            </Descriptions.Item>
            <Descriptions.Item label="变更名称">
              {record.changeName}
            </Descriptions.Item>
            {!!record.flowUrl && (
              <Descriptions.Item label="审批链接">
                <a href={record.flowUrl} target="_blank">
                  审批链接
                </a>
              </Descriptions.Item>
            )}
            {!!record.rollbackFrom && (
              <Descriptions.Item label="回滚来源发布单ID">
                {record.rollbackFrom}
              </Descriptions.Item>
            )}
          </Descriptions>
        )
      },
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      width: 200,
      render: (status, record): ReactNode => {
        if (PUBLISH_STATUS_RENDER_CONFIG[Number(status)]) {
          const config = PUBLISH_STATUS_RENDER_CONFIG[Number(status)]
          return (
            <>
              <Tag color={config.color} icon={config.icon}>
                {config.txt}
              </Tag>
              {!!Number(record.isOnline) && <Tag color="#87d068">online</Tag>}
            </>
          )
        }
      },
    },
    {
      title: '类型',
      key: 'type',
      dataIndex: 'type',
      render: (type): ReactNode => {
        return Number(type) === 0 ? (
          <Badge status="default" text="发布" />
        ) : (
          <Badge status="warning" text="回滚" />
        )
      },
    },
    {
      title: '发布信息',
      dataIndex: 'publisher',
      key: 'publisher',
      width: 300,
      render: (publisher, record): ReactNode => {
        return (
          <Descriptions title="" column={1}>
            <Descriptions.Item label="发布人">{publisher}</Descriptions.Item>
            <Descriptions.Item label="发布时间">
              {dateFormat(record.publishTime)}
            </Descriptions.Item>
          </Descriptions>
        )
      },
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      render: (updateTime: number) => dateFormat(updateTime),
    },
    {
      title: '发布单创建人',
      key: 'createUser',
      dataIndex: 'createUser',
    },
    {
      title: '操作',
      key: 'action',
      render: (...[, record]): ReactNode => {
        return (
          <Space>
            {Number(record.status) === PUBLISH_STATUS.FINISHED &&
              !record.rollbackFrom &&
              Number(record.isOnline) === 1 && (
                <Popconfirm
                  title="确定要回滚该变更？会使用该变更关联模块的前一次线上模块进行回滚"
                  onConfirm={(): Promise<void> => rollBackCurrent(record)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button danger>回滚</Button>
                </Popconfirm>
              )}

            {/* {Number(record.status) === PUBLISH_STATUS.FINISHED &&
              Number(record.isOnline) === 0 && (
                <Popconfirm
                  title="确定要回滚到该版本？请谨慎check！！！！"
                  onConfirm={(): void => {
                    rollBack(record)
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="link" danger>
                    回滚到该版本
                  </Button>
                </Popconfirm>
              )} */}
            {!!record.greyId && <GreyHistoryList greyId={record.greyId} />}
          </Space>
        )
      },
    },
  ]

  useEffect(() => {
    getPublishListFunc()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage])
  return (
    <Table
      columns={columns}
      locale={locale}
      dataSource={publishList}
      rowKey={'id'}
      loading={loading}
      pagination={{
        pageSize: 10,
        total: total,
        showSizeChanger: false,
        current: currentPage,
      }}
      onChange={onPageChange}
    />
  )
}
export default ChangePublishList
