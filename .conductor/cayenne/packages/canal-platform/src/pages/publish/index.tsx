import TeamJump from '@/components/change/team-jump'
import {
  changeControllergetDetail,
  doCanalDeploy,
  doPublish,
  getNewestPublish,
  quitPublish,
  refreshPublishFlowState,
  sendMessage,
  startPublish,
} from '@/services/backend/change'
import type { ChangeDto } from '@/services/backend/models'
import { deployStaging } from '@/services/backend/module'
import {
  deployGreyRequest,
  updateGreyRequest,
} from '@/services/backend/publish'
import { userStore } from '@/stores/user-store'
import { checkRedirectToNewPlatform } from '@/utils/lingzhu'
import { useSearchParams } from '@kmi/react'
import {
  Alert,
  Button,
  Card,
  Descriptions,
  Empty,
  Popconfirm,
  Space,
  Steps,
  Tag,
  message,
} from '@m-ui/react'
import { useEffect, useMemo, useState, type FC } from 'react'
import CreatePublishGrey from '../grey/create-publish-grey'
import { GreyDeployStatus, type IGreySubmitConfig } from '../grey/grey-const'
import GreyHistoryList from '../grey/grey-history-list'
import GreyInfo from '../grey/grey-info'
import {
  PUBLISH_FLOW_STATUS_TEXT,
  PUBLISH_STATUS,
  PUBLISH_STATUS_RENDER_CONFIG,
  PUBLISH_TYPE,
} from './const'
import { type NewestPublish } from './model.type'
import ChangePublishList from './publish-list'
const { Step } = Steps
const FLOW_STATE: {
  [key: string]: {
    message?: string
    action?: string
    normalMessage?: string
    notNormalMessage?: string
  }
} = {
  DRAFT: {
    message: '您的审批单还未提交，请去审批详情页提交',
  },
  SUBMIT: {
    message: '审批还未通过',
  },
  WITHDRAW: {
    message: '审批已经撤回',
  },
  BACK: {
    message: '审批已经被驳回',
  },
  CANCEL: {
    message: '审批已经撤销',
    action: 'refresh',
  },
  OVER: {
    normalMessage: '审批已经完成，请继续发布',
    notNormalMessage:
      '审批流未正常完结，审批被拒绝或者已撤回，请重新发起或退出发布',
    action: 'refresh',
  },
  NULL: {
    message: '发生错误',
  },
}
/**
 * 主页
 */
export const Publish: FC = () => {
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode') || ''
  const [newestPublish, setNewestPublish] = useState<NewestPublish>()
  const [quitting, setQuitting] = useState(false)
  const [publishing, setPublishing] = useState(false)
  const [pushFulling, setPushFulling] = useState(false)
  const [changeInfo, setChangeInfo] = useState<ChangeDto>()
  const [deploying, setDeploying] = useState(false)
  const [refreshLoading, setRefreshLoading] = useState(false)
  const [deployGreyVisible, setDeployGreyVisible] = useState(false)
  // 发布流程
  const current = useMemo(() => {
    if (newestPublish) {
      if (
        [PUBLISH_STATUS.FLOWING, PUBLISH_STATUS.FLOW_FAIL].indexOf(
          newestPublish?.status,
        ) > -1
      ) {
        return 0
      }
      if (
        [
          PUBLISH_STATUS.FLOW_END,
          PUBLISH_STATUS.ING,
          PUBLISH_STATUS.NO_NEED_FLOW,
        ].indexOf(newestPublish?.status) > -1
      ) {
        return 1
      }
    }
    return -1
  }, [newestPublish])
  // 退出条件
  const showQuit = useMemo(() => {
    if (newestPublish) {
      if (
        [
          PUBLISH_STATUS.INIT,
          PUBLISH_STATUS.FAIL,
          PUBLISH_STATUS.NO_NEED_FLOW,
          PUBLISH_STATUS.FLOWING,
          PUBLISH_STATUS.FLOW_END,
          PUBLISH_STATUS.FLOW_FAIL,
          PUBLISH_STATUS.ING,
        ].indexOf(newestPublish?.status) > -1
      ) {
        return true
      }
    }
    return false
  }, [newestPublish])
  const refreshFlowState = async (): Promise<void> => {
    if (!newestPublish?.flowId) return
    setRefreshLoading(true)
    const res = await refreshPublishFlowState({
      id: newestPublish.id,
      flowId: newestPublish.flowId,
    })
    if (res.result === 1 && res.data) {
      const state = res.data?.state?.state
      const overState = res.data.state?.overState
      const config = FLOW_STATE[state as keyof typeof FLOW_STATE]
      if (config) {
        if (state !== 'OVER') {
          if (config.message) {
            message.info(config.message)
          }
        } else {
          message.info(
            overState === 'Normal'
              ? config.normalMessage
              : config.notNormalMessage,
          )
        }
        if (config.action) {
          if (config.action === 'refresh') {
            getNewestPublishInfo()
          }
        }
      }
    }
    setRefreshLoading(false)
  }
  // 审批流动作
  const FLOW_ACTION = useMemo(() => {
    if (newestPublish?.status === PUBLISH_STATUS.FLOWING) {
      return (
        <>
          <Space>
            <Button onClick={refreshFlowState} loading={refreshLoading}>
              刷新
            </Button>
            <a href={newestPublish.flowUrl} target="_blank">
              查看审批详情
            </a>
          </Space>
        </>
      )
    }
    if (newestPublish?.status === PUBLISH_STATUS.FLOW_FAIL) {
      return (
        <>
          <Button
            onClick={(): Promise<void> => startPublishAction()}
            loading={publishing}
          >
            重新发起
          </Button>
          <a
            href={newestPublish.flowUrl}
            target="_blank"
            style={{
              color: '#F93920',
              paddingLeft: '8px',
            }}
          >
            查看审批详情
          </a>
        </>
      )
    }
    if (newestPublish?.status === PUBLISH_STATUS.FLOW_END) {
      return (
        <a href={newestPublish.flowUrl} target="_blank">
          查看审批详情
        </a>
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newestPublish])
  // 开始按钮
  const showBegin = useMemo(() => {
    return (
      newestPublish?.status === PUBLISH_STATUS.INIT ||
      newestPublish?.status === PUBLISH_STATUS.FAIL
    )
  }, [newestPublish])

  async function getNewestPublishInfo(): Promise<void> {
    try {
      const res = await getNewestPublish({ domainCode })
      if (res.result === 1 && res.data) {
        res.data.status = Number(res.data.status)
        setNewestPublish(res.data)
        if (res.data.changeId) {
          try {
            const changeRes = await changeControllergetDetail({
              changeId: res.data.changeId,
            })
            if (changeRes?.result === 1 && changeRes.data) {
              setChangeInfo(changeRes.data)
            }
          } catch (e) {
            // 不是强制依赖
            console.log(e)
          }
        }
      }
    } catch (error) {
      message.error('获取最新发布单失败')
    }
  }
  const confirmQuit = async (): Promise<void> => {
    // todo 退出
    try {
      if (newestPublish) {
        if (checkRedirectToNewPlatform(newestPublish.changeId, domainCode)) {
          return
        }
        setQuitting(true)
        const res = await quitPublish({ id: Number(newestPublish.id) })
        if (res.result === 1) {
          message.success('退出成功，您可以重新创建发布单')
          setTimeout(() => {
            location.reload()
          }, 300)
        } else {
          message.error(res.msg || '退出失败')
        }
      }
    } catch (error) {
      message.error('退出失败')
    }
    setQuitting(false)
  }
  // 部署灰度
  const publishGrey = async (editInfo: IGreySubmitConfig): Promise<void> => {
    if (
      newestPublish &&
      checkRedirectToNewPlatform(newestPublish.changeId, domainCode)
    ) {
      return
    }
    if (!newestPublish?.grey || !editInfo.greyType) return
    const currentGrey = newestPublish.grey
    setDeployGreyVisible(false)
    setDeploying(true)
    /**
     * 更新灰度单
     */
    await updateGreyRequest({
      id: currentGrey.id,
      publishId: newestPublish.id,
      greyType: editInfo.greyType,
      greyValue: editInfo.greyValue || '',
      greyKey: editInfo.greyKey || '',
    })
    const res = await deployGreyRequest({ id: Number(newestPublish.id) })
    if (res.result === 1) {
      message.success('灰度部署成功')
      await getNewestPublishInfo()
    } else {
      message.error(res.msg || '灰度部署失败')
    }
    setDeploying(false)
  }
  // 开始部署，审批流发起
  const publish = async (): Promise<void> => {
    try {
      setDeploying(true)
      setPushFulling(true)
      if (newestPublish) {
        if (checkRedirectToNewPlatform(newestPublish.changeId, domainCode)) {
          return
        }
        const res = await doPublish({ id: Number(newestPublish.id) })
        if (res.result === 1) {
          message.success('发布成功')
          if (process.env.KMI_ENV === 'online') {
            sendMessage({
              changeAddress: `https://canal.corp.kuaishou.com/change/detail?changeId=${newestPublish.changeId}&domainCode=${domainCode}`,
              changeName: changeInfo?.changeName || '',
              publisher: userStore.user?.userCode || '',
              type:
                newestPublish.type == PUBLISH_TYPE.ROLLBACK ? '回滚' : '发布',
            })
          }
          // todo部署变更到staging、prt、beta主干
          await deployMasterAfterPublish()
          setTimeout(() => {
            location.reload()
          }, 300)
        } else {
          message.error(res.msg || '发布失败')
        }
      }
    } catch (error) {
      message.error('发布失败')
    }
    setDeploying(false)
    setPushFulling(false)
  }

  const deployMasterAfterPublish = async (): Promise<void> => {
    try {
      if (newestPublish) {
        for (const stage of ['prt', 'beta']) {
          await doCanalDeploy({
            changeId: newestPublish.changeId,
            stage,
            lane: '',
            frontLane: '',
          })
        }
        const isSuccess = await deployStaging(
          true,
          domainCode,
          newestPublish.changeId,
          'staging',
          '',
          '',
        )
        await doCanalDeploy({
          changeId: newestPublish.changeId,
          stage: 'staging',
          lane: '',
          frontLane: '',
          status: isSuccess ? 1 : 0,
        })
      }
    } catch (err) {
      console.error('deploy master after publish err', err)
    }
  }

  const deployGrey = async (): Promise<void> => {
    //灰度单部署
    if (newestPublish && newestPublish.grey) {
      if (checkRedirectToNewPlatform(newestPublish.changeId, domainCode)) {
        return
      }
      // 先更新信息，然后进行确认
      setDeploying(true)
      await getNewestPublishInfo()
      setDeployGreyVisible(true)
    }
  }
  // 开始部署，审批流发起
  const startPublishAction = async (): Promise<void> => {
    try {
      if (newestPublish) {
        if (checkRedirectToNewPlatform(newestPublish.changeId, domainCode)) {
          return
        }
        setPublishing(true)
        const res = await startPublish({ id: Number(newestPublish.id) })
        if (res.result === 1) {
          setTimeout(() => {
            location.reload()
          }, 300)
        } else {
          message.error(res.msg || '发布失败')
        }
      }
    } catch (error) {
      message.error('发布失败')
    }
    setPublishing(false)
  }
  useEffect(() => {
    getNewestPublishInfo()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const publishStatus = useMemo(() => {
    if (newestPublish && PUBLISH_STATUS_RENDER_CONFIG[newestPublish.status]) {
      const config = PUBLISH_STATUS_RENDER_CONFIG[newestPublish.status]
      return (
        <>
          <Tag color={config.color} icon={config.icon}>
            {config.txt}
          </Tag>
          {!!Number(newestPublish.isOnline) && (
            <Tag color="#87d068">online</Tag>
          )}
        </>
      )
    }
  }, [newestPublish])
  return (
    <>
      <Card
        title="最新发布单"
        bordered={false}
        extra={
          showQuit && (
            <Popconfirm
              title="确定退出此次发布?"
              onConfirm={confirmQuit}
              okText="确定"
              cancelText="取消"
            >
              <Button danger loading={quitting}>
                退出
              </Button>
            </Popconfirm>
          )
        }
      >
        {!!newestPublish && (
          <>
            <Descriptions>
              <Descriptions.Item label="变更ID">
                <a
                  href={`/change/detail?changeId=${newestPublish?.changeId}&domainCode=${newestPublish?.domainCode}`}
                  target="_blank"
                >
                  {newestPublish?.changeId}
                </a>
              </Descriptions.Item>
              <Descriptions.Item label="开发">
                {changeInfo?.developer?.join(',')}
              </Descriptions.Item>
              <Descriptions.Item label="Team地址">
                <TeamJump teamId={changeInfo?.teamId || ''} />
              </Descriptions.Item>
              <Descriptions.Item label="发布单ID">
                {newestPublish?.id}
              </Descriptions.Item>
              <Descriptions.Item label="发布单创建人">
                {newestPublish?.createUser}
              </Descriptions.Item>
              <Descriptions.Item label="测试">
                {changeInfo?.tester?.join(',')}
              </Descriptions.Item>
              <Descriptions.Item label="推送环境">线上</Descriptions.Item>
              <Descriptions.Item label="类型">
                {Number(newestPublish?.type) === PUBLISH_TYPE.PUBLISH ? (
                  '发布'
                ) : (
                  <Tag color="error">回滚</Tag>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {publishStatus}
              </Descriptions.Item>
              {!!newestPublish?.rollbackFrom && (
                <Descriptions.Item label="回滚来源变更">
                  <a
                    href={`/change/detail?changeId=${newestPublish.rollbackFrom}&domainCode=${newestPublish?.domainCode}`}
                    target="_blank"
                  >
                    {newestPublish.rollbackFrom}
                  </a>
                </Descriptions.Item>
              )}
            </Descriptions>
            {current > -1 && (
              <Steps
                current={current}
                direction="vertical"
                status="process"
                style={{ margin: '8px 0' }}
              >
                <Step
                  title="审批流程"
                  subTitle={
                    PUBLISH_FLOW_STATUS_TEXT[newestPublish.status] || ''
                  }
                  description={FLOW_ACTION}
                />
                <Step
                  title={
                    <>
                      部署
                      {newestPublish?.grey?.status ===
                        GreyDeployStatus.EFFECTIVE && (
                        <Popconfirm
                          title="确认灰度校验功能无误后，再推全，请谨慎操作。确认发布?"
                          onConfirm={publish}
                          okText="确定"
                          cancelText="取消"
                        >
                          <Button
                            type="primary"
                            style={{ marginLeft: 8 }}
                            loading={pushFulling}
                          >
                            推全
                          </Button>
                        </Popconfirm>
                      )}
                    </>
                  }
                  subTitle={
                    current === 1 && (
                      <>
                        {newestPublish &&
                        (newestPublish.grey?.status === GreyDeployStatus.INIT ||
                          newestPublish.grey?.status ===
                            GreyDeployStatus.EFFECTIVE) ? (
                          <>
                            {newestPublish.grey?.status ===
                              GreyDeployStatus.EFFECTIVE && (
                              <Alert
                                message="若想中止灰度流程，请整单退出发布单"
                                type="warning"
                                banner
                                style={{ marginTop: 16 }}
                              />
                            )}
                            <GreyInfo
                              grey={newestPublish.grey}
                              domainCode={domainCode}
                              publishId={newestPublish.id}
                              rightAction={
                                <>
                                  <Button
                                    type="primary"
                                    size={'middle'}
                                    loading={deploying}
                                    onClick={deployGrey}
                                    disabled={pushFulling}
                                  >
                                    点击调整灰度部署
                                  </Button>
                                  {newestPublish.grey.status !==
                                    GreyDeployStatus.INIT && (
                                    <GreyHistoryList
                                      greyId={newestPublish.grey.id}
                                    />
                                  )}
                                </>
                              }
                            />
                          </>
                        ) : (
                          <Button
                            type="primary"
                            loading={deploying}
                            onClick={publish}
                          >
                            点击进行部署
                          </Button>
                        )}
                      </>
                    )
                  }
                />
              </Steps>
            )}
            <Space>
              {showBegin && (
                <Popconfirm
                  title="确定发布此次变更?"
                  onConfirm={startPublishAction}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="primary"
                    loading={publishing}
                    danger={
                      Number(newestPublish?.type) === PUBLISH_TYPE.ROLLBACK
                    }
                  >
                    {Number(newestPublish?.type) === PUBLISH_TYPE.PUBLISH
                      ? '开始发布'
                      : '回滚'}
                  </Button>
                </Popconfirm>
              )}
            </Space>
          </>
        )}
        {!newestPublish && <Empty description={'暂无进行中的发布单'} />}
        {deployGreyVisible && (
          <CreatePublishGrey
            visible={deployGreyVisible}
            onCancel={(): void => {
              setDeployGreyVisible(false)
              setDeploying(false)
            }}
            onOk={(editInfo: IGreySubmitConfig): Promise<void> =>
              publishGrey(editInfo)
            }
            domainCode={domainCode}
            greyEditInfo={newestPublish?.grey}
          />
        )}
      </Card>
      <Card title="发布列表" bordered={false} style={{ marginTop: 24 }}>
        <ChangePublishList refreshCallback={getNewestPublishInfo} />
      </Card>
    </>
  )
}

export default Publish
