import {
  type COMPARE_PROPS,
  type ComponentSchemaConfigMap,
} from '@/pages/diff/type'
import type { ComponentDetail } from '@/services/backend/models'
import { CanalImplementsField } from '@ad/e2e-schema-utils'
import type { CMSProp } from '@kael/material-schema'
import { Col, Collapse, Row, Space, Tag, Typography } from '@m-ui/react'
import { DiffEditor } from '@monaco-editor/react'
import { isEqual } from 'lodash'
import { useEffect, useState } from 'react'
import ExpressionDiff from '../component/expression-code-diff'
import type { CompareStructure, ILinkageConfig } from '../type'
import { generateLinkageConfig } from '../utils/linkage-utils'
import type { CompareComponentDataSourceResult } from '../utils/util'
import {
  compareComponentDataSource,
  compareComponentMaterial,
  compareIsEqualByConfigPath,
  drawDiffFromConfigs,
  getComponentPropsConfig,
} from '../utils/util'
import ApiCompare from './api-compare'
import LinkageCompare from './linkage-compare'
import MaterialCompare from './material-compare'
import RenderCompare from './render-compare'
const { Panel } = Collapse
// 仅做展示
// todo: 基于getComponentMaterialPropsMergedByImplements 搞一下
const IMPLEMENT_PROPS_CONFIG: Record<
  string,
  {
    setter?: {
      type: string
    }
    name: string
    desc?: string
  }
> = {
  style: {
    setter: {
      type: 'JSON',
    },
    name: '样式',
  },
  value: {
    name: '组件值',
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  __$backData: {
    name: '数据',
    desc: '全局属性-表达式返回的非 null 的值，会按键值覆盖前端运行时里的数据',
  },
}

const RenderCompareItem: CompareStructure = [
  {
    txt: '条件渲染',
    path: ['props', CanalImplementsField.IF],
  },
  {
    txt: '列表渲染',
    path: ['backFor'],
    deepProps: [
      {
        txt: '列表数据',
        key: 'items',
        path: ['items'],
      },
      {
        txt: '数据健值',
        key: 'key',
        path: ['key'],
      },
    ],
  },
]

const ComponentCompare = ({
  targetId,
  developComponentsConfig,
  baseComponentsConfig,
  developSchema,
  baseSchema,
  tag,
}: COMPARE_PROPS): JSX.Element => {
  const [diffInfo, setDiffInfo] = useState<
    {
      propsKey: string
      developProps: CMSProp | undefined
      masterProps: CMSProp | undefined
    }[]
  >([])
  const [materialCompare, setMaterialCompare] = useState<{
    develop: ComponentDetail | undefined
    base: ComponentDetail | undefined
  }>()
  const [dataSourceCompareInfo, setDataSourceCompareInfo] = useState<
    CompareComponentDataSourceResult[]
  >([])

  const [developLinkageConfig, setDevelopLinkageConfig] =
    useState<ILinkageConfig>()
  const [baseLinkageConfig, setBaseLinkageConfig] = useState<ILinkageConfig>()
  const [isRenderEqual, setIsRenderEqual] = useState<boolean | undefined>()

  async function getCompare(): Promise<void> {
    const deltaResult = drawDiffFromConfigs(
      developComponentsConfig,
      baseComponentsConfig,
      targetId,
    )

    if (!deltaResult) {
      return
    }
    const developProps = await getComponentPropsConfig(
      developComponentsConfig,
      targetId,
    )
    const masterProps = await getComponentPropsConfig(
      baseComponentsConfig,
      targetId,
    )
    // 比较物料
    const material = await compareComponentMaterial(
      developComponentsConfig[targetId]?.code,
      baseComponentsConfig[targetId]?.code,
      developComponentsConfig[targetId]?.config.type,
    )
    if (material) {
      setMaterialCompare({
        develop: material.develop,
        base: material.base,
      })
    }
    // 比较组件级数据源配置
    const dataSourceCompare = await compareComponentDataSource(
      developComponentsConfig[targetId]?.config?.apis || {},
      baseComponentsConfig[targetId]?.config?.apis || {},
    )
    if (dataSourceCompare) {
      setDataSourceCompareInfo(dataSourceCompare)
    }
    // 比较联动配置
    setDevelopLinkageConfig(
      generateLinkageConfig(developSchema?.linkage, targetId),
    )
    setBaseLinkageConfig(generateLinkageConfig(baseSchema?.linkage, targetId))
    // 比较渲染配置

    // 比较props
    const differentProps =
      typeof deltaResult === 'object' &&
      deltaResult !== null &&
      'config' in deltaResult &&
      typeof deltaResult.config === 'object' &&
      deltaResult.config !== null &&
      'props' in deltaResult.config
        ? Object.keys(
            (deltaResult.config as { props: Record<string, unknown> }).props,
          )
        : []
    // TODO: 考虑一下深层path的情况，看怎么优化
    setDiffInfo(
      differentProps
        .map((key) => {
          if (!getPropsName(key, developProps?.get?.(key))) {
            return undefined
          }
          return {
            propsKey: key,
            developProps: developProps?.get?.(key),
            masterProps: masterProps?.get?.(key),
          }
        })
        .filter(
          (
            item,
          ): item is {
            propsKey: string
            developProps: CMSProp | undefined
            masterProps: CMSProp | undefined
          } => item !== undefined,
        ),
    )

    setIsRenderEqual(
      compareIsEqualByConfigPath(
        developComponentsConfig[targetId]?.config,
        baseComponentsConfig[targetId]?.config,
        RenderCompareItem,
      ),
    )
  }

  useEffect(() => {
    getCompare()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  function getPropsName(
    propsKey: string,
    diffItemProps: CMSProp | undefined,
  ): string | JSX.Element {
    if (diffItemProps?.name) {
      return String(diffItemProps.name)
    } else if (propsKey in IMPLEMENT_PROPS_CONFIG) {
      return (
        <Space>
          {IMPLEMENT_PROPS_CONFIG[propsKey].name}：
          {!!IMPLEMENT_PROPS_CONFIG[propsKey].desc && (
            <Tag color="blue">{IMPLEMENT_PROPS_CONFIG[propsKey].desc}</Tag>
          )}
        </Space>
      )
    }
    return ''
  }

  function getPropsValue(
    propsKey: string,
    componentSchemaConfig: ComponentSchemaConfigMap,
  ): unknown {
    const propsValue =
      componentSchemaConfig?.[targetId]?.config?.props?.[propsKey]
    return propsValue || ''
  }

  return (
    <>
      <Collapse destroyInactivePanel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>展开组件完整协议DIFF</Space>
            </Typography.Text>
          }
          key={baseComponentsConfig[targetId].config.id}
        >
          <DiffEditor
            height={400}
            width={1200}
            original={JSON.stringify(
              baseComponentsConfig[targetId].config,
              null,
              '\t',
            )}
            modified={JSON.stringify(
              developComponentsConfig[targetId].config,
              null,
              '\t',
            )}
            language={'json'}
            options={{
              renderIndicators: true,
              readOnly: true,
            }}
          />
        </Panel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                物料
                {materialCompare ? (
                  <Tag color="orange">配置有差异</Tag>
                ) : (
                  <Tag>无差异</Tag>
                )}
              </Space>
            </Typography.Text>
          }
          key="0"
        >
          {!!materialCompare && (
            <MaterialCompare materialCompareInfo={materialCompare} />
          )}
        </Panel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                Props
                <Tag color={diffInfo?.length > 0 ? 'orange' : ''}>
                  共有【{diffInfo.length}】项配置有差异
                </Tag>
              </Space>
            </Typography.Text>
          }
          key="1"
        >
          <div style={{ marginBottom: 16, border: '1px solid #e9e9e9' }}>
            {diffInfo.map((item) => {
              return (
                <div key={item.propsKey} style={{ padding: 16 }}>
                  <Row>
                    <Col span={12}>
                      <Typography.Text mark strong>
                        {getPropsName(item.propsKey, item.developProps)}
                      </Typography.Text>
                    </Col>
                    <Col span={12}>
                      <Typography.Text mark strong>
                        {getPropsName(item.propsKey, item.masterProps)}
                      </Typography.Text>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={24}>
                      <ExpressionDiff
                        develop={getPropsValue(
                          item.propsKey,
                          developComponentsConfig,
                        )}
                        base={getPropsValue(
                          item.propsKey,
                          baseComponentsConfig,
                        )}
                      />
                    </Col>
                  </Row>
                </div>
              )
            })}
          </div>
        </Panel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                组件接口
                {dataSourceCompareInfo.length > 0 ? (
                  <Tag color="orange">
                    共有【{dataSourceCompareInfo.length}】个组件接口配置有差异
                  </Tag>
                ) : (
                  <Tag>无差异</Tag>
                )}
              </Space>
            </Typography.Text>
          }
          key="2"
        >
          {dataSourceCompareInfo.length > 0 && (
            <ApiCompare
              dataSourceCompareInfo={dataSourceCompareInfo}
              tag={tag}
            />
          )}
        </Panel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                联动配置
                {isEqual(developLinkageConfig, baseLinkageConfig) ? (
                  <Tag>无差异</Tag>
                ) : (
                  <Tag color="orange">有差异</Tag>
                )}
              </Space>
            </Typography.Text>
          }
          key="3"
        >
          <LinkageCompare
            developLinkageConfig={developLinkageConfig}
            baseLinkageConfig={baseLinkageConfig}
            tag={tag}
          />
        </Panel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                渲染配置
                {isRenderEqual ? (
                  <Tag>无差异</Tag>
                ) : (
                  <Tag color="orange">有差异</Tag>
                )}
              </Space>
            </Typography.Text>
          }
          key="4"
        >
          {!isRenderEqual && (
            <RenderCompare
              developComponent={developComponentsConfig[targetId]?.config}
              baseComponent={baseComponentsConfig[targetId]?.config}
              renderConfig={RenderCompareItem}
            />
          )}
        </Panel>
        {!isEqual(
          developComponentsConfig[targetId]?.config?.effect,
          baseComponentsConfig[targetId]?.config?.effect,
        ) && (
          <Panel
            header={
              <Space style={{ fontWeight: 'bold' }}>
                <Typography.Text strong>副作用</Typography.Text>
                <Tag color="orange">有差异</Tag>
              </Space>
            }
            key="5"
          >
            <ExpressionDiff
              develop={developComponentsConfig[targetId]?.config?.effect}
              base={baseComponentsConfig[targetId]?.config?.effect}
              codeTxt={'副作用'}
            />
          </Panel>
        )}
      </Collapse>
    </>
  )
}

export default ComponentCompare
