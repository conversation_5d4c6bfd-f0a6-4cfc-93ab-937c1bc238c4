import { Collapse, Space, Typography } from '@m-ui/react'
import { useMemo } from 'react'
import { type IDIFF_COMMON_PROPS } from '../type'
import ComponentCompare from './component-compare'
const { Title, Text } = Typography
const { Panel } = Collapse

const ComponentPanel = (props: IDIFF_COMMON_PROPS): JSX.Element => {
  const EditedComponentsRender: React.ReactNode = useMemo(() => {
    const editedComponents = []
    if (!props.diffResult?.edited) return
    for (const [key] of props.diffResult.edited) {
      const config = props.developComponentsConfig[key]
      editedComponents.push(
        <Panel
          header={
            <>
              <Text strong style={{ fontSize: 14 }}>
                {'组件：'}
                <Text copyable>{key}</Text>
                {'【' + config?.config?.name + '】'}
              </Text>
            </>
          }
          key={key}
        >
          <ComponentCompare targetId={key} {...props}></ComponentCompare>
        </Panel>,
      )
    }
    return editedComponents
  }, [props])

  return (
    <div style={{ height: '700px', overflow: 'scroll', padding: 16 }}>
      <Title level={5}>
        <Space>
          被修改的组件配置
          <Text type="secondary" style={{ fontSize: 13 }}>
            根组件属性中包含全局配置，如全局样式、全局数据覆盖等
          </Text>
        </Space>
      </Title>
      <div>{props.tag}</div>
      <Collapse
        defaultActiveKey={[props.diffResult?.edited?.keys?.()?.next()?.value]}
        accordion={true}
      >
        {EditedComponentsRender}
      </Collapse>
    </div>
  )
}

export default ComponentPanel
