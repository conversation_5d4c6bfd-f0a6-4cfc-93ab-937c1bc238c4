import { Col, Row, Typography } from '@m-ui/react'
import type { ILinkageConfig } from '../type'
const { Text } = Typography

const TXT_MAP = {
  autoRefresh: '自动刷新',
  autoTakeValue: '自动携带',
  dataBySubmit: '提交携带',
  dataByOutsideParams: '外部参数携带',
  dataByAll: '全部携带',
}

const LinkageCompare = ({
  developLinkageConfig,
  baseLinkageConfig,
  tag,
}: {
  developLinkageConfig: ILinkageConfig | undefined
  baseLinkageConfig: ILinkageConfig | undefined
  tag: JSX.Element
}): JSX.Element => {
  return (
    <>
      {tag}
      {Object.keys(TXT_MAP).map((key) => {
        const developValue = developLinkageConfig?.[key as keyof ILinkageConfig]
        const baseValue = baseLinkageConfig?.[key as keyof ILinkageConfig]
        if (developValue === baseValue) {
          return null
        }
        return (
          <Row key={key} style={{ marginBottom: 8 }}>
            <Col span={12}>
              <Text strong>{TXT_MAP[key as keyof typeof TXT_MAP]}：</Text>
              {String(developValue)}
            </Col>
            <Col span={12}>
              <Text strong>{TXT_MAP[key as keyof typeof TXT_MAP]}：</Text>
              {String(baseValue)}
            </Col>
          </Row>
        )
      })}
    </>
  )
}

export default LinkageCompare
