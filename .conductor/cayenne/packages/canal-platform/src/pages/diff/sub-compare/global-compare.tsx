// 初始化一个react组件
import type { BindIIFEAction, E2EServerSchema } from '@ad/e2e-schema'
import { isApiExpression } from '@ad/e2e-schema-utils'
import { normalizeExpression } from '@kael/schema-utils'
import { Collapse, Row, Space, Tag, Typography } from '@m-ui/react'
import { DiffEditor } from '@monaco-editor/react'
import { isEqual, isUndefined } from 'lodash'
import { useEffect, useState } from 'react'
import ExpressionDiff from '../component/expression-code-diff'
import type { CompareComponentDataSourceResult } from '../utils/util'
import { compareComponentDataSource } from '../utils/util'
import ApiCompare from './api-compare'
const { Panel } = Collapse
const GlobalCompare = ({
  developSchema,
  baseSchema,
  tag,
}: {
  developSchema: E2EServerSchema | undefined
  baseSchema: E2EServerSchema | undefined
  tag: JSX.Element
}): JSX.Element => {
  const [dataSourceCompareInfo, setDataSourceCompareInfo] = useState<
    CompareComponentDataSourceResult[]
  >([])
  const [isModelEqual, setIsModelEqual] = useState(true)

  function decodeActions(
    iife: E2EServerSchema['iife'] | undefined,
  ): BindIIFEAction[] {
    let actions: BindIIFEAction[] = []
    const normalizedIIFE = isUndefined(iife) ? iife : normalizeExpression(iife)
    if (isApiExpression(normalizedIIFE)) {
      if (normalizedIIFE.transform) {
        const normalizedTransform = normalizeExpression(
          normalizedIIFE.transform,
        )
        const m = normalizedTransform?.code?.match?.(
          /actions = JSON.parse\((.*)\);\n/,
        )

        if (m?.[1]) {
          try {
            actions = JSON.parse(JSON.parse(m[1])) || actions
          } catch (err) {
            console.error('decodeJSExpr2BindIIFEFormValues err', err)
          }
        }
      }
    }

    return actions
  }
  async function getCompare(): Promise<void> {
    // 比较组件级数据源配置
    const dataSourceCompare = await compareComponentDataSource(
      developSchema?.apis || {},
      baseSchema?.apis || {},
    )
    if (dataSourceCompare) {
      setDataSourceCompareInfo(dataSourceCompare)
    }
    setIsModelEqual(isEqual(baseSchema?.model, developSchema?.model))
  }
  useEffect(() => {
    getCompare()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return (
    <div style={{ height: '700px', overflow: 'scroll' }}>
      <div>{tag}</div>
      <Collapse defaultActiveKey={['1']}>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                模块级接口
                {dataSourceCompareInfo.length > 0 ? (
                  <Tag color="orange">
                    共有【{dataSourceCompareInfo.length}】个组件接口配置有差异
                  </Tag>
                ) : (
                  <Tag>无差异</Tag>
                )}
              </Space>
            </Typography.Text>
          }
          key="1"
        >
          {dataSourceCompareInfo.length > 0 && (
            <ApiCompare
              dataSourceCompareInfo={dataSourceCompareInfo}
              tag={tag}
            />
          )}
        </Panel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                埋点列表
                {!isEqual(baseSchema?.tracks, developSchema?.tracks) ? (
                  <Tag color="orange">有差异</Tag>
                ) : (
                  <Tag>无差异</Tag>
                )}
              </Space>
            </Typography.Text>
          }
          key="2"
        >
          {!isEqual(baseSchema?.tracks, developSchema?.tracks) && (
            <DiffEditor
              height={400}
              width={1200}
              language={'json'}
              original={JSON.stringify(baseSchema?.tracks || [], null, '\t')}
              modified={JSON.stringify(developSchema?.tracks || [], null, '\t')}
              options={{
                renderIndicators: true,
                readOnly: true,
              }}
            />
          )}
        </Panel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                前端动作
                {!isEqual(baseSchema?.iife, developSchema?.iife) ? (
                  <Tag color="orange">有差异</Tag>
                ) : (
                  <Tag>无差异</Tag>
                )}
              </Space>
            </Typography.Text>
          }
          key="3"
        >
          {!isEqual(baseSchema?.iife, developSchema?.iife) && (
            <>
              <ExpressionDiff
                base={baseSchema?.iife}
                develop={developSchema?.iife}
                codeTxt={'生成动作编号'}
              />
              <Row>
                <Typography.Text mark>{'动作列表'}：</Typography.Text>
              </Row>
              <DiffEditor
                height={400}
                width={1200}
                language="json"
                original={JSON.stringify(
                  decodeActions(baseSchema?.iife),
                  null,
                  '\t',
                )}
                modified={JSON.stringify(
                  decodeActions(developSchema?.iife),
                  null,
                  '\t',
                )}
                options={{
                  renderIndicators: true,
                  readOnly: true,
                }}
              />
            </>
          )}
        </Panel>
        <Panel
          header={
            <Typography.Text strong>
              <Space>
                模型
                {!isModelEqual ? (
                  <Tag color="orange">有差异</Tag>
                ) : (
                  <Tag>无差异</Tag>
                )}
              </Space>
            </Typography.Text>
          }
          key="4"
        >
          {!isModelEqual && (
            <ExpressionDiff
              base={
                baseSchema?.model
                  ? {
                      type: 'js',
                      ...baseSchema?.model,
                    }
                  : ''
              }
              develop={
                developSchema?.model
                  ? {
                      type: 'js',
                      ...developSchema?.model,
                    }
                  : ''
              }
              codeTxt={'模型'}
            />
          )}
        </Panel>
      </Collapse>
    </div>
  )
}

export default GlobalCompare
