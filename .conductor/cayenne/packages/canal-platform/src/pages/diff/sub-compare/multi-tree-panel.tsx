import { Card, Col, Row, Space, Tag } from '@m-ui/react'
import { useMemo } from 'react'
import PureTree from '../component/tree'
import { type IDIFF_COMMON_PROPS } from '../type'
import { getTreeRenderData } from '../utils/util'

const MultiTreePanel = ({
  baseSchema,
  developSchema,
  type,
  developComponentsConfig,
  baseComponentsConfig,
  diffResult,
  tag,
}: IDIFF_COMMON_PROPS): JSX.Element => {
  // const [mergedTree, setMergedTree] = useState<TreeData>()

  const masterTree = useMemo(() => {
    if (baseSchema && diffResult) {
      return getTreeRenderData(
        baseSchema.flattenedView?.childComponentIdMap,
        baseComponentsConfig,
        diffResult,
        baseSchema.flattenedView.rootComponentId,
        true,
      )
    }
  }, [baseSchema, baseComponentsConfig, diffResult])
  const developTree = useMemo(() => {
    if (developSchema) {
      return getTreeRenderData(
        developSchema.flattenedView?.childComponentIdMap,
        developComponentsConfig,
        diffResult,
        developSchema.flattenedView?.rootComponentId,
        false,
      )
    }
  }, [developSchema, developComponentsConfig, diffResult])
  return (
    <>
      <div style={{ padding: '8px 0' }}>
        <Tag color="success">
          <Space>新增{diffResult?.added?.size || 0}</Space>
        </Tag>
        <Tag color="warning">
          <Space>修改{diffResult?.edited?.size || 0}</Space>
        </Tag>
        <Tag color="error">
          <Space>删除{diffResult?.removed?.size || 0}</Space>
        </Tag>
      </div>
      <div>{tag}</div>
      <Row style={{ padding: '8px 0' }}>
        <Col
          span={type === 'merge' ? 8 : 12}
          style={{
            border: '1px solid #f0f0f0',
            borderRadius: 4,
          }}
        >
          <Card
            bordered={false}
            bodyStyle={{ height: 550, overflow: 'scroll' }}
          >
            {!!masterTree && (
              <PureTree tree={masterTree} isDragEnabled={false} />
            )}
          </Card>
        </Col>
        <Col
          span={type === 'merge' ? 8 : 12}
          style={{
            border: '1px solid #f0f0f0',
            borderRadius: 4,
          }}
        >
          <Card
            bordered={false}
            bodyStyle={{ height: 550, overflow: 'scroll' }}
          >
            {!!developTree && (
              <PureTree tree={developTree} isDragEnabled={false} />
            )}
          </Card>
        </Col>

        {type === 'merge' && (
          <Col
            span={8}
            style={{
              border: '1px solid #f0f0f0',
              borderRadius: 4,
            }}
          >
            {/* {!!mergedTree && (
              <PureTree tree={mergedTree} isDragEnabled={true} />
            )} */}
          </Col>
        )}
      </Row>
    </>
  )
}

export default MultiTreePanel
