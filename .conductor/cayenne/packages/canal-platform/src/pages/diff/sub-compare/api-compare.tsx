import type { E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue } from '@ad/e2e-schema'
import { Col, Collapse, Row, Space, Typography } from '@m-ui/react'
import { isEqual } from 'lodash'
import CodeDiff from '../component/code-diff'
import type { CompareComponentDataSourceResult } from '../utils/util'
const { Panel } = Collapse
const TXT_MAP = {
  editName: '编辑面板配置的名称',
  dataSourceName: '数据源名称',
  path: '接口路径',
  method: '方法',
  type: '类型',
  dataSourceId: '数据源ID',
  domain: '域名',
  args: '参数',
  if: '执行条件',
}
const CODE_RENDER = ['if', 'args']
type TxtMapKeys = keyof typeof TXT_MAP

const ApiCompare = ({
  dataSourceCompareInfo,
  tag,
}: {
  dataSourceCompareInfo: CompareComponentDataSourceResult[]
  tag: JSX.Element
}): JSX.Element => {
  return (
    <Collapse>
      {dataSourceCompareInfo.map((item, index) => {
        return (
          <Panel
            key={item.id}
            header={
              <Row>
                <Col style={{ width: 350 }}>
                  <Typography.Text strong>
                    <Space>
                      <span>接口{index + 1}</span>
                      <span>id：{item.id}</span>
                    </Space>
                  </Typography.Text>
                </Col>
                <Col span={24}>
                  <Space>
                    {!!item.develop?.editName && (
                      <span>
                        【当前版本编辑名称】： {item.develop?.editName}
                      </span>
                    )}
                    {!!item.develop?.dataSourceName && (
                      <span>
                        【当前版本数据源名称】： {item.develop?.dataSourceName}
                      </span>
                    )}
                  </Space>
                </Col>
              </Row>
            }
          >
            {tag}
            {Object.keys(TXT_MAP).map((key) => {
              const typedKey = key as TxtMapKeys // Cast key to TxtMapKeys
              const isCodeRender = CODE_RENDER.includes(typedKey)
              if (isEqual(item.base?.[typedKey], item.develop?.[typedKey])) {
                return null
              }
              if (!isCodeRender) {
                return (
                  <div key={typedKey}>
                    <Row style={{ marginBottom: 16 }}>
                      <Col span={12}>
                        <Space>
                          {
                            <Typography.Text strong>
                              {TXT_MAP[typedKey]}：
                            </Typography.Text>
                          }
                          {String(item.base?.[typedKey] || '')}
                        </Space>
                      </Col>
                      <Col span={12}>
                        <Space>
                          {
                            <Typography.Text strong>
                              {TXT_MAP[typedKey]}：
                            </Typography.Text>
                          }
                          {String(item.develop?.[typedKey] || '')}
                        </Space>
                      </Col>
                    </Row>
                  </div>
                )
              } else {
                return (
                  <div key={typedKey}>
                    <Row style={{ marginBottom: 16 }}>
                      <Col span={12}>
                        <Space>
                          {
                            <Typography.Text strong>
                              {TXT_MAP[typedKey]}：
                            </Typography.Text>
                          }
                        </Space>
                      </Col>
                      <Col span={12}>
                        <Space>
                          {
                            <Typography.Text strong>
                              {TXT_MAP[typedKey]}：
                            </Typography.Text>
                          }
                        </Space>
                      </Col>
                    </Row>
                    <Row>
                      <CodeDiff
                        base={
                          item.base?.[typedKey] as
                            | E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue
                            | undefined
                        }
                        develop={
                          item.develop?.[typedKey] as
                            | E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue
                            | undefined
                        }
                      />
                    </Row>
                  </div>
                )
              }
            })}
          </Panel>
        )
      })}
    </Collapse>
  )
}

export default ApiCompare
