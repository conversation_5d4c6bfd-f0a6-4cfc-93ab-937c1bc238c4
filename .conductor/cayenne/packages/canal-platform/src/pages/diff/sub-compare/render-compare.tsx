import type { E2EServerSchemaComponent } from '@ad/e2e-schema'
import { Collapse, Space, Tag, Typography } from '@m-ui/react'
import { get, isEqual } from 'lodash'
import ExpressionDiff from '../component/expression-code-diff'
import type { CompareStructure } from '../type'
const { Panel } = Collapse

const RenderCompare = ({
  developComponent,
  baseComponent,
  renderConfig,
  tag,
}: {
  developComponent: E2EServerSchemaComponent
  baseComponent: E2EServerSchemaComponent
  renderConfig: CompareStructure
  tag: JSX.Element
}): JSX.Element => {
  return (
    <>
      {tag}
      <Collapse>
        {renderConfig?.map((item) => {
          const baseValue = get(baseComponent, item.path)
          const developValue = get(developComponent, item.path)
          const valueIsEqual = isEqual(baseValue, developValue)
          return (
            <Panel
              key={item.txt}
              header={
                <Typography.Text strong>
                  <Space>
                    {item.txt}
                    {valueIsEqual ? (
                      <Tag>无差异</Tag>
                    ) : (
                      <Tag color="orange">有差异</Tag>
                    )}
                  </Space>
                </Typography.Text>
              }
            >
              {!item.deepProps && (
                <ExpressionDiff base={baseValue} develop={developValue} />
              )}
              {!!item.deepProps && (
                <Collapse>
                  {item.deepProps?.map((deepItem) => {
                    return (
                      <Panel
                        key={deepItem.key}
                        header={
                          <Typography.Text strong>
                            {deepItem.txt}
                          </Typography.Text>
                        }
                      >
                        <ExpressionDiff
                          base={get(baseValue || {}, deepItem.path)}
                          develop={get(developValue || {}, deepItem.path)}
                        />
                      </Panel>
                    )
                  })}
                </Collapse>
              )}
            </Panel>
          )
        })}
      </Collapse>
    </>
  )
}
export default RenderCompare
