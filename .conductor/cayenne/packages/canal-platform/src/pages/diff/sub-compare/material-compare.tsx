import type { ComponentDetail } from '@/services/backend/models'
import { Col, Row, Typography } from '@m-ui/react'
import { DiffEditor } from '@monaco-editor/react'
import { isEqual } from 'lodash'
const DIFF_MATERIAL_TYPE = {
  name: {
    txt: '名称',
    type: 'string',
  },
  version: {
    txt: '版本号（若有差异，请在编辑时候考虑升级）',
    type: 'string',
  },
  propsConfig: {
    txt: '映射属性',
    type: 'JSON',
  },
  resourceUrl: {
    txt: '资源路径',
    type: 'string',
  },
  associatedComponentId: {
    txt: '关联物料中心ID',
    type: 'string',
  },
  associatedComponentVersion: {
    txt: '关联物料中心版本',
    type: 'string',
  },
}

const MaterialCompare = ({
  materialCompareInfo,
}: {
  materialCompareInfo: {
    develop: ComponentDetail | undefined
    base: ComponentDetail | undefined
  }
}): JSX.Element => {
  return (
    <div style={{ marginBottom: 16, border: '1px solid #e9e9e9' }}>
      {Object.entries(DIFF_MATERIAL_TYPE).map(([key, item]) => {
        const developValue =
          materialCompareInfo?.develop?.[key as keyof ComponentDetail]
        const baseValue =
          materialCompareInfo?.base?.[key as keyof ComponentDetail]
        const isSame = isEqual(developValue, baseValue)
        if (isSame) {
          return null
        }
        return (
          <div key={key} style={{ padding: 16 }}>
            <Row>
              <Col>
                <Typography.Text mark>{item.txt}：</Typography.Text>
              </Col>
            </Row>
            <div>
              <DiffEditor
                height={item.type === 'JSON' ? 400 : 30}
                width={1200}
                modified={(developValue as string) || ''}
                original={(baseValue as string) || ''}
                language={item.type === 'JSON' ? 'json' : 'javascript'}
                options={{
                  renderIndicators: true,
                  enableSplitViewResizing: true,
                  readOnly: true,
                }}
              />
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default MaterialCompare
