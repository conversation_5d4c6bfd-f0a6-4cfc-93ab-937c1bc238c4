import type {
  ComponentCode,
  E2EServerSchema,
  E2EServerSchemaComponent,
} from '@ad/e2e-schema'
import { type ItemId, type TreeItem } from '@atlaskit/tree'

export interface IDIFF_COMMON_PROPS {
  baseSchema: E2EServerSchema | undefined
  developSchema: E2EServerSchema | undefined
  type: 'diff' | 'merge'
  domainCode: string
  developComponentsConfig: ComponentSchemaConfigMap
  baseComponentsConfig: ComponentSchemaConfigMap
  diffResult: DiffInfo | undefined
  tag: JSX.Element
}

export type ComponentSchemaConfigMap = Record<
  string,
  {
    config: E2EServerSchemaComponent
    code: ComponentCode | undefined
  }
>

export interface TreeData {
  rootId: ItemId
  items: { [id: string]: TreeItem }
}

export type DiffInfo = {
  added: Map<string, number>
  removed: Map<string, number>
  edited: Map<string, number>
}

export interface COMPARE_PROPS extends IDIFF_COMMON_PROPS {
  targetId: string
}

export interface ILinkageConfig {
  autoRefresh: boolean
  autoTakeValue: boolean
  dataBySubmit: boolean
  dataByOutsideParams: boolean
  dataByAll: boolean
}

export type CompareStructure = {
  txt: string
  path: string[]
  deepProps?: {
    txt: string
    key: string
    path: string[]
  }[]
}[]
