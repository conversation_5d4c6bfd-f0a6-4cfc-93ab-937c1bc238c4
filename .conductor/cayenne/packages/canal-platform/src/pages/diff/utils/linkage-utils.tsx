import type { E2EServerSchema } from '@ad/e2e-schema'
import type { ILinkageConfig } from '../type'
type Linkage = E2EServerSchema['linkage'] | undefined
export const isAutoRefresh = (linkage: Linkage, id: string): boolean => {
  return linkage?.autoRefreshByComponent?.includes(id) || false
}
export const isAutoTakeValue = (linkage: Linkage, id: string): boolean => {
  return (
    linkage?.componentDataParams?.byRefreshType?.auto?.includes(id) || false
  )
}
export const isComponentDataBySubmit = (
  linkage: Linkage,
  id: string,
): boolean => {
  return (
    linkage?.componentDataParams?.byRefreshType?.submit?.includes(id) || false
  )
}

export const isComponentDataByOutsideParams = (
  linkage: Linkage,
  id: string,
): boolean => {
  return (
    linkage?.componentDataParams?.byRefreshType?.['outside-params']?.includes(
      id,
    ) || false
  )
}

export const isComponentDataByAll = (linkage: Linkage, id: string): boolean => {
  return linkage?.componentDataParams?.common?.includes(id) || false
}

export const generateLinkageConfig = (
  linkage: Linkage,
  id: string,
): ILinkageConfig => {
  return {
    autoRefresh: isAutoRefresh(linkage, id),
    autoTakeValue: isAutoTakeValue(linkage, id),
    dataBySubmit: isComponentDataBySubmit(linkage, id),
    dataByOutsideParams: isComponentDataByOutsideParams(linkage, id),
    dataByAll: isComponentDataByAll(linkage, id),
  }
}
