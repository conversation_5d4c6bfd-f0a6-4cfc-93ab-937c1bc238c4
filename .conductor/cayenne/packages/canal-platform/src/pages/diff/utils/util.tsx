import { getInternalComponentLibSchemaUrl } from '@/pages/designer/materials'
import { getComponentDetail } from '@/services/backend/component'
import { getDataSourceDetail } from '@/services/backend/data_source'
import type { ComponentDetail } from '@/services/backend/models'
import { INTERNAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import type {
  ComponentCode,
  E2ESchemaChildComponentIdAndPath,
  E2EServerAPI,
  E2EServerSchema,
  E2EServerSchemaComponent,
  E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue,
} from '@ad/e2e-schema'
import { type TreeItem } from '@atlaskit/tree'
import type { CMSProp, ComponentMaterialSchema } from '@kael/material-schema'
import { Empty } from '@m-ui/react'
import { DiffEditor, Editor } from '@monaco-editor/react'
import * as jsondiffpatch from 'jsondiffpatch'
import {
  get,
  has,
  isEmpty,
  isEqual,
  isObject,
  isString,
  isUndefined,
} from 'lodash'
import {
  type CompareStructure,
  type ComponentSchemaConfigMap,
  type DiffInfo,
  type TreeData,
} from '../type'

let canalInternalComponentConfig: ComponentMaterialSchema[]
export interface ApiConfigInfo {
  path: string
  method: string
  type: string
  args: E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue | undefined
  if: E2EServerSchemaNormalizedExpressionWithDegradedJSONataValue | undefined
  dataSourceName?: string | undefined
  dataSourceId?: string | undefined
  domain: string
  editName: string
}
export interface CompareComponentDataSourceResult {
  id: string
  develop: ApiConfigInfo | undefined
  base: ApiConfigInfo | undefined
}

async function getInternalComponent(): Promise<void> {
  const localComponentInfoUrl = await getInternalComponentLibSchemaUrl()
  if (localComponentInfoUrl) {
    const res = await fetch(localComponentInfoUrl)
    const data = await res.json()
    canalInternalComponentConfig = data?.components || []
  }
}
/**
 * 找到目标内部组件配置
 * @param type
 * @returns
 */
function findTargetInternalComponentConfig(
  type: string,
): Map<string, CMSProp> | undefined {
  const target = canalInternalComponentConfig.find((item) => item.type === type)
  if (target) {
    return generatePropsConfigMap(target)
  }
  return undefined
}

function generatePropsConfigMap(
  target: ComponentMaterialSchema,
): Map<string, CMSProp> {
  const propsMap = new Map<string, CMSProp>()
  function recurse(items: CMSProp[]): void {
    items.forEach((item) => {
      if (item.path) {
        // TODO,涉及到比较特殊的实现比如style，真的有多层嵌套，这个可能不一定能匹配上属性配置，记得优化
        propsMap.set(item.path.join('.'), item)
      }
      if ('items' in item && Array.isArray(item.items)) {
        recurse(item.items)
      }
    })
  }
  if (target && Array.isArray(target.props)) {
    recurse(target.props)
  }
  return propsMap
}

export const getTreeRenderData = (
  IdMap: Record<string, (string | E2ESchemaChildComponentIdAndPath)[]>,
  componentsConfig: ComponentSchemaConfigMap,
  diffInfo: DiffInfo | undefined,
  rootId: string,
  isBase?: boolean,
): TreeData => {
  const tree: TreeData = {
    rootId: 'root',
    items: {},
  }
  function construct(key: string | E2ESchemaChildComponentIdAndPath): void {
    let id: string = ''

    if (isString(key)) {
      id = key
    } else if (key.id) {
      id = key.id
    }
    const children: string[] = []
    IdMap[id]?.map?.((child) => {
      if (isString(child)) {
        children.push(child)
      } else {
        children.push(child.id)
      }
    })
    const item: TreeItem = {
      id: id,
      children: children || [],
      hasChildren: !!children.length,
      isExpanded: true,
      isChildrenLoading: false,
      data: {
        id: id,
        name: componentsConfig[id]?.config?.name || key,
        path: !isString(key) && key.path,
      },
    }
    if (isBase) {
      if (diffInfo?.removed.has(id)) {
        item.data.isRemoved = true
      }
    }
    if (isBase === false) {
      if (diffInfo?.added.has(id)) {
        item.data.isAdded = true
      }
      if (diffInfo?.edited.has(id)) {
        item.data.isEdited = true
      }
    }

    tree.items[id] = item
    IdMap[id]?.forEach?.((child) => {
      construct(child)
    })
  }
  construct(rootId || 'root')
  return tree
}

export const getComponentConfigMap = (
  schema: E2EServerSchema | undefined,
): ComponentSchemaConfigMap => {
  const componentMap: ComponentSchemaConfigMap = {}
  const arr = schema?.flattenedView?.components || []
  const componentCode = schema?.componentCodes || {}
  arr.forEach((item) => {
    componentMap[item.id] = {
      config: item,
      code: componentCode[item.type],
    }
  })
  return componentMap
}
/**
 * 转化为Map
 * @param obj
 * @param configMap
 * @returns
 */
export const collectKeys = (
  obj: Record<string, (string | E2ESchemaChildComponentIdAndPath)[]>,
  configMap: ComponentSchemaConfigMap,
): Map<
  string,
  {
    config: E2EServerSchemaComponent
    code: ComponentCode | undefined
    struct?: {
      id: string
      path: string[]
    }
  }
> => {
  const keys = new Map() // 使用 Set 以避免重复
  function recurse(
    current: Record<string, (string | E2ESchemaChildComponentIdAndPath)[]>,
  ): void {
    for (const key in current) {
      keys.set(key, configMap[key] || {}) // 添加当前键
      const value = current[key]
      if (Array.isArray(value)) {
        value.forEach((item) => {
          if (typeof item === 'object' && item !== null) {
            // 如果是对象，提取 id
            if (item.id) {
              keys.set(item.id, { ...(configMap[item.id] || {}), struct: item })
            }
          } else if (typeof item === 'string') {
            // 如果是字符串，直接添加
            keys.set(item, configMap[item] || {})
          }
        })
      }
    }
  }

  recurse(obj)
  return keys
}

export const getPureTwoDiffInfo = (
  developSchema: E2EServerSchema,
  masterSchema: E2EServerSchema,
): DiffInfo => {
  const compareResult: DiffInfo = {
    added: new Map<string, number>(),
    removed: new Map<string, number>(),
    edited: new Map<string, number>(),
  }
  const developComponent = collectKeys(
    developSchema?.flattenedView?.childComponentIdMap || {},
    getComponentConfigMap(developSchema),
  )
  const masterComponent = collectKeys(
    masterSchema?.flattenedView?.childComponentIdMap || {},
    getComponentConfigMap(masterSchema),
  )
  const keysDev = new Set(developComponent.keys())
  const keysMaster = new Set(masterComponent.keys())
  const removed = [...keysMaster].filter((key) => !keysDev.has(key))
  const added = [...keysDev].filter((key) => !keysMaster.has(key))
  const sameKeys = [...keysDev].filter((key) => keysMaster.has(key))
  sameKeys.forEach((key) => {
    const developConfig = developComponent.get(key)
    const masterConfig = masterComponent.get(key)
    if (!isEqual(developConfig, masterConfig)) {
      compareResult.edited.set(key, 1)
    }
  })
  compareResult.added = new Map(added.map((value, index) => [value, index]))
  compareResult.removed = new Map(removed.map((value, index) => [value, index]))
  console.log(compareResult)
  return compareResult
}

export const drawDiffFromConfigs = (
  developComponentsConfig: ComponentSchemaConfigMap,
  masterComponentsConfig: ComponentSchemaConfigMap,
  targetId: string,
): unknown => {
  const target = developComponentsConfig[targetId]
  const base = masterComponentsConfig[targetId]
  if (!target || !base) {
    return
  }
  const delta = jsondiffpatch.diff(base, target)
  console.log(delta)
  return delta
}

export const getComponentPropsConfig = async (
  componentsSchemaConfig: ComponentSchemaConfigMap,
  targetId: string,
): Promise<Map<string, CMSProp> | undefined> => {
  const conf = componentsSchemaConfig[targetId]
  if (conf) {
    // 大运河内置组件
    if (
      !conf.code &&
      conf.config?.type?.startsWith(INTERNAL_COMPONENT_LIB_NAME + '::')
    ) {
      if (!canalInternalComponentConfig) {
        await getInternalComponent()
      }
      // @ad/canal-components::Submodule  提取Submodule这个字符串
      const componentType = conf.config.type.split('::')[1]
      const internalComponentConfig =
        findTargetInternalComponentConfig(componentType)
      return internalComponentConfig
    } else {
      const bizComponentConfig = await getBizComponentConfig(
        conf.code,
        conf.config.type,
      )

      if (bizComponentConfig) {
        return generatePropsConfigMap(
          safeJsonParse(
            bizComponentConfig.propsConfig,
          ) as ComponentMaterialSchema,
        )
      }
    }
  }
  return undefined
}

export function isValidJSON(str: string): boolean {
  try {
    JSON.parse(str)
    return true
  } catch (e) {
    return false
  }
}

export function isSafeStringifiable(value: unknown): boolean {
  // 检查类型
  const type = typeof value
  // 允许的类型：string, number, boolean, symbol, undefined
  return (
    type === 'string' ||
    type === 'number' ||
    type === 'boolean' ||
    type === 'symbol' ||
    type === 'undefined'
  )
}

export function commonValueDiffRender(
  developValue: unknown,
  baseValue: unknown,
): JSX.Element {
  return (
    <DiffEditor
      height={isSafeStringifiable(developValue) ? 30 : 300}
      width={1200}
      modified={
        isSafeStringifiable(developValue)
          ? String(developValue as string)
          : JSON.stringify(developValue, null, '\t')
      }
      original={
        isSafeStringifiable(baseValue)
          ? String(baseValue as string)
          : JSON.stringify(baseValue, null, '\t')
      }
      language={isSafeStringifiable(developValue) ? 'javascript' : 'json'}
      options={{
        renderIndicators: true,
        enableSplitViewResizing: true,
        readOnly: true,
      }}
    />
  )
}
export function commonValueRender(value: unknown): JSX.Element {
  if (isUndefined(value)) {
    return (
      <Empty
        style={{ width: 600 }}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description={<span>没有配置</span>}
      />
    )
  }
  return (
    <Editor
      height={200}
      width={600}
      language={isSafeStringifiable(value) ? 'javascript' : 'json'}
      value={
        isSafeStringifiable(value)
          ? String(value as string)
          : JSON.stringify(value, null, '\t')
      }
    />
  )
}

/**
 * 比较两个组件的物料
 * @param develop
 * @param base
 * @param type
 * @returns
 */
export async function compareComponentMaterial(
  develop: ComponentCode | undefined,
  base: ComponentCode | undefined,
  type: string,
): Promise<
  | {
      develop: ComponentDetail | undefined
      base: ComponentDetail | undefined
    }
  | undefined
> {
  if (!isEqual(develop, base)) {
    return {
      develop: await getBizComponentConfig(develop, type),
      base: await getBizComponentConfig(base, type),
    }
  }
}
export async function getBizComponentConfig(
  componentCode: ComponentCode | undefined,
  type: string,
): Promise<ComponentDetail | undefined> {
  if (type?.startsWith('@ad/canal-biz-components::') && componentCode) {
    const version = componentCode.version
    const storedItem = sessionStorage.getItem(type + 'version:' + version)
    if (storedItem) {
      return safeJsonParse(storedItem) as ComponentDetail | undefined
    }
    const componentID = type.split('::')[1]

    const componentConfig = await getComponentDetail({
      id: componentID,
      version,
    })
    if (componentConfig.result === 1 && componentConfig.data) {
      sessionStorage.setItem(
        type + 'version:' + version,
        JSON.stringify(componentConfig.data),
      )
      return componentConfig.data
    }
  }
}

export function safeJsonParse(jsonString: string): unknown {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.error('Invalid JSON string:', error)
    return null // or you can return a default value
  }
}
export async function compareComponentDataSource(
  develop: Record<string, E2EServerAPI> | null,
  base: Record<string, E2EServerAPI> | null,
): Promise<CompareComponentDataSourceResult[] | void> {
  const diff = jsondiffpatch.diff(develop, base)
  const diffKeys = Object.keys(diff || {})
  if (!diffKeys.length) return
  const diffResult: Array<CompareComponentDataSourceResult> = []
  for (const key of diffKeys) {
    const developConfig = develop?.[key]
      ? await getApiConfigInfo(develop[key])
      : undefined
    const baseConfig = base?.[key]
      ? await getApiConfigInfo(base[key])
      : undefined
    diffResult.push({
      id: key,
      develop: developConfig,
      base: baseConfig,
    })
  }
  return diffResult
}

async function getApiConfigInfo(
  data: E2EServerAPI,
): Promise<ApiConfigInfo | undefined> {
  if (data.dataSourceId) {
    const dataSource = await getDataSourceDetail({
      id: data.dataSourceId,
    })
    if (dataSource.result === 1 && dataSource.data) {
      return {
        path: dataSource.data.path || '',
        method: dataSource.data.method || '',
        type: dataSource.data.type || 'http',
        args: data.args,
        if: data.if,
        dataSourceName: dataSource.data.name,
        dataSourceId: dataSource.data.id,
        domain: dataSource.data.productionDomain || '',
        editName: data.name || '',
      }
    }
  } else {
    return {
      path: data.method || '',
      method: 'POST', //原来默认的都是post
      type: 'http',
      args: data.args,
      if: data.if,
      domain: data.service || '',
      editName: data.name || '',
    }
  }
}

export const isNonEmptyObject = (value: unknown): boolean => {
  return isObject(value) && !isEmpty(value)
}

export const compareIsEqualByConfigPath = (
  baseValue: unknown,
  developValue: unknown,
  config: CompareStructure,
): boolean => {
  let equal = true
  if (isObject(baseValue) && isObject(developValue)) {
    config?.forEach?.((item) => {
      const base = get(baseValue, item.path)
      const develop = get(developValue, item.path)
      if (!isEqual(base, develop)) {
        equal = false
      }
    })
  } else {
    return isEqual(baseValue, developValue)
  }
  return equal
}

export function getTrueValue(valueParams: unknown): string {
  if (isObject(valueParams)) {
    if (has(valueParams, 'value')) {
      if (isObject(get(valueParams, 'value'))) {
        return JSON.stringify(get(valueParams, 'value'), null, '/t')
      } else {
        return String(get(valueParams, 'value'))
      }
    } else {
      return JSON.stringify(valueParams, null, '/t')
    }
  } else {
    return String(valueParams)
  }
}
