import { findModuleVersionLogDetail } from '@/services/backend/module-version-log'
import type { E2EServerSchema } from '@ad/e2e-schema'
import { Modal, Space, Tooltip, Typography } from '@m-ui/react'
import { useState } from 'react'
import { dateFormat } from '../change/const'
import DiffTag from './component/diff-tag'
import SingleModuleDiffPanel from './component/single-module-diff-panel'

const { Text } = Typography

const LogDiffModel = ({
  currenLogId,
  baseLogId,
  domainCode,
}: {
  currenLogId: string
  baseLogId: string
  domainCode: string
}): JSX.Element => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [baseSchema, setBaseSchema] = useState<E2EServerSchema>()
  const [currentSchema, setCurrentSchema] = useState<E2EServerSchema>()
  const [baseTagText, setBaseTagText] = useState<string>('')
  const [currentTagText, setCurrentTagText] = useState<string>('')
  const getData = async (
    id: string,
    callback: (schema: E2EServerSchema | undefined, tag: string) => void,
  ): Promise<void> => {
    if (!id) return callback(undefined, '')
    return await findModuleVersionLogDetail({
      logId: id,
    }).then((res) => {
      if (res.data?.moduleContent) {
        try {
          const content = JSON.parse(res.data.moduleContent)
          callback &&
            callback(
              content,
              `版本：${res.data.moduleVersion}  编辑时间：${dateFormat(
                res.data.createdAt,
              )}`,
            )
        } catch (e) {
          console.log(e)
        }
      }
    })
  }

  const requestSchema = async (): Promise<void> => {
    await Promise.all([
      baseLogId
        ? getData(baseLogId as string, (schema, tag) => {
            if (schema) {
              setBaseSchema(schema)
              setBaseTagText(tag)
            }
          })
        : Promise.resolve(),
      getData(currenLogId, (schema, tag) => {
        if (schema) {
          setCurrentSchema(schema)
          setCurrentTagText(tag)
        }
      }),
    ])
      .then(() => {
        setIsModalOpen(true)
      })
      .catch((error) => {
        console.error('Error fetching data:', error)
      })
  }
  return (
    <>
      <Tooltip placement="top" title={'和同版本的上一个编辑记录做对比'}>
        <div
          onClick={(): void => {
            requestSchema()
          }}
          style={{
            cursor: 'pointer',
            display: 'inline-block',
            color: '#0075FF',
          }}
        >
          diff
        </div>
      </Tooltip>
      <Modal
        title={
          <Space>
            Diff面板
            <Text type="secondary" style={{ fontSize: 14 }}>
              点击步骤序号也可切换查看
            </Text>
          </Space>
        }
        visible={isModalOpen}
        width={1400}
        onOk={(): void => {}}
        onCancel={(): void => {
          setIsModalOpen(false)
        }}
        footer={null}
        destroyOnClose
        maskClosable={false}
      >
        <SingleModuleDiffPanel
          baseSchema={baseSchema}
          developSchema={currentSchema}
          type={'diff'}
          domainCode={domainCode}
          tag={
            <DiffTag
              baseTagText={baseTagText}
              developTagText={currentTagText}
            />
          }
        />
      </Modal>
    </>
  )
}

export default LogDiffModel
