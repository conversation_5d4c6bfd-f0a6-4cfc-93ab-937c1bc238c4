import type { E2EGlobalSchema, Model } from '@ad/e2e-schema'
import { DiffEditor } from '@monaco-editor/react'
import { memo, type FC } from 'react'

/**
 * 全局模块 DIFF 面板属性
 */
export interface GlobalModuleDiffPanelProps {
  /**
   * 原始 Schema
   */
  originalSchema?: E2EGlobalSchema
  /**
   * 当前 Schema
   */
  modifiedSchema?: E2EGlobalSchema
}

/**
 * 全局模块 DIFF 面板
 */
export const GlobalModuleDiffPanel: FC<GlobalModuleDiffPanelProps> = memo(
  ({ originalSchema, modifiedSchema }) => {
    if (!originalSchema || !modifiedSchema) {
      return null
    }
    return (
      <div>
        <h3>全局前端模型</h3>
        <DiffEditor
          height={380}
          original={getModelCode(originalSchema.globalFrontModel)}
          modified={getModelCode(modifiedSchema.globalFrontModel)}
          language={'json'}
          options={{
            renderIndicators: true,
            readOnly: true,
          }}
        />
        <h3>全局后端模型</h3>
        <DiffEditor
          height={380}
          original={getModelCode(originalSchema.globalBackModel)}
          modified={getModelCode(modifiedSchema.globalBackModel)}
          language={'json'}
          options={{
            renderIndicators: true,
            readOnly: true,
          }}
        />
      </div>
    )
  },
)

/**
 * 获取模型代码
 */
function getModelCode(model?: Model): string {
  return model?.codeTS || model?.codeES || 'null'
}
