import type { E2ESchemaNormalizedExpression } from '@ad/e2e-schema'
import { get, isObject } from 'lodash'
import { commonValueRender } from '../utils/util'
import ExpressRender from './express-render'
const UnknownTypeRender = ({
  propsValue,
  codeTxt,
}: {
  propsValue: unknown
  codeTxt?: string
}): JSX.Element => {
  return (
    <>
      {isObject(propsValue) &&
      ['apis', 'api', 'js', 'jsonata'].includes(get(propsValue, ['type'])) ? (
        <ExpressRender
          expression={propsValue as E2ESchemaNormalizedExpression}
          codeTxt={codeTxt}
        />
      ) : (
        commonValueRender(propsValue)
      )}
    </>
  )
}
export default UnknownTypeRender
