import Tree, {
  moveItemOnTree,
  mutateTree,
  type ItemId,
  type RenderItemParams,
  type TreeDestinationPosition,
  type TreeItem,
  type TreeSourcePosition,
} from '@atlaskit/tree'
import {
  SystemArrowLargeDownFill,
  SystemArrowLargeRightFill,
} from '@m-ui/icons'
import { Tag } from '@m-ui/react'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { type TreeData } from '../type'

const PreTextIcon = styled.span({
  display: 'inline-block',
  width: '16px',
  justifyContent: 'center',
  cursor: 'pointer',
  fontSize: '16px',
  fontWeight: 'bold',
})

const HoverItem = styled.div`
  &:hover {
    background-color: #e6f6ff;
    color: #42526e;
    fill: rgba(9, 30, 66, 0.04);
    -webkit-text-decoration: none;
    text-decoration: none;
  }
`
const getIcon = (
  item: TreeItem,
  onExpand: (itemId: ItemId) => void,
  onCollapse: (itemId: ItemId) => void,
): React.ReactNode => {
  if (item.children && item.children.length > 0) {
    return item.isExpanded ? (
      <PreTextIcon onClick={(): void => onCollapse(item.id)}>
        <SystemArrowLargeDownFill />
      </PreTextIcon>
    ) : (
      <PreTextIcon onClick={(): void => onExpand(item.id)}>
        <SystemArrowLargeRightFill />
      </PreTextIcon>
    )
  }
  return <PreTextIcon></PreTextIcon>
}

const PureTree = ({
  tree,
  isDragEnabled,
}: {
  tree: TreeData
  isDragEnabled: boolean
}): JSX.Element => {
  const [treeData, setTreeData] = useState(tree)
  const [dragEnabled, setDragEnabled] = useState(isDragEnabled)
  useEffect(() => {
    setTreeData(tree)
  }, [tree])
  useEffect(() => {
    setDragEnabled(isDragEnabled)
  }, [isDragEnabled])
  const renderItem = ({
    item,
    onExpand,
    onCollapse,
    provided,
  }: RenderItemParams): JSX.Element => {
    return (
      <HoverItem
        ref={provided.innerRef}
        {...provided.draggableProps}
        {...provided.dragHandleProps}
      >
        <span
          style={{
            display: 'inline-block',
            verticalAlign: 'top',
          }}
        >
          {getIcon(item, onExpand, onCollapse)}
        </span>
        <div style={{ display: 'inline-block', paddingBottom: 8 }}>
          <div style={{ display: 'flex', alignItems: 'top' }}>
            <div style={{ marginLeft: 10 }}>
              <div style={{ fontSize: 12, fontWeight: 600 }}>
                {item.data ? item.data.name : ''}
                {item.data.isAdded && (
                  <Tag
                    color="success"
                    size={'small'}
                    style={{ marginLeft: 4, fontSize: 11 }}
                  >
                    新增
                  </Tag>
                )}
                {item.data.isEdited && (
                  <Tag
                    color="warning"
                    size={'small'}
                    style={{ marginLeft: 4, fontSize: 11 }}
                  >
                    配置有修改
                  </Tag>
                )}
                {item.data.isRemoved && (
                  <Tag
                    color="error"
                    size={'small'}
                    style={{ marginLeft: 4, fontSize: 11 }}
                  >
                    右侧变更版本将此节点删除
                  </Tag>
                )}
              </div>
              <div style={{ fontSize: 10, color: '#8c8c8c' }}>
                {item.data ? item.data.id : ''}
              </div>
            </div>
          </div>
        </div>
      </HoverItem>
    )
  }
  const onExpand = (itemId: ItemId): void => {
    setTreeData(mutateTree(treeData, itemId, { isExpanded: true }))
  }

  const onCollapse = (itemId: ItemId): void => {
    setTreeData(mutateTree(treeData, itemId, { isExpanded: false }))
  }

  const onDragEnd = (
    source: TreeSourcePosition,
    destination?: TreeDestinationPosition,
  ): void => {
    if (!destination) {
      return
    }
    const newTree = moveItemOnTree(treeData, source, destination)
    setTreeData(newTree)
  }

  return (
    <div style={{ width: 400, margin: '0 8px' }}>
      <Tree
        tree={treeData}
        renderItem={renderItem}
        onExpand={onExpand}
        onCollapse={onCollapse}
        onDragEnd={onDragEnd}
        isDragEnabled={dragEnabled}
        isNestingEnabled
      />
    </div>
  )
}

export default PureTree
