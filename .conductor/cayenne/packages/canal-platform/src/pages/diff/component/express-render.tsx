import type {
  E2ESchemaExpressionAPI,
  E2ESchemaExpressionAPIs,
  E2ESchemaNormalizedExpression,
} from '@ad/e2e-schema'
import { Row, Typography } from '@m-ui/react'
import Editor from '@monaco-editor/react'
import { get, has, isObject } from 'lodash'
import { commonValueRender } from '../utils/util'
import CodeRender from './code-render'
const ExpressRender = ({
  expression,
  codeTxt,
}: {
  expression: E2ESchemaNormalizedExpression
  codeTxt?: string
}): JSX.Element => {
  return (
    <>
      {['api', 'apis'].includes(expression.type) && (
        <>
          <Row>
            <Typography.Text strong>API ID：</Typography.Text>
          </Row>
          <Row>
            <Editor
              height={100}
              width={600}
              language={expression.type === 'apis' ? 'json' : 'javascript'}
              value={
                expression.type === 'apis'
                  ? JSON.stringify(
                      (expression as E2ESchemaExpressionAPIs)?.apiIds,
                    )
                  : String((expression as E2ESchemaExpressionAPI)?.apiId)
              }
            />
          </Row>
        </>
      )}
      <Row>
        <Typography.Text strong>{codeTxt || '生成属性'}：</Typography.Text>
      </Row>
      <Row>
        <CodeRender codeConfig={expression?.transform || expression} />
      </Row>

      {has(expression, 'defaultValue') && (
        <>
          <Row>
            <Typography.Text strong>默认值：</Typography.Text>
          </Row>
          <Row>
            {commonValueRender(
              isObject(get(expression, ['defaultValue']))
                ? get(expression, ['defaultValue', 'value'])
                : get(expression, ['defaultValue']),
            )}
          </Row>
        </>
      )}
    </>
  )
}
export default ExpressRender
