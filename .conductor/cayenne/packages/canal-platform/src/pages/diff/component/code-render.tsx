import type { E2ESchemaNormalizedExpression } from '@ad/e2e-schema'
import Editor from '@monaco-editor/react'
import { get } from 'lodash'
import { useEffect, useState } from 'react'

const CodeRender = ({
  codeConfig,
}: {
  codeConfig: E2ESchemaNormalizedExpression | undefined
}): JSX.Element => {
  const [renderConfig, setRenderConfig] = useState({
    language: 'javascript',
    code: '',
  })
  useEffect(() => {
    if (!codeConfig) return
    switch (codeConfig.type) {
      case 'jsonata':
        setRenderConfig({
          language: 'json',
          code: JSON.stringify(codeConfig.code, null, '\t'),
        })
        break
      case 'js':
        setRenderConfig({
          language: codeConfig.codeTS ? 'typescript' : 'javascript',
          code: codeConfig.codeTS || codeConfig.codeES || codeConfig.code,
        })
        break
      case 'api':
      case 'apis': {
        const codeTS = get(codeConfig, ['transform', 'codeTS']) || ''
        const codeES = get(codeConfig, ['transform', 'codeES']) || ''
        setRenderConfig({
          language: codeTS ? 'typescript' : 'javascript',
          code:
            codeTS || codeES || get(codeConfig, ['transform', 'code']) || '',
        })
        break
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return (
    <Editor
      height={300}
      width={600}
      language={renderConfig.language}
      options={{
        minimap: {
          enabled: false,
        },
        tabSize: 2,
        lineNumbersMinChars: 3,
        lineDecorationsWidth: 2,
      }}
      value={renderConfig.code}
    />
  )
}
export default CodeRender
