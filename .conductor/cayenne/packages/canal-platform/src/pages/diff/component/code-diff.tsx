import { DiffEditor } from '@monaco-editor/react'
const CodeDiff = ({
  base,
  develop,
}: {
  base: {
    type: string
    code: string
    codeTS?: string
    codeES?: string
  }
  develop: {
    type: string
    code: string
    codeTS?: string
    codeES?: string
  }
}): JSX.Element => {
  return (
    <DiffEditor
      height={400}
      width={1200}
      original={
        base?.type === 'jsonata'
          ? JSON.stringify(base.code, null, '\t')
          : base?.codeTS || base?.codeES || base?.code || ''
      }
      modified={
        develop?.type === 'jsonata'
          ? JSON.stringify(develop.code, null, '\t')
          : develop?.codeTS || develop?.codeES || develop?.code || ''
      }
      language={base?.type === 'jsonata' ? 'json' : 'typeScript'}
      options={{
        renderIndicators: true,
        readOnly: true,
      }}
    />
  )
}

export default CodeDiff
