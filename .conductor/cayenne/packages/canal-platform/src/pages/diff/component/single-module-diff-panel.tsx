import type { E2EServerSchema } from '@ad/e2e-schema'
import { Button, Space, Steps } from '@m-ui/react'
import { DiffEditor } from '@monaco-editor/react'
import { useMemo, useState } from 'react'
import ComponentPanel from '../sub-compare/component-panel'
import GlobalCompare from '../sub-compare/global-compare'
import MultiTreePanel from '../sub-compare/multi-tree-panel'
import { getComponentConfigMap, getPureTwoDiffInfo } from '../utils/util'

const { Step } = Steps

const SingleModuleDiffPanel = ({
  type,
  domainCode,
  baseSchema,
  developSchema,
  tag,
}: {
  type: 'diff' | 'merge'
  domainCode: string
  baseSchema: E2EServerSchema | undefined
  developSchema: E2EServerSchema | undefined
  tag: JSX.Element
}): JSX.Element => {
  const [current, setCurrent] = useState(0)
  const onStepChange = (currentStep: number): void => {
    setCurrent(currentStep)
  }

  const diffResult = useMemo(() => {
    if (developSchema && baseSchema && type === 'diff') {
      return getPureTwoDiffInfo(developSchema, baseSchema)
    }
  }, [developSchema, baseSchema, type])

  const developComponentsConfig = useMemo(() => {
    return getComponentConfigMap(developSchema)
  }, [developSchema])

  const baseComponentsConfig = useMemo(() => {
    return getComponentConfigMap(baseSchema)
  }, [baseSchema])

  return (
    <>
      <Steps current={current} onChange={onStepChange} size={'small'}>
        <Step
          title="页面结构"
          description={
            <div style={{ fontSize: 12, width: 300 }}>
              {type === 'diff'
                ? '请查看和基线模块结构的差异'
                : '请核对线上模块结构和当前模块结构的差异，并调整合并结果'}
            </div>
          }
        />
        <Step
          title="组件配置"
          description={
            <div style={{ fontSize: 12, width: 300 }}>
              {type === 'diff'
                ? '请查看和基线模块结构的差异'
                : '请核对线上模块结构和当前模块结构的差异，并调整合并结果'}
            </div>
          }
        />
        <Step
          title="全局配置"
          description={
            <div style={{ fontSize: 12, width: 300 }}>
              包含模块级接口、埋点列表、前端动作、模型层等
            </div>
          }
        />
        <Step
          title="完整协议"
          description={
            <div style={{ fontSize: 12, width: 300 }}>查看整体协议的diff</div>
          }
        />
      </Steps>
      {current === 0 && (
        <>
          {
            <>
              <MultiTreePanel
                baseSchema={baseSchema}
                developSchema={developSchema}
                type={type}
                domainCode={domainCode}
                developComponentsConfig={developComponentsConfig}
                baseComponentsConfig={baseComponentsConfig}
                diffResult={diffResult}
                tag={tag}
              />
              <div style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  style={{ marginBottom: 16 }}
                  size="large"
                  onClick={(): void => {
                    setCurrent(1)
                  }}
                >
                  下一步
                </Button>
              </div>
            </>
          }
        </>
      )}
      {current === 1 && (
        <>
          <ComponentPanel
            baseSchema={baseSchema}
            developSchema={developSchema}
            type={type}
            domainCode={domainCode}
            developComponentsConfig={developComponentsConfig}
            baseComponentsConfig={baseComponentsConfig}
            diffResult={diffResult}
            tag={tag}
          />
          <div style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              style={{ marginBottom: 16 }}
              size="large"
              onClick={(): void => {
                setCurrent(2)
              }}
            >
              下一步
            </Button>
          </div>
        </>
      )}
      {current === 2 && (
        <>
          <GlobalCompare
            developSchema={developSchema}
            baseSchema={baseSchema}
            tag={tag}
          />
          <div style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              style={{ marginBottom: 16 }}
              size="large"
              onClick={(): void => {
                setCurrent(3)
              }}
            >
              下一步
            </Button>
          </div>
        </>
      )}
      {current === 3 && (
        <div style={{ margin: '16px 0' }}>
          <Space direction="vertical">
            {tag}
            <DiffEditor
              height={700}
              width={1350}
              original={JSON.stringify(baseSchema || '', null, '\t')}
              modified={JSON.stringify(developSchema || '', null, '\t')}
              language={'json'}
              options={{
                renderIndicators: true,
                readOnly: true,
              }}
            />
          </Space>
        </div>
      )}
    </>
  )
}

export default SingleModuleDiffPanel
