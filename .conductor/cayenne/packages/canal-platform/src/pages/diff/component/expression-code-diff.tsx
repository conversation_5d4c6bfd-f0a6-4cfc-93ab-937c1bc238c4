import type {
  E2ESchemaExpressionAPI,
  E2ESchemaExpressionAPIs,
  E2ESchemaExpressionJS,
  E2ESchemaExpressionJSONata,
  E2ESchemaNormalizedExpression,
} from '@ad/e2e-schema'
import { Col, Row, Typography } from '@m-ui/react'
import { DiffEditor } from '@monaco-editor/react'
import { get, isEqual, isObject } from 'lodash'
import { useMemo } from 'react'
import {
  commonValueDiffRender,
  getTrueValue,
  isSafeStringifiable,
} from '../utils/util'
import CodeDiff from './code-diff'
import UnknownTypeRender from './unkown-type-render'

const ExpressionDiff = ({
  base,
  develop,
  codeTxt,
}: {
  base: E2ESchemaNormalizedExpression | unknown
  develop: E2ESchemaNormalizedExpression | unknown
  codeTxt?: string
}): JSX.Element => {
  const type = useMemo(() => {
    return {
      baseType: isSafeStringifiable(base)
        ? 'basic'
        : isObject(base) && 'type' in base
        ? base.type
        : 'basic',
      developType: isSafeStringifiable(develop)
        ? 'basic'
        : isObject(develop) && 'type' in develop
        ? develop.type
        : 'basic',
    }
  }, [base, develop])

  const getDefaultValueDiffRender = (): JSX.Element | undefined => {
    const baseDefault =
      isObject(base) && 'defaultValue' in base ? base.defaultValue : undefined
    const devDefault =
      isObject(develop) && 'defaultValue' in develop
        ? develop.defaultValue
        : undefined
    if (!baseDefault && !devDefault) return
    if (isEqual(baseDefault, devDefault)) return
    return (
      <>
        <Row>
          <Col>
            <Typography.Text mark>默认值：</Typography.Text>
          </Col>
        </Row>
        <Row>
          <DiffEditor
            height={200}
            width={1200}
            original={getTrueValue(baseDefault)}
            modified={getTrueValue(devDefault)}
            options={{
              renderIndicators: true,
              readOnly: true,
            }}
            language="javascript"
          />
        </Row>
      </>
    )
  }

  /**
   * 这里的表达式有两种情况，前端/后端表达式
   * @returns
   */
  const renderExpressionDiff = (): JSX.Element | undefined => {
    if (!base || !develop) return
    const expressionCodeRender: JSX.Element[] = []
    // 有表达式
    if (
      isObject(base) &&
      isObject(develop) &&
      'type' in base &&
      'type' in develop
    ) {
      // 前端表达式
      if (
        ['js', 'jsonata'].includes(base.type as string) &&
        ['js', 'jsonata'].includes(develop.type as string)
      ) {
        // 前端表达式
        expressionCodeRender.push(
          <>
            <Row>
              <Typography.Text mark>{codeTxt || '生成属性'}：</Typography.Text>
            </Row>
            <Row>
              <CodeDiff
                base={
                  base as E2ESchemaExpressionJS | E2ESchemaExpressionJSONata
                }
                develop={
                  develop as E2ESchemaExpressionJS | E2ESchemaExpressionJSONata
                }
              />
            </Row>
          </>,
        )
      }
      // 后端表达式
      if (
        ['apis', 'api'].includes(base.type as string) &&
        ['apis', 'api'].includes(develop.type as string)
      ) {
        expressionCodeRender.push(
          <>
            <Row>
              <Typography.Text mark>{codeTxt || '生成属性'}：</Typography.Text>
            </Row>
            <Row>
              <CodeDiff
                base={get(base, ['transform'])}
                develop={get(develop, ['transform'])}
              />
            </Row>
          </>,
        )
      }
    }
    return <Row>{expressionCodeRender}</Row>
  }

  const getExpressionDiffRender = (): JSX.Element | undefined => {
    if (isEqual(base, develop)) return
    // 类型一致
    if (type.baseType === type.developType) {
      if (type.baseType === 'basic') {
        return <>{commonValueDiffRender(develop, base)}</>
      }
      if (type.baseType === 'static') {
        return commonValueDiffRender(
          (develop as { value: unknown }).value,
          (base as { value: unknown }).value,
        )
      }
      if (type.baseType === 'js' || type.baseType === 'jsonata') {
        return (
          <>
            {getDefaultValueDiffRender()}
            {renderExpressionDiff()}
          </>
        )
      }
      if (type.baseType === 'apis' || type.baseType === 'api') {
        return (
          <>
            {!isEqual(
              get(develop, 'apiIds') || get(develop, 'apiId'),
              get(base, 'apiIds') || get(base, 'apiId'),
            ) && (
              <Row>
                <Row>
                  API ID:
                  <DiffEditor
                    height={100}
                    width={1200}
                    language={'javascript'}
                    modified={
                      type.baseType === 'apis'
                        ? JSON.stringify(
                            (develop as E2ESchemaExpressionAPIs)?.apiIds,
                          )
                        : String((develop as E2ESchemaExpressionAPI)?.apiId)
                    }
                    original={
                      type.baseType === 'apis'
                        ? JSON.stringify(
                            (base as E2ESchemaExpressionAPIs)?.apiIds,
                          )
                        : String((base as E2ESchemaExpressionAPI)?.apiId)
                    }
                  />
                </Row>
              </Row>
            )}
            {getDefaultValueDiffRender()}
            {renderExpressionDiff()}
          </>
        )
      }
      // 兜底
      return <>{commonValueDiffRender(develop, base)}</>
    } else {
      return (
        <Row>
          <Col span={12}>
            <UnknownTypeRender propsValue={base} codeTxt={codeTxt} />
          </Col>
          <Col span={12}>
            <UnknownTypeRender propsValue={develop} codeTxt={codeTxt} />
          </Col>
        </Row>
      )
    }
    return <></>
  }
  return <>{getExpressionDiffRender()}</>
}
export default ExpressionDiff
