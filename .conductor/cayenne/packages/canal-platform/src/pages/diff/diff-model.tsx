import { checkBeforeMerge, getModuleDetail } from '@/services/backend/module'
import { ModuleType } from '@ad/canal-shared'
import type { E2EGlobalSchema, E2EServerSchema } from '@ad/e2e-schema'
import { Badge, Button, Modal, Space, Typography } from '@m-ui/react'
import { isString } from 'lodash'
import { useEffect, useState } from 'react'
import { parseGlobalSchemaFromModuleContent } from '../designer-global/utils/schema'
import DiffTag from './component/diff-tag'
import { GlobalModuleDiffPanel } from './component/global-module-diff-panel'
import SingleModuleDiffPanel from './component/single-module-diff-panel'
const { Text } = Typography

const DiffModel = ({
  changeId,
  moduleId,
  developVersion,
  domainCode,
  autoOpen,
  btnTxt,
}: {
  changeId: string
  moduleId: string
  developVersion: string
  domainCode: string
  autoOpen?: boolean
  btnTxt?: string
}): JSX.Element => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [mergeLoading, setMergeLoading] = useState(false)
  const [type, setType] = useState<'diff' | 'merge'>()
  const [baseSchema, setBaseSchema] = useState<E2EServerSchema>()
  const [developSchema, setDevelopSchema] = useState<E2EServerSchema>()
  const [baseVersion, setBaseVersion] = useState<string>()
  const [checkFinish, setCheckFinish] = useState<boolean>(false)
  const [moduleType, setModuleType] = useState<ModuleType>(ModuleType.MODULE)

  const checkMerge = async (): Promise<void> => {
    setMergeLoading(true)
    const ret = await checkBeforeMerge({
      changeId,
      moduleId,
    })
    if (ret.result === 1 && ret.data) {
      if (ret.data.hasOnlineVersion) {
        if (ret.data.checkoutVersion && ret.data.onlineVersion) {
          if (ret.data.checkoutVersion !== ret.data.onlineVersion) {
            setType('merge')
            setBaseVersion(ret.data.onlineVersion)
          } else {
            // 一期仅提供diff
            setType('diff')
            setBaseVersion(ret.data.onlineVersion)
          }
        }
      }
      if (autoOpen) {
        setTimeout(() => {
          requestSchema(ret.data?.onlineVersion)
        }, 200)
      }
    }
    setCheckFinish(true)
    setMergeLoading(false)
  }

  const getData = async (
    version: string,
    callback: (schema: E2EServerSchema) => void,
  ): Promise<void> => {
    return await getModuleDetail({
      id: moduleId,
      version: version,
    }).then((res) => {
      if (isString(res.data?.content)) {
        try {
          const mt = res.data.type
          const ct = res.data.content
          setModuleType(mt)
          const content =
            mt === ModuleType.GLOBAL
              ? parseGlobalSchemaFromModuleContent(ct)
              : JSON.parse(ct)
          callback && callback(content)
        } catch (e) {
          console.log(e)
        }
      }
    })
  }
  const requestSchema = async (responseBaseVersion?: string): Promise<void> => {
    setMergeLoading(true)

    Promise.all([
      baseVersion || responseBaseVersion
        ? getData(baseVersion || (responseBaseVersion as string), setBaseSchema)
        : Promise.resolve(),
      getData(developVersion, setDevelopSchema),
    ])
      .then(() => {
        setIsModalOpen(true)
      })
      .catch((error) => {
        console.error('Error fetching data:', error)
      })
      .finally(() => {
        setMergeLoading(false)
      })
  }

  useEffect(() => {
    checkMerge()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return (
    <>
      {checkFinish && (
        <Badge
          count={type === 'merge' ? '请注意检查是否落后线上版本' : ''}
          color={'red'}
          offset={[10, -8]}
        >
          <Button
            onClick={(): void => {
              requestSchema()
            }}
            type="link"
            loading={mergeLoading}
          >
            {btnTxt || '和线上版本diff'}
          </Button>
        </Badge>
      )}

      <Modal
        title={
          <Space>
            Diff面板{' '}
            {moduleType !== ModuleType.GLOBAL && (
              <Text type="secondary" style={{ fontSize: 14 }}>
                点击步骤序号也可切换查看
              </Text>
            )}
          </Space>
        }
        visible={isModalOpen}
        width={1400}
        onOk={(): void => {}}
        onCancel={(): void => {
          setIsModalOpen(false)
        }}
        footer={null}
        destroyOnClose
        maskClosable={false}
      >
        {moduleType === ModuleType.GLOBAL ? (
          <GlobalModuleDiffPanel
            originalSchema={baseSchema as E2EGlobalSchema}
            modifiedSchema={developSchema as E2EGlobalSchema}
          />
        ) : (
          <SingleModuleDiffPanel
            baseSchema={baseSchema}
            developSchema={developSchema}
            type={'diff'}
            domainCode={domainCode}
            tag={<DiffTag />}
          />
        )}
      </Modal>
    </>
  )
}

export default DiffModel
