import {
  getModuleDeployDetailById,
  getPreviousRecordById,
} from '@/services/backend/module'
import { ModuleType } from '@ad/canal-shared'
import type { E2EGlobalSchema, E2EServerSchema } from '@ad/e2e-schema'
import { Button, Modal, Space, Tooltip, Typography, message } from '@m-ui/react'
import { isString } from 'lodash'
import { useState } from 'react'
import { dateFormat } from '../change/const'
import { parseGlobalSchemaFromModuleContent } from '../designer-global/utils/schema'
import DiffTag from './component/diff-tag'
import { GlobalModuleDiffPanel } from './component/global-module-diff-panel'
import SingleModuleDiffPanel from './component/single-module-diff-panel'

const { Text } = Typography

const HistoryDiffModel = ({
  developDeployId,
  domainCode,
  moduleType,
}: {
  developDeployId: string
  domainCode: string
  moduleType: number
}): JSX.Element => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [, setBaseDeployId] = useState<string>()
  const [baseSchema, setBaseSchema] = useState<E2EServerSchema>()
  const [currentSchema, setCurrentSchema] = useState<E2EServerSchema>()
  const [baseTagText, setBaseTagText] = useState<string>('')
  const [currentTagText, setCurrentTagText] = useState<string>('')
  const getData = async (
    id: string,
    callback: (schema: E2EServerSchema, tag: string) => void,
  ): Promise<void> => {
    return await getModuleDeployDetailById({
      id,
      domainCode,
    }).then((res) => {
      if (isString(res.data?.content)) {
        try {
          const ct = res.data.content
          const content =
            moduleType === ModuleType.GLOBAL
              ? parseGlobalSchemaFromModuleContent(ct)
              : JSON.parse(ct)
          callback &&
            callback(
              content,
              `版本：${res.data.version}  发布时间：${dateFormat(
                res.data.createTime,
              )}`,
            )
        } catch (e) {
          console.log(e)
        }
      }
    })
  }

  const requestDeploySchema = async (): Promise<void> => {
    setLoading(true)
    const res = await getPreviousRecordById({ id: developDeployId })
    if (res.result === 1) {
      if (!res.data?.id) {
        message.info('在此版本没有历史上线')
        setLoading(false)
        return
      } else {
        setBaseDeployId(res.data.id)
        await Promise.all([
          getData(res.data.id as string, (schema, tag) => {
            setBaseSchema(schema)
            setBaseTagText(tag)
          }),
          getData(developDeployId, (schema, tag) => {
            setCurrentSchema(schema)
            setCurrentTagText(tag)
          }),
        ])
          .then(() => {
            setIsModalOpen(true)
          })
          .catch((error) => {
            console.error('Error fetching data:', error)
          })
      }
    } else {
      message.error(res.msg || '请求失败')
    }
    setLoading(false)
  }
  return (
    <>
      <Tooltip placement="top" title={'和该模块上一次线上版本做对比'}>
        <Button
          onClick={(): void => {
            requestDeploySchema()
          }}
          type="link"
          loading={loading}
        >
          {'查看更改'}
        </Button>
      </Tooltip>
      <Modal
        title={
          <Space>
            Diff面板{' '}
            {moduleType !== ModuleType.GLOBAL && (
              <Text type="secondary" style={{ fontSize: 14 }}>
                点击步骤序号也可切换查看
              </Text>
            )}
          </Space>
        }
        visible={isModalOpen}
        width={1400}
        onOk={(): void => {}}
        onCancel={(): void => {
          setIsModalOpen(false)
        }}
        footer={null}
        destroyOnClose
        maskClosable={false}
      >
        {moduleType === ModuleType.GLOBAL ? (
          <GlobalModuleDiffPanel
            originalSchema={baseSchema as E2EGlobalSchema}
            modifiedSchema={currentSchema as E2EGlobalSchema}
          />
        ) : (
          <SingleModuleDiffPanel
            baseSchema={baseSchema}
            developSchema={currentSchema}
            type={'diff'}
            domainCode={domainCode}
            tag={
              <DiffTag
                baseTagText={baseTagText}
                developTagText={currentTagText}
              />
            }
          />
        )}
      </Modal>
    </>
  )
}

export default HistoryDiffModel
