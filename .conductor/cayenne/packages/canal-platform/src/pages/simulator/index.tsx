import type { TransformedConfig } from '@/utils'
import {
  loadConfigTransformForDesignerRuntime,
  loadDependenciesForDesignerRuntime,
  loadExtraPluginsForDesignerRuntime,
} from '@/utils'
import {
  CanalReactRuntimeFC,
  disableAssertWeblog,
  disableCaptureException,
} from '@ad/canal-react-runtime'
import type { Plugin } from '@kael/runtime'
import { pluginMobxHandleViewEffect } from '@kael/runtime-plugin-mobx-handle-view-effect'
import { Simulator as KaelSimulator } from '@kael/simulator-react'
import { observer } from 'mobx-react-lite'
import { type ComponentProps, type FC } from 'react'
import { componentHocErrorBoundary } from './hocs'
import {
  pluginCanalExpressionExtensionAPIMock,
  pluginDependenciesOverride,
  pluginFixHocs,
  pluginFixSimulatorProps,
} from './plugins'
import { SimulatorStore } from './simulator-store'

disableCaptureException()
disableAssertWeblog()

/**
 * 插件
 */
const plugins = [
  pluginMobxHandleViewEffect,
  pluginFixHocs,
  pluginDependenciesOverride,
  pluginCanalExpressionExtensionAPIMock,
  pluginFixSimulatorProps,
  ...(await loadExtraPluginsForDesignerRuntime()),
] as unknown as Plugin[]

const dependencies = await loadDependenciesForDesignerRuntime()
const configTransform = await loadConfigTransformForDesignerRuntime()
const config = configTransform({
  rendererComponentsHocs: {
    componentHocs: [componentHocErrorBoundary],
  },
  dependencies,
} satisfies ComponentProps<typeof KaelSimulator>['config'] as TransformedConfig)

/**
 * 模拟器
 */
export const Simulator: FC = observer(() => {
  return (
    <KaelSimulator
      Renderer={CanalReactRuntimeFC as FC}
      plugins={plugins}
      config={config}
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      simulatorStoreClass={SimulatorStore as any}
    />
  )
})

export default Simulator
