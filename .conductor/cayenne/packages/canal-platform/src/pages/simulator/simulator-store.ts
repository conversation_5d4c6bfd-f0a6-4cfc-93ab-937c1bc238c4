import { setRootElementFontSizeForPx2rem } from '@/utils'
import { replaceByComponentJsUrlMap } from '@ad/canal-react-runtime'
import type { E2ESchema } from '@ad/e2e-schema'
import { b2fE2ESchema, f2bE2ESchema } from '@ad/e2e-schema-utils'
import { BaseDependencyStore, BaseSimulatorStore } from '@kael/simulator-shared'
import { DESIGNER_QUERY, IS_DEBUG_MODE } from '../designer/constants'
import { debugComponentJsUrlMapLoader } from '../designer/stores/debug-mode-store/debug-component-js-url-map-loader'

/**
 * 模拟器仓库
 */
export class SimulatorStore extends BaseSimulatorStore {
  public constructor(
    ...args: ConstructorParameters<typeof BaseSimulatorStore>
  ) {
    super(...args)
    // 去掉 BaseDependencyStore 里的物料加载逻辑
    this.dependencyService.updateComponentLibraries = async function (
      this: BaseDependencyStore,
    ): Promise<void> {
      await this.loadMaterialLibSchema()
    }

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(window as any).debugSimulatorStore = this
    }
  }

  /**
   * 设置 Schema（文件）
   * @override
   * @param schema 由设计器传过来的 Schema 文件
   */
  public setSchema(schema: E2ESchema): void {
    console.log('SimulatorStore raw schema', schema)

    // 处理 keepValueProp, keepIfProp 引入的中间态
    schema = b2fE2ESchema(f2bE2ESchema(schema))
    // 模拟器不依赖 fetchSchema，用不上 componentJsUrlMap，直接在这里替换
    if (IS_DEBUG_MODE && DESIGNER_QUERY.localCl) {
      const componentJsUrlMap = debugComponentJsUrlMapLoader.load()
      replaceByComponentJsUrlMap(schema, componentJsUrlMap)
    }

    console.log('SimulatorStore processed schema', schema)
    setRootElementFontSizeForPx2rem(schema)
    return super.setSchema(schema)
  }
}
