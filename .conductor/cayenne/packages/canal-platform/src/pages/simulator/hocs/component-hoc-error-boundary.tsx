import { type RendererReactComponentHocs } from '@kael/renderer-react'
import type { Component, DefaultPluginsPropertiesExt } from '@kael/runtime'
import type { PluginIdPropertiesExt } from '@kael/runtime-plugin-id'
import { useMemo, type FC } from 'react'
import { ErrorBoundary, type FallbackProps } from 'react-error-boundary'

/**
 * 组件高阶组件：错误边界
 * @param Comp 组件
 */
export const componentHocErrorBoundary: RendererReactComponentHocs = (Comp) => {
  return (props) => {
    const { component } = props
    const {
      id: componentId,
      schema: { type: componentType },
    } = component as unknown as Component<
      DefaultPluginsPropertiesExt & PluginIdPropertiesExt
    >

    const FallbackComponent = useMemo(() => {
      const FallbackComp: FC<FallbackProps> = () => {
        return (
          <div>
            组件（{componentId}）渲染出错，类型：{componentType}
          </div>
        )
      }
      return FallbackComp
    }, [componentId, componentType])

    return (
      <ErrorBoundary FallbackComponent={FallbackComponent}>
        <Comp {...props} />
      </ErrorBoundary>
    )
  }
}
