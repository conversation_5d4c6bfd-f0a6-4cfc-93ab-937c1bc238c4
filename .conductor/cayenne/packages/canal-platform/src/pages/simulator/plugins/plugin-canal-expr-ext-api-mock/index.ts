import { type E2ESchemaExpressionAPI } from '@ad/e2e-schema'
import {
  PluginExpressionExtension,
  type ExpressionExtension,
  type Plugin,
  type PluginEvalExpressionPropertiesExt,
} from '@kael/runtime'

/**
 * 大运河 api mock 表达式
 */
export const canalExpressionExtensionAPIMock: ExpressionExtension<
  E2ESchemaExpressionAPI,
  PluginEvalExpressionPropertiesExt
> = {
  type: 'api',
  eval({ defaultValue }, ctx, options) {
    const relativePath = options?.relativePath || []
    return (
      defaultValue &&
      ctx.evalExpression(defaultValue, {
        ...options,
        relativePath: [...relativePath, 'defaultValue'],
      })
    )
  },
}

/**
 * 大运河 api mock 表达式扩展插件
 */
export const pluginCanalExpressionExtensionAPIMock: Plugin = {
  id: 'CANAL_EXPRESSION_EXTENSION_API_MOCK',
  position: {
    type: 'after',
    targetId: PluginExpressionExtension.id,
  },
  extendsContainer(container) {
    container.registerExpressionExtension(canalExpressionExtensionAPIMock)
  },
}
