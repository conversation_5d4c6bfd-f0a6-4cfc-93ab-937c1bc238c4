import { INTERNAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import { PluginDependencies, type Plugin } from '@kael/runtime'
import { SubmoduleForSimulator } from './components'

/**
 * 依赖覆盖
 */
export const pluginDependenciesOverride: Plugin = {
  id: 'DEPENDENCIES_OVERRIDE',
  position: {
    type: 'after',
    targetId: PluginDependencies.id,
    replaceIds: [PluginDependencies.id],
  },
  extendsContext(ctx) {
    const deps = ctx.runtime.constructorOptions.config?.dependencies || {}
    deps[INTERNAL_COMPONENT_LIB_NAME] = {
      ...(deps[INTERNAL_COMPONENT_LIB_NAME] as Record<string, unknown>),
      Submodule: SubmoduleForSimulator,
    }
    Object.defineProperty(ctx, 'dependencies', {
      get() {
        return deps
      },
    })
  },
}
