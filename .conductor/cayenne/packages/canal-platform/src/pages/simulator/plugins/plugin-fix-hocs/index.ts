import { type Plugin } from '@kael/runtime'
import { type PluginReactRendererPropertiesExt } from '@kael/runtime-plugin-react-renderer'
import { WithPositionToken } from '@kael/simulator-react'

/**
 * 修复高阶组件插件
 */
export const pluginFixHocs: Plugin<PluginReactRendererPropertiesExt> = {
  id: 'FIX_HOCS',
  extendsContainer(container) {
    const idx = container.rendererComponentsHocs.componentHocs.findIndex(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (hoc) => (hoc as any).token === WithPositionToken,
    )
    if (idx !== -1) {
      container.rendererComponentsHocs.componentHocs.splice(idx, 1)
    }
  },
}
