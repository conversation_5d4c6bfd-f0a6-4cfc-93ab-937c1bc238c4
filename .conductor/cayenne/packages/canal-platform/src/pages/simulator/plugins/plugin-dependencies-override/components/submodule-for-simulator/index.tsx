import type { SubmoduleProps } from '@ad/canal-react-components'
import { Card, Typography } from '@m-ui/react'
import { type FC } from 'react'
import styled from 'styled-components'

/**
 * 子模块
 */
export const SubmoduleForSimulator: FC<SubmoduleProps> = ({
  schemaId,
  params,
}) => {
  return (
    <Container>
      <h3>
        子模块
        <Tip type="secondary">（仅占位示意，线上渲染为子模块的下发内容）</Tip>
      </h3>
      <div>ID: {schemaId}</div>
      <div>参数: {JSON.stringify(params)}</div>
    </Container>
  )
}

const Container = styled(Card)`
  margin: 10px;
`

const Tip = styled(Typography.Text)`
  font-weight: normal;
  font-size: 14px;
`
