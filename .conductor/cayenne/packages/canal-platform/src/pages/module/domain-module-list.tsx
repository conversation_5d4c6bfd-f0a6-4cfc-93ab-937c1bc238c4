import { EditableModuleName } from '@/components/editable-module-name'
import { ModuleTypeTag } from '@/components/module-type-tag'
import ServerMonitor from '@/components/monitor'
import { dateFormat } from '@/pages/change/const'
import { type OnlineModuleVersion } from '@/services/backend/models'
import { getAllOnlineModuleList } from '@/services/backend/module'
import { addModuleTags } from '@/services/backend/tags'
import { createDesignerUrl } from '@/utils'
import { ModuleType } from '@ad/canal-shared'
import { useSearchParams } from '@kmi/react'
import { SystemAddCircleLine, SystemPreviewopenLine } from '@m-ui/icons'
import {
  Button,
  Empty,
  Form,
  Input,
  Modal,
  Space,
  Spin,
  Table,
  Typography,
  message,
} from '@m-ui/react'
import type { ColumnsType, TablePaginationConfig } from '@m-ui/react/es/table'
import type { ReactNode } from 'react'
import { memo, useEffect, useRef, useState, type FC } from 'react'
import AddTemplateModule from '../template/add-template-modal'
import AddTag from './add-tag'
import ModulePublishHistory from './module-publish-history'
import ModuleTag from './module-tag'

const { Text, Title } = Typography

/**
 * 域名模块列表
 */
export const DomainModuleList: FC = memo(() => {
  const [searchParams] = useSearchParams()
  const [allModuleList, setAllModuleList] = useState<OnlineModuleVersion[]>([])
  const [spinning, setSpinning] = useState(false)
  const [modelVisible, setModelVisible] = useState(false)
  const domainCode = searchParams.get('domainCode') || ''
  const searchModuleId = searchParams.get('searchModuleId') || ''
  const [total, setTotal] = useState(0)
  const [pageNum, setPageNum] = useState(1)
  const [addVisible, setAddVisible] = useState(false)
  const [addModuleId, setAddModuleId] = useState<string>()
  const [addTagId, setAddTagId] = useState<number>()
  const [addParams, setAddParams] = useState<{
    domainCode: string
    moduleId: string
    version: number
  }>()
  const updateSingleTagsFunctionRef = useRef<(() => void) | null>(null)
  const [form] = Form.useForm()
  const [formValues, setFormValues] = useState({})

  useEffect(() => {
    if (searchModuleId) {
      form.setFieldsValue({ moduleId: searchModuleId }) // 同步到表单
    }
  }, [searchModuleId, form])

  const addModuleTagCallback = (
    moduleId: string,
    updateFun: () => void,
  ): void => {
    setAddModuleId(moduleId)
    setAddVisible(true)
    updateSingleTagsFunctionRef.current = updateFun
  }
  const handleAddCancel = (): void => {
    setAddModuleId(undefined)
    setAddVisible(false)
    setAddTagId(undefined)
    updateSingleTagsFunctionRef.current = null
  }
  const handleAddConfirm = async (): Promise<void> => {
    if (!addTagId || !addModuleId) {
      return
    } else {
      const res = await addModuleTags({
        moduleId: addModuleId,
        tagId: addTagId,
      })
      if (res.result === 1) {
        message.success('添加成功')
        updateSingleTagsFunctionRef.current?.()
      } else {
        message.error(res.msg || '添加失败')
      }
    }
    handleAddCancel()
  }

  const columns: ColumnsType<OnlineModuleVersion> = [
    {
      title: '模块名称/ID',
      dataIndex: 'module',
      key: 'id',
      render: (v, record): ReactNode => {
        return (
          <Space direction={'vertical'}>
            <Title level={5}>
              <EditableModuleName
                id={record.module?.id}
                name={record.module?.name}
                onNameChanged={(): void => {}}
              />
            </Title>
            <Text copyable>{v.id}</Text>
          </Space>
        )
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render(...[, record]): JSX.Element {
        return <ModuleTypeTag type={record.module.type} />
      },
    },
    {
      title: '线上版本',
      dataIndex: 'version',
      key: 'version',
      render: (v, record): ReactNode => {
        return (
          <Space>
            {v}
            <ModulePublishHistory
              moduleId={record.module?.id}
              moduleName={record.module?.name}
              domainCode={record.module.businessDomainCode}
              moduleType={record.module.type}
            />
          </Space>
        )
      },
    },
    {
      title: '模块打标',
      dataIndex: 'module',
      key: 'tag',
      render: (v): ReactNode => {
        return (
          <ModuleTag
            moduleId={v.id}
            addTagCallback={(moduleId: string, fun: () => void): void =>
              addModuleTagCallback(moduleId, fun)
            }
          />
        )
      },
    },
    {
      title: '操作人',
      key: 'createUser',
      dataIndex: 'createUser',
      render: (...[, record]): ReactNode => {
        return (
          <Space direction={'vertical'}>
            <span>创建人：{record.createUser}</span>
            <span>更新人：{record.updateUser}</span>
          </Space>
        )
      },
    },
    {
      title: '时间',
      key: 'createTime',
      dataIndex: 'createTime',
      render: (...[, record]): ReactNode => {
        return (
          <Space direction={'vertical'}>
            <span>创建时间：{dateFormat(record.createTime)}</span>
            <span>更新时间：{dateFormat(record.updateTime)}</span>
          </Space>
        )
      },
    },
  ]

  /**
   * 页码变动
   * @param pagination
   */
  const onPageChange = (pagination: TablePaginationConfig): void => {
    if (typeof pagination.current !== 'undefined') {
      setPageNum(pagination.current)
    }
  }

  const getOnlineModuleList = async (): Promise<void> => {
    try {
      setSpinning(true)
      const params = {
        domainCode,
        pageNum,
        pageSize: 10,
        moduleId: searchModuleId,
        ...(formValues || {}),
      }

      const res = await getAllOnlineModuleList(params)
      if (res.result === 1 && Array.isArray(res.data.list)) {
        setAllModuleList(res.data.list)
        setTotal(res.data.total)
      }
    } catch (e) {
      console.log(e)
    }
    setSpinning(false)
  }
  const locale = {
    emptyText: <Empty description={'该业务域下没有已上线模块'} />,
  }
  const addTemplate = ({ version, module }: OnlineModuleVersion): void => {
    setAddParams({
      domainCode: domainCode,
      moduleId: module.id,
      version: +version,
    })
    setModelVisible(true)
  }
  const onSearch = (values: {
    createUser: string
    changeName: string
    moduleId: string
  }): void => {
    setPageNum(1)
    setFormValues(values)
  }
  useEffect(() => {
    if (!domainCode) return
    getOnlineModuleList()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNum, formValues])
  return (
    <div>
      {addParams && (
        <AddTemplateModule
          visible={modelVisible}
          params={addParams}
          onCancel={(): void => setModelVisible(false)}
        />
      )}
      <Spin tip="加载中..." spinning={spinning}>
        <Form
          name="customized_form_controls"
          layout="inline"
          onFinish={onSearch}
          form={form}
          style={{ marginBottom: '16px' }}
        >
          <Form.Item label="模块名称" name="name">
            <Input
              placeholder="请输入模块名称（模糊查找）"
              style={{ width: 300 }}
            ></Input>
          </Form.Item>
          <Form.Item label="创建人" name="createUser">
            <Input placeholder="请输入模块创建人" />
          </Form.Item>
          <Form.Item label="模块ID" name="moduleId">
            <Input placeholder="请输入模块Id" style={{ width: 300 }} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
            <Button
              style={{ margin: '0 8px' }}
              onClick={(): void => {
                form.resetFields()
                setPageNum(1)
              }}
            >
              清空
            </Button>
          </Form.Item>
        </Form>
        <Table
          columns={[
            ...columns,
            {
              title: '操作',
              key: 'action',
              render: (...[, record]): ReactNode => {
                return (
                  <Space direction="vertical" align="start">
                    <Button
                      type="link"
                      href={createDesignerUrl(record.module.type, {
                        moduleId: `${record.module.id}`,
                        moduleVersion: `${record.version}`,
                        domainCode,
                      })}
                      target="_blank"
                    >
                      <SystemPreviewopenLine /> 查看配置
                    </Button>
                    {record.module.type !== ModuleType.GLOBAL && (
                      <Space>
                        <Button
                          type="link"
                          onClick={(): void => addTemplate(record)}
                        >
                          <SystemAddCircleLine />
                          加入模版
                        </Button>
                        <ServerMonitor moduleId={record.module.id} />
                      </Space>
                    )}
                  </Space>
                )
              },
            },
          ]}
          locale={locale}
          dataSource={allModuleList}
          rowKey={'id'}
          pagination={{
            pageSize: 10,
            total: total,
            showSizeChanger: false,
            current: pageNum,
          }}
          onChange={onPageChange}
        />
      </Spin>
      <Modal
        title={'添加标签'}
        okText="确定"
        cancelText="取消"
        visible={addVisible}
        onOk={handleAddConfirm}
        onCancel={handleAddCancel}
        maskClosable={false}
        destroyOnClose={true}
      >
        <AddTag
          domainCode={domainCode}
          moduleId={addModuleId || ''}
          onIdChange={(id: number): void => {
            setAddTagId(id)
          }}
        />
      </Modal>
    </div>
  )
})
