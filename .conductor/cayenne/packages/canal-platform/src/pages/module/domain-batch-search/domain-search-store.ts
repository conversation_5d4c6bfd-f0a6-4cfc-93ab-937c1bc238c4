import {
  getBizComponentLibInfoByDomainCode,
  getInternalComponentLibInfo,
  getOldBizComponentLibInfoByBizSchema,
} from '@/pages/designer/materials'
import { getModuleSchema } from '@/pages/designer/tools'
import { getAllOnlineModuleList } from '@/services/backend/module'
import { SearchStore, type SearchResultItem } from '@/stores/search-store'
import { createDesignerUrl } from '@/utils'
import { ModuleType } from '@ad/canal-shared'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { mergeComponentLibWithImplementProps } from '@ad/e2e-material-schema-utils'
import type { E2ESchema } from '@ad/e2e-schema'
import { encodeComponentType } from '@kael/schema-utils'
import { fromPairs, once } from 'lodash'
import { makeObservable, observable, reaction, runInAction } from 'mobx'

/**
 * 业务域模块详情
 */
export interface DomainModuleDetail {
  /**
   * 模块 uuid
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * （线上）版本
   */
  version: string
  /**
   * 模块类型
   */
  type: ModuleType
}

/**
 * 业务域搜索仓库
 */
export class DomainSearchStore extends SearchStore {
  /**
   * 业务域模块对象表
   */
  private _domainModuleObjMap: Record<string, DomainModuleDetail> = {}

  /**
   * 业务域模块对象表
   */
  public get domainModuleObjMap(): Record<string, DomainModuleDetail> {
    return this._domainModuleObjMap
  }

  /**
   * 模块等待对象表，模块 ID -> 等待 Promise
   */
  private _moduleWaitObjMap: Record<string, Promise<void> | undefined> = {}

  /**
   * Schema 缓存，模块 ID -> 搭建 Schema
   */
  private _schemaCache: Record<string, E2ESchema | null | undefined> = {}

  /**
   * 物料 Schema 缓存，模块 ID -> 类型 -> 版本 -> 组件物料 Schema
   */
  private _materialSchemaCache: Record<
    string,
    | Record<
        string,
        Record<string, E2ERemoteComponentMaterialSchema | null> | undefined
      >
    | undefined
  > = {}

  /**
   * 业务域搜索仓库
   * @param domainCode （业务）域代码
   */
  public constructor(public readonly domainCode: string) {
    super()
    makeObservable<DomainSearchStore, '_domainModuleObjMap'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _domainModuleObjMap: observable.ref,
    })
    this._init()

    if (process.env.NODE_ENV === 'development') {
      Object.assign(window, {
        domainSearchStore: this,
      })
    }
  }

  /**
   * 初始化
   */
  private async _init(): Promise<void> {
    const res = await getAllOnlineModuleList({
      domainCode: this.domainCode,
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
    })
    const domainModules: DomainModuleDetail[] = res.data.list.map((item) => ({
      id: item.module.id,
      name: item.module.name,
      version: item.version,
      type: item.module.type,
    }))
    runInAction(() => {
      this._domainModuleObjMap = fromPairs(domainModules.map((m) => [m.id, m]))
    })
    // 过滤全局模块
    const searchModuleIds = domainModules
      .filter((m) => m.type !== ModuleType.GLOBAL)
      .map((m) => m.id)
    reaction(
      () => this.keyword,
      (keyword) => {
        this.cancelSearch()
        if (!keyword) return
        this.search(keyword, searchModuleIds, true)
      },
    )
  }

  /**
   * 加载模块
   * @param moduleId 模块 ID
   */
  private _loadModule(moduleId: string): Promise<void> {
    let p = this._moduleWaitObjMap[moduleId]
    if (!p) {
      p = this._moduleWaitObjMap[moduleId] = (async (): Promise<void> => {
        try {
          const [schema, internalComponentLibInfo, bizComponentLibInfo] =
            await Promise.all([
              getModuleSchema(
                moduleId,
                this._domainModuleObjMap[moduleId].version,
              ),
              getInternalComponentLibInfo(),
              this._fetchBizComponentLibInfo(),
            ])
          const oldBizComponentLibInfo =
            await getOldBizComponentLibInfoByBizSchema(
              bizComponentLibInfo.schemaJson,
              schema,
            )

          this._schemaCache[moduleId] = schema
          for (const libInfo of [
            internalComponentLibInfo,
            bizComponentLibInfo,
            oldBizComponentLibInfo,
          ]) {
            for (const component of mergeComponentLibWithImplementProps(
              libInfo.schemaJson,
            ).components) {
              const type = encodeComponentType(
                // 老业务组件库重定向到业务组件库
                libInfo.name === oldBizComponentLibInfo.name
                  ? bizComponentLibInfo.name
                  : libInfo.name,
                component.type,
              )
              const version = `${
                (component as E2ERemoteComponentMaterialSchema).version
              }`
              let typeMap = this._materialSchemaCache[moduleId]
              if (!typeMap) {
                typeMap = this._materialSchemaCache[moduleId] = {}
              }
              let versionMap = typeMap[type]
              if (!versionMap) {
                versionMap = typeMap[type] = {}
              }
              versionMap[version] = component
            }
          }
        } catch (err) {
          console.error('DomainSearchStore::_loadModule err', err)
        }
      })()
    }
    return p
  }

  /**
   * 获取业务组件库（物料）信息
   */
  private _fetchBizComponentLibInfo = once(() =>
    getBizComponentLibInfoByDomainCode(this.domainCode),
  )

  /**
   * 打开设计器
   * @param item 搜索结果条目
   */
  public openDesigner(item: SearchResultItem): void {
    const domainModule = this._domainModuleObjMap[item.moduleId]
    const designerUrl = createDesignerUrl(domainModule.type, {
      moduleId: item.moduleId,
      moduleVersion: domainModule.version,
      domainCode: this.domainCode,
      anchorStr: JSON.stringify(item.anchor),
    })
    window.open(designerUrl)
  }

  /**
   * 获取 Schema
   * @param moduleId 模块 ID
   */
  protected async handleGetSchema(moduleId: string): Promise<E2ESchema | null> {
    await this._loadModule(moduleId)
    return this._schemaCache[moduleId] || null
  }

  /**
   * 处理【获取组件物料 Schema】
   * @param moduleId 模块 ID
   * @param type 组件类型
   * @param version 版本
   */
  protected async handleGetComponentMaterialSchema(
    moduleId: string,
    type: string,
    version?: string,
  ): Promise<E2ERemoteComponentMaterialSchema | null> {
    await this._loadModule(moduleId)
    // if (moduleId === '1d5c7c05-9c4a-4d1a-ac3e-b3acbdf886e9') {
    //   console.log('debug handleGetComponentMaterialSchema', {
    //     moduleId,
    //     type,
    //     version,
    //     ret:
    //       this._materialSchemaCache[moduleId]?.[type]?.[`${version}`] || null,
    //   })
    // }
    return this._materialSchemaCache[moduleId]?.[type]?.[`${version}`] || null
  }
}
