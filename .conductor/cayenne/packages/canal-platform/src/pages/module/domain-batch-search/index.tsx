import { Icon<PERSON>utton } from '@/components/icon-button'
import { SearchResultTree } from '@/components/search-result-tree'
import { SearchResultTreeLeafNode } from '@/components/search-result-tree-leaf-node'
import { SearchResultTreeNonLeafNode } from '@/components/search-result-tree-non-leaf-node'
import { SearchTaskStatus, groupSearchResultItems } from '@/stores/search-store'
import { useLatestFn } from '@ad/canal-shared-ui'
import { useSearchParams } from '@kmi/react'
import { NormalEditLine } from '@m-ui/icons'
import { Form, Input, Spin, Tooltip } from '@m-ui/react'
import type { DataNode } from '@m-ui/react/lib/tree'
import { sumBy } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useMemo, type ChangeEvent } from 'react'
import styled from 'styled-components'
import { DomainSearchStore } from './domain-search-store'

/**
 * 业务域批量搜索
 */
export const DomainBatchSearch = observer(() => {
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode') || ''
  const domainSearchStore = useMemo(
    () => new DomainSearchStore(domainCode),
    [domainCode],
  )
  const { keyword, currentTask, domainModuleObjMap } = domainSearchStore
  const rawResultItems = currentTask?.resultItems
  const resultItems = useMemo(() => rawResultItems || [], [rawResultItems])
  const moduleGroups = useMemo(
    () => groupSearchResultItems(resultItems),
    [resultItems],
  )
  const resultSummary = useMemo(() => {
    if (!resultItems.length) return ''
    return `${moduleGroups.length} 个模块中有 ${resultItems.length} 个结果`
  }, [moduleGroups.length, resultItems.length])
  const treeData: DataNode[] = useMemo(() => {
    return moduleGroups.map((moduleGroup) => ({
      key: moduleGroup.moduleId,
      title: (
        <SearchResultTreeNonLeafNode
          title={`${domainModuleObjMap[moduleGroup.moduleId].name} (${
            moduleGroup.moduleId
          }) (${sumBy(
            moduleGroup.items,
            (categoryGroup) => categoryGroup.items.length,
          )})`}
        />
      ),
      selectable: false,
      children: moduleGroup.items.map((categoryGroup, categoryGroupIndex) => ({
        key: JSON.stringify([
          moduleGroup.moduleId,
          categoryGroup.category,
          categoryGroupIndex,
        ]),
        title: <SearchResultTreeNonLeafNode title={categoryGroup.category} />,
        selectable: false,
        children: categoryGroup.items.map((item, itemIndex) => {
          const key = JSON.stringify([
            moduleGroup.moduleId,
            categoryGroup.category,
            categoryGroupIndex,
            itemIndex,
          ])
          return {
            key,
            selectable: false,
            isLeaf: true,
            title: (
              <SearchResultTreeLeafNode
                item={item}
                actions={
                  <Tooltip title="打开设计器">
                    <IconButton
                      onClick={(): void => domainSearchStore.openDesigner(item)}
                    >
                      <NormalEditLine />
                    </IconButton>
                  </Tooltip>
                }
              />
            ),
          }
        }),
      })),
    }))
  }, [domainModuleObjMap, domainSearchStore, moduleGroups])
  const handleSearchInputChange = useLatestFn(
    (ev: ChangeEvent<HTMLInputElement>) => {
      domainSearchStore.setKeyword(ev.target.value)
    },
  )
  return (
    <Container>
      <div className="search-form">
        <Form.Item label="搜索" tooltip="暂不含全局模块">
          <Input value={keyword} onChange={handleSearchInputChange} />
        </Form.Item>
      </div>
      {currentTask && (
        <div className="search-summary">
          <div>
            {currentTask.status === SearchTaskStatus.RUNNING && <Spin />}
            {resultSummary && <span>{resultSummary}</span>}
            {currentTask.status === SearchTaskStatus.FINISHED &&
              !resultItems.length && <span>未找到结果</span>}
          </div>
        </div>
      )}
      <div className="search-body">
        <SearchResultTree key={currentTask?.id} treeData={treeData} />
      </div>
    </Container>
  )
})

const Container = styled.div`
  height: calc(100vh - 110px);
  margin-bottom: -24px;
  display: flex;
  flex-direction: column;
  position: relative;

  .search-form {
    flex: none;
    max-width: 800px;
    width: 100%;
    margin: auto;
  }

  .search-summary {
    display: flex;
    justify-content: space-between;
  }

  .search-body {
    margin-top: 10px;
    height: 0;
    flex: auto;
  }

  ${IconButton} {
    margin-left: 6px;

    &:last-child {
      margin-right: 12px;
    }
  }
`
