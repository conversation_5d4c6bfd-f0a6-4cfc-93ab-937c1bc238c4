import {
  deleteModuleTags,
  getAllTagsByModuleId,
  getInfoByIds,
} from '@/services/backend/tags'
import { SystemAddLine } from '@m-ui/icons'
import { Tag, message } from '@m-ui/react'
import { useEffect, useState } from 'react'
import { type ITag } from '../business-domain/domain-tag'

const ModuleTag = ({
  moduleId,
  addTagCallback,
}: {
  moduleId: string
  addTagCallback: (moduleId: string, fun: () => void) => void
}): JSX.Element => {
  const [tags, setTags] = useState<ITag[]>([])

  const fetchAllTags = async (): Promise<void> => {
    const res = await getAllTagsByModuleId({ moduleId })
    if (res.result === 1 && Array.isArray(res.data) && res.data.length > 0) {
      const ids = res.data.map((tag) => tag.tagId)
      const tagsRes = await getInfoByIds({ ids })
      if (tagsRes.result === 1 && Array.isArray(tagsRes.data)) {
        const mergedArray = res.data.map((item1) => {
          const matchingItem = tagsRes.data.find(
            (item2) => item2.id === Number(item1.tagId),
          )
          return {
            ...item1,
            ...{
              l1Tag: matchingItem?.l1Tag,
              l2Tag: matchingItem?.l2Tag,
            },
          }
        })
        setTags(mergedArray)
      }
    }
  }
  const mark = (): void => {
    addTagCallback && addTagCallback(moduleId, () => fetchAllTags())
  }
  const deleteTag = async (id: number): Promise<void> => {
    const res = await deleteModuleTags({ id })
    if (res.result === 1) {
      message.success('删除成功')
      fetchAllTags()
    } else {
      message.error(res.msg)
    }
  }
  useEffect(() => {
    fetchAllTags()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div>
      {tags.map((tagInfo) => {
        return (
          <Tag
            color="blue"
            style={{ cursor: 'pointer' }}
            closable={true}
            onClose={(): Promise<void> => deleteTag(tagInfo.id)}
            key={tagInfo.id}
          >
            ({tagInfo.l1Tag})-
            {tagInfo.l2Tag}
          </Tag>
        )
      })}
      <Tag onClick={mark} style={{ cursor: 'pointer' }}>
        <SystemAddLine /> 打标
      </Tag>
    </div>
  )
}

export default ModuleTag
