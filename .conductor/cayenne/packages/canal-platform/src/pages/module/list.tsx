import { Card, Tabs } from '@m-ui/react'
import styled from 'styled-components'
import { DomainBatchSearch } from './domain-batch-search'
import { DomainModuleList } from './domain-module-list'

const ModuleList = (): JSX.Element => {
  return (
    <Card title="已上线模块列表" bordered={false}>
      <StyledTabs>
        <Tabs.TabPane tab="模块列表" key="模块列表">
          <DomainModuleList />
        </Tabs.TabPane>
        <Tabs.TabPane tab="批量搜索" key="批量搜索">
          <DomainBatchSearch />
        </Tabs.TabPane>
      </StyledTabs>
    </Card>
  )
}

export default ModuleList

const StyledTabs = styled(Tabs)`
  &&& {
    overflow: visible;
  }
`
