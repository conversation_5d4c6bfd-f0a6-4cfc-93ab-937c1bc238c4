import { getAllTagsByDomain } from '@/services/backend/tags'
import { Select } from '@m-ui/react'
import { useEffect, useState } from 'react'
import { FIRST_LEVEL, type ITag } from '../business-domain/domain-tag'
const { Option } = Select
const AddTag = ({
  moduleId,
  domainCode,
  onIdChange,
}: {
  moduleId: string
  domainCode: string
  onIdChange: (id: number) => void
}): JSX.Element => {
  console.log(moduleId)
  const [tags, setTags] = useState<Record<string, ITag[]>>({})
  const [l2s, setL2s] = useState<ITag[]>([])
  const [selectId, setSelectId] = useState<number>()
  const getDomainTag = async (): Promise<void> => {
    const res = await getAllTagsByDomain({ domainCode })
    if (res.result === 1 && res.data) {
      setTags(res.data)
      const l2sArr = res.data?.[FIRST_LEVEL[0]] || []
      setL2s(l2sArr)
      setSelectId(l2sArr[0].id)
    }
  }

  const handleL1Change = (value: string): void => {
    const l2Arr = tags[value] || []
    setL2s(l2Arr)
    setSelectId(l2Arr?.[0]?.id)
  }

  const onl2Change = (value: number): void => {
    setSelectId(value)
  }
  useEffect(() => {
    selectId && onIdChange(selectId)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectId])

  useEffect(() => {
    getDomainTag()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return (
    <>
      <Select
        defaultValue={FIRST_LEVEL[0]}
        style={{ width: 120 }}
        onChange={handleL1Change}
      >
        {FIRST_LEVEL.map((tag) => (
          <Option key={tag} value={tag}>
            {tag}
          </Option>
        ))}
      </Select>
      <Select style={{ width: 200 }} value={selectId} onChange={onl2Change}>
        {l2s.map((tag) => (
          <Option key={tag.id} value={tag.id}>
            {tag.l2Tag}
          </Option>
        ))}
      </Select>
    </>
  )
}

export default AddTag
