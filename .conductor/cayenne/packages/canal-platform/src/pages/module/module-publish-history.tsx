import HistoryDiffModel from '@/pages/diff/history-diff-model'
import type { moduleDeployList } from '@/services/backend/models'
import { getModuleDeployHistoryList } from '@/services/backend/module'
import { createDesignerUrl } from '@/utils'
import { NormalHistoryLine, SystemPreviewopenLine } from '@m-ui/icons'
import { Button, Drawer, Space, Table, Tooltip, Typography } from '@m-ui/react'
import qs from 'query-string'
import { useEffect, useState } from 'react'
import { dateFormat } from '../change/const'

const ModulePublishHistory = ({
  domainCode,
  moduleId,
  moduleName,
  moduleType,
}: {
  domainCode: string
  moduleId: string
  moduleName: string
  moduleType: number
}): JSX.Element => {
  const [drawerVisible, setDrawerVisible] = useState(false)
  const [tableData, setTableData] = useState<moduleDeployList[]>([])
  const [total, setTotal] = useState<number>(0)
  const [pageNum, setPageNum] = useState<number>(1)
  const [loading, setLoading] = useState(false)

  const handleButtonClick = (): void => {
    setDrawerVisible(true)
    // Fetch table data here
  }

  const handleDrawerClose = (): void => {
    setDrawerVisible(false)
  }
  const requestHistory = async (): Promise<void> => {
    setLoading(true)
    const res = await getModuleDeployHistoryList({
      moduleId,
      domainCode,
      pageNum: pageNum,
      pageSize: 10,
    })
    if (res.result === 1) {
      setTableData(res.data.list)
      setTotal(res.data.total)
    }
    setLoading(false)
  }

  useEffect(() => {
    if (drawerVisible) {
      requestHistory()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNum, drawerVisible])
  return (
    <div>
      <Button type="link" onClick={handleButtonClick}>
        <Tooltip placement="top" title={`点击查看发布记录`}>
          <NormalHistoryLine />
          发布历史
        </Tooltip>
      </Button>
      <Drawer
        title={
          <Space>
            {`【${moduleName}】发布历史`}
            模块ID：<Typography.Text copyable>{moduleId}</Typography.Text>
          </Space>
        }
        placement="right"
        closable={true}
        onClose={handleDrawerClose}
        visible={drawerVisible}
        width={1200}
      >
        <Table
          dataSource={tableData}
          loading={loading}
          pagination={{
            current: pageNum,
            pageSize: 10,
            total: total,
          }}
          columns={[
            {
              title: '部署ID',
              dataIndex: 'id',
            },
            {
              title: '版本',
              dataIndex: 'version',
            },
            {
              title: '发布人',
              dataIndex: 'createUser',
            },
            {
              title: '发布时间',
              dataIndex: 'createTime',
              render: (createTime: number): string => {
                return dateFormat(createTime)
              },
            },
            {
              title: '关联的变更',
              dataIndex: 'changeId',
              render: (...[, record]): JSX.Element => {
                const query = {
                  changeId: record.changeId,
                  domainCode: domainCode,
                }
                return (
                  <Button
                    type="link"
                    href={`/change/detail?${qs.stringify(query)}`}
                    target="_blank"
                  >
                    {record.changeName}
                  </Button>
                )
              },
            },
            {
              title: '操作',
              key: 'action',
              render: (...[, record]): JSX.Element => {
                return (
                  <Space>
                    <Button
                      type="link"
                      href={createDesignerUrl(moduleType, {
                        moduleId: record.moduleId,
                        moduleVersion: record.version,
                        domainCode,
                      })}
                      target="_blank"
                    >
                      <SystemPreviewopenLine /> 查看配置
                    </Button>
                    <HistoryDiffModel
                      developDeployId={record.id}
                      domainCode={domainCode}
                      moduleType={moduleType}
                    />
                  </Space>
                )
              },
            },
          ]}
          onChange={({ current }): void => {
            if (typeof current !== 'undefined') {
              setPageNum(current)
            }
          }}
        />
      </Drawer>
    </div>
  )
}

export default ModulePublishHistory
