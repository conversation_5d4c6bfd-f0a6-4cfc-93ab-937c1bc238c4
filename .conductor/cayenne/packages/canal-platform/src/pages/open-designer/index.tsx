import { getModuleDetail } from '@/services/backend/module'
import { createDesignerUrl } from '@/utils'
import { Spin } from '@m-ui/react'
import { useEffect } from 'react'
import styled from 'styled-components'
import { DESIGNER_QUERY } from '../designer/constants'

/**
 * 打开设计器
 */
// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export const OpenDesigner = () => {
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    ;(async () => {
      const { data } = await getModuleDetail({
        id: DESIGNER_QUERY.moduleId,
      })
      location.replace(
        createDesignerUrl(data.type, {
          ...DESIGNER_QUERY,
          domainCode: data.businessDomainCode,
          moduleId: data.id,
          moduleVersion: `${data.version}`,
        }),
      )
    })()
  }, [])
  return (
    <Container>
      <Spin />
    </Container>
  )
}

const Container = styled.div`
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
`

export default OpenDesigner
