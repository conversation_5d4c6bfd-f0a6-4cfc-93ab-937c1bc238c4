import { Config } from '@/components/help-tip'
import {
  getAllBusinessDomain,
  getDomainRouteConfig,
} from '@/services/backend/business_domain'
import type { BusinessDomain, User } from '@/services/backend/models'
import { history, useModel } from '@kmi/react'
import { SmileTwoTone } from '@m-ui/icons'
import { Button, Card, Col, Row, Space, Tabs } from '@m-ui/react'
import Empty from '@m-ui/react/es/empty'
import { useEffect, useState, type FC } from 'react'
import styled from 'styled-components'
const { TabPane } = Tabs

/**
 * 主页
 */
export const Home: FC = () => {
  const { initialState, setInitialState } = useModel('@@initialState')
  const [list, setList] = useState<BusinessDomain[]>([])
  const [myList, setMyList] = useState<BusinessDomain[]>([])
  const [activeTab, setActiveTab] = useState<string>('my')
  const [routesConfig, setRoutesConfig] = useState<
    Record<string, Record<string, boolean>>
  >({})

  const goToDomainHome = (item: BusinessDomain) => {
    setInitialState({
      userInfo: { ...initialState?.userInfo } as User,
      domainInfo: { ...item },
    })
    if (routesConfig[item.domainCode]?.showNewOverview) {
      history.push(`/business-domain/overview?domainCode=${item.domainCode}`)
    } else {
      history.push(`/business-domain/detail?domainCode=${item.domainCode}`)
    }
  }

  const getDomainList = async (activeKey: string): Promise<void> => {
    const list = await getAllBusinessDomain({
      type: 'All',
      filterByUserPermission: activeKey === 'my',
    })
    if (activeKey === 'my') {
      setMyList(list.data)
    } else {
      setList(list.data)
    }
  }

  const getRoutesConfig = async (): Promise<void> => {
    const config = (await getDomainRouteConfig())?.data || {}
    setRoutesConfig(config)
  }

  const handleTabChange = (activeKey: string) => {
    setActiveTab(activeKey)
    getDomainList(activeKey)
  }

  useEffect(() => {
    getDomainList('my')
    getRoutesConfig()
  }, [])
  return (
    <Row style={{ display: 'flex', flexWrap: 'nowrap' }}>
      <Col flex="auto">
        <Card
          style={{
            borderRadius: 4,
            margin: '0 32px',
          }}
          title=""
        >
          <Tabs defaultActiveKey={activeTab} onChange={handleTabChange}>
            <TabPane tab="我的" key="my">
              {myList.length === 0 && <Empty description={false} />}
              <HomeContainer>
                {myList.map((item) => {
                  return (
                    <HomeItem
                      onClick={() => goToDomainHome(item)}
                      key={item.domainCode}
                    >
                      <HomeItemLogo
                        style={{ backgroundImage: `url(${item.logo})` }}
                      />
                      <div style={{ marginLeft: 24, flex: 1, minWidth: 0 }}>
                        <HomeItemName>{item.domainName}</HomeItemName>
                        <HomeItemDesc>{item.desc}</HomeItemDesc>
                      </div>
                    </HomeItem>
                  )
                })}
              </HomeContainer>
            </TabPane>
            <TabPane tab="全部业务域" key="all">
              <HomeContainer>
                {list.map((item) => {
                  return (
                    <HomeItem
                      onClick={() => goToDomainHome(item)}
                      key={item.domainCode}
                    >
                      <HomeItemLogo
                        style={{ backgroundImage: `url(${item.logo})` }}
                      />
                      <div style={{ marginLeft: 24, flex: 1, minWidth: 0 }}>
                        <HomeItemName>{item.domainName}</HomeItemName>
                        <HomeItemDesc>{item.desc}</HomeItemDesc>
                      </div>
                    </HomeItem>
                  )
                })}
              </HomeContainer>
            </TabPane>
          </Tabs>
        </Card>
      </Col>
      <Col flex="100px">
        <Card
          title={
            <>
              必知必会
              <SmileTwoTone
                style={{ fontSize: '20px', marginLeft: 8 }}
                twoToneColor="#eb2f96"
              />
            </>
          }
        >
          <Space direction="vertical">
            <Button
              type="link"
              href={Config.troubleshooting.docLink}
              target="_blank"
            >
              {Config.troubleshooting.txt}
            </Button>
            <Button
              type="link"
              href={Config.serverEnvConfig.docLink}
              target="_blank"
            >
              {Config.serverEnvConfig.txt}
            </Button>
          </Space>
        </Card>
      </Col>
    </Row>
  )
}

export default Home

const HomeContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  column-gap: 16px;
  row-gap: 16px;
`

const HomeItem = styled.div`
  padding: 16px;
  border: 1px solid #ebedf0;
  display: flex;
  align-items: center;
  &:hover {
    box-shadow:
      0 8px 16px -8px #0c121f14,
      0 1px 8px #0c121f14;
    transition: all ease 0.4s;
    cursor: pointer;
  }
`

const HomeItemLogo = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
`

const HomeItemName = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #1d2126;
  line-height: 26px;
  margin-bottom: 8px;
`

const HomeItemDesc = styled.div`
  color: #8a9199;
  line-height: 22px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 44px;
`
