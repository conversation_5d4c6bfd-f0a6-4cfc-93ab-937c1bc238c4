import Reference from '@/components/reference'
import { getListByDomain } from '@/services/backend/data_source'
import { type DataSourceConfigDto } from '@/services/backend/models'
import { useSearchParams } from '@kmi/react'
import { SystemQuestionmarkCircleLine } from '@m-ui/icons'
import {
  Button,
  Col,
  Form,
  Input,
  Popconfirm,
  Row,
  Space,
  Table,
  Typography,
  message,
} from '@m-ui/react'
import type { TablePaginationConfig } from '@m-ui/react/es/table'
import React, { useEffect, useState } from 'react'
import DataSourceConfigModal from './modal'

const PAGE_SIZE = 10
const DataSourceConfigTable: React.FC = () => {
  const [searchParams] = useSearchParams()
  const [data, setData] = useState<DataSourceConfigDto[]>([])
  const [total, setTotal] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(false)
  const [pageNum, setPageNum] = useState<number>(1)
  const [modalVisible, setModalVisible] = useState<boolean>(false)
  const domainCode = searchParams.get('domainCode') || ''
  const [editData, setEditData] = useState<DataSourceConfigDto | undefined>()
  const [form] = Form.useForm()
  const [formValues, setFormValues] = useState({})

  useEffect(() => {
    fetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNum, formValues])

  const fetchData = async (): Promise<void> => {
    setLoading(true)
    try {
      const response = await getListByDomain({
        pageNum,
        pageSize: PAGE_SIZE,
        domainCode,
        ...formValues,
      })
      setData(response.data.list)
      setTotal(response.data.total)
    } catch (error) {
      message.error('查询失败')
    } finally {
      setLoading(false)
    }
  }
  const showAddModal = (): void => {
    setModalVisible(true)
  }
  const cancelModal = (): void => {
    setModalVisible(false)
    setEditData(undefined)
  }
  const onOk = (): void => {
    fetchData()
    cancelModal()
  }
  const edit = (record: DataSourceConfigDto): void => {
    setEditData(record)
    setModalVisible(true)
  }

  const columns = [
    {
      title: '名称/ID',
      dataIndex: 'id',
      key: 'id',
      width: 500,
      render: (id, record): JSX.Element => {
        return (
          <Space direction="vertical">
            <Typography.Title level={5}>{record.name}</Typography.Title>
            <Typography.Text>{id}</Typography.Text>
          </Space>
        )
      },
    },
    { title: 'Path', dataIndex: 'path', key: 'path' },
    { title: 'http方法类型', dataIndex: 'method', key: 'method' },
    {
      title: '配置',
      key: 'config',
      width: 500,
      render: (_, record): JSX.Element => {
        return (
          <>
            <Row>
              <Col style={{ width: '80px', fontWeight: 'bold' }}>Staging：</Col>
              <Col span={18}>{record.stagingDomain}</Col>
            </Row>
            <Row>
              <Col style={{ width: '80px', fontWeight: 'bold' }}>Prt：</Col>
              <Col span={18}>{record.prtDomain}</Col>
            </Row>
            <Row>
              <Col style={{ width: '80px', fontWeight: 'bold' }}>Beta：</Col>
              <Col span={18}>{record.betaDomain}</Col>
            </Row>
            <Row>
              <Col style={{ width: '80px', fontWeight: 'bold' }}>prod：</Col>
              <Col span={18}>{record.productionDomain}</Col>
            </Row>
          </>
        )
      },
    },
    { title: '更新人', dataIndex: 'updateUser', key: 'updateUser' },
    {
      title: '操作',
      key: 'action',
      render: (_text, record) => (
        <Space>
          <Popconfirm
            title="确认修改吗？"
            icon={<SystemQuestionmarkCircleLine style={{ color: 'red' }} />}
            onConfirm={(): void => {
              edit(record)
            }}
          >
            <Button type="link">编辑</Button>
          </Popconfirm>
          <Reference
            dataSourceId={record.id}
            domainCode={domainCode}
            name={record.name}
          />
        </Space>
      ),
    },
  ]

  /**
   * 页码变动
   * @param pagination
   */
  const handleTableChange = (pagination: TablePaginationConfig): void => {
    if (typeof pagination.current !== 'undefined') {
      setPageNum(pagination.current)
    }
  }

  const onSearch = (values: { path: string }): void => {
    setPageNum(1)
    setFormValues(values)
  }

  return (
    <>
      <div style={{ margin: '16px 0', float: 'right' }}>
        <Button type="primary" onClick={showAddModal}>
          新增数据源
        </Button>
      </div>
      <Form
        name="component_search"
        layout="inline"
        onFinish={onSearch}
        form={form}
        style={{ marginBottom: '16px' }}
      >
        <Form.Item label="Path" name="path">
          <Input
            placeholder="请输入要查找的path（模糊查找）"
            style={{ width: 300 }}
          ></Input>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            搜索
          </Button>
          <Button
            style={{ margin: '0 8px' }}
            onClick={(): void => {
              form.resetFields()
              setFormValues({})
              setPageNum(1)
            }}
          >
            清空
          </Button>
        </Form.Item>
      </Form>
      <DataSourceConfigModal
        visible={modalVisible}
        onCancel={cancelModal}
        onOk={onOk}
        domainCode={domainCode}
        initialValues={editData}
        assistedFillingHostList={data}
      />
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          current: pageNum,
          pageSize: PAGE_SIZE,
          total: total,
          showSizeChanger: false,
        }}
        onChange={handleTableChange}
      />
    </>
  )
}

export default DataSourceConfigTable
