import { JSONEditor } from '@/components/json-editor'
import {
  add,
  addStaging,
  edit,
  editStaging,
} from '@/services/backend/data_source'
import { type DataSourceConfigDto } from '@/services/backend/models'
import { jsonObjRule } from '@/utils'
import {
  Alert,
  Button,
  Col,
  Dropdown,
  Form,
  Input,
  Menu,
  Modal,
  Radio,
  Row,
  message,
} from '@m-ui/react'
import React, { useEffect, useMemo, useState } from 'react'

interface DataSourceConfigModalProps {
  visible: boolean
  onCancel: () => void
  onOk: () => void
  initialValues?: DataSourceConfigDto
  domainCode: string
  assistedFillingHostList?: DataSourceConfigDto[]
}

const DataSourceConfigModal: React.FC<DataSourceConfigModalProps> = ({
  visible,
  onCancel,
  onOk,
  initialValues,
  domainCode,
  assistedFillingHostList,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState<boolean>(false)
  const isStaging = process.env.KMI_ENV === 'staging'
  const assistedHostList = useMemo(() => {
    const map: Record<string, number> = {}
    const list: React.ReactElement[] = []
    assistedFillingHostList?.forEach((item) => {
      const key =
        (item.productionDomain || '') +
        (item.stagingDomain || '') +
        (item.prtDomain || '')
      if (!!key && !map[key]) {
        list.push(
          <Menu.Item
            key={JSON.stringify({
              stagingDomain: item.stagingDomain,
              prtDomain: item.prtDomain,
              betaDomain: item.betaDomain,
              productionDomain: item.productionDomain,
            })}
          >
            <div style={{ width: '500px', borderBottom: '1px solid #e8e8e8' }}>
              <Row>
                <Col style={{ width: '80px', fontWeight: 'bold' }}>
                  Staging：
                </Col>
                <Col span={18}>{item.stagingDomain}</Col>
              </Row>
              <Row>
                <Col style={{ width: '80px', fontWeight: 'bold' }}>Prt：</Col>
                <Col span={18}>{item.prtDomain}</Col>
              </Row>
              <Row>
                <Col style={{ width: '80px', fontWeight: 'bold' }}>Beta：</Col>
                <Col span={18}>{item.betaDomain}</Col>
              </Row>
              <Row>
                <Col style={{ width: '80px', fontWeight: 'bold' }}>prod：</Col>
                <Col span={18}>{item.productionDomain}</Col>
              </Row>
            </div>
          </Menu.Item>,
        )
        map[key] = 1
      }
    })
    return list
  }, [assistedFillingHostList])

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues)
    } else {
      form.resetFields()
    }
  }, [initialValues, form])

  const handleSubmit = async (values: DataSourceConfigDto): Promise<void> => {
    setLoading(true)
    try {
      if (initialValues && initialValues.id) {
        // Edit mode
        const res = await edit({
          config: { ...values, domainCode },
          id: initialValues.id,
        })
        if (res.result === 1) {
          message.success('更新成功')
        } else {
          message.error(res.msg)
        }
        if (!isStaging) {
          editStaging({
            config: { ...values, domainCode },
            id: initialValues.id,
          })
        }

        onOk()
      } else {
        // Add mode
        const res = await add({ ...values, domainCode })
        if (res.result === 1) {
          message.success('添加成功')
        } else {
          message.error(res.msg)
        }
        if (!isStaging) {
          addStaging({ ...values, id: res.data.id, domainCode })
        }
      }
      form.resetFields()
      onOk()
    } catch (error) {
      message.error('Failed to submit data')
    } finally {
      setLoading(false)
    }
  }
  const handleMenuClick = ({ key }: { key: string }): void => {
    const { stagingDomain, prtDomain, betaDomain, productionDomain } =
      JSON.parse(key)
    form.setFieldsValue({
      stagingDomain,
      prtDomain,
      betaDomain,
      productionDomain,
    })
  }

  return (
    <Modal
      visible={visible}
      title={initialValues ? '修改数据源配置' : '新增数据源配置'}
      okText={initialValues ? '更新' : '添加'}
      onCancel={onCancel}
      maskClosable={false}
      width={800}
      footer={[
        <Button
          key="cancel"
          onClick={(): void => {
            form.resetFields()
            onCancel && onCancel()
          }}
        >
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={(): void => form.submit()}
        >
          {initialValues ? '更新' : '添加'}
        </Button>,
      ]}
    >
      <Alert
        message="为了保持线上稳定性，关键配置(path,线上域名和请求类型）一但填写后不允许更改，请谨慎填写，若要变更请新建数据源"
        type="warning"
        style={{ marginBottom: '16px' }}
      />
      <Form
        form={form}
        onFinish={handleSubmit}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
      >
        <Form.Item
          name="path"
          label="Path"
          rules={[
            { required: true, message: '请输入path' },
            {
              pattern: /^\/.*$/,
              message: 'Invalid API path!',
            },
          ]}
        >
          <Input disabled={!!initialValues?.path} />
        </Form.Item>
        <Form.Item
          name="method"
          label="请求类型"
          rules={[{ required: true, message: '请选择请求类型!' }]}
        >
          <Radio.Group disabled={!!initialValues?.method}>
            <Radio value={'POST'}>post</Radio>
            <Radio value={'GET'}>get</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="name"
          label="数据源名称"
          rules={[{ required: true, message: '请输入名称' }]}
        >
          <Input />
        </Form.Item>
        {assistedHostList?.length > 0 && (
          <Form.Item label="选择已有域名辅助填写">
            <Dropdown
              overlay={
                <Menu onClick={handleMenuClick}>{assistedHostList}</Menu>
              }
            >
              <Button type="primary">选择已有域名填写</Button>
            </Dropdown>
          </Form.Item>
        )}
        <Form.Item
          name="stagingDomain"
          label="Staging域名"
          rules={[
            {
              pattern: /^(https?:\/\/)([a-zA-Z0-9_-]+\.)+[a-zA-Z]{2,}$/,
              message: 'Invalid URL!',
            },
          ]}
        >
          <Input placeholder={'请输入带http(s)://的域名'} />
        </Form.Item>
        <Form.Item
          name="prtDomain"
          label="PRT域名"
          rules={[
            {
              pattern: /^(https?:\/\/)([a-zA-Z0-9_-]+\.)+[a-zA-Z]{2,}$/,
              message: 'Invalid URL!',
            },
          ]}
        >
          <Input placeholder={'请输入带http(s)://的域名'} />
        </Form.Item>
        <Form.Item
          name="betaDomain"
          label="Beta域名"
          rules={[
            {
              pattern: /^(https?:\/\/)([a-zA-Z0-9_-]+\.)+[a-zA-Z]{2,}$/,
              message: 'Invalid URL!',
            },
          ]}
        >
          <Input placeholder={'请输入带http(s)://的域名'} />
        </Form.Item>
        <Form.Item
          name="productionDomain"
          label="线上域名"
          rules={[
            {
              pattern: /^(https?:\/\/)([a-zA-Z0-9_-]+\.)+[a-zA-Z]{2,}$/,
              message: 'Invalid URL!',
            },
          ]}
        >
          <Input
            disabled={!!initialValues?.productionDomain}
            placeholder={'请输入带http(s)://的域名'}
          />
        </Form.Item>
        <Form.Item label="Mock 响应" name="mockRes" rules={[jsonObjRule]}>
          <JSONEditor height={400} />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default DataSourceConfigModal
