import ChangeStatus from '@/components/change/change-status'
import DomainServerMonitor from '@/components/domain-server-monitor'
import FrontMonitor from '@/components/front-monitor'
import {
  getDetailBusinessDomain,
  getMemberBusinessDomain,
} from '@/services/backend/business_domain'
import { getCanalProcessingChangeList } from '@/services/backend/change'
import type { BusinessDomainDetail, Member } from '@/services/backend/models'
import { Link, history, useSearchParams } from '@kmi/react'
import { Button, Card, Space, Table, Tag } from '@m-ui/react'
import { useEffect, useState, type FC } from 'react'
import styled from 'styled-components'
import { dateFormat, getTypeLabel } from '../change/const'
import { type ChangeModel } from '../change/model'
export const BusinessDomain: FC = () => {
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode')
  const [domainDetail, setDomainDetail] = useState<BusinessDomainDetail>()
  const [domainMembers, setDomainMembers] = useState<Member[]>([])
  const [processingChangeList, setProcessingChangeList] = useState<
    ChangeModel[]
  >([])

  const getDomainDetail = async (domainCode: string) => {
    try {
      const res = await getDetailBusinessDomain({ code: domainCode })
      if (res.result === 1) {
        setDomainDetail(res.data)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const getDomainMembers = async (domainCode: string) => {
    try {
      const res = await getMemberBusinessDomain({ code: domainCode })
      if (res.result === 1) {
        setDomainMembers(res?.data)
      }
    } catch (error) {
      console.log(error)
    }
  }
  /**
   * 获取该业务域下正在进行的变更
   */
  const getDomainProcessListData = async (domainCode: string) => {
    try {
      const res = await getCanalProcessingChangeList({ domainCode })
      console.log(res)
      if (res?.result === 1 && Array.isArray(res.data?.list)) {
        setProcessingChangeList(res.data.list)
      }
      // setProcessingChangeList(res)
    } catch (error) {
      console.log(error)
    }
  }

  const getColumns = () => {
    return [
      {
        title: '变更名称',
        dataIndex: 'changeName',
        render: (_, record): ReactNode => {
          return <div>{record.changeName}</div>
        },
      },
      {
        title: '创建者',
        dataIndex: 'createUser',
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        render: (type: number) => getTypeLabel(type),
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: number): ReactNode => {
          return <ChangeStatus status={status} />
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        render: (createTime: number) => {
          return dateFormat(createTime)
        },
      },
      {
        title: '操作',
        key: 'action',
        render: (_, record): ReactNode => {
          return (
            <Button type="link">
              <Link
                to={`/change/detail?changeId=${record.changeId}&domainCode=${record.domainCode}`}
              >
                进入变更
              </Link>
            </Button>
          )
        },
      },
    ]
  }
  /**
   * 创建变更
   */
  function goToCreateChange() {
    history.push(`/change/create?domainCode=${domainCode}`)
  }
  /**
   * 变更列表
   */
  function goToChangeList() {
    history.push(`/change/list?domainCode=${domainCode}`)
  }
  /**
   * 模块列表
   */
  function goToComponentList() {
    history.push(`/business-domain/component/list?domainCode=${domainCode}`)
  }

  useEffect(() => {
    if (domainCode) {
      getDomainDetail(domainCode)
      getDomainMembers(domainCode)
      getDomainProcessListData(domainCode)
    }
  }, [domainCode])

  return (
    <div>
      {/* <Alert type="info" message="暂无公告" showIcon closable /> */}
      <DomainWrapper>
        <DomainColumn>
          <Card
            style={{
              borderRadius: 4,
              marginBottom: 24,
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', height: 92 }}>
              <DomainValueItem onClick={goToChangeList}>
                <DomainItemLabel>变更数量</DomainItemLabel>
                <DomainItemValue>
                  <a href="javascript:void(0)">{domainDetail?.changeCount}</a>
                </DomainItemValue>
              </DomainValueItem>
              <DomainValueItem>
                <DomainItemLabel>模块数</DomainItemLabel>
                <DomainItemValue>{domainDetail?.moduleCount}</DomainItemValue>
              </DomainValueItem>
              <DomainValueItem onClick={goToComponentList}>
                <DomainItemLabel>组件资产数</DomainItemLabel>
                <DomainItemValue>
                  <a href="javascript:void(0)">
                    {domainDetail?.componentCount}
                  </a>
                </DomainItemValue>
              </DomainValueItem>
            </div>
          </Card>
          <Card
            title="进行中变更"
            style={{
              borderRadius: 4,
            }}
          >
            <Table
              columns={getColumns()}
              dataSource={processingChangeList}
              rowKey={'changeId'}
            />
          </Card>
        </DomainColumn>
        <DomainBlock>
          <Card
            title="快捷操作"
            style={{
              borderRadius: 4,
              marginBottom: 24,
            }}
          >
            <Space>
              <Button type="primary" onClick={() => goToCreateChange()}>
                新建变更
              </Button>
              {!!domainDetail && (
                <>
                  <FrontMonitor
                    projectId={JSON.parse(
                      domainDetail?.radarProjects || '[]',
                    ).join(',')}
                  />
                  <DomainServerMonitor
                    domain={JSON.parse(domainDetail?.schemaDomains || '[]')}
                    code={domainCode || ''}
                  />
                </>
              )}
            </Space>
          </Card>
          <Card
            title="成员"
            style={{
              borderRadius: 4,
              marginBottom: 24,
            }}
          >
            {domainMembers?.map((member) => {
              return (
                <Tag key={member.userId}>
                  {member.userId}
                  {member.permissionLevel === 'admin' && (
                    <svg
                      t="1729071954991"
                      class="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="2311"
                      width="15"
                      height="15"
                    >
                      <path
                        d="M755.2 780.8L896 640l59.733333 59.733333-200.533333 200.533334-119.466667-119.466667 59.733334-59.733333 59.733333 59.733333zM597.333333 768v85.333333H256v-170.666666h4.266667l85.333333-85.333334h375.466667l85.333333 85.333334h-426.666667l-38.4 38.4V768h256z m213.333334-81.066667z m-85.333334-324.266666c0 106.666667-85.333333 192-192 192S341.333333 469.333333 341.333333 362.666667 426.666667 170.666667 533.333333 170.666667 725.333333 256 725.333333 362.666667z m-85.333333 0C640 302.933333 593.066667 256 533.333333 256S426.666667 302.933333 426.666667 362.666667s46.933333 106.666667 106.666666 106.666666S640 422.4 640 362.666667z"
                        fill="#e0620d"
                        p-id="2312"
                      ></path>
                    </svg>
                  )}
                </Tag>
              )
            })}
          </Card>
          <Card
            title="基本信息"
            style={{
              borderRadius: 4,
            }}
          >
            <DomainRow style={{ marginBottom: 8 }}>
              <DomainLabel>名称：</DomainLabel>
              <DomainValue>{domainDetail?.name}</DomainValue>
            </DomainRow>
            <DomainRow>
              <DomainLabel>描述：</DomainLabel>
              <DomainValue>{domainDetail?.desc}</DomainValue>
            </DomainRow>
          </Card>
        </DomainBlock>
      </DomainWrapper>
    </div>
  )
}

export default BusinessDomain

const DomainWrapper = styled.div`
  display: flex;
`

const DomainColumn = styled.div`
  flex: 2;
`

const DomainBlock = styled.div`
  flex: 1;
  margin-left: 24px;
`

const DomainValueItem = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
`

const DomainItemLabel = styled.div`
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
`

const DomainItemValue = styled.div`
  height: 38px;
  margin-top: 4px;
  margin-bottom: 0;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.85);
  font-size: 30px;
  line-height: 38px;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
  color:;
`

const DomainRow = styled.div`
  display: flex;
  align-items: center;
`

const DomainLabel = styled.div`
  font-size: 14px;
  line-height: 20px;
`

const DomainValue = styled.div`
  font-size: 14px;
  line-height: 20px;
  color: rgb(138, 145, 153);
  margin-left: 5px;
`
