import { getAllTagsByDomain } from '@/services/backend/tags'
import { useSearchParams } from '@kmi/react'
import { Alert, Card, Collapse } from '@m-ui/react'
import { useEffect, useState, type FC } from 'react'
import EditableTagGroup from './tags'
const { Panel } = Collapse
export const FIRST_LEVEL = ['图表', '表格', '表单', '列表', '其他']

export interface ITag {
  l2Tag: string
  estimateTime: number
  id: number
  l1Tag?: string
}

const DomainTag: FC = () => {
  const [tags, setTags] = useState<Record<string, ITag[]>>({})
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode') || ''
  const getDomainTag = async (): Promise<void> => {
    const res = await getAllTagsByDomain({ domainCode })
    if (res.result === 1 && res.data) {
      setTags(res.data)
    }
  }

  useEffect(() => {
    getDomainTag()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <Alert message="请在以下基础大类内添加业务域的分类标签" type="info" />
      <Card bordered={false}>
        <Collapse defaultActiveKey={[FIRST_LEVEL[0]]}>
          {FIRST_LEVEL.map((item: string) => {
            return (
              <Panel header={item} key={item}>
                <EditableTagGroup
                  domainCode={domainCode}
                  l1Tag={item}
                  l2Tags={tags[item] || []}
                  updateCallback={getDomainTag}
                />
              </Panel>
            )
          })}
        </Collapse>
      </Card>
    </>
  )
}

export default DomainTag // 导出组件
