import {
  addDomainMember,
  getMemberBusinessDomain,
} from '@/services/backend/business_domain'
import type { Member } from '@/services/backend/models'
import { useSearchParams } from '@kmi/react'
import {
  Button,
  Divider,
  Form,
  Input,
  Select,
  Space,
  Tag,
  message,
} from '@m-ui/react'
import React, { useEffect, useState } from 'react'

const Permission: React.FC = () => {
  const [domainMembers, setDomainMembers] = useState<Member[]>([])
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode')
  const [loading, setLoading] = useState(false)

  const [form] = Form.useForm()

  const onFinish = async (): Promise<void> => {
    if (!domainCode) return
    form.validateFields().then(async (values) => {
      setLoading(true)
      const addResult = await addDomainMember({
        domainCode,
        permissionLevel: values.permissionLevel,
        userId: values.userId,
      })
      if (addResult.result === 1) {
        message.success('添加成功')
        getDomainMembers()
        form.resetFields()
      } else {
        message.error(addResult.msg || '失败')
      }
      setLoading(false)
    })
  }

  const getDomainMembers = async (): Promise<void> => {
    try {
      if (!domainCode) return
      const res = await getMemberBusinessDomain({ code: domainCode })
      if (res.result === 1) {
        setDomainMembers(res?.data)
      } else {
        message.error(res.msg || '获取失败')
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    if (domainCode) {
      getDomainMembers()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div>
      <Space direction="vertical">
        <div>
          <h3>管理员</h3>
          {domainMembers.map((item) => {
            if (item.permissionLevel === 'admin') {
              return <Tag key={item.userId}>{item.userId}</Tag>
            }
          })}
        </div>
        <Divider />
        <div>
          <h3>维护者（开发者）</h3>
          {domainMembers.map((item) => {
            if (item.permissionLevel !== 'admin') {
              return <Tag key={item.userId}>{item.userId}</Tag>
            }
          })}
        </div>
        <Divider />
        <div>
          <h3>添加权限</h3>
          <Form form={form} onFinish={onFinish} style={{ width: 300 }}>
            <Form.Item
              label="用户权限类型"
              name="permissionLevel"
              rules={[{ required: true, message: '不能为空' }]}
            >
              <Select>
                <Select.Option value="admin">管理员</Select.Option>
                <Select.Option value="maintainer">维护者</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              label="用户"
              name="userId"
              rules={[
                { required: true, message: '请填写英文用户名' },
                { pattern: /^\S+$/, message: '用户名不能为空或仅包含空格' },
              ]}
              getValueFromEvent={(e): string => e.target.value.trim()}
            >
              <Input placeholder="请填写英文用户名，仅限一个" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" loading={loading} htmlType="submit">
                添加
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Space>
    </div>
  )
}

export default Permission
