import { addDomainTags, editDomainTags } from '@/services/backend/tags'
import { SystemAddLine, SystemEditLine } from '@m-ui/icons'
import { Form, Input, InputNumber, Modal, Tag, message } from '@m-ui/react'
import { useState } from 'react'
import { type ITag } from './domain-tag'

const EditableTagGroup = ({
  domainCode,
  l1Tag,
  l2Tags,
  updateCallback,
}: {
  domainCode: string
  l1Tag: string
  l2Tags: ITag[]
  updateCallback: () => void
}): JSX.Element => {
  const [inputVisible, setInputVisible] = useState<boolean>(false)
  const [editingId, setEditingId] = useState<number>()
  const [form] = Form.useForm()

  const showInput = (): void => {
    setEditingId(undefined)
    setInputVisible(true)
    form.resetFields()
  }
  const handleInputConfirm = (): void => {
    form
      .validateFields()
      .then(async (values) => {
        console.log('Received values of form: ', values)
        if (editingId) {
          // 编辑
          const res = await editDomainTags({
            id: editingId,
            domainCode,
            l1Tag,
            ...values,
          })
          if (res.result === 1) {
            message.info('编辑成功')
            updateCallback && updateCallback()
          } else {
            message.error(res.msg || '编辑出错')
          }
        } else {
          const res = await addDomainTags({
            domainCode,
            l1Tag,
            ...values,
          })
          if (res.result === 1) {
            message.info('添加成功')
            updateCallback && updateCallback()
          } else {
            message.error(res.msg || '添加出错')
          }
        }
        form.resetFields()
        setInputVisible(false)
      })
      .catch((info) => {
        console.log('Validate Failed:', info)
      })
  }
  const handleInputCancel = (): void => {
    setInputVisible(false)
    form.resetFields()
  }
  /**
   * 编辑tag
   */
  const editTag = (tagInfo: ITag): void => {
    setEditingId(tagInfo.id)
    form.setFieldsValue(tagInfo)
    setInputVisible(true)
  }
  return (
    <>
      {l2Tags.map((tagInfo) => {
        return (
          <Tag
            onClick={(): void => editTag(tagInfo)}
            color="blue"
            style={{ cursor: 'pointer' }}
          >
            {tagInfo.l2Tag} <SystemEditLine style={{ marginLeft: '4px' }} />
          </Tag>
        )
      })}
      <Modal
        title={editingId ? '编辑标签' : '新建标签'}
        okText="确定"
        cancelText="取消"
        visible={inputVisible}
        onOk={handleInputConfirm}
        onCancel={handleInputCancel}
        maskClosable={false}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="二级标签名称"
            name="l2Tag"
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="预估全码方式时间（单位：人日）" name="estimateTime">
            <InputNumber />
          </Form.Item>
        </Form>
      </Modal>

      <Tag onClick={showInput} style={{ cursor: 'pointer' }}>
        <SystemAddLine /> 新标签
      </Tag>
    </>
  )
}

export default EditableTagGroup
