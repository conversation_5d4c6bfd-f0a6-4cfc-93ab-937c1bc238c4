import MonitorConfig from '@/components/monitor-config'
import { Card, Tabs } from '@m-ui/react'
import React from 'react' // 导入 React
import DataSourceConfigTable from '../data-source-config'
import DomainGrayConfig from '../grey/domain-grey-config'
import DomainTag from './domain-tag'
import Permission from './permission'
const { TabPane } = Tabs
// 创建一个 function 组件，它接收一个 message 属性
const MyComponent: React.FC = () => {
  return (
    <Card>
      <Tabs tabPosition={'left'}>
        <TabPane tab="数据源配置" key="1">
          <DataSourceConfigTable />
        </TabPane>
        <TabPane tab="灰度配置" key="2">
          <DomainGrayConfig />
        </TabPane>
        <TabPane tab="标签配置" key="3">
          <DomainTag />
        </TabPane>
        <TabPane tab="人员权限" key="4">
          <Permission />
        </TabPane>
        <TabPane tab="监控查询条件" key="5">
          <MonitorConfig />
        </TabPane>
      </Tabs>
    </Card>
  )
}

export default MyComponent // 导出组件
