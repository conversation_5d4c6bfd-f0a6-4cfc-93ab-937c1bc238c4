import { history } from '@kmi/react'
import { Button, Result } from '@m-ui/react'
import { useCallback, type FC } from 'react'

/**
 * 未找到（页面），404
 */
export const NotFound: FC = () => {
  const handleBtnClick = useCallback((): void => history.push('/'), [])
  return (
    <Result
      status="404"
      title="404"
      subTitle="抱歉，您访问的页面不存在"
      extra={
        <Button type="primary" onClick={handleBtnClick}>
          回到首页
        </Button>
      }
    />
  )
}

export default NotFound
