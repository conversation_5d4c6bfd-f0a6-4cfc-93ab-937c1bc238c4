import { type ModuleTemplate } from '@/services/backend/models'
import { getModuleTemplateList } from '@/services/backend/module-template'
import { createDesignerUrl } from '@/utils'
import { ModuleType } from '@ad/canal-shared'
import { useSearchParams } from '@kmi/react'
import { SystemArrowMediumRightLine } from '@m-ui/icons'
import {
  Alert,
  Button,
  Card,
  Col,
  Empty,
  Pagination,
  Row,
  Space,
  Spin,
} from '@m-ui/react'
import { useEffect, useState } from 'react'

const TemplateList = ({
  showChooseAction,
  onChoose,
}: {
  showChooseAction?: boolean
  onChoose?: (value: ModuleTemplate) => void
}): JSX.Element => {
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode')
  const [pageNum, setPageNum] = useState(1)
  const [total, setTotal] = useState(0)
  const [list, setList] = useState<ModuleTemplate[]>([])
  const [spinning, setSpinning] = useState(false)

  const fetchListData = async (): Promise<void> => {
    if (!domainCode) return
    try {
      setSpinning(true)
      const res = await getModuleTemplateList({
        domainCode: domainCode,
        pageSize: 8,
        pageNum,
      })
      if (res?.result === 1) {
        setList(res.data.list)
        setTotal(res.data.pageInfo.total)
      }
    } catch (error) {
      console.log(error)
    }
    setSpinning(false)
  }
  const onChange = (page: number): void => {
    setPageNum(page)
  }
  const goDetail = (item: ModuleTemplate): void => {
    if (!domainCode) return
    window.open(
      createDesignerUrl(ModuleType.MODULE, {
        moduleId: `${item.moduleId}`,
        moduleVersion: `${item.version}`,
        domainCode,
      }),
      '_blank',
    )
  }
  const chooseTemplate = (template: ModuleTemplate): void => {
    onChoose?.(template)
  }
  useEffect(() => {
    fetchListData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNum])
  return (
    <Spin tip="加载中..." spinning={spinning}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Alert message="模版可以在新建模块时用于快捷创建" type="info" />
        <Card>
          {!list.length && <Empty />}
          <div className="site-card-wrapper">
            <Row gutter={16}>
              {list.map?.((item) => {
                const actions = [
                  <Button
                    type="link"
                    onClick={(): void => {
                      goDetail(item)
                    }}
                  >
                    查看配置 <SystemArrowMediumRightLine />{' '}
                  </Button>,
                ]
                if (showChooseAction) {
                  actions.push(
                    <Button
                      type="link"
                      onClick={(): void => {
                        chooseTemplate(item)
                      }}
                    >
                      选择此模版
                    </Button>,
                  )
                }
                return (
                  <Col span={6} style={{ padding: '8px' }}>
                    <Card
                      hoverable
                      cover={
                        <div
                          style={{
                            width: '100%',
                            paddingTop: '60%',
                            backgroundImage: 'url(' + item.demoImage + ')',
                            backgroundSize: 'contain',
                            backgroundRepeat: 'no-repeat',
                            backgroundPosition: '50%',
                          }}
                        ></div>
                      }
                      actions={actions}
                    >
                      <div style={{ height: '50px' }}>
                        <h4>{item.name}</h4>
                        <div>{item.desc} </div>
                      </div>
                    </Card>
                  </Col>
                )
              })}
            </Row>
          </div>
          <Pagination
            simple
            current={pageNum}
            total={total}
            pageSize={8}
            onChange={onChange}
            style={{ float: 'right', paddingTop: '8px' }}
          />
        </Card>
      </Space>
    </Spin>
  )
}
export default TemplateList
