import CustomUpload from '@/components/custom-upload'
import {
  createModuleTemplateUsingPost,
  overwriteTemplateUsingPost,
} from '@/services/backend/module-template'
import { Form, Input, Modal, Radio, message } from '@m-ui/react'
import { useState } from 'react'
import AddTemplateFormItem from './add-template-form-item'

interface FormValues {
  name: string
  desc?: string
  demoImage: string
  templateId: string
}

const AddTemplateModule = ({
  visible,
  params,
  onCancel,
}: {
  visible: boolean
  onCancel: () => void
  params: {
    domainCode: string
    moduleId: string
    version: number
  }
}): JSX.Element => {
  const [form] = Form.useForm()
  const [type, setType] = useState(0)
  const addTemplate = async (values: FormValues): Promise<void> => {
    if (!params?.domainCode || !params?.moduleId || !params?.version) {
      message.error('参数缺失')
      return
    }
    try {
      const res = await createModuleTemplateUsingPost({
        ...params,
        ...values,
      })
      if (res?.result === 1) {
        if (res.data?.id) {
          message.success('添加模版成功')
        } else {
          message.error(res.msg || '添加模版失败')
        }
        form.resetFields()
        setType(0)
      }
      onCancel?.()
    } catch (error) {
      console.log(error)
    }
  }
  const overwriteTemplate = async (values: FormValues): Promise<void> => {
    if (!params?.domainCode || !params?.moduleId || !params?.version) {
      message.error('参数缺失')
      return
    }
    try {
      const res = await overwriteTemplateUsingPost({
        ...params,
        ...values,
      })
      if (res?.result === 1) {
        message.success('覆盖模版成功')
      } else {
        message.error(res.msg || '覆盖模版失败')
      }
      form.resetFields()

      setType(0)
      onCancel?.()
    } catch (error) {
      console.log(error)
    }
  }
  const onTypeChange = (e): void => {
    setType(e.target.value)
  }
  return (
    <Modal
      title="加入模版"
      visible={visible}
      onCancel={(): void => {
        form.resetFields()
        setType(0)
        onCancel?.()
      }}
      onOk={(): void => {
        form
          .validateFields()
          .then((values) => {
            if (type === 0) {
              addTemplate(values)
            } else {
              overwriteTemplate(values)
            }
          })
          .catch((info) => {
            console.log('Validate Failed:', info)
          })
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="form_in_modal"
        initialValues={{
          type: 0,
        }}
      >
        <Form.Item name="type" label="选择方式">
          <Radio.Group onChange={onTypeChange}>
            <Radio value={0}>新增模版</Radio>
            <Radio value={1}>覆盖现有模版</Radio>
          </Radio.Group>
        </Form.Item>
        {type === 1 && (
          <Form.Item
            label="模版"
            name="templateId"
            rules={[{ required: true, message: '请选择一个现有模版' }]}
          >
            <AddTemplateFormItem label={'选择覆盖的模版'} />
          </Form.Item>
        )}
        {type === 0 && (
          <>
            <Form.Item
              name="name"
              label="模版名称"
              rules={[
                {
                  required: true,
                  message: '请输入模版名称',
                },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item name="desc" label="模版描述">
              <Input
                type="textarea"
                placeholder="请填写描述帮助开发者选择模版"
              />
            </Form.Item>
            <Form.Item
              label="模版示例图"
              name="demoImage"
              rules={[{ required: true, message: '请上传模版示例图' }]}
            >
              <CustomUpload />
            </Form.Item>
          </>
        )}
      </Form>
    </Modal>
  )
}

export default AddTemplateModule
