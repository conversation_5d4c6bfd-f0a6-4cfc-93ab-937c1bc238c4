import { type ModuleTemplate } from '@/services/backend/models'
import { Button, Modal } from '@m-ui/react'
import { useState } from 'react'
import TemplateList from './index'

interface AddTemplateProps {
  value?: number
  onChange?: (value: number) => void
  label?: string
}

const AddTemplateFormItem: React.FC<AddTemplateProps> = ({
  value = {},
  onChange,
  label,
}) => {
  const [visible, setVisible] = useState(false)
  const [chooseItem, setChooseItem] = useState<ModuleTemplate>()
  const showModal = (): void => {
    setVisible(true)
  }
  const hideModal = (): void => {
    setVisible(false)
  }
  const triggerChange = (changedValue: ModuleTemplate): void => {
    setChooseItem(changedValue)
    onChange?.(changedValue.id)
    hideModal()
  }
  return (
    <>
      <Button onClick={showModal} type="primary">
        {!chooseItem && (label || '选择模版进行创建')}
        {!!chooseItem && `已选择【${chooseItem.name}】——id【${value}】`}
      </Button>
      <Modal
        title="选择模版"
        visible={visible}
        onCancel={hideModal}
        width={1200}
        footer={null}
      >
        <TemplateList
          showChooseAction={true}
          onChoose={triggerChange}
        ></TemplateList>
      </Modal>
    </>
  )
}

export default AddTemplateFormItem
