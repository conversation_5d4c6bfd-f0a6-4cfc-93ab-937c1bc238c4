import { type ModuleDetail } from '@/services/backend/models'
import { search } from '@/services/backend/module'
import { useSearchParams } from '@kmi/react'
import { Button, Card, Input, List, Row, message } from '@m-ui/react'
import { useCallback, useEffect, useState } from 'react'

const { Search } = Input

type CombinedModuleDetail = ModuleDetail & {
  changeId: string
  version: string
}

const SearchComponent = (): JSX.Element => {
  const [results, setResults] = useState<CombinedModuleDetail[]>([])
  const [searchParams] = useSearchParams()
  const [searchValue, setSearchValue] = useState(
    (searchParams.get('searchText') as string) || '',
  )

  // 使用 useCallback 包裹 handleSearch 函数，避免在每次渲染时重新创建
  const handleSearch = useCallback(async (value: string): Promise<void> => {
    if (!value) {
      message.error('搜索内容不能为空')
      return
    }
    const res = await search({ searchText: value })
    if (res.result === 1) {
      setResults(res.data)
    }
  }, [])

  useEffect(() => {
    // 只在组件初始化时执行一次搜索，如果URL中有搜索参数
    const initialSearchText = searchParams.get('searchText')
    if (initialSearchText) {
      handleSearch(initialSearchText)
    }
  }, [handleSearch, searchParams])

  return (
    <Card>
      <div style={{ textAlign: 'center', paddingTop: 100 }}>
        <Search
          placeholder="输入模块ID来查找它"
          onSearch={(value: string): void => {
            handleSearch(value)
          }}
          onChange={(e): void => {
            setSearchValue(e.target.value)
          }}
          size="large"
          value={searchValue}
          enterButton
          style={{ width: 500 }}
        />
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <List
          style={{
            marginTop: 50,
            width: 800,
          }}
          bordered={false}
          dataSource={results}
          renderItem={(item): React.ReactNode => (
            <List.Item
              actions={[
                <Button
                  key="list-jump"
                  target="blank"
                  href={`/module/list?domainCode=${item.businessDomainCode}&&searchModuleId=${item.id}`}
                >
                  去线上模块列表搜索
                </Button>,
                <Button
                  key="change"
                  type="primary"
                  target="blank"
                  href={`/change/detail?changeId=${item.changeId}&domainCode=${item.businessDomainCode}`}
                >
                  去最新版本关联的变更
                </Button>,
              ]}
            >
              <List.Item.Meta
                title={item.id + '-【名称：' + item.name + '】'}
                description={
                  <>
                    <Row>业务域:{item.businessDomainCode}</Row>
                    <Row>最新版本：{item.version}</Row>
                  </>
                }
              />
            </List.Item>
          )}
        />
      </div>
    </Card>
  )
}

export default SearchComponent
