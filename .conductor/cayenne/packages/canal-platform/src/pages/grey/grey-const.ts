export const GREY_TYPE_VALUE_TYPE: Record<string, IGreyConfig> = {
  white: {
    valueLabel: '白名单值',
    selectText: '白名单',
    extra: '多人请用英文逗号隔开',
    props: {
      placeholder: '请输入白名单值,多人用英文,逗号隔开',
    },
  },
  // lane: {
  //   valueLabel: '泳道值',
  //   selectText: '泳道',
  //   props: {
  //     placeholder: '请输入泳道值',
  //   },
  // },
  percentage: {
    valueLabel: '百分比值',
    type: 'InputNumber',
    selectText: '百分比',
    extra: '请输入1-99的整数数字',
    props: {
      min: 1,
      max: 99,
    },
  },
  host: {
    valueLabel: '宿主产品版本号',
    selectText: '跟随宿主',
    extra: '请确保宿主工程使用kfx部署，并且在window变量下注入kfxEnv变量',
    props: {
      placeholder: '请输入宿主工程的灰度产品版本号',
    },
  },
}

export enum GREY_TYPE {
  white = 'white',
  lane = 'lane',
  percentage = 'percentage',
  host = 'host',
}
export interface IGreyConfig {
  valueLabel: string
  selectText: string
  component?: string
  placeholder?: string
  type?: string
  extra?: string
  props?: {
    min?: number
    max?: number
    placeholder?: string
  }
}

export interface IGreySubmitConfig {
  greyType?: 'white' | 'lane' | 'percentage' | 'host'
  greyValue?: string | number
  greyKey?: string
}

export interface IGreyDeploy extends IGreySubmitConfig {
  id: number
  publishId: number
  status: number
  greyType: 'white' | 'lane' | 'percentage' | 'host'
}

export enum GreyDeployStatus {
  INIT = 0,
  EFFECTIVE = 1,
  SUCCESS = 2,
  INVALID = 3,
}

export interface IUpdateGreyDeploy {
  id: number
  publishId: number
  greyType: 'white' | 'lane' | 'percentage' | 'host'
  greyValue: string | number
  greyKey: string
  status?: number
  createUser?: string
  updateUser?: string
  createTime?: number
  updateTime?: number
}
