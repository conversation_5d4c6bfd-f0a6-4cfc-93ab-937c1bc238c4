// 初始化这个tsx组件  引用antd的table组件   props是类型为number的 greyId
import { dateFormat } from '@/pages/change/const'
import { getGreyHistory } from '@/services/backend/publish'
import { Button, Modal, Table } from '@m-ui/react'
import { useEffect, useState } from 'react'
import { GREY_TYPE_VALUE_TYPE, type IUpdateGreyDeploy } from './grey-const'
const GreyHistoryList = (props: { greyId: number }): JSX.Element => {
  const [dataSource, setDataSource] = useState<IUpdateGreyDeploy[]>([])
  const [loading, setLoading] = useState(false)
  const [visible, setVisible] = useState(false)
  useEffect(() => {
    if (!visible) return
    setLoading(true)
    const fetchData = async (): Promise<void> => {
      await getGreyHistory({
        greyId: props.greyId,
      }).then((res) => {
        if (res.result === 1) {
          setDataSource(res.data)
        }
        setLoading(false)
      })
    }
    fetchData()
  }, [props.greyId, visible])
  const columns = [
    {
      title: '操作内容',
      key: 'content',
      render: (_, record: IUpdateGreyDeploy): JSX.Element => {
        return (
          <>
            <div>
              灰度类型：{GREY_TYPE_VALUE_TYPE[record.greyType]?.selectText}
            </div>
            <div>
              {GREY_TYPE_VALUE_TYPE[record.greyType]?.valueLabel}：
              {record.greyValue}
            </div>
            {record.greyType === 'white' && (
              <div>cookie键值：{record.greyKey}</div>
            )}
          </>
        )
      },
    },
    {
      title: '操作人',
      dataIndex: 'createUser',
      key: 'createUser',
    },
    {
      title: '操作时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      render: (createTime: number): string => dateFormat(createTime),
    },
  ]
  return (
    <>
      <Button type="link" onClick={(): void => setVisible(true)} size={'small'}>
        灰度历史记录
      </Button>
      <Modal
        visible={visible}
        onCancel={(): void => setVisible(false)}
        footer={<Button onClick={(): void => setVisible(false)}>关闭</Button>}
        title="灰度历史记录"
        width={1000}
      >
        <Table
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
          bordered
          scroll={{ y: 300 }}
        />
      </Modal>
    </>
  )
}
export default GreyHistoryList
