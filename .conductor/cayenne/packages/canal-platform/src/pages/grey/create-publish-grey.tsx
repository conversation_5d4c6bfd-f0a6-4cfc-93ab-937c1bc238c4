import HelpTip from '@/components/help-tip'
import { getSimpleDetailBusinessDomain } from '@/services/backend/business_domain'
import {
  Alert,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Switch,
} from '@m-ui/react'
import type React from 'react'
import { useEffect, useState } from 'react'
import {
  GREY_TYPE,
  GREY_TYPE_VALUE_TYPE,
  type IGreyConfig,
  type IGreyDeploy,
  type IGreySubmitConfig,
} from './grey-const'

interface CreatePublishGreyModalProps {
  visible: boolean
  onCancel: () => void
  onOk: (greyConfig: IGreySubmitConfig) => void
  domainCode: string
  greyEditInfo?: IGreyDeploy
  tip: string
}
interface GreyForm {
  isGray: boolean
  isFollowHost: boolean
  greyType: string
  greyValue: string
  greyKey: string
}
const { Option } = Select
const types = Object.keys(GREY_TYPE_VALUE_TYPE)

const CreatePublishGrey: React.FC<CreatePublishGreyModalProps> = ({
  visible,
  onCancel,
  onOk,
  domainCode,
  greyEditInfo,
  tip,
}) => {
  const [form] = Form.useForm()
  const [whiteKey, setWhiteKey] = useState('')
  const [isGrey, setIsGrey] = useState(!!greyEditInfo)
  const handleSubmit = (): void => {
    form.validateFields().then((values) => {
      const greyConfig: IGreySubmitConfig = {}
      if (values.isGray || greyEditInfo) {
        greyConfig.greyType = values.greyType
        greyConfig.greyValue = values.greyValue
        greyConfig.greyKey = values.greyKey || ''
      }

      onOk?.(greyConfig)
      form.resetFields()
      setIsGrey(!!greyEditInfo)
    })
  }
  useEffect(() => {
    if (greyEditInfo) {
      const processedInitialData = {
        isGrey: true,
        greyType: greyEditInfo?.greyType,
        greyValue: greyEditInfo?.greyValue || '',
        greyKey: greyEditInfo?.greyKey || '',
      }
      setIsGrey(true)
      form.setFieldsValue(processedInitialData)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [greyEditInfo])
  const handleCancel = (): void => {
    onCancel?.()
    form.resetFields()
    setIsGrey(!!greyEditInfo)
  }

  useEffect(() => {
    if (visible) {
      getSimpleDetailBusinessDomain({
        code: domainCode,
      }).then((res) => {
        if (res.result === 1) {
          const white = JSON.parse(res.data?.greyConfig || '{}').white
          setWhiteKey(white)
        }
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible])

  const onValuesChange = (changedValues: GreyForm): void => {
    if (changedValues.greyType === GREY_TYPE.white && whiteKey) {
      form.setFieldsValue({
        greyKey: whiteKey,
      })
    }
    if (changedValues.greyType) {
      form.setFieldsValue({
        greyValue: '',
      })
    }
    changedValues.isGray !== undefined && setIsGrey(!!changedValues.isGray)
  }

  const renderDynamicComponent = (config: IGreyConfig): React.ReactNode => {
    const DynamicComponent =
      config?.type === 'InputNumber' ? InputNumber : Input
    return <DynamicComponent {...(config?.props || {})} />
  }

  return (
    <Modal
      title={
        <>
          {greyEditInfo ? '确认信息并部署' : '创建发布单'}
          <HelpTip configKey={'grey'} />
        </>
      }
      visible={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      okText="确定"
      cancelText="取消"
      maskClosable={false}
      width={800}
      destroyOnClose={true}
    >
      {!!tip && (
        <Alert message={<div style={{ color: '#F93920' }}>{tip}</div>} banner />
      )}
      <Form
        form={form}
        onFinish={handleSubmit}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        onValuesChange={onValuesChange}
      >
        {!greyEditInfo && (
          <Form.Item name="isGray" label="是否灰度">
            <Switch />
          </Form.Item>
        )}
        {isGrey && (
          <>
            <Form.Item
              name="greyType"
              label="灰度策略类型"
              rules={[{ required: true, message: '请选择灰度类型' }]}
            >
              <Select style={{ width: 120 }}>
                {types.map((item) => (
                  <Option value={item} key={item}>
                    {GREY_TYPE_VALUE_TYPE[item]?.selectText}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              shouldUpdate={(prevValues, currentValues): boolean =>
                prevValues.greyType !== currentValues.greyType
              }
              noStyle
            >
              {({ getFieldValue }): React.ReactNode => {
                return getFieldValue('greyType') === GREY_TYPE.white ? (
                  <Form.Item
                    name="greyKey"
                    label="cookie键"
                    rules={[{ required: true }]}
                  >
                    <Input
                      defaultValue={whiteKey}
                      placeholder="请输入白名单标识"
                    />
                  </Form.Item>
                ) : null
              }}
            </Form.Item>
            <Form.Item
              shouldUpdate={(prevValues, currentValues): boolean =>
                prevValues.greyType !== currentValues.greyType
              }
              noStyle
            >
              {({ getFieldValue }): React.ReactNode => {
                return getFieldValue('greyType') ? (
                  <Form.Item
                    name="greyValue"
                    label={
                      GREY_TYPE_VALUE_TYPE[form.getFieldValue('greyType')]
                        ?.valueLabel
                    }
                    rules={[{ required: true, message: '请填写灰度值' }]}
                    extra={
                      GREY_TYPE_VALUE_TYPE[form.getFieldValue('greyType')]
                        ?.extra || ''
                    }
                  >
                    {renderDynamicComponent(
                      GREY_TYPE_VALUE_TYPE[form.getFieldValue('greyType')],
                    )}
                  </Form.Item>
                ) : null
              }}
            </Form.Item>
          </>
        )}
      </Form>
    </Modal>
  )
}
export default CreatePublishGrey
