import { Card, Descriptions, Tag, Tooltip } from '@m-ui/react'
import { useEffect, useState } from 'react'
import {
  GREY_TYPE_VALUE_TYPE,
  GreyDeployStatus,
  type IGreyDeploy,
} from './grey-const'

interface GreyInfoProps {
  grey: IGreyDeploy
  domainCode: string
  publishId: number
  isEdit?: boolean
  rightAction?: React.ReactNode
}

const EditToolTip: { [key: number]: string } = {
  [GreyDeployStatus.INIT]: '灰度策略未生效，请先点击进行部署',
}
const GreyDeployStatusTxt: { [key: number]: string } = {
  [GreyDeployStatus.INIT]: '未生效',
  [GreyDeployStatus.EFFECTIVE]: '生效中',
  [GreyDeployStatus.SUCCESS]: '灰度单成功完结',
  [GreyDeployStatus.INVALID]: '灰度单失败',
}
const GreyInfo: React.FC<GreyInfoProps> = ({
  grey,
  isEdit = true,
  rightAction,
}) => {
  const [greyConfig, setGreyConfig] = useState(grey)
  useEffect(() => {
    setGreyConfig(grey)
  }, [grey])

  return (
    <Card
      type="inner"
      title={
        <>
          当前灰度策略
          {isEdit && (
            <Tag color="blue" style={{ marginLeft: 8 }}>
              {GreyDeployStatusTxt[greyConfig.status]}
            </Tag>
          )}
        </>
      }
      style={{ width: 600, margin: '16px 0' }}
      extra={
        isEdit &&
        !!rightAction && (
          <Tooltip
            title={EditToolTip?.[greyConfig.status] || '修改后可重新部署灰度'}
          >
            {rightAction}
          </Tooltip>
        )
      }
    >
      <Descriptions title="" column={1}>
        <Descriptions.Item label="灰度类型">
          {GREY_TYPE_VALUE_TYPE[greyConfig.greyType].selectText}
        </Descriptions.Item>
        {!!greyConfig.greyKey && (
          <Descriptions.Item label={'cookie键'}>
            {greyConfig.greyKey}
          </Descriptions.Item>
        )}
        <Descriptions.Item
          label={GREY_TYPE_VALUE_TYPE[greyConfig.greyType].valueLabel}
        >
          {greyConfig.greyValue}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  )
}
export default GreyInfo
