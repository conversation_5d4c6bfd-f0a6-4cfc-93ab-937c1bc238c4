import {
  editGreyConfig,
  getSimpleDetailBusinessDomain,
} from '@/services/backend/business_domain'
import { useSearchParams } from '@kmi/react'
import { SystemEditLine } from '@m-ui/icons'
import { Button, Card, Col, Input, Row, message } from '@m-ui/react'
import { useEffect, useState } from 'react'

const DomainGrayConfig: React.FC = () => {
  const [editMode, setEditMode] = useState(false)
  const [value, setValue] = useState('')
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode') || ''
  const editDomain = async (): Promise<void> => {
    setEditMode(false)

    const res = await editGreyConfig({
      code: domainCode,
      greyConfig: JSON.stringify({
        white: value,
      }),
    })
    if (res.result === 1) {
      message.success('修改成功')
      return
    }
    message.success('修改失败')
  }
  useEffect(() => {
    getSimpleDetailBusinessDomain({
      code: domainCode,
    }).then((res) => {
      if (res.result === 1) {
        const white = JSON.parse(res.data?.greyConfig || '{}').white
        setValue(white)
      }
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return (
    <Card bordered={false}>
      <Row align={'middle'} gutter={20}>
        <Col>白名单标识（cookie键）:</Col>
        <Col>
          {editMode ? (
            <>
              <Input
                placeholder="请输入白名单标识,目前仅支持唯一的标识"
                style={{ width: 300 }}
                value={value}
                onChange={(e): void => setValue(e.target.value)}
              />
              <Button type="primary" onClick={editDomain}>
                确定
              </Button>
            </>
          ) : (
            <>
              <span style={{ fontWeight: 'bold', padding: '0 8px' }}>
                {value}
              </span>
              <SystemEditLine
                style={{
                  color: '#0075FF',
                  fontSize: 18,
                  cursor: 'pointer',
                }}
                onClick={(): void => {
                  setEditMode(true)
                }}
              />
            </>
          )}
        </Col>
      </Row>
    </Card>
  )
}
export default DomainGrayConfig
