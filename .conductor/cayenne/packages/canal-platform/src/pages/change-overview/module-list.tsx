import ChangModuleList from '@/pages/change/change-module-list'
import type { ModuleDetail } from '@/services/backend/models'
import { checkModulesUsingPost } from '@/services/backend/module'
import { ModuleType } from '@ad/canal-shared'
import { request } from '@kmi/react'
import {
  CreateResource,
  UpdatePage,
  useOrderInfo,
  useSDKContext,
} from '@ks-lingzhu/common-sdk'
import { Button, Card, Col, Form, Radio, Row, Spin, message } from '@m-ui/react'
import { Drawer } from '@m-ui/react/lib'
import { useEffect, useMemo, useState, type FC } from 'react'
import { baseStyle } from '.'
import CheckErrorModal from '../../components/check-error-modal'
import {
  ChangeBatchEditor,
  type ChangeModuleSummary,
} from '../change/change-batch-editor'
import { STATUS_KEY_MAP } from '../change/const'
import DeployOnlineVersion from '../change/deploy-online-version'
import type { ChangeModel } from '../change/model'
import AddTemplateFormItem from '../template/add-template-form-item'

interface IProps {
  changeInfoLoading: boolean
  changeInfo: ChangeModel
  changeId: string
  domainCode: string
  refreshChangeInfo: () => void
}

const ModuleList: FC<IProps> = ({
  changeId,
  changeInfo,
  changeInfoLoading,
  domainCode,
  refreshChangeInfo,
}) => {
  const [changModuleListSpinning, setChangModuleListSpinning] = useState(false)
  const [changeModuleList, setChangeModuleList] = useState<ModuleDetail[]>([])
  const [originalModuleList, setOriginalModuleList] = useState<any[]>([])

  const [checkModalVisible, setCheckModalVisible] = useState(false)
  const [checkErrorInfo, setCheckErrorInfo] = useState<
    Array<{
      key: string
      content: Array<{
        moduleId: string
        errorTips: Array<{
          text: string
          content: string[]
        }>
      }>
    }>
  >([])
  const [checkingModules, setCheckingModules] = useState(false)

  const { eventBus } = useSDKContext()
  const { changeOrderDetail, refresh: refreshOrderInfo } = useOrderInfo()

  // 抽屉相关
  const [visible, setVisible] = useState(false)
  const showDrawer = () => {
    setVisible(true)
  }
  const onClose = () => {
    setVisible(false)
  }

  /**
   * 是否可以操作
   */
  const banOperate = useMemo(() => {
    if (!changeInfo) return false
    const status = STATUS_KEY_MAP[changeInfo.status as number]
    if (status) {
      if (['publishing', 'published', 'closed'].indexOf(status.key) > -1) {
        return true
      }
    }
    return false
  }, [changeInfo])

  const getModuleList = async () => {
    setChangModuleListSpinning(true)
    try {
      const data = await request(
        `/gateway/m/lingzhu/change/query/resource?changeOrderId=${changeId}&appKey=dyh_${domainCode}&tenant=dyh&from=dyh`,
      )
      if (data.result === 1) {
        setOriginalModuleList(data.data?.resources || [])
        setChangeModuleList(
          (data.data?.resources || [])
            .map(
              (item: any) => item?.tenantResourceInfoExt?.canalResourceInfoExt,
            )
            .filter((item: any) => !!item),
        )
      }
    } catch (e) {
      message.error(e?.msg || '列表加载失败')
    } finally {
      setChangModuleListSpinning(false)
    }
  }

  const handleDeleteResource = async (_: string, index: number) => {
    try {
      const originalData = originalModuleList?.[index]
      const data = await request(`/gateway/m/lingzhu/change/cancel/resource`, {
        method: 'post',
        data: {
          cancelResourceCode: originalData.resourceCode,
          cancelResourceId: originalData.resourceId,
          changeId,
          appKey: `dyh_${domainCode}`,
          tenant: 'dyh',
        },
      })
      if (data.result === 1) {
        message.success('资源删除成功')
        refresh()
      } else {
        message.error(data.baseResponseInfo?.respCodeDesc || '资源删除失败')
      }
    } catch (e) {
      message.error(e?.msg || '资源删除失败')
    }
  }

  // 检查模块内容合法性
  const handleCheckModules = async (): Promise<void> => {
    if (!changeModuleList || changeModuleList.length === 0) {
      message.warning('当前变更中没有模块')
      return
    }

    try {
      setCheckingModules(true)
      const moduleVersions = changeModuleList.map((module) => ({
        moduleId: module.id,
        version: module.version,
      }))

      const result = await checkModulesUsingPost({
        moduleVersions,
      })

      if (result?.result === 1) {
        if (result.data?.checkError && result.data.checkError.length > 0) {
          setCheckErrorInfo(result.data.checkError)
          setCheckModalVisible(true)
        } else {
          message.success('所有模块配置检查通过')
        }
      } else {
        message.error(result?.msg || '检查失败')
      }
    } catch (error: any) {
      console.error('检查模块配置出错:', error)
      message.error(error?.msg || '检查模块配置失败')
    } finally {
      setCheckingModules(false)
    }
  }

  const handleNameChanged = async (name: string, index: number) => {
    try {
      const originalData = originalModuleList?.[index]
      const data = await request('/gateway/m/lingzhu/change/resource', {
        method: 'post',
        data: {
          changeOrderId: changeId,
          appKey: `dyh_${domainCode}`,
          tenant: 'dyh',
          changeType: 2,
          resourceList: [
            {
              ...originalData,
              name,
            },
          ],
        },
      })
      if (data.result === 1) {
        message.success('资源名称修改成功')
        refresh()
      }
    } catch (e) {
      message.error(e?.msg || '资源名称修改失败')
    }
  }

  const refresh = () => {
    // getChangeModuleList()
    getModuleList()
  }

  const handleStageChanged = async () => {
    await refreshChangeInfo()
    refresh()
  }

  const clearCheckErrorInfo = (): void => {
    setCheckErrorInfo([])
    setCheckModalVisible(false)
  }

  useEffect(() => {
    if (!changeId) return
    refresh()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [changeId])

  useEffect(() => {
    // 变更阶段变化时，刷新变更单信息&资源列表
    if (changeOrderDetail?.stage) {
      handleStageChanged()
    }
  }, [changeOrderDetail?.stage])

  useEffect(() => {
    eventBus.on('useResourceTable-refresh', refresh)

    return () => {
      eventBus.off('useResourceTable-refresh', refresh)
    }
  }, [eventBus, refresh])

  return (
    <Card
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span>变更资源明细</span>
          <>
            <Button
              onClick={showDrawer}
              style={{
                display:
                  !banOperate && changeModuleList.length ? 'block' : 'none',
              }}
            >
              批量编辑
            </Button>
            <Drawer
              title="批量编辑"
              visible={visible}
              placement="right"
              onClose={onClose}
              width={640}
            >
              <ChangeBatchEditor
                domainCode={domainCode}
                changeId={changeId}
                changeModules={
                  changeModuleList as unknown as ChangeModuleSummary[]
                }
              />
            </Drawer>
          </>
        </div>
      }
      bordered={false}
      style={{ ...baseStyle, marginBottom: '36px' }}
    >
      <Row justify="end" align="middle" gutter={8} style={{ marginBottom: 16 }}>
        <Col>
          <CreateResource
            editFormItems={[
              () => (
                <>
                  <Form.Item label="模版" name="templateId">
                    <AddTemplateFormItem />
                  </Form.Item>
                  <Form.Item
                    name="type"
                    label="子类型"
                    rules={[{ required: true, message: '请选择子类型' }]}
                    initialValue={ModuleType.MODULE}
                  >
                    <Radio.Group>
                      <Radio value={ModuleType.MODULE}>模块</Radio>
                      <Radio value={ModuleType.PAGE}>页面</Radio>
                      <Radio value={ModuleType.GLOBAL}>全局</Radio>
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item
                    name="containerType"
                    label="容器类型"
                    rules={[{ required: true, message: '请选择容器类型' }]}
                    initialValue={0}
                  >
                    <Radio.Group>
                      <Radio value={0}>WEB</Radio>
                      <Radio value={1}>H5</Radio>
                      {/* <Radio value={2}>KRN</Radio> */}
                    </Radio.Group>
                  </Form.Item>
                </>
              ),
            ]}
          />
        </Col>
        <Col>
          <UpdatePage />
        </Col>
        <Col>
          <DeployOnlineVersion changeId={changeId} domainCode={domainCode} />
        </Col>
        <Col>
          <Button
            type="primary"
            onClick={handleCheckModules}
            loading={checkingModules}
          >
            检查内容合法性
          </Button>
        </Col>
      </Row>
      <Spin
        tip="加载中..."
        spinning={changModuleListSpinning || changeInfoLoading}
      >
        <ChangModuleList
          moduleList={changeModuleList}
          domainCode={changeInfo?.domainCode}
          disableAction={banOperate}
          changeId={changeInfo?.changeId}
          status={STATUS_KEY_MAP[changeInfo?.status as number]?.key}
          onNameChanged={handleNameChanged}
          updateCallback={(): void => {
            refresh()
            // 删除资源后刷新下变更信息
            refreshOrderInfo()
          }}
          customDelete={handleDeleteResource}
        />
      </Spin>

      {/* 检查内容合法性结果弹窗 */}
      <CheckErrorModal
        visible={checkModalVisible}
        onOk={clearCheckErrorInfo}
        onCancel={clearCheckErrorInfo}
        checkErrorInfo={checkErrorInfo}
      />
    </Card>
  )
}

export default ModuleList
