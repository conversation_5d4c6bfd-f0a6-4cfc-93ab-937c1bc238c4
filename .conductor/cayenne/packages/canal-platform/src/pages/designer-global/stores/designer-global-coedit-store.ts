import { CoeditStore } from '@/stores/coedit-store'
import { isEqual } from 'lodash'
import { DESIGNER_GLOBAL_QUERY } from '../constants/browser'
import { globalSchemaStore } from './global-schema-store'

/**
 * 单例：全局设计器协同编辑仓库
 */
export const designerGlobalCoeditStore = new CoeditStore({
  moduleId: DESIGNER_GLOBAL_QUERY.moduleId,
  moduleVersion: Number(DESIGNER_GLOBAL_QUERY.moduleVersion),
  savable(): boolean {
    return globalSchemaStore.savable
  },
  async initSchemaIsDirty(): Promise<boolean> {
    return !isEqual(
      await globalSchemaStore.waitDesignerGlobalSchemaInit,
      await globalSchemaStore.getDesignerGlobalSchema(),
    )
  },
})
