import type { ModuleDetail } from '@/services/backend/models'
import { getModuleDetail } from '@/services/backend/module'
import { saveAndDeployModule } from '@/utils/apis'
import { compileBackModel, compileFrontModel } from '@/utils/babel/combos'
import type { E2EGlobalSchema, Model } from '@ad/e2e-schema'
import { message } from '@m-ui/react'
import { produce, type Draft } from 'immer'
import { cloneDeep } from 'lodash'
import { action, computed, makeObservable, observable, runInAction } from 'mobx'
import { DESIGNER_GLOBAL_QUERY } from '../constants/browser'
import { parseGlobalSchemaFromModuleContent } from '../utils/schema'
import { designerGlobalCoeditStore } from './designer-global-coedit-store'

/**
 * 全局 Schema 仓库
 */
export class GlobalSchemaStore {
  /**
   * 模块详情初始值
   */
  private _moduleDetailInit: ModuleDetail | null = null

  /**
   * 模块详情初始值
   */
  public get moduleDetailInit(): ModuleDetail | null {
    return this._moduleDetailInit
  }

  /**
   * 等待设计器全局 Schema 初始值
   */
  public readonly waitDesignerGlobalSchemaInit: Promise<E2EGlobalSchema>

  /**
   * Schema
   */
  private _schema: E2EGlobalSchema | null = null

  /**
   * Schema
   */
  public get schema(): E2EGlobalSchema | null {
    return this._schema
  }

  /**
   * 已初始化
   */
  public get isInited(): boolean {
    return !!this._schema
  }

  /**
   * 最后保存的 Schema
   */
  private _lastSavedSchema: E2EGlobalSchema | null = null

  /**
   * 可编辑
   */
  public get editable(): boolean {
    return !!this.moduleDetailInit?.canEdit
  }

  /**
   * 可保存
   */
  public get savable(): boolean {
    return this._schema !== this._lastSavedSchema
  }

  public constructor() {
    makeObservable<
      GlobalSchemaStore,
      '_moduleDetailInit' | '_schema' | '_lastSavedSchema' | '_setSchema'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _moduleDetailInit: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _schema: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _lastSavedSchema: observable.ref,
      savable: computed,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _setSchema: action,
    })
    this.waitDesignerGlobalSchemaInit = (async (): Promise<E2EGlobalSchema> => {
      const md = await this._getModuleDetailByQuery()
      const schema = this._getSchemaByModuleContent(md.content)
      document.title = `大运河 - ${md.name}`
      runInAction(() => {
        this._moduleDetailInit = md
        this._setSchema(schema)
        this._lastSavedSchema = schema
      })
      return schema
    })()

    if (process.env.NODE_ENV === 'development') {
      Object.assign(window, {
        globalSchemaStore: this,
      })
    }
  }

  /**
   * 通过 URL Query 获取模块详情
   */
  private async _getModuleDetailByQuery(): Promise<ModuleDetail> {
    const { data } = await getModuleDetail({
      id: DESIGNER_GLOBAL_QUERY.moduleId,
      version: DESIGNER_GLOBAL_QUERY.moduleVersion,
    })
    return data
  }

  /**
   * 通过模块内容获取 Schema
   * @param moduleContent 模块内容
   */
  private _getSchemaByModuleContent(moduleContent: string): E2EGlobalSchema {
    return parseGlobalSchemaFromModuleContent(moduleContent)
  }

  /**
   * 获取设计器全局 Schema
   */
  public async getDesignerGlobalSchema(): Promise<E2EGlobalSchema> {
    const { content: moduleContent } = await this._getModuleDetailByQuery()
    return this._getSchemaByModuleContent(moduleContent)
  }

  /**
   * 设置 Schema
   * @param schema 设计器全局 Schema
   */
  private _setSchema(schema: E2EGlobalSchema | null): void {
    this._schema = schema
  }

  /**
   * 生产 Schema
   * @param recipe 配方函数
   */
  public produceSchema(recipe: GlobalSchemaStoreRecipe<E2EGlobalSchema>): void {
    const { schema } = this
    if (!schema) return
    this._setSchema(produce(schema, recipe))
  }

  /**
   * 保存 Schema
   */
  public async saveSchema(): Promise<void> {
    const { schema } = this
    if (
      !schema ||
      !designerGlobalCoeditStore.isCurrentRoomEditing ||
      !this.editable ||
      !this.savable
    ) {
      return
    }
    console.log('save schema', schema)
    try {
      runInAction(() => {
        this._lastSavedSchema = schema
      })
      const s = cloneDeep(schema)
      await Promise.all([
        compileFrontModel(s.globalFrontModel),
        compileBackModel(s.globalBackModel),
      ])
      await saveAndDeployModule({
        content: JSON.stringify(s),
        domainCode: DESIGNER_GLOBAL_QUERY.domainCode,
        moduleId: DESIGNER_GLOBAL_QUERY.moduleId,
        moduleVersion: +DESIGNER_GLOBAL_QUERY.moduleVersion,
        workload: 0, // 先不统计全局模块的工作量，后面有需要再加
        autoDeploy: DESIGNER_GLOBAL_QUERY.autoDeploy,
      })
      message.success('保存成功')
    } catch (err) {
      console.log('save err', err)
      message.error(`保存失败：${(err as Error).message}`)
    }
  }

  /**
   * 设置全局前端模型
   * @param model 全局前端模型
   */
  public setGlobalFrontModel(model?: Model): void {
    this.produceSchema((draft) => {
      if (model) {
        draft.globalFrontModel = model
      } else {
        delete draft.globalFrontModel
      }
    })
  }

  /**
   * 设置全局后端模型
   * @param model 全局后端模型
   */
  public setGlobalBackModel(model?: Model): void {
    this.produceSchema((draft) => {
      if (model) {
        draft.globalBackModel = model
      } else {
        delete draft.globalBackModel
      }
    })
  }
}

/**
 * 全局 Schema 仓库配方函数
 */
export type GlobalSchemaStoreRecipe<T> = (draft: Draft<T>) => Draft<T> | void

/**
 * 单例：全局 Schema 仓库
 */
export const globalSchemaStore = new GlobalSchemaStore()
