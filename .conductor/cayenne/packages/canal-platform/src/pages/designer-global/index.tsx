import { Tabs } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import type { FC } from 'react'
import styled from 'styled-components'
import { DesignerGlobalHeader } from './components/designer-global-header'
import { GlobalBackModelEditor } from './components/global-back-model-editor'
import { GlobalFrontModelEditor } from './components/global-front-model-editor'

/**
 * 全局设计器
 */
export const DesignerGlobal: FC = observer(() => {
  return (
    <Container>
      <DesignerGlobalHeader />
      <StyledTabs tabPosition="left">
        <Tabs.TabPane tab="全局前端模型" key="全局前端模型">
          <GlobalFrontModelEditor />
        </Tabs.TabPane>
        <Tabs.TabPane tab="全局后端模型" key="全局后端模型">
          <GlobalBackModelEditor />
        </Tabs.TabPane>
      </StyledTabs>
    </Container>
  )
})

export default DesignerGlobal

const Container = styled.div`
  height: 100vh;
  display: flex;
  flex-direction: column;
`

const StyledTabs = styled(Tabs)`
  flex: auto;

  .ant-tabs-content {
    height: 100%;
  }

  &&& .ant-tabs-tabpane {
    padding: 12px;
  }
`
