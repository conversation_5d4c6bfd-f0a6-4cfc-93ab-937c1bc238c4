import qs from 'query-string'

/**
 * 全局设计器 URL Query
 */
export const DESIGNER_GLOBAL_QUERY = qs.parse(
  location.search,
) as unknown as DesignerGlobalQuery

/**
 * 全局设计器 URL Query
 */
export interface DesignerGlobalQuery {
  /**
   * （业务）域代码
   */
  domainCode: string
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 模块版本
   */
  moduleVersion: string
  /**
   * 自动部署标识
   */
  autoDeploy?: 'beta' | 'prt'
}
