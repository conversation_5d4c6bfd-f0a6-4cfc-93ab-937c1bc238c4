import { JSExprEditor } from '@/components/js-expr-editor'
import { LongLabelNoAnimationFormItem } from '@/components/long-label-form-item'
import {
  BIG_JS_LINK,
  DEFAULT_JS_GLOBAL_BACK_MODEL_STR,
  DEFAULT_TS_GLOBAL_BACK_MODEL_STR,
  GLOBAL_BACK_JS_MODEL_LABEL,
  GLOBAL_BACK_TS_MODEL_LABEL,
} from '@/pages/designer/constants'
import { jsRule, manualValidate, tsRule, useMoneyRule } from '@/utils'
import {
  BACK_EXPRESSION_TYPE_JS_TS_OPTIONS,
  BackExpressionType,
  getBackCtxDts,
} from '@ad/canal-shared'
import { useLatestFn } from '@ad/canal-shared-ui'
import { Button, Form, Select } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useMemo, useState, type ReactNode } from 'react'
import styled from 'styled-components'
import { globalSchemaStore } from '../stores/global-schema-store'

/**
 * 全局后端模型编辑器
 */
export const GlobalBackModelEditor = observer(() => {
  const model = globalSchemaStore.schema?.globalBackModel
  const enabled = !!model
  const exprType = model?.codeTS
    ? BackExpressionType.TYPESCRIPT
    : BackExpressionType.JAVASCRIPT
  const [exprHelp, setExprHelp] = useState<ReactNode>(null)
  const handleSwitchBtnClick = useLatestFn(() => {
    if (enabled) {
      globalSchemaStore.setGlobalBackModel()
    } else {
      globalSchemaStore.setGlobalBackModel({
        code: '',
        codeES: DEFAULT_JS_GLOBAL_BACK_MODEL_STR,
      })
    }
  })
  const handleTypeSelectChange = useLatestFn((et: BackExpressionType) => {
    globalSchemaStore.setGlobalBackModel(
      et === BackExpressionType.TYPESCRIPT
        ? {
            code: '', // 保存时处理
            codeTS: DEFAULT_TS_GLOBAL_BACK_MODEL_STR,
          }
        : {
            code: '', // 保存时处理
            codeES: DEFAULT_JS_GLOBAL_BACK_MODEL_STR,
          },
    )
  })
  const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
  const handleExprChange = useLatestFn((expr = '') => {
    globalSchemaStore.setGlobalBackModel(
      exprType === BackExpressionType.TYPESCRIPT
        ? {
            code: '', // 保存时处理
            codeTS: expr,
          }
        : {
            code: '', // 保存时处理
            codeES: expr,
          },
    )
    ;(async (): Promise<void> => {
      const err = await manualValidate(
        [
          exprType === BackExpressionType.TYPESCRIPT ? tsRule : jsRule,
          moneyRule,
        ],
        expr,
      )
      setExprHelp(err ? err.message : null)
    })()
  })
  const ctxDts = useMemo(() => {
    return getBackCtxDts([])
  }, [])
  return (
    <Container>
      <div className="header">
        <Button type="primary" danger={enabled} onClick={handleSwitchBtnClick}>
          {enabled ? '关闭' : '开启'}
        </Button>
      </div>
      {enabled && (
        <div className="body">
          <Form.Item label="模型类型">
            <Select
              options={BACK_EXPRESSION_TYPE_JS_TS_OPTIONS}
              value={exprType}
              onChange={handleTypeSelectChange}
            />
          </Form.Item>
          <LongLabelNoAnimationFormItem
            label={
              <div>
                模型（
                {exprType === BackExpressionType.TYPESCRIPT
                  ? GLOBAL_BACK_TS_MODEL_LABEL
                  : GLOBAL_BACK_JS_MODEL_LABEL}
                ）
              </div>
            }
            labelCol={{
              span: 24,
            }}
            wrapperCol={{
              span: 24,
            }}
            validateStatus={exprHelp ? 'error' : undefined}
            help={exprHelp}
            extra={moneyRuleHasErrBefore && BIG_JS_LINK}
          >
            <JSExprEditor
              height={`calc(100vh - 248px${
                moneyRuleHasErrBefore ? ' - 24px' : ''
              })`}
              language={
                exprType === BackExpressionType.TYPESCRIPT
                  ? 'typescript'
                  : 'javascript'
              }
              ctxDts={ctxDts}
              value={model.codeTS || model.codeES}
              onChange={handleExprChange}
            />
          </LongLabelNoAnimationFormItem>
        </div>
      )}
    </Container>
  )
})

const Container = styled.div`
  height: 100%;
  overflow: auto;

  .header {
    margin-bottom: 24px;
  }
`
