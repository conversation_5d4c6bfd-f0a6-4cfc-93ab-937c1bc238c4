import { JSExprEditor } from '@/components/js-expr-editor'
import { LongLabelNoAnimationFormItem } from '@/components/long-label-form-item'
import {
  BIG_JS_LINK,
  DEFAULT_JS_GLOBAL_FRONT_MODEL_STR,
  DEFAULT_TS_GLOBAL_FRONT_MODEL_STR,
  GLOBAL_FRONT_JS_MODEL_LABEL,
  GLOBAL_FRONT_TS_MODEL_LABEL,
} from '@/pages/designer/constants'
import { jsRule, manualValidate, tsRule, useMoneyRule } from '@/utils'
import {
  FRONT_EXPRESSION_TYPE_OPTIONS,
  FrontExpressionType,
  getFrontCtxDts,
} from '@ad/canal-shared'
import { useLatestFn } from '@ad/canal-shared-ui'
import { Button, Form, Select } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useMemo, useState, type ReactNode } from 'react'
import styled from 'styled-components'
import { globalSchemaStore } from '../stores/global-schema-store'

/**
 * 全局前端模型编辑器
 */
export const GlobalFrontModelEditor = observer(() => {
  const model = globalSchemaStore.schema?.globalFrontModel
  const enabled = !!model
  const exprType = model?.codeTS
    ? FrontExpressionType.TYPESCRIPT
    : FrontExpressionType.JAVASCRIPT
  const [exprHelp, setExprHelp] = useState<ReactNode>(null)
  const handleSwitchBtnClick = useLatestFn(() => {
    if (enabled) {
      globalSchemaStore.setGlobalFrontModel()
    } else {
      globalSchemaStore.setGlobalFrontModel({
        code: '',
        codeES: DEFAULT_JS_GLOBAL_FRONT_MODEL_STR,
      })
    }
  })
  const handleTypeSelectChange = useLatestFn((et: FrontExpressionType) => {
    globalSchemaStore.setGlobalFrontModel(
      et === FrontExpressionType.TYPESCRIPT
        ? {
            code: '', // 保存时处理
            codeTS: DEFAULT_TS_GLOBAL_FRONT_MODEL_STR,
          }
        : {
            code: '', // 保存时处理
            codeES: DEFAULT_JS_GLOBAL_FRONT_MODEL_STR,
          },
    )
    setExprHelp(null)
  })
  const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
  const handleExprChange = useLatestFn((expr = '') => {
    globalSchemaStore.setGlobalFrontModel(
      exprType === FrontExpressionType.TYPESCRIPT
        ? {
            code: '', // 保存时处理
            codeTS: expr,
          }
        : {
            code: '', // 保存时处理
            codeES: expr,
          },
    )
    ;(async (): Promise<void> => {
      const err = await manualValidate(
        [
          exprType === FrontExpressionType.TYPESCRIPT ? tsRule : jsRule,
          moneyRule,
        ],
        expr,
      )
      setExprHelp(err ? err.message : null)
    })()
  })
  const ctxDts = useMemo(() => {
    return getFrontCtxDts([])
  }, [])
  return (
    <Container>
      <div className="header">
        <Button type="primary" danger={enabled} onClick={handleSwitchBtnClick}>
          {enabled ? '关闭' : '开启'}
        </Button>
      </div>
      {enabled && (
        <div className="body">
          <Form.Item label="模型类型">
            <Select
              options={FRONT_EXPRESSION_TYPE_OPTIONS}
              value={exprType}
              onChange={handleTypeSelectChange}
            />
          </Form.Item>
          <LongLabelNoAnimationFormItem
            label={
              <div>
                模型（
                {exprType === FrontExpressionType.TYPESCRIPT
                  ? GLOBAL_FRONT_TS_MODEL_LABEL
                  : GLOBAL_FRONT_JS_MODEL_LABEL}
                ）
              </div>
            }
            labelCol={{
              span: 24,
            }}
            wrapperCol={{
              span: 24,
            }}
            validateStatus={exprHelp ? 'error' : undefined}
            help={exprHelp}
            extra={moneyRuleHasErrBefore && BIG_JS_LINK}
          >
            <JSExprEditor
              height={`calc(100vh - 248px${
                moneyRuleHasErrBefore ? ' - 24px' : ''
              })`}
              language={
                exprType === FrontExpressionType.TYPESCRIPT
                  ? 'typescript'
                  : 'javascript'
              }
              ctxDts={ctxDts}
              value={model.codeTS || model.codeES}
              onChange={handleExprChange}
            />
          </LongLabelNoAnimationFormItem>
        </div>
      )}
    </Container>
  )
})

const Container = styled.div`
  height: 100%;
  overflow: auto;

  .header {
    margin-bottom: 24px;
  }
`
