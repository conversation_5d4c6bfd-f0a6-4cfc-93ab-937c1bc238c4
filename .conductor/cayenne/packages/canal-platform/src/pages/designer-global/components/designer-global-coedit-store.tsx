import { CoeditSave } from '@/components/coedit-save'
import { useLatestFn } from '@ad/canal-shared-ui'
import { Button } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useState, type FC } from 'react'
import styled from 'styled-components'
import { designerGlobalCoeditStore } from '../stores/designer-global-coedit-store'
import { globalSchemaStore } from '../stores/global-schema-store'

/**
 * 全局设计器协同编辑保存
 */
export const DesignerGlobalCoeditSave: FC = observer(() => {
  const [loading, setLoading] = useState(false)
  const handleSaveBtnClick = useLatestFn(async () => {
    try {
      setLoading(true)
      await globalSchemaStore.saveSchema()
    } finally {
      setLoading(false)
    }
  })
  const editable = useLatestFn(() => globalSchemaStore.editable)
  return (
    <Container>
      <CoeditSave
        coeditStore={designerGlobalCoeditStore}
        saveBtn={
          <SaveBtn
            type="primary"
            loading={loading}
            disabled={!globalSchemaStore.savable}
            onClick={handleSaveBtnClick}
          >
            保存
          </SaveBtn>
        }
        editable={editable}
        isGlobalModule={true}
      />
    </Container>
  )
})

const Container = styled.div`
  display: flex;
  align-items: center;
`

const SaveBtn = styled(Button)`
  padding: 0 26px;
  margin-right: 12px;
`
