import { DesignerTitle } from '@/components/designer-title'
import type { StyleProps } from '@ad/canal-shared-ui'
import { observer } from 'mobx-react-lite'
import { type FC } from 'react'
import styled from 'styled-components'
import { DESIGNER_GLOBAL_QUERY } from '../constants/browser'
import { globalSchemaStore } from '../stores/global-schema-store'
import { DesignerGlobalCoeditSave } from './designer-global-coedit-store'

/**
 * 全局设计器头部
 */
export const DesignerGlobalHeader: FC<StyleProps> = observer((props) => {
  return (
    <Container {...props}>
      <DesignerTitle
        domainCode={DESIGNER_GLOBAL_QUERY.domainCode}
        changeId={globalSchemaStore.moduleDetailInit?.changeId}
        title={globalSchemaStore.moduleDetailInit?.name}
      />
      <DesignerGlobalCoeditSave />
    </Container>
  )
})

const Container = styled.div`
  height: 48px;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`
