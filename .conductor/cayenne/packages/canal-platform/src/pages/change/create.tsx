import { createChangeUsingPost } from '@/services/backend/change'
import { history, useSearchParams } from '@kmi/react'
import { Button, Card, Form, Input, Radio, Space, message } from '@m-ui/react'
import { useState, type FC } from 'react'
import { CHANGE_TYPE } from './const'

export const ChangeCreate: FC = () => {
  const [searchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode')
  const [submitLoading, setSubmitLoading] = useState(false)
  const onCancel = (): void => {
    history.go(-1)
  }
  const onFinish = async (values: { type: number; teamId: string }): void => {
    setSubmitLoading(true)
    console.log(values)
    if (!domainCode) {
      message.error('需要在一个业务域下添加，缺少业务域code')
      return
    }
    // 新建变更接口调用
    try {
      const res = await createChangeUsingPost({
        domainCode: domainCode,
        type: values.type,
        teamId: values.teamId,
      })
      if (res?.result === 1 && !!res.data?.changeId) {
        message.success('创建成功')
        setTimeout(() => {
          history.push(
            `/change/detail?changeId=${res.data.changeId}&domainCode=${domainCode}`,
          )
        }, 500)
      } else {
        message.error(res.msg || '创建失败')
      }
    } catch (error) {
      console.log(error)
    } finally {
      setSubmitLoading(false)
    }
  }
  return (
    <Card>
      <Form
        name="createChange"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 24 }}
        initialValues={{ type: CHANGE_TYPE[0].value }}
        style={{ maxWidth: 400 }}
        onFinish={onFinish}
      >
        <Form.Item
          label="变更类型"
          name="type"
          rules={[{ required: true, message: '请选择变更类型' }]}
        >
          <Radio.Group>
            {CHANGE_TYPE.map((item) => (
              <Radio value={item.value} key={item.value}>
                {item.label}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="Team任务"
          name="teamId"
          rules={[{ required: true, message: '请选择对应的team任务' }]}
          extra="请填写产品需求的TeamID"
        >
          <Input />
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Space>
            <Button htmlType="button" onClick={onCancel}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={submitLoading}>
              提交
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  )
}
export default ChangeCreate
