import ChangeStatus from '@/components/change/change-status'
import TeamJump from '@/components/change/team-jump'
import { closeChange, getCanalChangeList } from '@/services/backend/change'
import { checkRedirectToNewPlatform } from '@/utils/lingzhu'
import { Link, history, useSearchParams } from '@kmi/react'
import {
  Button,
  Card,
  Form,
  Input,
  Popconfirm,
  Space,
  Table,
  message,
} from '@m-ui/react'
import { useEffect, useState, type FC, type ReactNode } from 'react'
import { dateFormat, getTypeLabel } from './const'

export const ChangeList: FC = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const domainCode = searchParams.get('domainCode') || ''
  const pageParam = searchParams.get('page')
  const searchChangeName = searchParams.get('changeName') || ''
  const searchCreateUser = searchParams.get('createUser') || ''
  const searchSortField = searchParams.get('sortField') || null
  const searchSortOrder = searchParams.get('sortOrder') || null

  const [changeList, setChangeList] = useState([])
  const [total, setTotal] = useState(0)
  // 初始化页码，如果 URL 中有 page 参数，则使用它
  const [currentPage, setCurrentPage] = useState(() => {
    return pageParam ? parseInt(pageParam, 10) : 1
  })
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()

  // 定义表单值的接口
  interface FormValues {
    changeName?: string
    createUser?: string
    // 添加其他可能的表单字段
  }

  // 初始化表单值状态，如果 URL 中有搜索参数，则使用它
  const [formValues, setFormValues] = useState<FormValues>(() => {
    const initialValues: FormValues = {}
    if (searchChangeName) initialValues.changeName = searchChangeName
    if (searchCreateUser) initialValues.createUser = searchCreateUser
    return initialValues
  })

  // 添加排序状态，如果 URL 中有排序参数，则使用它
  const [sortField, setSortField] = useState<string | null>(searchSortField)
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(
    searchSortOrder === 'asc'
      ? 'ascend'
      : searchSortOrder === 'desc'
      ? 'descend'
      : null,
  )

  const columns = [
    {
      title: '变更名称',
      dataIndex: 'changeName',
      key: 'changeName',
      sorter: true,
    },
    {
      title: '变更ID',
      dataIndex: 'changeId',
      key: 'changeId',
      sorter: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: number): ReactNode => getTypeLabel(type),
      sorter: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      key: 'createUser',
      sorter: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: number): ReactNode => {
        return <ChangeStatus status={status} />
      },
      sorter: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (createTime: number) => <>{dateFormat(createTime)}</>,
      sorter: true,
    },
    {
      title: 'Team地址',
      dataIndex: 'teamId',
      key: 'teamId',
      render: (teamId: string): ReactNode => <TeamJump teamId={teamId} />,
    },
    {
      title: '操作',
      key: 'action',
      render: (...[, record]): ReactNode => (
        <Space>
          <Button type="link">
            <Link
              to={`/change/detail?changeId=${record.changeId}&domainCode=${record.domainCode}`}
            >
              进入变更
            </Link>
          </Button>
          {/* 开发中可以关闭 */}
          {Number(record.status) === 0 && (
            <Popconfirm
              placement="top"
              title={'确定关闭该变更吗？'}
              onConfirm={(): void => {
                onClose(record)
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                关闭
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ]
  /**
   * 请求列表数据
   */
  async function getChangeListFunc(): Promise<void> {
    setLoading(true)
    try {
      const params = {
        domainCode: domainCode,
        pageNum: currentPage,
        pageSize: 10,
        ...(formValues || {}),
        // 添加排序参数
        ...(sortField && sortOrder
          ? {
              sortField,
              sortOrder: sortOrder === 'ascend' ? 'asc' : 'desc',
            }
          : {}),
      }
      const res = await getCanalChangeList(params)
      if (res?.result === 1 && Array.isArray(res.data?.list)) {
        setChangeList(res.data.list)
        setTotal(res.data.pageInfo?.total)
      } else {
        message.error(res.msg || '查询失败')
      }
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 更新 URL 参数的通用函数
   */
  const updateUrlParams = (params: Record<string, string | null>): void => {
    const newParams = new URLSearchParams(searchParams)

    // 遍历参数对象，更新或删除参数
    Object.entries(params).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') {
        newParams.delete(key)
      } else {
        newParams.set(key, value)
      }
    })

    setSearchParams(newParams)
  }

  /**
   * 处理表格排序变化
   */
  const handleTableChange = (pagination, filters, sorter): void => {
    // 处理页码变化
    const page = pagination.current
    setCurrentPage(page)

    // 处理排序变化
    let newSortField = null
    let newSortOrder = null

    if (sorter && sorter.field) {
      newSortField = sorter.field
      newSortOrder = sorter.order
      setSortField(newSortField)
      setSortOrder(newSortOrder)
    } else {
      // 清除排序
      setSortField(null)
      setSortOrder(null)
    }

    // 更新 URL 参数
    updateUrlParams({
      page: page > 1 ? page.toString() : null,
      sortField: newSortField,
      sortOrder:
        newSortOrder === 'ascend'
          ? 'asc'
          : newSortOrder === 'descend'
          ? 'desc'
          : null,
    })
  }
  /**
   * 关闭
   * @param record
   */
  async function onClose(record: { changeId: string }): Promise<void> {
    if (checkRedirectToNewPlatform(record.changeId, domainCode)) {
      return
    }
    setLoading(true)
    try {
      const res = await closeChange({
        changeId: record.changeId,
      })
      if (res.result === 1) {
        message.success('变更已关闭')
        getChangeListFunc()
      } else {
        message.error(res.msg || '关闭失败')
      }
    } catch (e) {
      message.success('关闭失败')
    }
    setLoading(false)
  }
  /**
   * 创建变更
   */
  function goToCreateChange(): void {
    // 保留当前的所有参数
    const currentParams = new URLSearchParams(searchParams)
    currentParams.delete('changeId') // 删除���能存在的 changeId

    const baseUrl = `/change/create?domainCode=${domainCode}`
    const otherParams = currentParams.toString()
    const queryString = otherParams ? `&${otherParams}` : ''

    history.push(`${baseUrl}${queryString}`)
  }
  const onSearch = (values: {
    createUser: string
    changeName: string
  }): void => {
    setCurrentPage(1)
    setFormValues(values)

    // 更新 URL 参数
    updateUrlParams({
      page: null, // 搜索时重置页码
      changeName: values.changeName || null,
      createUser: values.createUser || null,
    })
  }

  // 初始化表单显示值
  useEffect(() => {
    // 如果 URL 中有搜索参数，则设置表单显示值
    const initialValues: FormValues = {}
    if (searchChangeName) initialValues.changeName = searchChangeName
    if (searchCreateUser) initialValues.createUser = searchCreateUser

    if (Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues)
      // 确保 formValues 和表单值保持一致
      setFormValues(initialValues)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 监听数据变化，触发数据加载
  useEffect(() => {
    getChangeListFunc()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [domainCode, currentPage, formValues, sortField, sortOrder])

  return (
    <Card>
      <Form
        name="customized_form_controls"
        layout="inline"
        onFinish={onSearch}
        form={form}
      >
        <Form.Item label="变更名称" name="changeName">
          <Input
            placeholder="请输入变更名称（模糊查找）"
            style={{ width: 300 }}
          ></Input>
        </Form.Item>
        <Form.Item label="创建人" name="createUser">
          <Input placeholder="请输入变更创建人" />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            搜索
          </Button>
          <Button
            style={{ margin: '0 8px' }}
            onClick={(): void => {
              // 只清除表单字段和筛选条件，不影响排序状态
              form.resetFields()
              setFormValues({})
              setCurrentPage(1)

              // 清除 URL 中的筛选参数，保留排序和 domainCode
              const newParams = new URLSearchParams()
              if (domainCode) {
                newParams.set('domainCode', domainCode)
              }

              // 保留排序参数
              if (sortField) {
                newParams.set('sortField', sortField)
              }
              if (sortOrder) {
                const orderValue = sortOrder === 'ascend' ? 'asc' : 'desc'
                newParams.set('sortOrder', orderValue)
              }

              setSearchParams(newParams)
            }}
          >
            清空
          </Button>
        </Form.Item>
      </Form>
      <Button
        type="primary"
        style={{ float: 'right', marginBottom: '16px' }}
        onClick={(): void => goToCreateChange()}
      >
        新建变更
      </Button>
      <Table
        columns={columns.map((col) => {
          // 如果列支持排序，添加当前排序状态
          if (col.sorter && sortField === col.dataIndex) {
            return {
              ...col,
              sortOrder: sortOrder,
            }
          }
          return col
        })}
        dataSource={changeList}
        loading={loading}
        rowKey="changeId"
        pagination={{
          pageSize: 10,
          total: total,
          showSizeChanger: false,
          current: currentPage,
        }}
        onChange={handleTableChange}
      />
    </Card>
  )
}
export default ChangeList
