import { dateFormat } from '@/pages/change/const'
import { Button, Empty, Table, Tag } from '@m-ui/react'
import type { ReactNode } from 'react'
import { type ChangeDeployType } from './model'

const locale = {
  emptyText: <Empty description={'部署记录为空'} />,
}

const ChangeDeployList = ({
  deployList,
  doDeploy,
}: {
  deployList: ChangeDeployType[]
  doDeploy: (values: {
    stage: string
    lane: string | undefined
    frontLane: string | undefined
  }) => void
}): JSX.Element => {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
    },
    {
      title: '部署时间',
      dataIndex: 'publishTime',
      key: 'publishTime',
      render: (publishTime: number) => dateFormat(publishTime),
    },
    {
      title: '环境',
      key: 'stage',
      dataIndex: 'stage',
    },
    {
      title: '泳道信息',
      key: 'lane',
      dataIndex: 'lane',
      render: (...[, record]) => {
        return (
          <>
            <div>泳道：{record.lane}</div>
            <div>前端泳道：{record.frontLane}</div>
          </>
        )
      },
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      render: (status: string | number): ReactNode => {
        if (Number(status) === 1) {
          return <Tag color="success">成功</Tag>
        } else {
          return <Tag color="error">失败</Tag>
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (...[, record]) => {
        return (
          <Button
            type="link"
            onClick={(): void => {
              doDeployAgain(record)
            }}
          >
            再次部署
          </Button>
        )
      },
    },
  ]
  const doDeployAgain = (record: ChangeDeployType) => {
    doDeploy?.({
      stage: record.stage,
      lane: record.lane,
      frontLane: record.frontLane,
    })
  }
  return (
    <Table
      columns={columns}
      locale={locale}
      dataSource={deployList}
      rowKey={'id'}
    />
  )
}
export default ChangeDeployList
