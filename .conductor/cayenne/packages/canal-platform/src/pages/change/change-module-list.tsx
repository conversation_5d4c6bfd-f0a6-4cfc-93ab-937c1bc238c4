import CheckErrorModal from '@/components/check-error-modal'
import { EditableModuleName } from '@/components/editable-module-name'
import { ModuleTypeTag } from '@/components/module-type-tag'
import { dateFormat } from '@/pages/change/const'
import DiffModel from '@/pages/diff/diff-model'
import ModulePublishHistory from '@/pages/module/module-publish-history'
import type { ModuleDetail } from '@/services/backend/models'
import {
  checkModulesUsingPost,
  deleteModuleFromChange,
} from '@/services/backend/module'
import { createDesignerUrl } from '@/utils'
import { useSearchParams } from '@kmi/react'
import { ShareExportLine } from '@m-ui/icons'
import {
  Button,
  Empty,
  Popconfirm,
  Space,
  Table,
  Tooltip,
  message,
} from '@m-ui/react'
import type { ColumnsType } from '@m-ui/react/es/table'
import type { ReactNode } from 'react'
import { useState } from 'react'

const locale = {
  emptyText: (
    <Empty
      description={
        '本次变更还没有引入任何模块，请到第二个Tab全部线上模块列表中进行添加'
      }
    />
  ),
}
const ChangeModuleList = ({
  moduleList,
  domainCode,
  disableAction,
  changeId,
  onNameChanged,
  updateCallback,
  status,
  customDelete,
}: {
  moduleList: ModuleDetail[]
  domainCode: string
  disableAction: boolean
  onNameChanged: (newName: string, index: number) => void
  changeId: string
  updateCallback: () => void
  status: string | undefined
  customDelete?: (id: string, index: number) => void
}): JSX.Element => {
  // 检查内容合法性相关状态
  const [checkModalVisible, setCheckModalVisible] = useState(false)
  const [checkingModule, setCheckingModule] = useState<string>('')
  const [checkErrorInfo, setCheckErrorInfo] = useState<
    Array<{
      key: string
      content: Array<{
        moduleId: string
        errorTips: Array<{
          text: string
          content: string[]
        }>
      }>
    }>
  >([])
  const [searchParams] = useSearchParams()
  const openDiffId = searchParams.get('diffOpen')

  const deleteModuleFromChanges = async (moduleId: string): Promise<void> => {
    const deleteRes = await deleteModuleFromChange({
      moduleId,
      domainCode,
      changeId,
    })
    if (deleteRes.result === 1) {
      message.info('删除成功')
      updateCallback?.()
    } else {
      message.error(deleteRes.msg || '操作失败')
    }
  }
  const shareDiff = (record: ModuleDetail): void => {
    const url =
      window.location.origin +
      `/diff?moduleId=${record.id}&domainCode=${domainCode}&changeId=${changeId}&developVersion=${record.version}`
    navigator.clipboard.writeText(url)
    message.success('复制成功')
  }

  // 检查模块内容合法性
  const handleCheckModule = async (record: ModuleDetail): Promise<void> => {
    try {
      setCheckingModule(record.id)
      const moduleVersions = [
        {
          moduleId: record.id,
          version: record.version,
        },
      ]

      const result = await checkModulesUsingPost({
        moduleVersions,
      })

      if (result?.result === 1) {
        if (result.data?.checkError && result.data.checkError.length > 0) {
          setCheckErrorInfo(result.data.checkError)
          setCheckModalVisible(true)
        } else {
          message.success('模块配置检查通过')
        }
      } else {
        message.error(result?.msg || '检查失败')
      }
    } catch (error: any) {
      console.error('检查模块配置出错:', error)
      message.error(error?.msg || '检查模块配置失败')
    } finally {
      setCheckingModule('')
    }
  }

  const columns: ColumnsType<ModuleDetail> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (v, record, index): ReactNode => {
        void v
        return (
          <EditableModuleName
            id={record.id}
            name={record.name}
            onNameChanged={(name) => onNameChanged(name, index)}
          />
        )
      },
    },
    {
      title: '模块ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render(...[, record]): JSX.Element {
        return <ModuleTypeTag type={record.type} />
      },
    },
    {
      title: '当前版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '检出版本',
      dataIndex: 'checkoutModuleVersion',
      key: 'checkoutModuleVersion',
    },
    {
      title: '更新人',
      key: 'updateUser',
      dataIndex: 'updateUser',
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      render: (updateTime: number) => dateFormat(updateTime),
    },
    {
      title: '操作',
      key: 'action',
      render: (...[, record, index]): ReactNode => {
        const designerUrl = createDesignerUrl(record.type, {
          moduleId: `${record.id}`,
          moduleVersion: `${record.version}`,
          domainCode,
        })
        return (
          <Space>
            {!disableAction && (
              <a href={designerUrl} target="_blank">
                编辑
              </a>
            )}

            {!disableAction && (
              <Popconfirm
                title="确定将该模块从变更中删除吗，删除后不可恢复?"
                onConfirm={(): void => {
                  // 灵筑兼容：调用灵筑的删除接口
                  if (customDelete) {
                    customDelete(record.id, index)
                    return
                  }
                  deleteModuleFromChanges(record.id)
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button type="link">删除</Button>
              </Popconfirm>
            )}
            {disableAction && (
              <a href={designerUrl} target="_blank">
                查看配置
              </a>
            )}
            {/* 检查内容合法性按钮 */}
            {(status === 'developing' || status === 'publishing') && (
              <Tooltip title="检查配置内容的合法性">
                <Button
                  type="link"
                  onClick={(): Promise<void> => {
                    return handleCheckModule(record)
                  }}
                  loading={checkingModule === record.id}
                >
                  检查内容
                </Button>
              </Tooltip>
            )}
            {/* 在发布中也透出这个操作 */}
            {(status === 'publishing' || status === 'developing') && (
              <Space>
                <DiffModel
                  developVersion={`${record.version}`}
                  changeId={changeId}
                  moduleId={record.id}
                  domainCode={domainCode}
                  autoOpen={record.id === openDiffId}
                  btnTxt={+record.version == 1 ? '查看代码' : ''}
                />
                <Tooltip title="点击复制cr链接">
                  <Button
                    shape="circle"
                    onClick={(): void => {
                      shareDiff(record)
                    }}
                  >
                    <ShareExportLine />
                  </Button>
                </Tooltip>
              </Space>
            )}
            {status === 'published' && (
              <ModulePublishHistory
                moduleId={record.id}
                moduleName={record.name}
                domainCode={domainCode}
                moduleType={record.type}
              />
            )}
          </Space>
        )
      },
    },
  ]
  return (
    <>
      <Table
        columns={columns}
        locale={locale}
        dataSource={moduleList}
        rowKey={'id'}
        pagination={{
          showSizeChanger: moduleList.length > 10,
          showTotal: (total) => `共 ${total} 条`,
        }}
      />
      {/* 检查内容合法性结果弹窗 */}
      <CheckErrorModal
        visible={checkModalVisible}
        onOk={(): void => setCheckModalVisible(false)}
        onCancel={(): void => setCheckModalVisible(false)}
        checkErrorInfo={checkErrorInfo}
      />
    </>
  )
}
export default ChangeModuleList
