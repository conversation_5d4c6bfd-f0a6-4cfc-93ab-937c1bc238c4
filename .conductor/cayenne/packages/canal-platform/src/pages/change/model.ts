export interface ChangeModel {
  changeId: string
  changeName?: string
  type?: number
  createUser?: string
  publishTime?: number
  domainCode: string
  formatPublishTime?: string
  createTime?: number
  formatCreateTime?: string
  status?: number
  isOnline?: number
  teamId?: string
  developer?: string[]
  tester?: string[]
}

export interface ChangeModuleType {
  id: string
  name: string
  version: number
  updateUser: string
  updateTime: number
}

export interface OnlineModuleType {
  id: string
  name: string
  onlineVersion: number
  updateUser: string
  updateTime: number
  module?: {
    id: string
  }
}

export interface ChangeDeployType {
  id: number
  changeId: string
  stage: string
  operator: string
  lane?: string
  frontLane?: string
  status: number
  createUser: string
  publishTime: number
  createTime: number
  updateTime: number
  updateUser: string
}

export interface ChangePublishType {
  id: number
  changeId: string
  domainCode: string
  status?: number
  isOnline?: number
  type: number
  createUser: string
  updateUser: string
  createTime: number
  updateTime: number
}
