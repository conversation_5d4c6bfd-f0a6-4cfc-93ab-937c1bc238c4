import { IconButton } from '@/components/icon-button'
import { SearchResultTree } from '@/components/search-result-tree'
import { SearchResultTreeLeafNode } from '@/components/search-result-tree-leaf-node'
import { SearchResultTreeNonLeafNode } from '@/components/search-result-tree-non-leaf-node'
import type { ModuleDetail } from '@/services/backend/models'
import { groupSearchResultItems, SearchTaskStatus } from '@/stores/search-store'
import { useLatestFn } from '@ad/canal-shared-ui'
import { NormalEditLine, ShareAccessconversionLine } from '@m-ui/icons'
import { Form, Input, Spin, Tooltip } from '@m-ui/react'
import type { DataNode } from '@m-ui/react/lib/tree'
import { sumBy } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useMemo, type ChangeEvent, type FC } from 'react'
import styled from 'styled-components'
import { ChangeSearchStore } from './change-search-store'

/**
 * 变更模块概要
 */
export interface ChangeModuleSummary extends Omit<ModuleDetail, 'content'> {
  /**
   * 模块版本
   */
  moduleVersion: string
}

/**
 * 变更批量编辑属性
 */
export interface ChangeBatchEditorProps {
  /**
   * （业务）域代码
   */
  domainCode: string
  /**
   * 变更 ID
   */
  changeId: string
  /**
   * 变更模块
   */
  changeModules: ChangeModuleSummary[]
}

/**
 * 变更批量编辑
 */
export const ChangeBatchEditor: FC<ChangeBatchEditorProps> = observer(
  ({ domainCode, changeId, changeModules }) => {
    const changeSearchStore = useMemo(
      () => new ChangeSearchStore(domainCode, changeId, changeModules),
      [changeId, changeModules, domainCode],
    )
    const {
      keyword,
      replaceText,
      replacable,
      isReplacing,
      currentTask,
      changeModuleObjMap,
    } = changeSearchStore
    const rawResultItems = currentTask?.resultItems
    const resultItems = useMemo(() => rawResultItems || [], [rawResultItems])
    const moduleGroups = useMemo(
      () => groupSearchResultItems(resultItems),
      [resultItems],
    )
    const resultSummary = useMemo(() => {
      if (!resultItems.length) return ''
      return `${moduleGroups.length} 个模块中有 ${resultItems.length} 个结果`
    }, [moduleGroups.length, resultItems.length])
    const treeData: DataNode[] = useMemo(() => {
      return moduleGroups.map((moduleGroup) => ({
        key: moduleGroup.moduleId,
        title: (
          <SearchResultTreeNonLeafNode
            title={`${changeModuleObjMap[moduleGroup.moduleId].name} (${
              moduleGroup.moduleId
            }) (${sumBy(
              moduleGroup.items,
              (categoryGroup) => categoryGroup.items.length,
            )})`}
            actions={
              replacable && (
                <Tooltip title="替换模块下所有">
                  <IconButton
                    onClick={(): Promise<void> =>
                      changeSearchStore.batchReplace([moduleGroup])
                    }
                  >
                    <ShareAccessconversionLine />
                  </IconButton>
                </Tooltip>
              )
            }
          />
        ),
        selectable: false,
        children: moduleGroup.items.map(
          (categoryGroup, categoryGroupIndex) => ({
            key: JSON.stringify([
              moduleGroup.moduleId,
              categoryGroup.category,
              categoryGroupIndex,
            ]),
            title: (
              <SearchResultTreeNonLeafNode
                title={categoryGroup.category}
                actions={
                  replacable && (
                    <Tooltip title="替换分类下所有">
                      <IconButton
                        onClick={(): Promise<void> =>
                          changeSearchStore.batchReplace([
                            {
                              moduleId: moduleGroup.moduleId,
                              items: [categoryGroup],
                            },
                          ])
                        }
                      >
                        <ShareAccessconversionLine />
                      </IconButton>
                    </Tooltip>
                  )
                }
              />
            ),
            selectable: false,
            children: categoryGroup.items.map((item, itemIndex) => {
              const key = JSON.stringify([
                moduleGroup.moduleId,
                categoryGroup.category,
                categoryGroupIndex,
                itemIndex,
              ])
              return {
                key,
                selectable: false,
                isLeaf: true,
                title: (
                  <SearchResultTreeLeafNode
                    item={item}
                    actions={
                      <>
                        <Tooltip title="打开设计器">
                          <IconButton
                            onClick={(): void =>
                              changeSearchStore.openDesigner(item)
                            }
                          >
                            <NormalEditLine />
                          </IconButton>
                        </Tooltip>
                        {replacable && (
                          <Tooltip title="替换">
                            <IconButton
                              onClick={(): Promise<void> =>
                                changeSearchStore.batchReplace([
                                  {
                                    moduleId: moduleGroup.moduleId,
                                    items: [
                                      {
                                        category: categoryGroup.category,
                                        items: [item],
                                      },
                                    ],
                                  },
                                ])
                              }
                            >
                              <ShareAccessconversionLine />
                            </IconButton>
                          </Tooltip>
                        )}
                      </>
                    }
                    replaceText={replaceText}
                  />
                ),
              }
            }),
          }),
        ),
      }))
    }, [
      changeModuleObjMap,
      changeSearchStore,
      moduleGroups,
      replacable,
      replaceText,
    ])
    const handleSearchInputChange = useLatestFn(
      (ev: ChangeEvent<HTMLInputElement>) => {
        changeSearchStore.setKeyword(ev.target.value)
      },
    )
    const handleReplaceInputChange = useLatestFn(
      (ev: ChangeEvent<HTMLInputElement>) => {
        changeSearchStore.setReplaceText(ev.target.value)
      },
    )
    return (
      <Container>
        <div className="search-form">
          <Form.Item label="搜索" tooltip="暂不含全局模块">
            <Input value={keyword} onChange={handleSearchInputChange} />
          </Form.Item>
          <Form.Item label="替换">
            <Input value={replaceText} onChange={handleReplaceInputChange} />
          </Form.Item>
        </div>
        {currentTask && (
          <div className="search-summary">
            <div>
              {currentTask.status === SearchTaskStatus.RUNNING && <Spin />}
              {resultSummary && <span>{resultSummary}</span>}
              {currentTask.status === SearchTaskStatus.FINISHED &&
                !resultItems.length && <span>未找到结果</span>}
            </div>
            {resultSummary && replacable && (
              <div>
                <Tooltip title="替换所有">
                  <IconButton
                    onClick={(): Promise<void> =>
                      changeSearchStore.batchReplace(moduleGroups)
                    }
                  >
                    <ShareAccessconversionLine />
                  </IconButton>
                </Tooltip>
              </div>
            )}
          </div>
        )}
        <div className="search-body">
          <SearchResultTree key={currentTask?.id} treeData={treeData} />
        </div>
        {isReplacing && <Spin />}
      </Container>
    )
  },
)

const Container = styled.div`
  height: calc(100vh - 110px);
  margin-bottom: -48px;
  display: flex;
  flex-direction: column;
  position: relative;

  .search-form {
    flex: none;
    max-width: 800px;
    width: 100%;
    margin: auto;
  }

  .search-summary {
    display: flex;
    justify-content: space-between;
  }

  .search-body {
    margin-top: 10px;
    height: 0;
    flex: auto;
  }

  ${IconButton} {
    margin-left: 6px;

    &:last-child {
      margin-right: 12px;
    }
  }

  > .ant-spin {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 300px;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 1;
  }
`
