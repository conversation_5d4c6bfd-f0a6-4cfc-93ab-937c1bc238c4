import {
  getBizComponentLibInfoByDomainCode,
  getInternalComponentLibInfo,
  getOldBizComponentLibInfoByBizSchema,
} from '@/pages/designer/materials'
import { getModuleSchema } from '@/pages/designer/tools'
import { CoeditBatchStore } from '@/stores/coedit-batch-store'
import {
  SearchStore,
  SearchTaskStatus,
  replaceSchemaBySearchResultItems,
  type SearchResultItem,
  type SearchResultItemModuleGroup,
} from '@/stores/search-store'
import { createDesignerUrl } from '@/utils'
import { ModuleType } from '@ad/canal-shared'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { mergeComponentLibWithImplementProps } from '@ad/e2e-material-schema-utils'
import type { E2ESchema } from '@ad/e2e-schema'
import { encodeComponentType } from '@kael/schema-utils'
import { Modal } from '@m-ui/react'
import { fromPairs, once } from 'lodash'
import { action, makeObservable, observable, reaction } from 'mobx'
import type { ChangeModuleSummary } from '.'

/**
 * 变更搜索仓库
 */
export class ChangeSearchStore extends SearchStore {
  /**
   * 变更模块对象表
   */
  public readonly changeModuleObjMap: Record<string, ChangeModuleSummary> = {}

  /**
   * 替换文本
   */
  private _replaceText: string = ''

  /**
   * 替换文本
   */
  public get replaceText(): string {
    return this._replaceText
  }

  /**
   * 可替换
   */
  public get replacable(): boolean {
    return (
      !!this._replaceText &&
      this._replaceText !== this.$keyword &&
      this.$currentTask?.status === SearchTaskStatus.FINISHED
    )
  }

  /**
   * 模块等待对象表，模块 ID -> 等待 Promise
   */
  private _moduleWaitObjMap: Record<string, Promise<void> | undefined> = {}

  /**
   * Schema 缓存，模块 ID -> 搭建 Schema
   */
  private _schemaCache: Record<string, E2ESchema | null | undefined> = {}

  /**
   * 物料 Schema 缓存，模块 ID -> 类型 -> 版本 -> 组件物料 Schema
   */
  private _materialSchemaCache: Record<
    string,
    | Record<
        string,
        Record<string, E2ERemoteComponentMaterialSchema | null> | undefined
      >
    | undefined
  > = {}

  /**
   * 正在替换
   */
  private _isReplacing = false

  /**
   * 正在替换
   */
  public get isReplacing(): boolean {
    return this._isReplacing
  }

  /**
   * 变更搜索仓库
   * @param domainCode （业务）域代码
   * @param changeId 变更 ID
   * @param changeModules 变更模块
   */
  public constructor(
    public readonly domainCode: string,
    public readonly changeId: string,
    public readonly changeModules: ChangeModuleSummary[],
  ) {
    super()
    this.changeModuleObjMap = fromPairs(changeModules.map((m) => [m.id, m]))
    makeObservable<ChangeSearchStore, '_replaceText' | '_isReplacing'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _replaceText: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isReplacing: observable,
      setReplaceText: action,
      setIsReplacing: action,
    })
    // 过滤全局模块
    const searchModuleIds = changeModules
      .filter((m) => m.type !== ModuleType.GLOBAL)
      .map((m) => m.id)
    reaction(
      () => this.keyword,
      (keyword) => {
        this.cancelSearch()
        if (!keyword) return
        this.search(keyword, searchModuleIds, true)
      },
    )

    if (process.env.NODE_ENV === 'development') {
      Object.assign(window, {
        changeSearchStore: this,
      })
    }
  }

  /**
   * 设置替换文本
   * @param replaceText 替换文本
   */
  public setReplaceText(replaceText: string): void {
    this._replaceText = replaceText
  }

  /**
   * 设置正在替换
   * @param isReplacing 正在替换
   */
  public setIsReplacing(isReplacing: boolean): void {
    this._isReplacing = isReplacing
  }

  /**
   * 加载模块
   * @param moduleId 模块 ID
   */
  private _loadModule(moduleId: string): Promise<void> {
    let p = this._moduleWaitObjMap[moduleId]
    if (!p) {
      p = this._moduleWaitObjMap[moduleId] = (async (): Promise<void> => {
        try {
          const [schema, internalComponentLibInfo, bizComponentLibInfo] =
            await Promise.all([
              getModuleSchema(
                moduleId,
                this.changeModuleObjMap[moduleId].moduleVersion,
              ),
              getInternalComponentLibInfo(),
              this._fetchBizComponentLibInfo(),
            ])
          const oldBizComponentLibInfo =
            await getOldBizComponentLibInfoByBizSchema(
              bizComponentLibInfo.schemaJson,
              schema,
            )

          this._schemaCache[moduleId] = schema
          for (const libInfo of [
            internalComponentLibInfo,
            bizComponentLibInfo,
            oldBizComponentLibInfo,
          ]) {
            for (const component of mergeComponentLibWithImplementProps(
              libInfo.schemaJson,
            ).components) {
              const type = encodeComponentType(
                // 老业务组件库重定向到业务组件库
                libInfo.name === oldBizComponentLibInfo.name
                  ? bizComponentLibInfo.name
                  : libInfo.name,
                component.type,
              )
              const version = `${
                (component as E2ERemoteComponentMaterialSchema).version
              }`
              let typeMap = this._materialSchemaCache[moduleId]
              if (!typeMap) {
                typeMap = this._materialSchemaCache[moduleId] = {}
              }
              let versionMap = typeMap[type]
              if (!versionMap) {
                versionMap = typeMap[type] = {}
              }
              versionMap[version] = component
            }
          }
        } catch (err) {
          console.error('ChangeSearchStore::_loadModule err', err)
        }
      })()
    }
    return p
  }

  /**
   * 获取业务组件库（物料）信息
   */
  private _fetchBizComponentLibInfo = once(() =>
    getBizComponentLibInfoByDomainCode(this.domainCode),
  )

  /**
   * 打开设计器
   * @param item 搜索结果条目
   */
  public openDesigner(item: SearchResultItem): void {
    const changeModule = this.changeModuleObjMap[item.moduleId]
    const designerUrl = createDesignerUrl(changeModule.type, {
      moduleId: item.moduleId,
      moduleVersion: changeModule.moduleVersion,
      domainCode: this.domainCode,
      anchorStr: JSON.stringify(item.anchor),
    })
    window.open(designerUrl)
  }

  /**
   * 批量替换
   * @param groups 模块分组
   */
  public async batchReplace(
    groups: SearchResultItemModuleGroup[],
  ): Promise<void> {
    // console.log('ChangeSearchStore::batchReplace', { groups })
    if (this._isReplacing || !this.replacable || !groups.length) {
      return
    }
    const groupObjMap = fromPairs(
      groups.map((group) => [group.moduleId, group]),
    )
    this.setIsReplacing(true)
    try {
      await new CoeditBatchStore({
        domainCode: this.domainCode,
        modules: groups.map((group) => {
          const m = this.changeModuleObjMap[group.moduleId]
          const schema = this._schemaCache[group.moduleId]
          if (!schema) {
            throw new Error(`模块 ${group.moduleId} 未加载`)
          }
          return {
            id: group.moduleId,
            version: +m.moduleVersion,
            name: m.name,
            schema,
          }
        }),
        editSchema: async (module): Promise<E2ESchema> => {
          const { id, schema } = module
          const newSchema = await replaceSchemaBySearchResultItems(
            schema,
            groupObjMap[id].items.flatMap(
              (categoryGroup) => categoryGroup.items,
            ),
            this.$keyword,
            this.replaceText,
          )
          // console.log('ChangeSearchStore::batchReplace newSchema', {
          //   id,
          //   schema,
          //   newSchema,
          // })
          return newSchema
        },
      }).run()
      await this._clearAndResearch()
    } catch (err) {
      console.error('ChangeSearchStore::batchReplace err', err)
      if (err instanceof Error) {
        Modal.error({
          title: '批量替换失败',
          content: err.message,
          width: 800,
          onCancel: this._clearAndResearch.bind(this),
          onOk: this._clearAndResearch.bind(this),
        })
      }
    } finally {
      this.setIsReplacing(false)
    }
  }

  /**
   * 清空缓存并重新搜索
   */
  private async _clearAndResearch(): Promise<void> {
    await this._clearCache()
    this._research()
  }

  /**
   * 清空缓存
   */
  private async _clearCache(): Promise<void> {
    await Promise.all(this.changeModules.map((m) => this._unloadModule(m.id)))
  }

  /**
   * 卸载模块
   * @param moduleId 模块 ID
   */
  private async _unloadModule(moduleId: string): Promise<void> {
    delete this._moduleWaitObjMap[moduleId]
    delete this._schemaCache[moduleId]
    delete this._materialSchemaCache[moduleId]
    await this.$rpcEndpoint.call.deleteSchemaCache([moduleId])
  }

  /**
   * 重新搜索
   */
  private _research(): void {
    const { $currentTask: task } = this
    if (!task) return
    this.search(task.keyword, task.moduleIds, task.matchCase)
  }

  /**
   * 获取 Schema
   * @param moduleId 模块 ID
   */
  protected async handleGetSchema(moduleId: string): Promise<E2ESchema | null> {
    await this._loadModule(moduleId)
    return this._schemaCache[moduleId] || null
  }

  /**
   * 处理【获取组件物料 Schema】
   * @param moduleId 模块 ID
   * @param type 组件类型
   * @param version 版本
   */
  protected async handleGetComponentMaterialSchema(
    moduleId: string,
    type: string,
    version?: string,
  ): Promise<E2ERemoteComponentMaterialSchema | null> {
    await this._loadModule(moduleId)
    return this._materialSchemaCache[moduleId]?.[type]?.[`${version}`] || null
  }
}
