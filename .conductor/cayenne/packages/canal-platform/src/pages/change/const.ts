import dayjs from 'dayjs'
export const FORMAT = 'YYYY-MM-DD HH:mm:ss'

export const CHANGE_TYPE = [
  {
    value: 0,
    label: '需求开发',
  },
  {
    value: 1,
    label: '热修',
  },
  {
    value: 3,
    label: '回滚',
  },
]
export const getTypeLabel = (type: number | undefined): string => {
  let typeLabel = ''
  CHANGE_TYPE.forEach((item) => {
    if (item.value === type) typeLabel = item.label
  })
  return typeLabel
}

export const dateFormat = (time: number | undefined): string => {
  if (!time) return ''
  return dayjs(+time).format(FORMAT)
}

export const STATUS_MAP = [
  {
    color: '#108ee9',
    txt: '开发中',
    value: 0,
    key: 'developing',
  },
  { color: '#108ee9', txt: '测试中', value: 1, key: 'testing' },
  { color: '#87d068', txt: '发布中', value: 2, key: 'publishing' },
  { color: '#87d068', txt: '已发布', value: 3, key: 'published' },
  {
    color: '#f0f0f0',
    fontColor: '#434343',
    txt: '已关闭',
    value: 4,
    key: 'closed',
  },
]
const STATUS_KEY_MAP: Record<
  number,
  {
    color: string
    fontColor?: string
    txt: string
    value: number
    key: string
  }
> = {}
STATUS_MAP.forEach((item) => {
  STATUS_KEY_MAP[item.value] = item
})
export { STATUS_KEY_MAP }
