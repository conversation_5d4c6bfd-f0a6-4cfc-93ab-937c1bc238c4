import ChangeStatus from '@/components/change/change-status'
import TeamJump from '@/components/change/team-jump'
import ChangeAllModuleList from '@/pages/change/change-all-module-list'
import ChangeDeployList from '@/pages/change/change-deploy-list'
import ChangModuleList from '@/pages/change/change-module-list'
import { checkDomainPermission } from '@/services/backend/business_domain'
import {
  changeControllergetDetail,
  closeChange,
  doCanalDeploy,
  doChangeInfoRequest,
  getCanalChangeDeployList,
} from '@/services/backend/change'
import { type ModuleDetail } from '@/services/backend/models'
import {
  createModuleUsingPost,
  deployStaging,
  getAllModulesUsingPostByChangeId,
} from '@/services/backend/module'
import { isCreateFromLingZhu, jumpToNewChangeDetail } from '@/utils/lingzhu'
import { useSearchParams } from '@kmi/react'
import type { TabsProps } from '@m-ui/react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Descriptions,
  Modal,
  PageHeader,
  Popconfirm,
  Row,
  Space,
  Spin,
  Tabs,
  Typography,
  message,
} from '@m-ui/react'
import { useEffect, useMemo, useState, type FC } from 'react'
import styled from 'styled-components'
import CreatePublish from '../publish/create-publish'
import { AddModuleForm, type AddModuleFormValues } from './add-module'
import {
  ChangeBatchEditor,
  type ChangeModuleSummary,
} from './change-batch-editor'
import { CHANGE_TYPE, STATUS_KEY_MAP, dateFormat, getTypeLabel } from './const'
import DeployModuleForm from './deploy-module'
import DeployOnlineVersion from './deploy-online-version'
import { type ChangeDeployType, type ChangeModel } from './model'
const { Text } = Typography

export const ChangeDetail: FC = () => {
  const [searchParams] = useSearchParams()
  const changeId = searchParams.get('changeId') || ''
  const domainCode = searchParams.get('domainCode') || ''
  const ignoreComponentCheck =
    !!searchParams.get('ignoreComponentCheck') || false
  const [changeInfo, setChangeInfo] = useState<ChangeModel>({
    changeId,
    domainCode,
  })
  const [loading, setLoading] = useState(true)
  const [changeModuleList, setChangeModuleList] = useState<ModuleDetail[]>([])
  // 第一个列表的loading
  const [changModuleListSpinning, setChangModuleListSpinning] = useState(false)
  // 部署列表的loading
  const [changeDeploySpinning, setChangeDeploySpinning] = useState(false)

  const [deployList, setDeployList] = useState<ChangeDeployType[]>([])
  const [activeTabKey, setActiveTabKey] = useState('list')
  /**
   * 新增模块弹框
   */
  const [isModalOpen, setIsModalOpen] = useState(false)
  /**
   * 新增部署弹框
   */
  const [isDeployModalOpen, setIsDeployModalOpen] = useState(false)

  /**
   * 是否可以进入发布
   */
  const canPublish = useMemo(() => {
    const found = deployList.find(
      (item) => item.stage === 'prt' && Number(item.status) === 1,
    )
    return !!found
  }, [deployList])

  /**
   *
   */
  const addCallback = (): void => {
    getChangeModuleList()
    setActiveTabKey('list')
  }
  /**
   * 是否可以操作
   */
  const banOperate = useMemo(() => {
    if (!changeInfo) return false
    // 如果是回滚不允许编辑
    if (changeInfo.type === CHANGE_TYPE[2].value) return true
    const status = STATUS_KEY_MAP[changeInfo.status as number]
    if (status) {
      if (['publishing', 'published', 'closed'].indexOf(status.key) > -1) {
        return true
      }
    }
    return false
  }, [changeInfo])

  /**
   * 部署
   */
  const openDeployModule = async (values): Promise<void> => {
    setChangeDeploySpinning(true)
    setActiveTabKey('deploy')
    setIsDeployModalOpen(false)
    try {
      // 如果是prt则直接调用部署接口即可
      if (values.stage === 'prt' || values.stage === 'beta') {
        const res = await doCanalDeploy({
          changeId,
          ...values,
        })
        if (res.result === 1 && res.data?.id && res.data?.status === 1) {
          message.success('部署成功')
        }
      }
      if (values.stage === 'staging') {
        const isSuccess = await deployStaging(
          true,
          domainCode,
          changeInfo.changeId,
          values.stage,
          values.lane,
          values.frontLane,
        )
        // 部署记录
        await doCanalDeploy({
          changeId,
          ...values,
          status: isSuccess ? 1 : 0,
        })
        if (isSuccess) {
          message.success('部署成功')
        }
      }
    } catch (error) {
      message.error('部署失败')
    }
    setChangeDeploySpinning(false)
    getChangeDeployList()
  }
  /**
   * tab定义
   */
  const items: TabsProps['items'] = [
    {
      key: 'list',
      label: `本次变更模块（${changeModuleList.length}）`,
      children: (
        <>
          <Row justify="end" style={{ margin: '8px 0' }}>
            <DeployOnlineVersion changeId={changeId} domainCode={domainCode} />
          </Row>
          <Spin tip="加载中..." spinning={changModuleListSpinning}>
            <ChangModuleList
              moduleList={changeModuleList}
              domainCode={changeInfo?.domainCode}
              disableAction={banOperate}
              changeId={changeInfo?.changeId}
              status={STATUS_KEY_MAP[changeInfo?.status]?.key}
              onNameChanged={(): void => {
                // getOnlineModuleList()
              }}
              updateCallback={(): void => {
                getChangeModuleList()
              }}
            />
          </Spin>
        </>
      ),
    },
    {
      key: 'all',
      label: `全部线上模块`,
      children: (
        <ChangeAllModuleList
          changeId={changeId}
          disableAction={banOperate}
          domainCode={domainCode}
          addCallback={addCallback}
        />
      ),
    },
    {
      key: 'deploy',
      label: `部署记录（${deployList.length}）`,
      children: (
        <Spin tip="加载中..." spinning={changeDeploySpinning}>
          <ChangeDeployList
            deployList={deployList}
            doDeploy={openDeployModule}
          />
        </Spin>
      ),
    },
  ]
  if (!banOperate && changeModuleList.length) {
    items.push({
      key: 'batchEdit',
      label: `批量编辑`,
      children: (
        <ChangeBatchEditor
          domainCode={domainCode}
          changeId={changeId}
          // FIXME: changeModuleList 的 TS 定义有问题，缺失 checkoutModuleVersion 和 moduleVersion 等字段
          changeModules={changeModuleList as unknown as ChangeModuleSummary[]}
        />
      ),
    })
  }
  /**
   * 获取变更的基础信息
   */
  async function getChangeInfo(): Promise<void> {
    setLoading(true)
    try {
      const res = await changeControllergetDetail({ changeId })
      if (res?.result === 1 && res.data) {
        setChangeInfo(res.data)
      } else {
        message.error('详情获取失败')
      }
    } catch (e) {
      message.error(e.msg || message.error('详情获取失败'))
    }
    setLoading(false)
  }
  /**
   * 获取变更的关联模块
   */
  async function getChangeModuleList(): void {
    setChangModuleListSpinning(true)
    try {
      const res = await getAllModulesUsingPostByChangeId({ changeId })
      if (res?.result === 1 && res.data?.list) {
        setChangeModuleList(res.data.list)
      }
    } catch (e) {
      message.error(e?.msg || '列表加载失败')
    }
    setChangModuleListSpinning(false)
  }
  /**
   * 获取变更的部署列表
   */
  async function getChangeDeployList(): Promise<void> {
    setChangeDeploySpinning(true)
    try {
      const res = await getCanalChangeDeployList({ changeId })
      if (res?.result === 1 && Array.isArray(res.data)) {
        setDeployList(res.data)
      }
    } catch (error) {
      message.error(error.msg || '列表加载失败')
    }
    setChangeDeploySpinning(false)
  }
  /**
   * 打开添加模块模态框
   */
  const openAddModule = (): void => {
    if (changeDeploySpinning) {
      message.error('有部署正在进行中，请稍后添加')
      return
    }
    setIsModalOpen(true)
  }
  /**
   * 部署动作
   */
  const openDeploy = async (): Promise<void> => {
    const res = await checkDomainPermission({ code: domainCode })
    if (res.result === 1 && !res.data) {
      message.error('无权限')
      return
    }
    if (changeModuleList?.length === 0) {
      message.error('当前变更没有变更模块')
      return
    }
    // todo 需要检测是否有变更
    setIsDeployModalOpen(true)
  }
  /**
   * tab切换
   */
  const onTabChange = (key: string): void => {
    setActiveTabKey(key)
  }
  /**
   * 添加模块到变更
   */
  const onAddModule = async (values: AddModuleFormValues): Promise<boolean> => {
    if (!changeInfo.domainCode) {
      message.error('需要在业务域下添加')
      return false
    }
    try {
      const res = await createModuleUsingPost({
        ...values,
        changeId,
        businessDomainCode: changeInfo.domainCode,
      })
      if (res.result === 1) {
        message.success('添加模块成功')
        setIsModalOpen(false)
        setActiveTabKey('list')
        getChangeModuleList()
        return true
      } else {
        message.error(res.msg || '模块创建失败')
      }
    } catch (err) {
      console.error('onAddModule err', err)
      message.error('模块创建失败')
    }
    return false
  }

  const onClose = async (): Promise<void> => {
    try {
      const res = await closeChange({
        changeId: changeInfo?.changeId,
      })
      if (res.result === 1) {
        message.success('变更已关闭')
        location.reload()
      } else {
        message.error(res.msg || '关闭失败')
      }
    } catch (e) {
      message.success('关闭失败')
    }
  }
  const doChangeInfo = async (value: string, key: string): Promise<void> => {
    try {
      const res = await doChangeInfoRequest({
        changeId,
        updateValue: value,
        updateKey: key,
      })
      if (res.result === 1) {
        message.success('更新成功')
        if (key === 'changeName') {
          setChangeInfo((prevInfo) => ({
            ...prevInfo,
            changeName: value,
          }))
        }
      } else {
        message.error(res.msg || '更新失败')
      }
    } catch (e) {
      message.success('更新失败')
    }
  }

  useEffect(() => {
    if (!changeId) return
    if (isCreateFromLingZhu(changeId)) {
      Modal.info({
        title: '本变更为新版流程创建，上线等变更流程操作请转移至新版详情页操作',
        okText: '跳转至新版变更详情页',
        onOk() {
          jumpToNewChangeDetail(changeId, domainCode)
        },
      })
    }
    getChangeInfo()
    getChangeModuleList()
    getChangeDeployList()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [changeId])
  return (
    <>
      <Card loading={loading}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message={
              changeInfo.type === CHANGE_TYPE[2].value
                ? '【重要】本变更为回滚变更，不可以进行编辑变更操作'
                : '【重要】确认上线前所使用的组件包为正式版本，不允许beta或者alpha'
            }
            banner
          />

          <PageHeader
            title={
              <Text
                style={{ display: 'block', margin: '0 8px' }}
                editable={{
                  tooltip: '点击修改标题',
                  onChange: (value): void => {
                    if (!value.trim()) {
                      message.info('不能为空')
                      return
                    }
                    if (value === changeInfo.changeName) {
                      message.info('没有修改内容')
                      return
                    }
                    doChangeInfo(value, 'changeName')
                  },
                }}
              >
                {changeInfo.changeName || ''}
              </Text>
            }
            subTitle={<TeamJump teamId={changeInfo.teamId || ''} />}
            avatar={{
              src: 'https://cdnfile.corp.kuaishou.com/kc/files/a/adCanal/images/canal-logo-dark.09f71131fe34f329.png',
            }}
            tags={<ChangeStatus status={changeInfo.status}></ChangeStatus>}
            extra={[
              <Space>
                <CreatePublish
                  disabled={!canPublish || banOperate}
                  domainCode={changeInfo.domainCode}
                  changeId={changeInfo.changeId}
                  ignoreComponentCheck={ignoreComponentCheck}
                />
                {!!changeInfo && Number(changeInfo.status) === 0 && (
                  <Popconfirm
                    placement="top"
                    title={'确定关闭该变更吗？'}
                    onConfirm={onClose}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button type="primary" danger disabled={banOperate}>
                      关闭
                    </Button>
                  </Popconfirm>
                )}
              </Space>,
            ]}
          >
            <Descriptions size="small" column={4}>
              <Descriptions.Item label="变更ID">
                {changeInfo.changeId}
              </Descriptions.Item>
              <Descriptions.Item label="开发">
                {Array.isArray(changeInfo.developer) &&
                  changeInfo.developer.join('，')}
              </Descriptions.Item>
              <Descriptions.Item label="测试">
                {Array.isArray(changeInfo.tester) &&
                  changeInfo.tester.join(',')}
              </Descriptions.Item>
              <Descriptions.Item label="类型">
                {getTypeLabel(changeInfo.type)}
              </Descriptions.Item>
              <Descriptions.Item label="创建人">
                {changeInfo.createUser}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dateFormat(changeInfo.createTime)}
              </Descriptions.Item>
            </Descriptions>
          </PageHeader>
        </Space>

        <Card bordered={false}>
          <AddModuleForm
            visible={isModalOpen}
            setVisible={setIsModalOpen}
            onAdd={onAddModule}
          />
          <DeployModuleForm
            visible={isDeployModalOpen}
            setVisible={setIsDeployModalOpen}
            onDeploy={openDeployModule}
          />
          <Space>
            <Button
              type="primary"
              onClick={openDeploy}
              loading={changeDeploySpinning}
            >
              部署
            </Button>
            <Button onClick={openAddModule} disabled={banOperate}>
              新增模块
            </Button>
          </Space>
        </Card>
        <Card bordered={false}>
          <StyledTabs activeKey={activeTabKey} onChange={onTabChange}>
            {items.map((item) => {
              return (
                <Tabs.TabPane tab={item.label} key={item.key}>
                  {item.children}
                </Tabs.TabPane>
              )
            })}
          </StyledTabs>
        </Card>
      </Card>
    </>
  )
}
export default ChangeDetail

const StyledTabs = styled(Tabs)`
  &&& {
    overflow: visible;
  }
`
