import { ModuleTypeTag } from '@/components/module-type-tag'
import { dateFormat } from '@/pages/change/const'
import { type ModuleDetail } from '@/services/backend/models'
import {
  addModuleToChange,
  getAllOnlineModuleList,
} from '@/services/backend/module'
import {
  Button,
  Empty,
  Form,
  Input,
  Popconfirm,
  Table,
  message,
} from '@m-ui/react'
import type { ColumnsType, TablePaginationConfig } from '@m-ui/react/es/table'
import type { ReactNode } from 'react'
import { useEffect, useState } from 'react'

interface ModuleOnline extends ModuleDetail {
  /** content 模版内容 */
  module: {
    id: string
    name: string
    type: number
    businessDomainCode: string
  }
}

const ChangeAllModuleList = ({
  changeId,
  disableAction,
  domainCode,
  addCallback,
}: {
  changeId: string
  disableAction: boolean
  domainCode: string
  addCallback: () => void
}): JSX.Element => {
  const [allModuleList, setAllModuleList] = useState<ModuleOnline[]>([])
  const [pageNum, setPageNum] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const [form] = Form.useForm()
  const [formValues, setFormValues] = useState({})

  const getOnlineModuleList = async (): Promise<void> => {
    try {
      const params = {
        domainCode,
        pageNum,
        pageSize: 10,
        ...(formValues || {}),
      }
      const res = await getAllOnlineModuleList(params)
      if (res.result === 1 && Array.isArray(res.data.list)) {
        setAllModuleList(res.data.list)
        setTotal(res.data.total)
      }
    } catch (e) {
      console.log(e)
    }
  }
  useEffect(() => {
    getOnlineModuleList()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNum, formValues])
  const columns: ColumnsType<ModuleOnline> = [
    {
      title: '模块ID',
      dataIndex: 'module',
      key: 'id',
      render: (v): ReactNode => {
        return <div>{v.id}</div>
      },
    },
    {
      title: '模块名称',
      dataIndex: 'module',
      key: 'name',
      render: (v): ReactNode => {
        void v
        return <div>{v.name}</div>
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render(...[, record]): JSX.Element {
        return <ModuleTypeTag type={record.module.type} />
      },
    },
    {
      title: '线上版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '创建人',
      key: 'createUser',
      dataIndex: 'createUser',
    },
    {
      title: '创建时间',
      key: 'createTime',
      dataIndex: 'createTime',
      render: (createTime: number) => dateFormat(createTime),
    },
    {
      title: '更新人',
      key: 'updateUser',
      dataIndex: 'updateUser',
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      render: (updateTime: number) => dateFormat(updateTime),
    },
  ]
  async function addToChange(record: ModuleOnline): Promise<void> {
    try {
      const res = await addModuleToChange({
        changeId,
        domainCode,
        moduleId: record.module.id,
      })
      if (res.result === 1) {
        message.success('添加成功')
        addCallback?.()
      } else {
        message.error(res.msg || '添加失败')
      }
    } catch (e) {
      message.error('添加失败')
    }
  }
  /**
   * 页码变动
   * @param pagination
   */
  const onPageChange = (pagination: TablePaginationConfig): void => {
    if (typeof pagination.current !== 'undefined') {
      setPageNum(pagination.current)
    }
  }
  const locale = {
    emptyText: (
      <Empty
        description={
          '该业务域下没有已上线模块，可以点击新建；或当前搜索条件下无符合条件模块'
        }
      />
    ),
  }
  const onSearch = (values: {
    createUser: string
    changeName: string
  }): void => {
    setPageNum(1)
    setFormValues(values)
  }
  return (
    <>
      <Form
        name="customized_form_controls"
        layout="inline"
        onFinish={onSearch}
        form={form}
        style={{ marginBottom: '16px' }}
      >
        <Form.Item label="模块ID" name="moduleId">
          <Input placeholder="请输入模块ID" style={{ width: 300 }}></Input>
        </Form.Item>
        <Form.Item label="模块名称" name="name">
          <Input
            placeholder="请输入模块名称（模糊查找）"
            style={{ width: 300 }}
          ></Input>
        </Form.Item>
        <Form.Item label="创建人" name="createUser">
          <Input placeholder="请输入模块创建人" />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            搜索
          </Button>
          <Button
            style={{ margin: '0 8px' }}
            onClick={(): void => {
              form.resetFields()
              setFormValues({})
              setPageNum(1)
            }}
          >
            清空
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={[
          ...columns,
          {
            title: '操作',
            key: 'action',
            render: (...[, record]): ReactNode => {
              if (disableAction) return <></>
              return (
                <Popconfirm
                  title="确定要将此模块加入当前变更?"
                  onConfirm={(): void => {
                    addToChange(record)
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="link">加入当前变更</Button>
                </Popconfirm>
              )
            },
          },
        ]}
        locale={locale}
        dataSource={allModuleList}
        rowKey={'id'}
        pagination={{
          pageSize: 10,
          total: total,
          showSizeChanger: false,
          current: pageNum,
        }}
        onChange={onPageChange}
      />
    </>
  )
}

export default ChangeAllModuleList
