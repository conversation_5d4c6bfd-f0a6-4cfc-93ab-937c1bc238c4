import HelpTip from '@/components/help-tip'
import { Form, Input, Modal, Radio } from '@m-ui/react'

const DeployModuleForm = (props) => {
  const { visible, setVisible, onDeploy } = props
  const [form] = Form.useForm()

  const handleAdd = () => {
    form
      .validateFields()
      .then((values) => {
        form.resetFields()
        !!onDeploy && onDeploy(values)
      })
      .catch((info) => {
        console.log('Validate Failed:', info)
      })
  }
  return (
    <Modal
      visible={visible}
      title="部署变更"
      okText="确定"
      onCancel={() => {
        setVisible(false)
      }}
      onOk={handleAdd}
    >
      <Form form={form} layout="vertical" initialValues={{ stage: 'staging' }}>
        <Form.Item name="stage" label="请选择推送环境">
          <Radio.Group>
            <Radio value={'staging'}>staging</Radio>
            <Radio value={'prt'}>prt</Radio>
            <Radio value={'beta'}>beta</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="泳道" name="lane">
          <Input placeholder="请输入" />
        </Form.Item>
        <Form.Item
          label={
            <>
              前端泳道
              <HelpTip configKey={'frontLane'} />
            </>
          }
          name="frontLane"
        >
          <Input placeholder="请输入" />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default DeployModuleForm
