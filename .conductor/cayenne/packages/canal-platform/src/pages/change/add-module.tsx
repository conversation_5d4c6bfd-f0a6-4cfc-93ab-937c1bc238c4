import KoncallHelp from '@/components/koncall-help'
import AddTemplateFormItem from '@/pages/template/add-template-form-item'
import type { CreateModuleDto } from '@/services/backend/models'
import { ModuleType } from '@ad/canal-shared'
import { Form, Input, Modal, Radio } from '@m-ui/react'
import { memo, useCallback, type FC } from 'react'

/**
 * 添加模块表单属性
 */
export interface AddModuleFormProps {
  /**
   * 是否可见
   */
  visible: boolean
  /**
   * 设置是否可见
   * @param visible 是否可见
   */
  setVisible(visible: boolean): void
  /**
   * 添加事件
   * @param values 表单值
   * @returns 是否添加成功
   */
  onAdd(values: AddModuleFormValues): Promise<boolean>
}

/**
 * 添加模块表单值
 */
export type AddModuleFormValues = Pick<
  CreateModuleDto,
  'name' | 'templateId' | 'type' | 'containerType'
>

/**
 * 添加模块表单
 */
export const AddModuleForm: FC<AddModuleFormProps> = memo(
  (props): JSX.Element => {
    const { visible, setVisible, onAdd } = props
    const [form] = Form.useForm<AddModuleFormValues>()
    const handleOk = useCallback(async () => {
      try {
        const values = await form.validateFields()
        if (await onAdd(values)) {
          form.resetFields()
        }
      } catch (err) {
        console.error('add module err', err)
      }
    }, [form, onAdd])
    return (
      <Modal
        visible={visible}
        title="添加模块"
        okText="确定"
        onCancel={(): void => {
          setVisible(false)
        }}
        onOk={handleOk}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ type: 0, containerType: 0 }}
        >
          <Form.Item
            label="名称"
            name="name"
            rules={[{ required: true, message: '请输入模块名称' }]}
          >
            <Input placeholder="请输入模块名称" />
          </Form.Item>
          <Form.Item label="模版" name="templateId">
            <AddTemplateFormItem />
          </Form.Item>
          <Form.Item name="type" label="类型">
            <Radio.Group>
              <Radio value={ModuleType.PAGE}>页面</Radio>
              <Radio value={ModuleType.MODULE}>模块</Radio>
              <Radio value={ModuleType.GLOBAL}>
                全局
                <KoncallHelp question="全局模块" />
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="containerType" label="容器类型">
            <Radio.Group>
              <Radio value={0}>WEB</Radio>
              <Radio value={1}>H5</Radio>
              {/* <Radio value={2}>KRN</Radio> */}
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    )
  },
)
