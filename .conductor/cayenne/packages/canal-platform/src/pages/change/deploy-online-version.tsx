import { Button, message, Tooltip } from '@m-ui/react'
import React, { useState } from 'react'
import { deployOnlineVersionToTrunk } from '../../services/backend/change'
import {
  doStagingDeploy,
  getGlobalModuleDetail,
  getModuleDetail,
} from '../../services/backend/module'

interface Props {
  changeId: string
  domainCode: string
}

const DeployOnlineVersion: React.FC<Props> = ({ changeId, domainCode }) => {
  const [loading, setLoading] = useState<boolean>(false)
  const onClick = async (): Promise<void> => {
    setLoading(true)
    try {
      const res = await deployOnlineVersionToTrunk({
        changeId,
        domainCode,
      })
      if (res.result == 1) {
        if (Array.isArray(res.data)) {
          const globalModuleDetailRes = await getGlobalModuleDetail({
            domainCode,
          })
          const globalModuleId: string | undefined =
            globalModuleDetailRes.data?.id
          for (const component of res.data) {
            const detailRes = await getModuleDetail({
              id: component.moduleId,
              version: component.version,
            })
            if (detailRes.result === 1 && detailRes.data) {
              await doStagingDeploy({
                module: {
                  moduleId: component.moduleId,
                  version: component.version,
                  content: detailRes.data.content,
                  stage: 'staging',
                  frontLane: '',
                  lane: '',
                  globalModuleId:
                    globalModuleId === component.moduleId
                      ? undefined
                      : globalModuleId,
                },
                isNewDeploy: true,
              })
            }
          }
        }
        message.success('部署成功')
      }
    } catch (e) {
      message.success('部署失败')
    } finally {
      setLoading(false)
    }
  }
  return (
    <Tooltip title={'部署本次变更相关模块的线上版本到测试环境的主干'}>
      <Button type="primary" onClick={onClick} loading={loading}>
        部署线上版本到测试主干
      </Button>
    </Tooltip>
  )
}

export default DeployOnlineVersion
