import type { ModuleDetail } from '@/services/backend/models'
import {
  getAllModulesUsingPostByChangeId,
  getGlobalModuleDetail,
  getModuleDetail,
} from '@/services/backend/module'
import { findModuleVersionLogDetail } from '@/services/backend/module-version-log'
import { ModuleType } from '@ad/canal-shared'
import type { E2EGlobalSchema, E2ESchema } from '@ad/e2e-schema'
import { sleep } from '@kael/shared'
import { cloneDeep, isEqual } from 'lodash'
import { action, computed, makeObservable, observable, runInAction } from 'mobx'
import { DESIGNER_QUERY, IS_READONLY_MODE } from '../constants'
import { getObSchema, waitInitSchema } from '../services'
import { moduleContentToE2ESchema } from '../tools'
import { debugModeStore } from './debug-mode-store'

/**
 * Schema 仓库
 */
export class SchemaStore {
  /**
   * 模块详情初始值
   */
  private _moduleDetailInit: ModuleDetail | null = null

  /**
   * 模块详情初始值
   */
  public get moduleDetailInit(): ModuleDetail | null {
    return this._moduleDetailInit
  }

  /**
   * 等待设计器 Schema 初始值
   */
  public readonly waitDesignerSchemaInit: Promise<E2ESchema>

  /**
   * 最后保存的 Schema
   */
  private _lastSavedSchema: E2ESchema | null = null

  /**
   * 全局 Schema
   */
  private _globalSchema: E2EGlobalSchema | null = null

  /**
   * 全局 Schema
   */
  public get globalSchema(): E2EGlobalSchema | null {
    return this._globalSchema
  }

  /**
   * 可编辑
   */
  public get editable(): boolean {
    return !!this.moduleDetailInit?.canEdit
  }

  /**
   * 可保存
   */
  public get savable(): boolean {
    return !isEqual(getObSchema(), this._lastSavedSchema)
  }

  public constructor() {
    makeObservable<
      SchemaStore,
      '_moduleDetailInit' | '_lastSavedSchema' | '_globalSchema'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _moduleDetailInit: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _lastSavedSchema: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _globalSchema: observable.ref,
      savable: computed,
      setSavedSchema: action,
    })
    this.waitDesignerSchemaInit = (async (): Promise<E2ESchema> => {
      const md = await this._getModuleDetailByQuery()
      document.title = `大运河 - ${md.name}`
      runInAction(() => {
        this._moduleDetailInit = md
      })
      this._loogPollingGlobalSchema()
      const schema = await this._loadInitSchema(md)
      return debugModeStore.compareAndLoadSchemaFromLocal(schema)
    })()
    ;(async (): Promise<void> => {
      this.setSavedSchema(await waitInitSchema())
    })()

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(window as any).debugSchemaStore = this
    }
  }

  /**
   * 通过 URL Query 获取模块详情
   */
  private async _getModuleDetailByQuery(): Promise<ModuleDetail> {
    const { data } = await getModuleDetail({
      id: DESIGNER_QUERY.moduleId,
      version: DESIGNER_QUERY.moduleVersion,
    })
    return data
  }

  /**
   * 加载初始化 Schema
   * @param md 模块详情
   */
  private async _loadInitSchema(md: ModuleDetail): Promise<E2ESchema> {
    let { content: moduleContent } = md
    if (IS_READONLY_MODE && DESIGNER_QUERY.moduleVersionLogId) {
      const res = await findModuleVersionLogDetail({
        logId: DESIGNER_QUERY.moduleVersionLogId,
      })
      moduleContent = res.data.moduleContent
    }
    return moduleContentToE2ESchema(moduleContent)
  }

  /**
   * 加载全局 Schema
   */
  private async _loadGlobalSchema(): Promise<void> {
    const md = this._moduleDetailInit
    if (!md) return
    const { changeId, businessDomainCode } = md
    if (!changeId) return
    let content: string | undefined
    // 查看变更内有没有全局模块
    const allModulesRes = await getAllModulesUsingPostByChangeId({
      changeId,
    })
    if (allModulesRes.result === 1) {
      for (const item of allModulesRes.data.list) {
        if (item.type === ModuleType.GLOBAL) {
          const globalModelDetailRes = await getModuleDetail({
            id: item.id,
            version: `${item.version}`,
          })
          content = globalModelDetailRes.data.content
          break
        }
      }
    }
    if (!content) {
      // 获取业务域下的全局模块
      const globalModuleDetailRes = await getGlobalModuleDetail({
        domainCode: businessDomainCode,
      })
      if (globalModuleDetailRes.result === 1) {
        content = globalModuleDetailRes.data.content
      }
    }
    if (content) {
      runInAction(() => {
        this._globalSchema = JSON.parse(content)
      })
    }
  }

  /**
   * 轮询全局 Schema
   */
  private async _loogPollingGlobalSchema(): Promise<void> {
    for (;;) {
      try {
        await this._loadGlobalSchema()
      } catch (err) {
        console.error('轮询全局 Schema 失败:', err)
      }
      await sleep(30 * 1000) // 每 30 秒轮询一次
    }
  }

  /**
   * 获取设计器 Schema
   */
  public async getDesignerSchema(): Promise<E2ESchema> {
    const { content: moduleContent } = await this._getModuleDetailByQuery()
    return moduleContentToE2ESchema(moduleContent)
  }

  /**
   * 设置已保存的 Schema
   * @param schema Schema
   */
  public setSavedSchema(schema: E2ESchema): void {
    this._lastSavedSchema = cloneDeep(schema)
  }
}

/**
 * 单例：Schema 仓库
 */
export const schemaStore = new SchemaStore()
