import type { ComponentDetail } from '@/services/backend/models'
import { getMaterialPlatformPackageInfos } from '@/services/material/material-platform'
import { LocalStorageDataLoader } from '@/utils/storage'
import { LOCAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import type { E2ESchema } from '@ad/e2e-schema'
import type { RemoteComponentLibraryMaterialSchema } from '@kael/material-schema'
import type { MaterialLibInfo } from '@kael/material-utils'
import { decodeComponentType } from '@kael/schema-utils'
import { message } from '@m-ui/react'
import {
  assign,
  fromPairs,
  get,
  isArray,
  isEqual,
  isString,
  once,
} from 'lodash'
import { DESIGNER_QUERY, IS_DEBUG_MODE } from '../../constants'
import { debugComponentJsUrlMapLoader } from './debug-component-js-url-map-loader'

/**
 * 正则表达式：本地源
 */
const REG_EXP_LOCAL_ORIGIN = /^http(s?):\/\/localhost:\d*/

/**
 * 调试模式仓库
 */
export class DebugModeStore {
  /**
   * 原始的本地组件库 Schema 文件
   */
  private _rawLocalClSchema: RemoteComponentLibraryMaterialSchema | null = null

  /**
   * 业务组件 materialId -> 组件名 -> js urls
   */
  private _bizMaterialCompUrls = new Map<number, Map<string, string[]>>()

  /**
   * 获取到的 Schema 加载器
   */
  private _fetchedSchemaLoader = new LocalStorageDataLoader<E2ESchema | null>(
    `fetched-schema:${DESIGNER_QUERY.moduleId}:${DESIGNER_QUERY.moduleVersion}`,
    null,
  )

  /**
   * 编辑过的 Schema 加载器
   */
  private _editedSchemaLoader = new LocalStorageDataLoader<E2ESchema | null>(
    `edited-schema:${DESIGNER_QUERY.moduleId}:${DESIGNER_QUERY.moduleVersion}`,
    null,
  )

  public constructor() {
    assign(window, {
      debugModeStore: this,
    })
  }

  /**
   * 保存获取到的 Schema 文件到本地
   * @param schema Schema 文件
   */
  private _saveFetchedSchemaToLocal(schema: E2ESchema): void {
    this._fetchedSchemaLoader.save(schema)
  }

  /**
   * 从本地加载获取到的 Schema 文件
   */
  private _loadFetchedSchemaFromLocal(): E2ESchema | null {
    return this._fetchedSchemaLoader.load()
  }

  /**
   * 保存编辑过的 Schema 文件到本地
   * @param schema Schema 文件
   */
  public saveEditedSchemaToLocal(schema: E2ESchema): void {
    this._editedSchemaLoader.save(schema)
  }

  /**
   * 从本地加载编辑过的 Schema 文件
   */
  private _loadEditedSchemaFromLocal(): E2ESchema | null {
    return this._editedSchemaLoader.load()
  }

  /**
   * 比较并从本地加载 Schema 文件
   * @param schema 通过接口加载的 Schema 文件
   */
  public compareAndLoadSchemaFromLocal(schema: E2ESchema): E2ESchema {
    if (!IS_DEBUG_MODE) return schema
    const fetchedSchema = this._loadFetchedSchemaFromLocal()
    const editedSchema = this._loadEditedSchemaFromLocal()
    if (fetchedSchema && editedSchema && isEqual(schema, fetchedSchema)) {
      this._replaceLocalComponentCodes(editedSchema)
      return editedSchema
    }
    this._saveFetchedSchemaToLocal(schema)
    this.saveEditedSchemaToLocal(schema)
    return schema
  }

  /**
   * 替换本地组件代码，因为保存到 localStorage 后，下次本地服务可能会变更端口
   * @param schema Schema 文件
   */
  private _replaceLocalComponentCodes(schema: E2ESchema): void {
    const localOrigin = this._getLocalOrigin()
    if (!localOrigin) return
    const componentCodes = schema.componentCodes
    if (!componentCodes) return
    for (const key in componentCodes) {
      const [libName] = decodeComponentType(key)
      if (libName !== LOCAL_COMPONENT_LIB_NAME) continue
      const code = componentCodes[key]
      if (code.type !== 'low-code' && code.code.type !== 'multi') {
        if (isString(code.code.js)) {
          code.code.js = code.code.js.replace(REG_EXP_LOCAL_ORIGIN, localOrigin)
        } else {
          code.code.js = code.code.js.map((js) =>
            js.replace(REG_EXP_LOCAL_ORIGIN, localOrigin),
          )
        }
      }
    }
  }

  /**
   * 获取本地源
   */
  private _getLocalOrigin(): string | null {
    const { localCl } = DESIGNER_QUERY
    const m = localCl?.match(REG_EXP_LOCAL_ORIGIN)
    return m?.[0] || null
  }

  /**
   * 获取本地组件库信息
   */
  public getLocalComponentLibraryInfo = once(
    async (): Promise<MaterialLibInfo | null> => {
      if (!IS_DEBUG_MODE) return null
      const { localCl } = DESIGNER_QUERY
      if (!localCl) return null
      const ret = (await (
        await fetch(localCl)
      ).json()) as RemoteComponentLibraryMaterialSchema
      this._rawLocalClSchema = ret
      return {
        name: LOCAL_COMPONENT_LIB_NAME,
        schemaJson: {
          ...ret,
          name: LOCAL_COMPONENT_LIB_NAME,
          displayName: '本地组件',
        },
      }
    },
  )

  /**
   * 添加业务组件 js url
   * @param bizComp 业务组件
   */
  public addBizCompJsUrl(bizComp: ComponentDetail): void {
    if (!IS_DEBUG_MODE) return
    const { associatedComponentId, resourceUrl } = bizComp
    if (!associatedComponentId) return
    try {
      const [materialId, componentName] = JSON.parse(associatedComponentId) as [
        number,
        string,
      ]
      let m = this._bizMaterialCompUrls.get(materialId)
      if (!m) this._bizMaterialCompUrls.set(materialId, (m = new Map()))
      let urls = m.get(componentName)
      if (!urls) m.set(componentName, (urls = []))
      urls.push(resourceUrl)
    } catch (err) {
      console.log('addBizCompJsUrl error', err)
    }
  }

  /**
   * 生成并保存 js url 对照表
   */
  public async generateAndSaveJsUrlMap(): Promise<void> {
    if (!IS_DEBUG_MODE || !DESIGNER_QUERY.localCl) return
    const componentJsUrlMap: Record<string, string> = {}
    const materialIds = [...this._bizMaterialCompUrls.keys()]
    if (materialIds.length) {
      const { data: pkgInfos } = await getMaterialPlatformPackageInfos({
        ids: materialIds,
      })
      if (!isArray(pkgInfos)) {
        message.error(`物料中心接口出错`)
        console.error(`物料中心接口出错`, { pkgInfos })
      } else {
        const packageNameMap = fromPairs(
          pkgInfos.map((p) => [p.id, p.packageName]),
        )
        const clSchema = this._rawLocalClSchema
        if (clSchema) {
          const matchedMaterialId = materialIds.find(
            (materialId) => packageNameMap[materialId] === clSchema.name,
          )
          if (matchedMaterialId) {
            const compUrls = this._bizMaterialCompUrls.get(matchedMaterialId)
            for (const component of clSchema.components) {
              const jsUrl = get(component, 'code.code.js') as unknown as
                | string
                | string[]
              const bizJsUrls = compUrls?.get(component.type)
              if (bizJsUrls && isString(jsUrl)) {
                for (const bizJsUrl of bizJsUrls) {
                  componentJsUrlMap[bizJsUrl] = jsUrl
                }
              }
            }
          }
        }
      }
    }
    console.log('generateAndSaveJsUrlMap componentJsUrlMap', componentJsUrlMap)
    debugComponentJsUrlMapLoader.save(componentJsUrlMap)
  }
}

/**
 * 单例：调试模式仓库
 */
export const debugModeStore = new DebugModeStore()
