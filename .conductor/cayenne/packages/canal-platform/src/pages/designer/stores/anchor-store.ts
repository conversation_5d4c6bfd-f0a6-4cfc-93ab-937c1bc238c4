import type {
  ComponentPropPathSearchAnchor,
  ComponentPropTypeAPISearchAnchor,
  ComponentPropTypeOtherSearchAnchor,
  ComponentPropTypeSearchAnchor,
  ComponentSearchAnchor,
  LinkageSearchAnchor,
  SearchAnchor,
} from '@/stores/search-store'
import {
  CanalRootComponentField,
  isBindableExpression,
} from '@ad/e2e-schema-utils'
import { SkeletonSymbol, type Skeleton } from '@kael/designer-core'
import {
  PropertyServiceSymbol,
  type PropertyService,
} from '@kael/designer-property'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { DESIGNER_QUERY } from '../constants'
import {
  apiDetailStore,
  bindIIFEStore,
  bindPropStore,
} from '../extensions/property'
import { editBackModelModalBtnRef } from '../extensions/property/components/property-base-tab/edit-back-model-modal-btn'
import { editFrontModelModalBtnRef } from '../extensions/property/components/property-base-tab/edit-front-model-modal-btn'
import { otherComponentEffectEditorRef } from '../extensions/property/components/property-base-tab/property-base-tab-other-comp'
import { trackListEntryRef } from '../extensions/property/components/property-base-tab/track-list-entry'
import type { E2ESchemaService } from '../services'
import { selectComponentAndWait } from '../tools'

/**
 * 锚点仓库
 */
export class AnchorStore {
  /**
   * 跳转初始化锚点
   */
  public goToInitAnchor(): void {
    const { anchorStr } = DESIGNER_QUERY
    if (anchorStr) {
      this.goToAnchor(JSON.parse(anchorStr))
    }
  }

  /**
   * 跳转锚点
   * @param anchor 锚点
   */
  public goToAnchor(anchor: SearchAnchor): void {
    // console.log('CanalSearchService goToAnchor', anchor)
    switch (anchor.type) {
      case 'component': {
        this._goToComponentAnchor(anchor)
        break
      }
      case 'linkage': {
        this._goToLinkageAnchor(anchor)
      }
    }
  }

  /**
   * 跳转组件锚点
   * @param anchor 锚点
   */
  private async _goToComponentAnchor(
    anchor: ComponentSearchAnchor,
  ): Promise<void> {
    const comp = await selectComponentAndWait(anchor.componentId)
    if (!comp) return
    if ('propType' in anchor) {
      this._goToComponentPropTypeAnchor(anchor)
    } else {
      this._goToComponentPropPathAnchor(anchor)
    }
  }

  /**
   * 跳转属性类型锚点
   * @param anchor 锚点
   */
  private _goToComponentPropTypeAnchor(
    anchor: ComponentPropTypeSearchAnchor,
  ): void {
    switch (anchor.propType) {
      case 'api': {
        this._goToComponentPropAPITypeAnchor(anchor)
        break
      }
      default: {
        this._goToComponentPropOtherTypeAnchor(anchor)
        break
      }
    }
  }

  /**
   * 跳转属性类型其他锚点
   * @param anchor 锚点
   */
  private _goToComponentPropOtherTypeAnchor(
    anchor: ComponentPropTypeOtherSearchAnchor,
  ): void {
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const comp = schemaService.findComponentById(anchor.componentId)!
    switch (anchor.propType) {
      case 'value': {
        bindPropStore.openDrawer({
          componentId: comp.id,
          propPath: ['value'],
          propName: '值',
          frontExprEnabled: false,
        })
        break
      }
      case 'effect': {
        otherComponentEffectEditorRef?.open()
        break
      }
      case 'track': {
        trackListEntryRef?.open()
        break
      }
      case 'iife': {
        bindIIFEStore.openDrawer()
        break
      }
      case 'data': {
        bindPropStore.openDrawer({
          componentId: comp.id,
          propPath: [CanalRootComponentField.BACK_DATA],
          propName: '数据',
          frontExprEnabled: false,
        })
        break
      }
      case 'frontModel': {
        editFrontModelModalBtnRef?.open()
        break
      }
      case 'backModel': {
        editBackModelModalBtnRef?.open()
        break
      }
    }
  }

  /**
   * 跳转属性类型接口锚点
   * @param anchor 锚点
   */
  private _goToComponentPropAPITypeAnchor(
    anchor: ComponentPropTypeAPISearchAnchor,
  ): void {
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const comp = schemaService.findComponentById(anchor.componentId)!
    const api = comp.apis?.find((a) => a.id === anchor.apiId)
    if (!api) return
    apiDetailStore.openModal(api)
  }

  /**
   * 跳转属性路径锚点
   * @param anchor 锚点
   */
  private _goToComponentPropPathAnchor(
    anchor: ComponentPropPathSearchAnchor,
  ): void {
    if (anchor.propNamePath.length) {
      const propertyService = getInject<PropertyService>(PropertyServiceSymbol)
      propertyService.setActiveTab(
        `${anchor.componentId}.${anchor.propNamePath[0]}`,
      )
    }
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    const expr = schemaService.getComponentPropExpression(
      anchor.componentId,
      anchor.propPath,
    )
    if (isBindableExpression(expr)) {
      bindPropStore.openDrawer({
        componentId: anchor.componentId,
        propPath: anchor.propPath,
        propName: anchor.propNamePath[anchor.propNamePath.length - 1],
      })
    }
  }

  /**
   * 跳转联动锚点
   * @param anchor 锚点
   */
  private _goToLinkageAnchor(anchor: LinkageSearchAnchor): void {
    void anchor
    const skeleton = getInject<Skeleton>(SkeletonSymbol)
    if (skeleton.leftPanelHandler) {
      skeleton.leftPanelHandler.tabBar.currentIndex = 2
    }
  }
}

/**
 * 单例锚点仓库
 */
export const anchorStore = new AnchorStore()
