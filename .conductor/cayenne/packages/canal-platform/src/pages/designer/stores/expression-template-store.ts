import { findExpressionTemplates } from '@/services/backend/expression-template'
import type { ExpressionTemplate } from '@/services/backend/models'
import {
  BackExpressionType,
  FrontExpressionType,
  getEnumValues,
} from '@ad/canal-shared'
import { fromPairs } from 'lodash'
import { makeObservable, observable, runInAction } from 'mobx'
import { DESIGNER_QUERY } from '../constants'

/**
 * 表达式模板仓库
 */
export class ExpressionTemplateStore {
  /**
   * 表达式模板按类型
   */
  public readonly expressionTemplateByType = fromPairs(
    [
      ...getEnumValues(BackExpressionType),
      ...getEnumValues(FrontExpressionType),
    ].map((t) => [t, [] as ExpressionTemplate[]]),
  ) as Record<BackExpressionType | FrontExpressionType, ExpressionTemplate[]>

  public constructor() {
    makeObservable<ExpressionTemplateStore>(this, {
      expressionTemplateByType: observable,
    })

    for (const t of [
      ...getEnumValues(BackExpressionType),
      ...getEnumValues(FrontExpressionType),
    ]) {
      this.updateByType(t)
    }
  }

  /**
   * 通过类型更新
   * @param expressionType 表达式类型
   */
  public async updateByType(
    expressionType: BackExpressionType | FrontExpressionType,
  ): Promise<void> {
    const ret = await findExpressionTemplates({
      domainCode: DESIGNER_QUERY.domainCode,
      expressionType,
    })
    runInAction(() => {
      this.expressionTemplateByType[expressionType] = ret.data
    })
  }

  /**
   * 获取名称选项
   * @param expressionType 表达式类型
   */
  public getNameOptions(
    expressionType: BackExpressionType | FrontExpressionType,
  ): { label: string; value: string }[] {
    return this.expressionTemplateByType[expressionType].map((t) => ({
      label: t.name,
      value: t.name,
    }))
  }
}

/**
 * 单例：表达式模板仓库
 */
export const expressionTemplateStore = new ExpressionTemplateStore()
