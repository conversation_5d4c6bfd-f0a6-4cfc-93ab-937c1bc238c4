import { CoeditStore } from '@/stores/coedit-store'
import { isEqual } from 'lodash'
import { DESIGNER_QUERY } from '../constants'
import { schemaStore } from './schema-store'

/**
 * 单例：设计器协同编辑仓库
 */
export const designerCoeditStore = new CoeditStore({
  moduleId: DESIGNER_QUERY.moduleId,
  moduleVersion: Number(DESIGNER_QUERY.moduleVersion),
  mode: DESIGNER_QUERY.mode,
  savable(): boolean {
    return schemaStore.savable
  },
  async initSchemaIsDirty(): Promise<boolean> {
    return !isEqual(
      await schemaStore.waitDesignerSchemaInit,
      await schemaStore.getDesignerSchema(),
    )
  },
})
