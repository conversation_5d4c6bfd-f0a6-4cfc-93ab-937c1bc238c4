import { SearchStore } from '@/stores/search-store'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import type { E2ESchema } from '@ad/e2e-schema'
import { action, makeObservable, observable, reaction } from 'mobx'
import { DESIGNER_QUERY } from '../constants'
import { getObSchema } from '../services/e2e-schema-service'
import { getMergedComponentMaterialSchema } from '../tools'

/**
 * 设计器搜索仓库
 */
export class DesignerSearchStore extends SearchStore {
  /**
   * 搜索面板是否可见
   */
  private _searchPanelIsVisible = false

  /**
   * 搜索面板是否可见
   */
  public get searchPanelIsVisible(): typeof this._searchPanelIsVisible {
    return this._searchPanelIsVisible
  }

  /**
   * 搜索面板交叉观察器
   */
  public readonly searchPanelIntersectionObserver = new IntersectionObserver(
    action((entries) => {
      for (const entry of entries) {
        this._searchPanelIsVisible = entry.isIntersecting
      }
    }),
  )

  /**
   * 搜索中的 Schema
   */
  private _searchingSchema: E2ESchema | null = null

  public constructor() {
    super()
    makeObservable<DesignerSearchStore, '_searchPanelIsVisible'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _searchPanelIsVisible: observable,
    })
    reaction(
      () => {
        const { keyword, searchPanelIsVisible } = this
        const schema = getObSchema()
        return { keyword, searchPanelIsVisible, schema }
      },
      async ({ keyword, searchPanelIsVisible, schema }) => {
        if (!searchPanelIsVisible || !schema) return
        const { currentTask } = this
        if (
          currentTask &&
          currentTask.keyword === keyword &&
          this._searchingSchema === schema
        ) {
          return
        }
        this.cancelSearch()
        if (!keyword) return
        if (this._searchingSchema !== schema) {
          this._searchingSchema = schema
          await this.$rpcEndpoint.call.deleteSchemaCache([
            DESIGNER_QUERY.moduleId,
          ])
        }
        this.search(keyword, [DESIGNER_QUERY.moduleId])
      },
    )

    if (process.env.NODE_ENV === 'development') {
      Object.assign(window, {
        designerSearchStore: this,
      })
    }
  }

  /**
   * 获取 Schema
   * @param moduleId 模块 ID
   */
  protected async handleGetSchema(moduleId: string): Promise<E2ESchema | null> {
    // console.log('DesignerSearchStore::handleGetSchema', {
    //   moduleId,
    // })
    void moduleId
    return JSON.parse(JSON.stringify(this._searchingSchema))
  }

  /**
   * 处理【获取组件物料 Schema】
   * @param moduleId 模块 ID
   * @param type 组件类型
   * @param version 版本
   */
  protected async handleGetComponentMaterialSchema(
    moduleId: string,
    type: string,
    version?: string,
  ): Promise<E2ERemoteComponentMaterialSchema | null> {
    // console.log('DesignerSearchStore::handleGetComponentMaterialSchema', {
    //   moduleId,
    //   type,
    //   version,
    // })
    void moduleId
    return getMergedComponentMaterialSchema(type, version)
  }
}

/**
 * 单例：设计器搜索仓库
 */
export const designerSearchStore = new DesignerSearchStore()
