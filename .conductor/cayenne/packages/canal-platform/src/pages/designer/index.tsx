import '@kael/designer-core/styles/index.css'
import { loader } from '@monaco-editor/react'
import { useEffect, useRef, type FC } from 'react'
import styled from 'styled-components'
import { createDesignerApp } from './create-designer-app'
import { CanvasPreviewer } from './extensions/canvas/canvas-previewer'
import {
  ApplyExpressionTemplateModal,
  BindIIFEDrawer,
  BindPropDrawer,
  SaveExpressionTemplateModal,
} from './extensions/property'

/**
 * 设计器
 */
export const Designer: FC = () => {
  const refContainer = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (!refContainer.current) {
      return
    }
    const app = createDesignerApp(refContainer.current)
    loader.config({
      paths: {
        vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.50.0/min/vs',
      },
    })
    loader.init()
    return () => {
      app.dispose()
    }
  }, [])

  return (
    <>
      <Container ref={refContainer} />
      <BindPropDrawer />
      <BindIIFEDrawer />
      <ApplyExpressionTemplateModal />
      <SaveExpressionTemplateModal />
      <CanvasPreviewer />
    </>
  )
}

export default Designer

const Container = styled.div`
  width: 100vw;
  height: 100vh;

  // 降低导航栏 z-index，避免挡住 模态框
  .kael-skeleton .kael-skeleton-top-area {
    z-index: 999;
  }

  // 导航栏中间区域居中展示
  .navigation-area-middle {
    justify-content: center;
  }

  // 避免名字重叠
  .assets-list
    .assets-list-content-item
    .assets-list-content-item-picture-buttom {
    display: block;
  }
`
