/**
 * 设计器 URL Query
 */
export interface DesignerQuery {
  /**
   * （业务）域代码
   */
  domainCode: string
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 模块版本
   */
  moduleVersion: string
  /**
   * 模式
   */
  mode?: 'debug' | 'readonly'
  /**
   * 本地组件库（物料 Schema 文件链接）
   */
  localCl?: string
  /**
   * 本地预览器（本地业务页面 URL）
   */
  localPreviewer?: string
  /**
   * 自动部署标识
   */
  autoDeploy?: 'beta' | 'prt'
  /**
   * 模块版本日志 ID
   */
  moduleVersionLogId?: string
  /**
   * 锚点（JSON）字符串
   */
  anchorStr?: string
}

export type CollaborativeModel = 'single' | 'multiple'
