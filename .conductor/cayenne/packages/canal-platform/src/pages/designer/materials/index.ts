import {
  getAllComponentsUsingPost,
  getComponentDetail,
} from '@/services/backend/component'
import type { ComponentDetail } from '@/services/backend/models'
import { BIZ_COMPONENT_LIB_NAME } from '@ad/canal-react-runtime'
import { bizComp2ComponentMaterialSchema, filterNotNil } from '@ad/canal-shared'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import {
  INTERNAL_COMPONENT_LIB_NAME,
  INTERNAL_COMPONENT_LIB_PORT,
  INTERNAL_REACT_COMPONENT_LIB_NAME,
  OLD_BIZ_COMPONENT_LIB_NAME,
  getLatestMaterialLibSchemaUrl,
} from '@ad/e2e-material-schema-utils'
import type { E2ESchema } from '@ad/e2e-schema'
import type { MaterialServiceConfig } from '@kael/designer-service'
import type {
  CLMSGroup,
  RemoteComponentLibraryMaterialSchema,
} from '@kael/material-schema'
import {
  getLocalMaterialLibSchemaUrl,
  type MaterialLibInfo,
  type MaterialLibInfoWithJson,
} from '@kael/material-utils'
import {
  decodeComponentType,
  dfsGenComponentDetailBySchema,
} from '@kael/schema-utils'
import { message } from '@m-ui/react'
import { fromPairs, once, uniqBy } from 'lodash'
import { DESIGNER_QUERY } from '../constants'
import { debugModeStore } from '../stores/debug-mode-store'

/**
 * 大运河业务组件库物料 Schema 信息
 */
export interface CanalBizComponentLibraryMaterialSchemaInfo
  extends MaterialLibInfoWithJson {
  /**
   * 原始组件信息
   */
  rawComponents: ComponentDetail[]
}

/**
 * 获取组件库（物料）信息
 */
export const getMaterialLibInfo: MaterialServiceConfig['getMaterialLibInfo'] =
  once(async (schema) => {
    const ret: MaterialLibInfo[] = filterNotNil(
      await Promise.all([
        getInternalComponentLibInfo(),
        getBizComponentLibInfo(),
        getOldBizComponentLibInfo(schema as E2ESchema),
        debugModeStore.getLocalComponentLibraryInfo(),
      ]),
    )
    await debugModeStore.generateAndSaveJsUrlMap()
    console.log('getMaterialLibInfo ret', ret)
    return ret
  })

/**
 * 获取内部组件库（物料）信息
 */
export const getInternalComponentLibInfo = once(
  async (): Promise<MaterialLibInfoWithJson> => {
    return {
      name: INTERNAL_COMPONENT_LIB_NAME,
      schemaJson: await (
        await fetch(await getInternalComponentLibSchemaUrl())
      ).json(),
    }
  },
)

/**
 * 获取内部组件库（物料）Schema URL
 */
export async function getInternalComponentLibSchemaUrl(): Promise<string> {
  if (process.env.NODE_ENV === 'development') {
    return getLocalMaterialLibSchemaUrl(INTERNAL_COMPONENT_LIB_PORT)
  }
  const env = process.env.KMI_ENV === 'staging' ? 'staging' : 'production'
  return await getLatestMaterialLibSchemaUrl(
    INTERNAL_REACT_COMPONENT_LIB_NAME,
    env,
  )
}

/**
 * 获取业务组件库（物料）信息
 */
const getBizComponentLibInfo = once(async () => {
  const ret = await getBizComponentLibInfoByCurrentDomainCode()
  addComponentsToDebugModeStore(ret.rawComponents)
  return ret
})

/**
 * 通过当前（业务）域代码获取业务组件库（物料）信息
 */
const getBizComponentLibInfoByCurrentDomainCode = once(() =>
  getBizComponentLibInfoByDomainCode(DESIGNER_QUERY.domainCode),
)

/**
 * 通过（业务）域代码获取业务组件库（物料）信息
 *
 * 无副作用，可以脱离设计器使用
 * @param domainCode （业务）域代码
 */
export async function getBizComponentLibInfoByDomainCode(
  domainCode: string,
): Promise<CanalBizComponentLibraryMaterialSchemaInfo> {
  const {
    data: { list: rawComponents },
  } = await getAllComponentsUsingPost({
    businessDomainCode: domainCode,
    pageNum: 1,
    pageSize: Number.MAX_SAFE_INTEGER,
  })
  const groupMap = new Map<string, CLMSGroup>()
  const components: E2ERemoteComponentMaterialSchema[] = rawComponents.map(
    transformBizComponent,
  )
  for (let i = 0; i < rawComponents.length; i++) {
    const rawComp = rawComponents[i]
    if (rawComp.isDeleted) continue
    const groupName = rawComp.group || '未分组'
    let group = groupMap.get(groupName)
    if (!group) {
      groupMap.set(
        groupName,
        (group = {
          name: groupName,
          items: [],
        }),
      )
    }
    ;(group.items as string[]).push(components[i].type)
  }
  return {
    name: BIZ_COMPONENT_LIB_NAME,
    schemaJson: {
      schemaVersion: '0.0.1',
      name: BIZ_COMPONENT_LIB_NAME,
      version: '0.0.1',
      displayName: '业务组件',
      components,
      groups: [...groupMap.values()],
      dependencies: {},
    },
    rawComponents,
  }
}

/**
 * 转换业务组件
 * @param bizComp 业务组件
 */
function transformBizComponent(
  bizComp: ComponentDetail,
): E2ERemoteComponentMaterialSchema {
  try {
    return bizComp2ComponentMaterialSchema(bizComp)
  } catch (err) {
    console.error('transformBizComponent err', err, bizComp)
    message.error(`业务组件格式不正确：${bizComp.name}（${bizComp.id}）`)
    throw err
  }
}

/**
 * 添加组件到调试模式仓库
 * @param rawComponents 原始组件信息
 */
function addComponentsToDebugModeStore(rawComponents: ComponentDetail[]): void {
  for (const rawComp of rawComponents) {
    debugModeStore.addBizCompJsUrl(rawComp)
  }
}

/**
 * 获取老业务组件库（物料）信息
 * @param schema 搭建 Schema
 */
async function getOldBizComponentLibInfo(
  schema: E2ESchema,
): Promise<MaterialLibInfo> {
  const { schemaJson: bizMaterialSchema } =
    await getBizComponentLibInfoByCurrentDomainCode()
  const ret = await getOldBizComponentLibInfoByBizSchema(
    bizMaterialSchema,
    schema,
  )
  addComponentsToDebugModeStore(ret.rawComponents)
  return ret
}

/**
 * 通过业务 Schema 获取老业务组件库（物料）信息
 *
 * 无副作用，可以脱离设计器使用
 * @param bizMaterialSchema 业务物料 Schema
 * @param schema 搭建 Schema
 */
export async function getOldBizComponentLibInfoByBizSchema(
  bizMaterialSchema: RemoteComponentLibraryMaterialSchema,
  schema: E2ESchema,
): Promise<CanalBizComponentLibraryMaterialSchemaInfo> {
  const latestComponentMap: Record<string, E2ERemoteComponentMaterialSchema> =
    fromPairs(bizMaterialSchema.components.map((comp) => [comp.type, comp]))
  const oldComponentIdAndVersions: [string, string][] = []
  for (const cd of dfsGenComponentDetailBySchema(schema)) {
    const componentType = cd.component.type
    const [, compType] = decodeComponentType(componentType)
    const latestCompMaterial = latestComponentMap[compType] as
      | E2ERemoteComponentMaterialSchema
      | undefined
    const compCode = schema.componentCodes?.[componentType]
    if (
      !latestCompMaterial?.id ||
      !compCode?.version ||
      compCode.version === latestCompMaterial.version
    ) {
      continue
    }
    oldComponentIdAndVersions.push([latestCompMaterial.id, compCode.version])
  }
  const rawComponents: ComponentDetail[] = await Promise.all(
    uniqBy(oldComponentIdAndVersions, (idAndVersion) =>
      JSON.stringify(idAndVersion),
    ).map(async ([id, version]) => {
      const { data: rawComp } = await getComponentDetail({
        id,
        version,
      })
      return rawComp
    }),
  )
  const components: E2ERemoteComponentMaterialSchema[] = rawComponents.map(
    transformBizComponent,
  )
  return {
    name: OLD_BIZ_COMPONENT_LIB_NAME,
    schemaJson: {
      schemaVersion: '0.0.1',
      name: OLD_BIZ_COMPONENT_LIB_NAME,
      version: '0.0.1',
      displayName: '业务组件 old',
      components,
      groups: [
        {
          name: '所有',
          items: components.map((comp) => comp.type),
        },
      ],
      dependencies: {},
    },
    rawComponents,
  }
}
