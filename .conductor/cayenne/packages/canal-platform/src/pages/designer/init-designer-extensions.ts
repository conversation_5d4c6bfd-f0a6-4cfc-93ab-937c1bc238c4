import { IMPLEMENTS_PROPS } from '@ad/e2e-material-schema-utils'
import {
  AbstractSkeleton,
  init as initCore,
  SkeletonSymbol,
} from '@kael/designer-core'
import type { StorageService } from '@kael/designer-service'
import {
  init as initService,
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
  StorageServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import type { ImplementsProps } from '@kael/material-utils'
import { initAssets } from './extensions/assets'
import { initCanvas } from './extensions/canvas'
import { initKeybindings } from './extensions/keybindings'
import { initLinkagePanel } from './extensions/linkage-panel'
import { initNavigation } from './extensions/navigation'
import { initOutline } from './extensions/outline'
import { initProperty } from './extensions/property'
import { initSaveLogPanel } from './extensions/save-log-panel'
import { initSearchPanel } from './extensions/search-panel'
import { initSettingPanel } from './extensions/setting-panel'
import { getMaterialLibInfo } from './materials'
import {
  CanalMaterialService,
  CanalNodeControlService,
  CanalStateService,
  e2eSchemaServicePlugins,
  type E2ESchemaService,
} from './services'
import { anchorStore } from './stores/anchor-store'
import { schemaStore } from './stores/schema-store'

/**
 * 初始化设计器各个扩展
 */
export function initDesignerExtensions(): void {
  initCore({
    skeleton: {
      rightArea: {
        collapseMinSize: 0,
      },
    },
    restore: async (): Promise<void> => {
      const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
      const storageService = getInject<StorageService>(StorageServiceSymbol)
      const skeleton = getInject<AbstractSkeleton>(SkeletonSymbol)
      const stateService = getInject<StateService>(StateServiceSymbol)
      const layoutData = storageService.getData('designer-layout', '{}')
      skeleton.setSerializedLayoutData(layoutData)
      const schema = await schemaStore.waitDesignerSchemaInit
      console.log('init schema', schema)
      schemaService.initialize(schema)
      const state = storageService.getData('designer-state', {})
      stateService.initialize(state)
      stateService.setSelectedComponentId(schema.view.id)
      anchorStore.goToInitAnchor()

      if (process.env.NODE_ENV === 'development') {
        Object.assign(window, {
          debugSchemaService: schemaService,
          debugStateService: stateService,
          debugSkeleton: skeleton,
        })
      }
    },
  })
  initService({
    schemaService: {
      scene: 'lowCode',
      plugins: e2eSchemaServicePlugins,
    },
    stateService: {
      stateServiceClass: CanalStateService,
    },
    materialService: {
      materialServiceClass: CanalMaterialService,
      getMaterialLibInfo,
      implementsProps: IMPLEMENTS_PROPS as ImplementsProps,
    },
    nodeControlService: {
      nodeControlServiceClass: CanalNodeControlService,
    },
  })
  initKeybindings()

  initNavigation()
  initAssets()
  initOutline()
  initCanvas()
  initProperty()
  initLinkagePanel()
  initSearchPanel()
  initSaveLogPanel()
  initSettingPanel()
}
