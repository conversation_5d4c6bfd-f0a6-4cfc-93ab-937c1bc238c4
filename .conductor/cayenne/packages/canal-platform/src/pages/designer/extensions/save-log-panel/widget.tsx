import { ReactWidget } from '@kael/designer-core'
import { injectable, postConstruct, register } from '@kael/di'
import React from 'react'
import { createGlobalStyle } from 'styled-components'
import { SaveLogPanel } from './components/save-log-panel'
import IMG_SAVE_LOG_PANEL from './save-log-panel.svg'

/**
 * 保存记录面板控件
 */
@injectable()
export class SaveLogPanelWidget extends ReactWidget {
  public static readonly ID = 'save-log-panel'
  public static readonly LABEL = '保存记录面板'

  @postConstruct()
  public init(): void {
    this.id = SaveLogPanelWidget.ID
    this.title.label = SaveLogPanelWidget.LABEL
    this.title.caption = SaveLogPanelWidget.LABEL
    this.title.iconClass = 'tab-icon-save-log-panel'
    this.title.closable = true
  }

  public render(): React.ReactNode {
    return (
      <>
        <GlobalStyle />
        <SaveLogPanel />
      </>
    )
  }
}

/**
 * 注册保存记录面板控件
 */
export function registerSaveLogPanelWidget(): void {
  register({
    token: SaveLogPanelWidget.token,
    useClass: SaveLogPanelWidget,
  })
}

const GlobalStyle = createGlobalStyle`
  .tab-icon-save-log-panel {
    mask: url('${IMG_SAVE_LOG_PANEL}');
    -webkit-mask: url('${IMG_SAVE_LOG_PANEL}');
  }
`
