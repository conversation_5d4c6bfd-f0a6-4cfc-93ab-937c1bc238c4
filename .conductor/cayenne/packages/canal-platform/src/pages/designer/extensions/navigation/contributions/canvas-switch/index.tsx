import {
  NavigationContribution,
  type NavigationContributionPosition,
} from '@kael/designer-navigation'
import { injectable } from '@kael/di'
import { type ReactNode } from 'react'
import { CanvasSwitch } from './canvas-switch'

/**
 * 画布切换贡献点
 */
@injectable()
export class CanvasSwitchContribution extends NavigationContribution {
  public id = 'navigation-canvas-switch'
  public rank = 1
  public position: NavigationContributionPosition = 'middle'

  public render(): ReactNode {
    return <CanvasSwitch />
  }
}
