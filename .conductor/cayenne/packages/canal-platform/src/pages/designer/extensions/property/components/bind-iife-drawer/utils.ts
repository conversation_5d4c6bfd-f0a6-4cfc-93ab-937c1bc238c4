import {
  asyncBackJSExprToES5ByInternalMain,
  asyncBackTSExprToJSByInternalMain,
} from '@/utils'
import {
  BackExpressionType,
  type BackExpressionTypeJSTS,
} from '@ad/canal-shared'
import type { BindIIFEAction, E2ESchemaExpressionJS } from '@ad/e2e-schema'
import { getBindIIFEActionsFromCode } from '@ad/e2e-schema-utils'

/**
 * 绑定立即执行（前端动作）表单值
 */
export interface BindIIFEFormValues {
  /**
   * 接口 ID
   */
  apiId?: string[]
  /**
   * 表达式类型
   */
  exprType: BackExpressionTypeJSTS
  /**
   * js 表达式
   */
  jsExpr: string
  /**
   * js 表达式
   */
  tsExpr: string
  /**
   * 动作
   */
  actions: BindIIFEAction[]
}

/**
 * iife 代码包装
 * @param actions 动作
 * @param code js 或 ts 代码
 */
export function iifeCodeWrap(actions: BindIIFEAction[], code: string): string {
  return `export function __main(...args) {
  const actions = JSON.parse(${JSON.stringify(JSON.stringify(actions))})
  let actionIdxes = this.default.apply(this, args)
  if (typeof actionIdxes === 'number') {
    actionIdxes = [actionIdxes]
  }
  const getByOutput = (v, isPath) => isPath ? this.output[v] : v
  const actionsExpr = {
    type: 'actions',
    fns: actions.map((action) => {
      switch (action.type) {
        case 'open-url': {
          return {
            type: 'open-url',
            url: getByOutput(action.url, action.urlIsPath),
            inPlace: action.inPlace,
          }
        }
        case 'exec-external-fn': {
          return {
            type: 'bind',
            fn: {
              type: 'get-data',
              path: action.fnPath,
            },
            args: [
              {
                type: 'static',
                value: getByOutput(action.arg0, action.arg0IsPath),
              },
            ],
          }
        }
        case 'exec-effect': {
          return {
            type: 'bind',
            fn: {
              type: 'js',
              code: action.expr.code,
            },
            args: [
              {
                type: 'js',
                code: 'ctx',
              },
              {
                type: 'static',
                value: getByOutput(action.arg0, action.arg0IsPath),
              },
            ]
          }
        }
      }
    })
  }
  actionsExpr.fns = actionIdxes.map(idx => actionsExpr.fns[idx])
  return actionsExpr
}
${code}`
}

/**
 * 编码表单值为 JS 表达式
 * @param fv 表单值
 */
export async function encodeBindIIFEFormValues2JSExpr(
  fv: BindIIFEFormValues,
): Promise<E2ESchemaExpressionJS> {
  if (fv.exprType === BackExpressionType.TYPESCRIPT) {
    const code = iifeCodeWrap(fv.actions, fv.tsExpr)
    return {
      type: 'js',
      code: await asyncBackTSExprToJSByInternalMain(code),
      codeTS: fv.tsExpr,
    }
  } else {
    const code = iifeCodeWrap(fv.actions, fv.jsExpr)
    return {
      type: 'js',
      code: await asyncBackJSExprToES5ByInternalMain(code),
      codeES: fv.jsExpr,
    }
  }
}

/**
 * 解码 JS 表达式为表单值
 * @param expr JS 表达式
 */
export function decodeJSExpr2BindIIFEFormValues(
  expr: E2ESchemaExpressionJS,
): Partial<BindIIFEFormValues> {
  const actions = getBindIIFEActionsFromCode(expr.code)
  const ret: Partial<BindIIFEFormValues> = {
    exprType: expr.codeTS
      ? BackExpressionType.TYPESCRIPT
      : BackExpressionType.JAVASCRIPT,
  }
  if (actions) ret.actions = actions
  if (expr.codeTS) {
    ret.tsExpr = expr.codeTS
  } else {
    ret.jsExpr = expr.codeES
  }
  return ret
}
