import { ControlLabel } from '@/components/control-label'
import { InlineMarkdownPreview } from '@/components/inline-markdown-preview'
import type { JSONRuleRegExp, ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Input, Tooltip } from '@m-ui/react'
import { useCallback, type ChangeEvent, type FC } from 'react'

/**
 * JSON 规则：正则表达式，编辑器属性
 */
export interface JSONRuleRegExpEditorProps
  extends Required<ValueOnChangeProps<JSONRuleRegExp>> {}

/**
 * JSON 规则：正则表达式，编辑器
 */
export const JSONRuleRegExpEditor: FC<JSONRuleRegExpEditorProps> = ({
  value,
  onChange,
}) => {
  const handleExprChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        expr: e.target.value,
      })
    },
    [onChange, value],
  )
  const handleErrMsgChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        errMsg: e.target.value,
      })
    },
    [onChange, value],
  )
  return (
    <div>
      <div>
        <Tooltip title={<InlineMarkdownPreview source="比如: `ab`、`^abc$`" />}>
          <ControlLabel>表达式：</ControlLabel>
        </Tooltip>
        <Input size="small" value={value.expr} onChange={handleExprChange} />
      </div>
      <div>
        <ControlLabel>错误提示：</ControlLabel>
        <Input
          size="small"
          value={value.errMsg}
          onChange={handleErrMsgChange}
        />
      </div>
    </div>
  )
}
