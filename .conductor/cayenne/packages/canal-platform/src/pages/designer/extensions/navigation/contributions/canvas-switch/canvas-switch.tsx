import type { E2ESchemaService } from '@/pages/designer/services'
import { useLatestFn } from '@ad/canal-shared-ui'
import {
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { NormalCustomcolumnsLine } from '@m-ui/icons'
import { InputNumber } from '@m-ui/react'
import { isNumber } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useState } from 'react'
import { useEvent } from 'react-use'
import styled, { createGlobalStyle } from 'styled-components'
import { NavIconButton } from '../../components'
import { canvasSwitchStore } from './canvas-switch-store'
import { ReactComponent as SVGPC } from './pc.svg'
import { ReactComponent as SVGPhone } from './phone.svg'
import { ReactComponent as SVGTablet } from './tablet.svg'

/**
 * 画布切换
 */
export const CanvasSwitch = observer(() => {
  const { canvasWidth, canvasWidthPx } = canvasSwitchStore
  const activeKey = isNumber(canvasWidth) ? 'custom' : canvasWidth || 'pc'
  const [editingWidth, setEditingWidth] = useState<number | null>(canvasWidthPx)
  const handleIconBtnClick = useLatestFn((key: typeof activeKey) => {
    if (key === 'custom') {
      if (activeKey === 'custom') return
      const w = canvasSwitchStore.getNumberWidth(activeKey)
      canvasSwitchStore.setCanvasWidth(w)
      setEditingWidth(w)
    } else {
      canvasSwitchStore.setCanvasWidth(key)
    }
  })
  const handleInputBlur = useLatestFn(() => {
    if (editingWidth && editingWidth > 0) {
      canvasSwitchStore.setCanvasWidth(editingWidth)
    }
  })
  useEvent<typeof window>('click', (ev) => {
    const target = ev.target
    if (!(target instanceof HTMLElement)) return
    const elCanvas = target.closest('#canvas')
    if (!elCanvas) return
    const elCanvasIframeWrapper = target.closest('#canvas-iframe-wrapper')
    if (elCanvasIframeWrapper) return
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    const stateService = getInject<StateService>(StateServiceSymbol)
    stateService.setSelectedComponentId(schemaService.schema?.view.id)
  })
  return (
    <CanvasSwitchContainer>
      <CanvasIframeWrapperStyle
        width={
          canvasWidthPx > 0 && activeKey !== 'pc' ? canvasWidthPx : undefined
        }
      />
      <StyledNavIconButton
        $isActive={activeKey === 'pc'}
        onClick={handleIconBtnClick.bind(null, 'pc')}
      >
        <SVGPC />
      </StyledNavIconButton>
      <StyledNavIconButton
        $isActive={activeKey === 'tablet'}
        onClick={handleIconBtnClick.bind(null, 'tablet')}
      >
        <SVGTablet />
      </StyledNavIconButton>
      <StyledNavIconButton
        $isActive={activeKey === 'phone'}
        onClick={handleIconBtnClick.bind(null, 'phone')}
      >
        <SVGPhone />
      </StyledNavIconButton>
      <StyledNavIconButton
        $isActive={activeKey === 'custom'}
        onClick={handleIconBtnClick.bind(null, 'custom')}
      >
        <NormalCustomcolumnsLine />
      </StyledNavIconButton>
      <InputNumber
        size="small"
        disabled={activeKey !== 'custom'}
        addonAfter="px"
        min={1}
        value={activeKey === 'custom' ? editingWidth : canvasWidthPx}
        onChange={setEditingWidth}
        onBlur={handleInputBlur}
      />
    </CanvasSwitchContainer>
  )
})

const CanvasSwitchContainer = styled.div`
  display: flex;

  .ant-input-number-input {
    width: 60px;
  }
`

const StyledNavIconButton = styled(NavIconButton)<{
  $isActive?: boolean
}>`
  font-size: 24px;
  width: 24px;
  margin-right: 8px;
  ${
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    ({ $isActive }) =>
      $isActive && {
        background: '#f2f2f2 ',
      }
  }
`

const CanvasIframeWrapperStyle = createGlobalStyle<{
  width?: number
}>`
  #canvas-iframe-wrapper {
    ${
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      ({ width }) => ({ width })
    }
    margin: auto;
    max-width: 100%;
  }

  #kael-canvas {
    height: initial !important;
    background-color: initial !important;
  }
`
