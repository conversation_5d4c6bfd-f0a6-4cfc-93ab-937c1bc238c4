import { InlineMarkdownPreview } from '@/components/inline-markdown-preview'
import { LongSelect } from '@/components/long-select'
import { LongTooltip } from '@/components/long-tooltip'
import type { SetterRenderProps } from '@kael/designer-property'
import type { SchemaExpression } from '@kael/schema'
import { normalizeExpression } from '@kael/schema-utils'
import { Typography } from '@m-ui/react'
import { isArray, isFunction, isString, isUndefined } from 'lodash'
import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  type FC,
  type ReactNode,
} from 'react'
import { cacheSetterValue } from '../../hocs'

/**
 * 动态枚举设置器属性
 */
export interface DynamicEnumSetterProps {
  /**
   * 选项获取器，为 JS 代码，执行后可以得到一个函数，函数类型见 {@link OptionsFetcher}
   */
  optionsFetcher: string
}

/**
 * 选项获取器
 */
export interface OptionsFetcher {
  (): DynamicOption[] | Promise<DynamicOption[]>
}

/**
 * 动态选项
 */
export interface DynamicOption {
  /**
   * 标签，不填默认用 value，string 类型直接用，其他表达式走 JSON.stringify
   */
  label?: string
  /**
   * 提示，默认用 label
   */
  tip?: string
  /**
   * 值，支持表达式
   */
  value: SchemaExpression
}

/**
 * 动态枚举设置器
 */
export const DynamicEnumSetter: FC<SetterRenderProps<DynamicEnumSetterProps>> =
  cacheSetterValue(
    ({
      value: expr,
      onChange,
      setterProps: { optionsFetcher: optionsFetcherStr },
    }) => {
      const ne = normalizeExpression(expr)
      const [options, setOptions] = useState<DynamicOption[]>([])
      useEffect(() => {
        const optionsFetcher = eval(optionsFetcherStr) as OptionsFetcher
        if (!isFunction(optionsFetcher)) return
        ;(async (): Promise<void> => {
          const ret = await optionsFetcher()
          if (!isArray(ret)) return
          setOptions(ret)
        })()
      }, [optionsFetcherStr])
      const finalOptions = useMemo(() => {
        const fo: { label: ReactNode; value: string }[] = options.map((o) => ({
          label: mergeLabelTip(o.label || exprToLabel(o.value), o.tip),
          value: exprToValue(o.value),
        }))
        if (!isUndefined(ne.value)) {
          const exprOption = {
            label: mergeLabelTip(exprToLabel(ne)),
            value: exprToValue(ne),
          }
          if (fo.every((o) => o.value !== exprOption.value)) {
            fo.push(exprOption)
          }
        }
        return fo
      }, [ne, options])
      const finalValue = useMemo(
        () => (isUndefined(ne.value) ? ne.value : exprToValue(ne)),
        [ne],
      )
      const handleOnChange = useCallback(
        (value?: string) => {
          onChange(isUndefined(value) ? value : JSON.parse(value))
        },
        [onChange],
      )
      return (
        <LongSelect
          size="small"
          value={finalValue}
          options={finalOptions}
          onChange={handleOnChange}
          allowClear
        />
      )
    },
  )

/**
 * 合并标签和提示
 * @param label 标签
 * @param tip 提示
 */
function mergeLabelTip(label: string, tip?: string): ReactNode {
  if (tip) {
    return (
      <LongTooltip
        placement="left"
        title={<InlineMarkdownPreview source={tip} />}
      >
        <Typography.Text ellipsis>{label}</Typography.Text>
      </LongTooltip>
    )
  }
  return (
    <Typography.Text
      ellipsis={{
        tooltip: {
          placement: 'left',
          title: label,
        },
      }}
    >
      {label}
    </Typography.Text>
  )
}

/**
 * 表达式转标签
 * @param expr 表达式
 */
function exprToLabel(expr: SchemaExpression): string {
  const ne = normalizeExpression(expr)
  if (ne.type === 'static') {
    if (isString(ne.value)) return ne.value
    return JSON.stringify(ne.value)
  }
  return JSON.stringify(ne)
}

/**
 * 表达式转值
 * @param expr 表达式
 */
function exprToValue(expr: SchemaExpression): string {
  const ne = normalizeExpression(expr)
  return JSON.stringify(ne)
}
