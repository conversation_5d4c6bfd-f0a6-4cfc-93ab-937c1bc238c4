import { CoeditSave } from '@/components/coedit-save'
import { designerCoeditStore } from '@/pages/designer/stores/designer-coedit-store'
import { schemaStore } from '@/pages/designer/stores/schema-store'
import { useLatestFn } from '@ad/canal-shared-ui'
import { observer } from 'mobx-react-lite'
import { type FC } from 'react'
import styled from 'styled-components'
import { MergeSaveBtn } from './merge-save-btn'
import { SaveBtn } from './save-btn'

/**
 * 设计器协同编辑保存
 */
export const DesignerCoeditSave: FC = observer(() => {
  const editable = useLatestFn(() => schemaStore.editable)
  return (
    <CoeditSave
      coeditStore={designerCoeditStore}
      saveBtn={
        designerCoeditStore.collaborativeModel === 'single' ? (
          <StyledSaveBtn />
        ) : (
          <StyleMergeSaveBtn />
        )
      }
      editable={editable}
    />
  )
})

const StyledSaveBtn = styled(SaveBtn)`
  margin-right: 12px;
`
const StyleMergeSaveBtn = styled(MergeSaveBtn)`
  margin-right: 12px;
`
