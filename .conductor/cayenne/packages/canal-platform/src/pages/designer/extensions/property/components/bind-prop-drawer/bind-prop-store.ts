import {
  DOMAIN_CODE_AD_DSP_REPORT,
  DOMAIN_CODE_AD_ESP_CREATE,
  DOMAIN_CODE_AD_ESP_MOBILE_CREATE,
  DOMAIN_CODE_AD_ESP_REPORT,
  DOMAIN_CODE_AD_LSP_REPORT,
} from '@/constants/domain-codes'
import { DESIGNER_QUERY } from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import { preloadBabel } from '@/utils'
import { ExpressionEnv } from '@ad/canal-shared'
import type {
  E2ESchemaExpressionAPI,
  E2ESchemaExpressionJS,
  E2ESchemaNormalizedExpression,
} from '@ad/e2e-schema'
import { isBindableExpression } from '@ad/e2e-schema-utils'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { normalizeExpression } from '@kael/schema-utils'
import { action, computed, makeObservable, observable } from 'mobx'

/**
 * 绑定属性仓库
 */
export class BindPropStore {
  /**
   * 抽屉是否可见
   */
  private _isDrawerVisible = false

  /**
   * 抽屉是否可见
   */
  public get isDrawerVisible(): boolean {
    return this._isDrawerVisible
  }

  /**
   * 抽屉配置
   */
  private _drawerConfig: BindPropStoreDrawerConfig | null = null

  /**
   * 抽屉配置
   */
  public get drawerConfig(): BindPropStoreDrawerConfig | null {
    return this._drawerConfig
  }

  /**
   * 表达式环境
   */
  private _expressionEnv = ExpressionEnv.BACK

  /**
   * 表达式环境
   */
  public get expressionEnv(): ExpressionEnv {
    return this._expressionEnv
  }

  /**
   * 编辑中的属性表达式
   */
  public get editingPropExpression(): E2ESchemaNormalizedExpression | null {
    if (!this._drawerConfig) {
      return null
    }
    const schemaService = getInject<E2ESchemaService | null>(
      SchemaServiceSymbol,
    )
    if (!schemaService) {
      return null
    }
    return schemaService.getComponentPropExpression(
      this._drawerConfig.componentId,
      this._drawerConfig.propPath,
    )
  }

  public constructor() {
    makeObservable<
      BindPropStore,
      '_isDrawerVisible' | '_drawerConfig' | '_expressionEnv'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isDrawerVisible: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _drawerConfig: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _expressionEnv: observable,
      editingPropExpression: computed,
      openDrawer: action,
      cancelDrawer: action,
      submitDrawer: action,
    })
  }

  /**
   * 打开抽屉
   * @param config 配置
   */
  public openDrawer(config: BindPropStoreDrawerConfig): void {
    const { frontExprEnabled = true } = config
    this._isDrawerVisible = true
    this._drawerConfig = config
    if (
      frontExprEnabled &&
      (!this.editingPropExpression ||
        this.editingPropExpression.type === 'static')
    ) {
      this._expressionEnv = [
        DOMAIN_CODE_AD_ESP_CREATE,
        DOMAIN_CODE_AD_ESP_MOBILE_CREATE,
        DOMAIN_CODE_AD_DSP_REPORT,
        DOMAIN_CODE_AD_ESP_REPORT,
        DOMAIN_CODE_AD_LSP_REPORT,
      ].includes(DESIGNER_QUERY.domainCode)
        ? ExpressionEnv.FRONT
        : ExpressionEnv.BACK
    } else {
      this._expressionEnv =
        this.editingPropExpression?.type === 'js'
          ? ExpressionEnv.FRONT
          : ExpressionEnv.BACK
    }
    preloadBabel()
  }

  /**
   * 取消抽屉
   */
  public cancelDrawer(): void {
    this._isDrawerVisible = false
  }

  /**
   * 提交抽屉
   * @param api 接口，null 表示取消接口绑定
   */
  public submitDrawer(
    api: E2ESchemaExpressionAPI | E2ESchemaExpressionJS | null = null,
  ): void {
    const editingPropExpression = this.editingPropExpression
    if (this._drawerConfig && editingPropExpression) {
      const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
      if (!api) {
        if (isBindableExpression(editingPropExpression)) {
          schemaService.updateComponentPropExpression(
            this._drawerConfig.componentId,
            this._drawerConfig.propPath,
            normalizeExpression(editingPropExpression.defaultValue ?? null),
          )
        }
      } else {
        schemaService.updateComponentPropExpression(
          this._drawerConfig.componentId,
          this._drawerConfig.propPath,
          api,
        )
      }
    }
    this._isDrawerVisible = false
  }

  /**
   * 设置表达式环境
   * @param env 环境
   */
  public setExpressionEnv(env: ExpressionEnv): void {
    this._expressionEnv = env
  }
}

/**
 * 抽屉配置
 */
export interface BindPropStoreDrawerConfig {
  /**
   * 组件 ID
   */
  componentId: string
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
  /**
   * 前端表达式开启，默认：true
   */
  frontExprEnabled?: boolean
  /**
   * 默认前端 js 表达式
   */
  defaultFrontJsExpr?: string
  /**
   * 默认前端 ts 表达式
   */
  defaultFrontTsExpr?: string
  /**
   * 默认后端 js 表达式
   */
  defaultBackJsExpr?: string
  /**
   * 默认后端 ts 表达式
   */
  defaultBackTsExpr?: string
}

/**
 * 绑定属性仓库，单例
 */
export const bindPropStore = new BindPropStore()
