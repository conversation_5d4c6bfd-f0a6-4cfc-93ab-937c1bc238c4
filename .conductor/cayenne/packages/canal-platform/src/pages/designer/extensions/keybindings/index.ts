import { updateModuleUsingPost } from '@/services/backend/module'
import { findModuleVersionLogDetail } from '@/services/backend/module-version-log'
import { saveAndDeployModule } from '@/utils/apis'
import type { E2EServerSchema } from '@ad/e2e-schema'
import { f2bE2ESchema } from '@ad/e2e-schema-utils'
import { SchemaServiceSymbol, StateServiceSymbol } from '@kael/designer-service'
import {
  initCommands,
  initKeybindings as initKaelKeybindings,
} from '@kael/designer-shared'
import { getInject } from '@kael/di'
import { message } from '@m-ui/react'
import { toJS } from 'mobx'
import {
  DESIGNER_QUERY,
  IS_DEBUG_MODE,
  IS_READONLY_MODE,
} from '../../constants'
import type { CanalStateService, E2ESchemaService } from '../../services'
import { debugModeStore } from '../../stores/debug-mode-store'
import { designerCoeditStore } from '../../stores/designer-coedit-store'
import { schemaStore } from '../../stores/schema-store'
import { canvasPreviewerStore } from '../canvas/canvas-previewer/canvas-previewer-store'
import { collapseIconStore } from '../navigation/contributions/collapse/collapse-icon-store'
import { saveLogStore } from '../save-log-panel/save-log-store'
import { HOTKEY_MAP, HOTKEY_NAME, HotkeyType } from './keys'

export * from './keys'

/**
 * 初始化快捷键
 */
export function initKeybindings(): void {
  initCommands((commandRegistry) => {
    commandRegistry.registerCommand(
      { id: HotkeyType.PREVIEW, label: HOTKEY_NAME[HotkeyType.PREVIEW] },
      {
        execute: () => {
          canvasPreviewerStore.togglePreview()
        },
      },
    )
    commandRegistry.registerCommand(
      { id: HotkeyType.REDO, label: HOTKEY_NAME[HotkeyType.REDO] },
      {
        execute: () => {
          const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
          schemaService.redo()
        },
      },
    )
    commandRegistry.registerCommand(
      { id: HotkeyType.UNDO, label: HOTKEY_NAME[HotkeyType.UNDO] },
      {
        execute: () => {
          const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
          schemaService.undo()
        },
      },
    )
    commandRegistry.registerCommand(
      { id: HotkeyType.SAVE, label: HOTKEY_NAME[HotkeyType.SAVE] },
      {
        execute: async () => {
          if (
            IS_READONLY_MODE ||
            (!designerCoeditStore.isCurrentRoomEditing &&
              designerCoeditStore.collaborativeModel === 'single') ||
            !schemaStore.editable ||
            !schemaStore.savable
          ) {
            return
          }
          const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
          if (!schemaService.schema) {
            message.error('保存失败，Schema 不存在')
            console.error('schemaService', schemaService)
            return
          }
          schemaStore.setSavedSchema(schemaService.schema)
          const schema = toJS(schemaService.schema)
          if (IS_DEBUG_MODE) {
            debugModeStore.saveEditedSchemaToLocal(schema)
            message.success('保存成功')
            return
          }
          const serverSchema = f2bE2ESchema(schema)
          console.log('save schema', { schema, serverSchema })
          try {
            const content = JSON.stringify(serverSchema)
            await updateModuleContentAndAutoDeploy(content)
            message.success('保存成功')
            saveLogStore.loadCurrentVersionLogs()
          } catch (err) {
            console.log('save err', err)
            message.error(`保存失败：${(err as Error).message}`)
          }
        },
      },
    )
    commandRegistry.registerCommand(
      { id: HotkeyType.RESUME },
      {
        execute: async (logId: string) => {
          try {
            const res = await findModuleVersionLogDetail({
              logId,
            })
            await updateModuleContentAndAutoDeploy(res.data.moduleContent)
            message.success('恢复成功')
            window.location.reload()
          } catch (err) {
            console.log('resume err', err)
            message.error(`恢复失败：${(err as Error).message}`)
          }
        },
      },
    ),
      commandRegistry.registerCommand(
        { id: HotkeyType.MERGE_SAVE },
        {
          execute: async (mergedSchema: E2EServerSchema) => {
            try {
              const content = JSON.stringify(mergedSchema)
              await updateModuleContentAndAutoDeploy(content)
              message.success('保存成功')
              designerCoeditStore.isReloadAfterCollaborativMerge = true
              setTimeout(() => {
                window.location.reload()
              }, 200)
            } catch (err) {
              console.log('save err', err)
              message.error(`保存失败：${(err as Error).message}`)
            }
          },
        },
      )
    commandRegistry.registerCommand(
      { id: HotkeyType.COLLAPSE, label: HOTKEY_NAME[HotkeyType.COLLAPSE] },
      {
        execute: () => {
          collapseIconStore.toggle()
        },
      },
    )
  })
  initKaelKeybindings(
    {
      copy: true,
      paste: true,
      delete: true,
      moveUp: true,
      moveDown: true,
      moveTop: true,
      moveBottom: true,
    },
    (keybindingRegistry) => {
      keybindingRegistry.registerKeybindings([
        {
          keybinding: [HOTKEY_MAP[HotkeyType.PREVIEW]],
          action: HotkeyType.PREVIEW,
        },
        {
          keybinding: [HOTKEY_MAP[HotkeyType.REDO]],
          action: HotkeyType.REDO,
        },
        {
          keybinding: [HOTKEY_MAP[HotkeyType.UNDO]],
          action: HotkeyType.UNDO,
        },
        {
          keybinding: [HOTKEY_MAP[HotkeyType.SAVE]],
          action: HotkeyType.SAVE,
        },
        {
          keybinding: [HOTKEY_MAP[HotkeyType.COLLAPSE]],
          action: HotkeyType.COLLAPSE,
        },
      ])
    },
  )
}

/**
 * 更新模块内容并自动部署
 * @param content 内容
 */
function updateModuleContentAndAutoDeploy(
  content: string,
): ReturnType<typeof updateModuleUsingPost> {
  const canalStateService = getInject<CanalStateService>(StateServiceSymbol)
  return saveAndDeployModule({
    content,
    domainCode: DESIGNER_QUERY.domainCode,
    moduleId: DESIGNER_QUERY.moduleId,
    moduleVersion: +DESIGNER_QUERY.moduleVersion,
    workload: canalStateService.getAndClearModuleWorkload(),
    autoDeploy: DESIGNER_QUERY.autoDeploy,
  })
}
