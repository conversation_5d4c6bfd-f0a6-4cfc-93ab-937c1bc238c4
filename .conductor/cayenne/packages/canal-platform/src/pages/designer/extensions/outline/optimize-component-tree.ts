import { FIXED_TREE_PROPS } from '@kael/designer-outline/esm/components/component-tree'
import { getInject, register } from '@kael/di'
import { CanalOutlineService } from './canal-outline-service'

/**
 * 优化组件树
 */
export function optimizeComponentTree(): void {
  // TODO: 修改常量 FIXED_TREE_PROPS 比较 hack，后面升级引擎版本的时候，可以让引擎那边提供更好的修改方式
  FIXED_TREE_PROPS.onDragStart = (info): void => {
    // console.log('optimizeComponentTree.onDragStart')
    const div = document.createElement('div')
    div.innerText = info.event.currentTarget.innerText
    div.style.position = 'absolute'
    div.style.top = '-9999px'
    div.style.color = 'rgba(0,0,0,.5)'
    document.body.appendChild(div)
    // 修改拖拽图像后，可以方便看清拖拽位置的线条
    info.event.dataTransfer.setDragImage(div, 0, 0)
    setTimeout(() => {
      document.body.removeChild(div)
    })

    const canalOutlineService = getInject<CanalOutlineService>(
      CanalOutlineService.token,
    )
    canalOutlineService.setIsDragging(true)
  }
  FIXED_TREE_PROPS.onDragEnd = (): void => {
    // console.log('optimizeComponentTree.onDragEnd')
    const canalOutlineService = getInject<CanalOutlineService>(
      CanalOutlineService.token,
    )
    canalOutlineService.setIsDragging(false)
  }
  // 有时候 FIXED_TREE_PROPS.onDragEnd 不会调用，用 mouseup 兜底
  window.addEventListener('mouseup', () => {
    // console.log('optimizeComponentTree.mouseup')
    const canalOutlineService = getInject<CanalOutlineService>(
      CanalOutlineService.token,
    )
    canalOutlineService.setIsDragging(false)
  })
  register({
    token: CanalOutlineService.token,
    useClass: CanalOutlineService,
    override: true,
  })
}
