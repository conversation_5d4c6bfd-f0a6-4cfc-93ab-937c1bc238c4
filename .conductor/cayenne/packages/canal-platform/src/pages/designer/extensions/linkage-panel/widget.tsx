import { ReactWidget } from '@kael/designer-core'
import { injectable, postConstruct, register } from '@kael/di'
import React from 'react'
import { createGlobalStyle } from 'styled-components'
import { LinkagePanel } from './components'
import IMG_LINKAGE_PANEL from './linkage-panel.svg'

/**
 * 联动面板控件
 */
@injectable()
export class LinkagePanelWidget extends ReactWidget {
  public static readonly ID = 'linkage-panel'
  public static readonly LABEL = '联动面板'

  @postConstruct()
  public init(): void {
    this.id = LinkagePanelWidget.ID
    this.title.label = LinkagePanelWidget.LABEL
    this.title.caption = LinkagePanelWidget.LABEL
    this.title.iconClass = 'tab-icon-linkage-panel'
    this.title.closable = true
  }

  public render(): React.ReactNode {
    return (
      <>
        <GlobalStyle />
        <LinkagePanel />
      </>
    )
  }
}

/**
 * 注册联动面板控件
 */
export function registerLinkagePanelWidget(): void {
  register({
    token: LinkagePanelWidget.token,
    useClass: LinkagePanelWidget,
  })
}

const GlobalStyle = createGlobalStyle`
  .tab-icon-linkage-panel {
    mask: url('${IMG_LINKAGE_PANEL}');
    -webkit-mask: url('${IMG_LINKAGE_PANEL}');
  }
`
