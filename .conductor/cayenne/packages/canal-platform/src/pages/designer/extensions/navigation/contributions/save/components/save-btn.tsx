import { IS_DEBUG_MODE } from '@/pages/designer/constants'
import { schemaStore } from '@/pages/designer/stores/schema-store'
import { CommandRegistry, CommandRegistrySymbol } from '@kael/designer-core'
import { getInject } from '@kael/di'
import { observer } from 'mobx-react-lite'
import { useCallback, useState, type ComponentProps, type FC } from 'react'
import { HotkeyType } from '../../../../keybindings'
import { NavActionButton } from '../../../components'

/**
 * 保存按钮
 */
export const SaveBtn: FC<ComponentProps<typeof NavActionButton>> = observer(
  (props) => {
    const [loading, setLoading] = useState(false)
    const handleBtnClick = useCallback(async () => {
      const commandRegistry = getInject<CommandRegistry>(CommandRegistrySymbol)
      try {
        setLoading(true)
        await commandRegistry.executeCommand(HotkeyType.SAVE)
      } finally {
        setLoading(false)
      }
    }, [])
    return (
      <NavActionButton
        {...props}
        type="primary"
        loading={loading}
        disabled={!schemaStore.savable}
        onClick={handleBtnClick}
      >
        {IS_DEBUG_MODE ? '保存到本地' : '保存'}
      </NavActionButton>
    )
  },
)
