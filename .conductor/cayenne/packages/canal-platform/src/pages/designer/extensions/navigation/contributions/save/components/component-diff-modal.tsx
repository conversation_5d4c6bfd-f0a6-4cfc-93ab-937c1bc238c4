import type { ComponentCode, E2EServerSchemaComponent } from '@ad/e2e-schema'
import {
  <PERSON>ert,
  Button,
  Collapse,
  Modal,
  Radio,
  Tabs,
  Tag,
  message,
} from '@m-ui/react'
import { DiffEditor } from '@monaco-editor/react'
import React, { useState } from 'react'
import type { ComponentChangeType } from './merge-save-btn'

const { TabPane } = Tabs
const { Panel } = Collapse

// 组件数据类型
export type ComponentMapData = {
  component: E2EServerSchemaComponent
  componentCode: ComponentCode | undefined
}

// 组件差异项
export interface ComponentDiff {
  id: string
  type: ComponentChangeType
  currentConfig?: ComponentMapData
  latestConfig?: ComponentMapData
}

// 组件差异选择
export type DiffSelection = {
  [componentId: string]: 'current' | 'remote' | ''
}

interface ComponentDiffModalProps {
  visible: boolean
  diffs: ComponentDiff[]
  onCancel: () => void
  onOk: (selections: DiffSelection) => void
}

/**
 * 格式化JSON字符串
 */
const formatJson = (obj: unknown): string => {
  try {
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    console.error('JSON格式化失败:', error)
    return String(obj) || ''
  }
}

export const needUpdateActionTypes = [
  'UPDATE',
  'CONFLICT_UPDATE',
  'REMOTE_UPDATE',
  'LOCAL_UPDATE', // 添加本地更新类型，使其也可以进行选择
]

/**
 * 组件差异对比Modal
 */
const ComponentDiffModal: React.FC<ComponentDiffModalProps> = ({
  visible,
  diffs,
  onCancel,
  onOk,
}) => {
  // 用户选择结果
  const [selections, setSelections] = useState<DiffSelection>(() => {
    // 默认不选择任何版本
    const initialSelections: DiffSelection = {}
    diffs.forEach((diff) => {
      // 只有 UPDATE 类型的组件需要选择
      if (needUpdateActionTypes.includes(diff.type)) {
        // UPDATE 类型需要用户手动选择
        initialSelections[diff.id] = ''
      }
      // 非 UPDATE 类型的组件不需要选择
    })
    return initialSelections
  })

  // 处理选择变更
  const handleSelectionChange = (
    componentId: string,
    value: 'current' | 'remote',
  ): void => {
    setSelections((prev) => ({
      ...prev,
      [componentId]: value,
    }))
  }

  // 获取组件名称
  const getComponentName = (diff: ComponentDiff): string => {
    const { currentConfig, latestConfig } = diff
    if (currentConfig?.component.name) {
      return currentConfig.component.name
    }
    if (latestConfig?.component.name) {
      return latestConfig.component.name
    }
    return diff.id
  }

  return (
    <Modal
      title="组件配置差异"
      visible={visible}
      width={1200}
      onCancel={(): void => {
        onCancel()
        setSelections({})
      }}
      destroyOnClose
      maskClosable={false}
      footer={[
        <Button
          key="cancel"
          onClick={(): void => {
            onCancel()
            setSelections({})
          }}
        >
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={(): void => {
            // 只检查 UPDATE 类型的组件是否有选择
            const updateTypeDiffs = diffs.filter((diff) =>
              needUpdateActionTypes.includes(diff.type),
            )
            const hasEmptySelections = updateTypeDiffs.some(
              (diff) => !selections[diff.id],
            )
            if (hasEmptySelections && updateTypeDiffs.length > 0) {
              message.error(
                '请为修改类型的组件选择使用本地还是远程版本，否则无法保存',
              )
              return
            }
            onOk(selections)
          }}
        >
          确认信息并保存
        </Button>,
      ]}
    >
      <div style={{ maxHeight: '80vh', overflow: 'auto' }}>
        <Alert
          message="远程有新内容提交，请进行确认或选择"
          description={
            <ul>
              <li>对同一id的组件的远程有修改提供二选一选项，必须执行选择;</li>
              <li>展开可以查看差异详情;</li>
              <li>
                对远程【新增】组件为了防止丢失会自动添加到dom树结尾，请保存后自行调整结构；
              </li>
              <li>对远程操作的【删除】并不执行，请保存后自行删除;</li>
            </ul>
          }
          type="info"
          style={{ marginBottom: 16 }}
          showIcon
        />
        <Collapse accordion defaultActiveKey={[]}>
          {/* 按照类型对差异列表进行排序，将修改类型的组件排在前面 */}
          {[...diffs]
            .sort((a, b) => {
              // 定义类型的排序权重
              const getTypeWeight = (type: ComponentChangeType): number => {
                // 先排冲突修改，然后是远程修改和本地修改，最后是新增和删除
                switch (type) {
                  case 'CONFLICT_UPDATE':
                    return 1
                  case 'REMOTE_UPDATE':
                    return 2
                  case 'LOCAL_UPDATE':
                    return 3
                  case 'UPDATE':
                    return 4
                  case 'REMOTE_ADD':
                    return 5
                  case 'LOCAL_ADD':
                    return 6
                  case 'REMOTE_DELETE':
                    return 7
                  case 'LOCAL_DELETE':
                    return 8
                  default:
                    return 9
                }
              }

              return getTypeWeight(a.type) - getTypeWeight(b.type)
            })
            .map((diff) => {
              const { id, currentConfig, latestConfig } = diff
              const componentName = getComponentName(diff)

              // 根据组件变更类型获取对应的中文描述和颜色
              const getTypeInfo = (
                type: ComponentChangeType,
              ): { text: string; color: string } => {
                switch (type) {
                  case 'REMOTE_ADD':
                    return { text: '远程新增', color: 'green' }
                  case 'LOCAL_ADD':
                    return { text: '本地新增', color: 'blue' }
                  case 'REMOTE_DELETE':
                    return { text: '远程删除', color: 'red' }
                  case 'LOCAL_DELETE':
                    return { text: '本地删除', color: 'orange' }
                  case 'CONFLICT_UPDATE':
                    return { text: '冲突修改', color: 'magenta' }
                  case 'REMOTE_UPDATE':
                    return { text: '远程修改', color: 'cyan' }
                  case 'LOCAL_UPDATE':
                    return { text: '本地修改', color: 'geekblue' }
                  default:
                    return { text: '配置变更', color: 'purple' }
                }
              }

              const typeInfo = getTypeInfo(diff.type)

              return (
                <Panel
                  key={id}
                  header={
                    <span>
                      组件: {componentName}{' '}
                      <Tag color={typeInfo.color}>{typeInfo.text}</Tag>
                    </span>
                  }
                  extra={
                    // 只对 UPDATE 类型的组件显示操作选项
                    needUpdateActionTypes.includes(diff.type) ? (
                      <Radio.Group
                        value={selections[id]}
                        onChange={(e): void =>
                          handleSelectionChange(id, e.target.value)
                        }
                        buttonStyle="solid"
                      >
                        <Radio.Button value="current">使用本地</Radio.Button>
                        <Radio.Button value="remote">使用远程</Radio.Button>
                      </Radio.Group>
                    ) : (
                      // 其他类型显示自动处理的提示
                      <Tag color="default">自动处理</Tag>
                    )
                  }
                >
                  <Tabs defaultActiveKey="component">
                    <TabPane tab="组件配置" key="component">
                      <div style={{ height: 300 }}>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            marginBottom: '4px',
                          }}
                        >
                          <Tag color="blue">本地版本</Tag>
                          <Tag color="green">远程版本</Tag>
                        </div>
                        <DiffEditor
                          height="100%"
                          original={formatJson(currentConfig?.component || {})}
                          modified={formatJson(latestConfig?.component || {})}
                          language="json"
                          options={{
                            renderSideBySide: true,
                            readOnly: true,
                            minimap: { enabled: false },
                            folding: true,
                            lineNumbers: 'on',
                            scrollBeyondLastLine: false,
                            automaticLayout: true,
                          }}
                        />
                      </div>
                    </TabPane>
                    <TabPane tab="组件静态资源和版本" key="code">
                      <div style={{ height: 300 }}>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            marginBottom: '4px',
                          }}
                        >
                          <Tag color="blue">本地版本</Tag>
                          <Tag color="green">远程版本</Tag>
                        </div>
                        <DiffEditor
                          height="100%"
                          original={formatJson(
                            currentConfig?.componentCode || {},
                          )}
                          modified={formatJson(
                            latestConfig?.componentCode || {},
                          )}
                          language="json"
                          options={{
                            renderSideBySide: true,
                            readOnly: true,
                            minimap: { enabled: false },
                            folding: true,
                            lineNumbers: 'on',
                            scrollBeyondLastLine: false,
                            automaticLayout: true,
                          }}
                        />
                      </div>
                    </TabPane>
                  </Tabs>
                </Panel>
              )
            })}
        </Collapse>
      </div>
    </Modal>
  )
}

export default ComponentDiffModal
