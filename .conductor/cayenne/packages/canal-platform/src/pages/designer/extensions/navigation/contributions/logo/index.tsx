import { DesignerTitle } from '@/components/designer-title'
import { DESIGNER_QUERY } from '@/pages/designer/constants'
import { schemaStore } from '@/pages/designer/stores/schema-store'
import {
  NavigationContribution,
  type NavigationContributionPosition,
} from '@kael/designer-navigation'
import { injectable } from '@kael/di'
import { observer } from 'mobx-react-lite'
import { type ReactNode } from 'react'

/**
 * Logo 贡献点
 */
@injectable()
export class LogoContribution extends NavigationContribution {
  public id = 'navigation-logo'
  public rank = 1
  public position: NavigationContributionPosition = 'left'

  public render(): ReactNode {
    return <ObDesignerTitle />
  }
}

/**
 * 可观察的设计器标题
 */
const ObDesignerTitle = observer(() => {
  return (
    <DesignerTitle
      mode={DESIGNER_QUERY.mode}
      domainCode={DESIGNER_QUERY.domainCode}
      changeId={schemaStore.moduleDetailInit?.changeId}
      title={schemaStore.moduleDetailInit?.name}
    />
  )
})
