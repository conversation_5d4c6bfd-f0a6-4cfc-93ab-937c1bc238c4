import { DESIGNER_QUERY } from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services/e2e-schema-service'
import { schemaStore } from '@/pages/designer/stores/schema-store'
import { getModuleDetail } from '@/services/backend/module'
import { findModuleVersionLogBrief } from '@/services/backend/module-version-log'
import type {
  ComponentCode,
  E2EServerSchema,
  E2EServerSchemaComponent,
} from '@ad/e2e-schema'
import { f2bE2ESchema } from '@ad/e2e-schema-utils'
import { CommandRegistry, CommandRegistrySymbol } from '@kael/designer-core'
import { SchemaServiceSymbol } from '@kael/designer-service/esm/schema-service'
import { getInject } from '@kael/di'
import { Modal, message } from '@m-ui/react'
import { isEqual } from 'lodash'
import { toJS } from 'mobx'
import { observer } from 'mobx-react-lite'
import { useCallback, useState, type ComponentProps, type FC } from 'react'
import { HotkeyType } from '../../../../keybindings'
import { saveLogStore } from '../../../../save-log-panel/save-log-store'
import { NavActionButton } from '../../../components'
import ComponentDiffModal, {
  needUpdateActionTypes,
  type ComponentDiff,
  type DiffSelection,
} from './component-diff-modal'

/**
 * 组件变更类型
 */
export type ComponentChangeType =
  | 'REMOTE_ADD' // 远程新增
  | 'LOCAL_ADD' // 本地新增
  | 'REMOTE_DELETE' // 远程删除
  | 'LOCAL_DELETE' // 本地删除
  | 'CONFLICT_UPDATE' // 双方都修改，存在冲突
  | 'REMOTE_UPDATE' // 只有远程修改
  | 'LOCAL_UPDATE' // 只有本地修改
  | 'UPDATE' // 兼容旧版本的一般更新类型

type ComponentMapData = {
  component: E2EServerSchemaComponent
  componentCode: ComponentCode | undefined
}

/**
 * 保存按钮
 */
export const MergeSaveBtn: FC<ComponentProps<typeof NavActionButton>> =
  observer((props) => {
    const [loading, setLoading] = useState(false)
    const [diffModalVisible, setDiffModalVisible] = useState(false)
    const [componentDiffs, setComponentDiffs] = useState<ComponentDiff[]>([])
    // 存储基准日志ID，用于比较是否有新的远程提交
    const [baselineLogId, setBaselineLogId] = useState<string | null>(null)

    // 重置合并状态并重新开始
    const resetAndRestartMerge = useCallback(() => {
      // 重置状态
      setComponentDiffs([])
      setDiffModalVisible(false)

      // 重新获取最新数据并开始合并过程
      const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
      if (!schemaService.schema) {
        message.error('重新合并失败，Schema 不存在')
        return
      }

      const currentSchema = toJS(schemaService.schema)
      const initialSchema = schemaStore.moduleDetailInit?.content
        ? JSON.parse(schemaStore.moduleDetailInit?.content)
        : null

      // 重新比较并开始合并过程
      setLoading(true)
      compareLatestSchema(f2bE2ESchema(currentSchema), initialSchema).finally(
        () => {
          setLoading(false)
        },
      )
    }, [])

    const save = useCallback(async (): Promise<void> => {
      const commandRegistry = getInject<CommandRegistry>(CommandRegistrySymbol)
      try {
        setLoading(true)
        await commandRegistry.executeCommand(HotkeyType.SAVE)
      } finally {
        setLoading(false)
      }
    }, [])

    // 处理差异选择结果
    const handleDiffSelection = useCallback(
      async (selections: DiffSelection) => {
        console.log('handleDiffSelection', selections)

        // 获取 schema 服务和当前 schema
        const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
        if (!schemaService.schema) {
          message.error('处理差异失败，Schema 不存在')
          return
        }

        // 创建当前 schema 的可变副本
        const currentSchema = f2bE2ESchema(toJS(schemaService.schema))

        // 处理每个组件差异
        componentDiffs.forEach((diff) => {
          const { id, type, currentConfig, latestConfig } = diff
          const selection = selections[id]

          // 只处理需要用户选择的组件类型
          if (needUpdateActionTypes.includes(type) && selection) {
            // 根据用户选择决定使用哪个配置
            const configToUse =
              selection === 'current' ? currentConfig : latestConfig

            if (configToUse) {
              // 更新组件配置
              const componentIndex =
                currentSchema.flattenedView?.components?.findIndex(
                  (comp) => comp.id === id,
                )

              if (componentIndex !== undefined && componentIndex >= 0) {
                // 更新已存在的组件
                if (
                  currentSchema.flattenedView &&
                  currentSchema.flattenedView.components
                ) {
                  currentSchema.flattenedView.components[componentIndex] =
                    configToUse.component
                }
              }
            }

            // 更新组件代码配置（如果存在）
            if (
              configToUse &&
              configToUse.componentCode &&
              configToUse.component.type
            ) {
              // 确保 componentCodes 对象存在
              if (!currentSchema.componentCodes) {
                currentSchema.componentCodes = {}
              }

              // 更新组件代码
              currentSchema.componentCodes[configToUse.component.type] =
                configToUse.componentCode
            }
          }
          // 对于远程新增的组件，如果用户选择了远程版本，则添加到组件列表
          else if (type === 'REMOTE_ADD' && latestConfig) {
            // 添加远程新增的组件到末尾
            if (
              currentSchema.flattenedView &&
              currentSchema.flattenedView.components
            ) {
              currentSchema.flattenedView.components.push(
                latestConfig.component,
              )
            }

            // 添加组件代码（如果存在）
            if (latestConfig.componentCode && latestConfig.component.type) {
              if (currentSchema.componentCodes) {
                currentSchema.componentCodes[latestConfig.component.type] =
                  latestConfig.componentCode
              }
            }
            // 添加childComponentIdMap到根节点
            if (currentSchema.flattenedView?.childComponentIdMap) {
              const rootId = schemaService.rootId || 'root'
              if (currentSchema.flattenedView.childComponentIdMap[rootId]) {
                currentSchema.flattenedView.childComponentIdMap[rootId].push(
                  latestConfig.component.id,
                )
              } else {
                // 如果根节点的子组件数组不存在，则创建一个
                currentSchema.flattenedView.childComponentIdMap[rootId] = [
                  latestConfig.component.id,
                ]
              }
            }
          }
        })

        // 获取远程日志，用于验证是否有新的提交
        const res = await findModuleVersionLogBrief({
          moduleId: DESIGNER_QUERY.moduleId,
          moduleVersion: +DESIGNER_QUERY.moduleVersion,
        })

        if (res.data && res.data.logs && res.data.logs.length > 0) {
          console.log('远程有日志记录，需要进行比较')
          // 获取远程最新的日志记录
          const remoteLatestLog = res.data.logs[0]

          // 比较远程最新日志ID和基准日志ID是否相同
          const isSameLog = baselineLogId === remoteLatestLog.id

          // 如果日志不同，说明在用户进行合并操作期间有新的提交
          if (!isSameLog) {
            console.log('远程日志与基准日志不同，可能有新提交')
            console.log('基准日志ID:', baselineLogId)
            console.log('远程日志ID:', remoteLatestLog.id)

            Modal.confirm({
              title: '检测到新的远程提交',
              content:
                '在您进行合并操作期间，其他用户已提交了新的变更。为避免覆盖他人修改，需要重新获取最新内容并重新合并。',
              okText: '重新合并',
              cancelText: '取消',
              onOk: () => {
                setBaselineLogId(remoteLatestLog.id)
                // 重置状态并重新开始合并过程
                resetAndRestartMerge()
              },
            })
            return // 中断当前保存操作，重新开始合并过程
          }
        }

        // 使用不同的变量名避免与save函数中的commandRegistry冲突
        await getInject<CommandRegistry>(CommandRegistrySymbol).executeCommand(
          HotkeyType.MERGE_SAVE,
          currentSchema,
        )
        setDiffModalVisible(false)
        setComponentDiffs([])
      },
      [componentDiffs, baselineLogId, resetAndRestartMerge],
    )

    /**
     * 检查是否有新的提交
     * 比较本地存储的日志和远程最新日志，判断是否有其他人的新提交
     * 如果有新提交，则获取最新schema并比较组件配置是否有变化
     * @param currentSchema 当前的schema对象
     * @returns 如果没有新提交或组件配置没有变化，返回true，否则返回false
     */
    const checkNoNewCommits = useCallback(
      async (
        currentSchema: E2EServerSchema,
        initialSchema: E2EServerSchema | null = null,
      ): Promise<boolean> => {
        if (!DESIGNER_QUERY.moduleId || !DESIGNER_QUERY.moduleVersion) {
          message.error('模块ID或版本不存在')
          return false
        }
        if (!currentSchema) {
          message.error('Schema 不存在')
          return false
        }

        // 获取当前模块在store中的状态
        const currentModuleState = saveLogStore.states.find(
          (state) =>
            state.moduleId === DESIGNER_QUERY.moduleId &&
            state.moduleVersion === +DESIGNER_QUERY.moduleVersion,
        )

        // 如果本地没有日志记录，需要获取远程日志进行判断
        if (!currentModuleState || currentModuleState.logs.length === 0) {
          console.log('本地没有保存日志记录，需要获取远程日志')

          // 获取远程日志
          const res = await findModuleVersionLogBrief({
            moduleId: DESIGNER_QUERY.moduleId,
            moduleVersion: +DESIGNER_QUERY.moduleVersion,
          })

          // 如果远程也没有日志，说明这是一个新模块或者从未有人提交过
          if (!res.data || !res.data.logs || res.data.logs.length === 0) {
            console.log('本地和远程都没有日志记录，可以直接保存')
            return true // 如果本地和远程都没有日志，可以直接保存
          }

          // 如果远程有日志但本地没有，说明有新提交
          console.log('本地没有日志但远程有日志，可能有新提交')
          // 获取最新的schema并比较组件配置
          // 如果有 initialSchema 则使用，否则传入 null
          return await compareLatestSchema(currentSchema, initialSchema)
        }

        // 获取远程最新的日志记录
        const res = await findModuleVersionLogBrief({
          moduleId: DESIGNER_QUERY.moduleId,
          moduleVersion: +DESIGNER_QUERY.moduleVersion,
        })

        if (!res.data || !res.data.logs || res.data.logs.length === 0) {
          console.log('远程没有日志记录')
          return true // 如果远程没有日志，可以认为没有新提交
        }

        // 获取本地最新的日志记录
        const localLatestLog = currentModuleState.logs[0]
        // 获取远程最新的日志记录
        const remoteLatestLog = res.data.logs[0]

        // 比较两个日志记录是否相同
        const isSameLog =
          localLatestLog &&
          remoteLatestLog &&
          localLatestLog.id === remoteLatestLog.id &&
          localLatestLog.createdAt === remoteLatestLog.createdAt

        // 如果日志不同，但schema内容没有变化，也可以认为不需要合并
        if (!isSameLog && initialSchema) {
          // 直接使用传入的 initialSchema参数
          const noSchemaChanges = isEqual(initialSchema, currentSchema)

          console.log('schema是否有变化:', !noSchemaChanges)
          if (noSchemaChanges) {
            console.log('虽然有新提交，但当前schema没有修改，可以跳过合并')
            return true
          }

          // 如果本地schema有修改，则获取最新的schema并比较组件配置
          return await compareLatestSchema(currentSchema, initialSchema)
        }

        return isSameLog
      },
      [],
    )
    /**
     * 获取最新的schema并与当前schema比较组件配置
     * @param currentSchema 当前的schema对象
     * @returns 如果组件配置没有变化返回true，否则返回false
     */
    const compareLatestSchema = async (
      currentSchema: E2EServerSchema,
      initialSchema: E2EServerSchema | null,
    ): Promise<boolean> => {
      try {
        // 获取最新的模块详情
        const { data: latestModuleDetail } = await getModuleDetail({
          id: DESIGNER_QUERY.moduleId,
          version: DESIGNER_QUERY.moduleVersion,
        })

        if (!latestModuleDetail) {
          console.log('获取最新模块详情失败')
          return false
        }

        // 将模块内容转换为E2ESchema格式
        const latestSchema: E2EServerSchema = JSON.parse(
          latestModuleDetail.content || '{}',
        )

        // 比较当前schema和最新schema的组件配置
        const currentComponents = currentSchema.flattenedView?.components || []
        const latestComponents = latestSchema.flattenedView?.components || []

        // 找出组件配置中不同的部分
        const componentsChanges: string[] = []
        const changedComponents: Array<{
          id: string
          currentConfig?: ComponentMapData
          latestConfig?: ComponentMapData
          type: ComponentChangeType
        }> = []

        // 创建组件映射表，便于比较同一ID的组件
        const initialComponentsMap = new Map<string, ComponentMapData>()
        const currentComponentsMap = new Map<string, ComponentMapData>()
        const latestComponentsMap = new Map<string, ComponentMapData>()

        // 将初始组件添加到映射表
        if (initialSchema?.flattenedView?.components) {
          initialSchema.flattenedView.components.forEach((component) => {
            const componentCode = initialSchema.componentCodes?.[component.type]
            initialComponentsMap.set(component.id, {
              component,
              componentCode,
            })
          })
        }

        // 将当前组件添加到映射表
        currentComponents.forEach((component) => {
          const componentCode = currentSchema.componentCodes?.[component.type]
          currentComponentsMap.set(component.id, {
            component,
            componentCode,
          })
        })

        // 将最新组件添加到映射表
        latestComponents.forEach((component) => {
          const componentCode = latestSchema.componentCodes?.[component.type]
          latestComponentsMap.set(component.id, {
            component,
            componentCode,
          })
        })

        // 收集所有组件ID
        const allComponentIds = new Set([
          ...initialComponentsMap.keys(),
          ...currentComponentsMap.keys(),
          ...latestComponentsMap.keys(),
        ])

        // 比较每个组件的配置
        allComponentIds.forEach((id) => {
          const initialData = initialComponentsMap.get(id)
          const currentData = currentComponentsMap.get(id)
          const latestData = latestComponentsMap.get(id)

          // 远程添加（在初始和当前版本中不存在，但在远程版本中存在）
          if (!initialData && !currentData && latestData) {
            changedComponents.push({
              id,
              currentConfig: undefined,
              latestConfig: latestData,
              type: 'REMOTE_ADD',
            })
            componentsChanges.push(
              `远程新增了组件「${latestData.component.name || id}」`,
            )
          }
          // 本地删除（在初始和远程版本中存在，但在当前版本中不存在）
          else if (initialData && !currentData && latestData) {
            changedComponents.push({
              id,
              currentConfig: undefined,
              latestConfig: latestData,
              type: 'LOCAL_DELETE',
            })
            componentsChanges.push(
              `本地删除了组件「${latestData.component.name || id}」`,
            )
          }
          // 远程删除（在初始和当前版本中存在，但在远程版本中不存在）
          else if (initialData && currentData && !latestData) {
            changedComponents.push({
              id,
              currentConfig: currentData,
              latestConfig: undefined,
              type: 'REMOTE_DELETE',
            })
            componentsChanges.push(
              `远程删除了组件「${currentData.component.name || id}」`,
            )
          }
          // 本地添加（在初始和远程版本中不存在，但在当前版本中存在）
          else if (!initialData && currentData && !latestData) {
            changedComponents.push({
              id,
              currentConfig: currentData,
              latestConfig: undefined,
              type: 'LOCAL_ADD',
            })
            componentsChanges.push(
              `本地新增了组件「${currentData.component.name || id}」`,
            )
          }
          // 双方都修改（在初始、当前和远程版本中都存在，且有变化）
          else if (initialData && currentData && latestData) {
            // 如果当前和远程版本与初始版本相比都有变化
            if (
              !isEqual(currentData, initialData) &&
              !isEqual(latestData, initialData) &&
              !isEqual(currentData, latestData)
            ) {
              changedComponents.push({
                id,
                currentConfig: currentData,
                latestConfig: latestData,
                type: 'CONFLICT_UPDATE',
              })
              componentsChanges.push(
                `组件「${currentData.component.name || id}」存在冲突修改`,
              )
            }
            // 如果只有远程版本与初始版本相比有变化
            else if (
              isEqual(currentData, initialData) &&
              !isEqual(latestData, initialData)
            ) {
              changedComponents.push({
                id,
                currentConfig: currentData,
                latestConfig: latestData,
                type: 'REMOTE_UPDATE',
              })
              componentsChanges.push(
                `远程修改了组件「${latestData.component.name || id}」`,
              )
            }
            // 如果只有当前版本与初始版本相比有变化
            else if (
              !isEqual(currentData, initialData) &&
              isEqual(latestData, initialData)
            ) {
              changedComponents.push({
                id,
                currentConfig: currentData,
                latestConfig: latestData,
                type: 'LOCAL_UPDATE',
              })
              componentsChanges.push(
                `本地修改了组件「${currentData.component.name || id}」`,
              )
            }
          }
          // 如果没有初始版本作为参考，回退到原来的两路比较
          else if (!initialData) {
            // 如果组件在当前和远程版本中都存在，但配置不同
            if (
              currentData &&
              latestData &&
              !isEqual(currentData, latestData)
            ) {
              changedComponents.push({
                id,
                currentConfig: currentData,
                latestConfig: latestData,
                type: 'REMOTE_UPDATE',
              })
              componentsChanges.push(
                `组件「${currentData.component.name || id}」的配置有变化`,
              )
            }
            // 如果组件在当前版本中不存在，但在远程版本中存在
            else if (!currentData && latestData) {
              changedComponents.push({
                id,
                currentConfig: undefined,
                latestConfig: latestData,
                type: 'LOCAL_ADD',
              })
              componentsChanges.push(
                `远程新增了组件「${latestData.component.name || id}」`,
              )
            }
            // 如果组件在当前版本中存在，但在远程版本中不存在
            else if (currentData && !latestData) {
              changedComponents.push({
                id,
                currentConfig: currentData,
                latestConfig: undefined,
                type: 'LOCAL_ADD',
              })
              componentsChanges.push(
                `本地新增了组件「${currentData.component.name || id}」`,
              )
            }
          }
        })

        // 检查组件代码的变化（组件类型层面）
        const currentComponentCodes = currentSchema.componentCodes || {}
        const latestComponentCodes = latestSchema.componentCodes || {}
        const allComponentTypes = new Set([
          ...Object.keys(currentComponentCodes),
          ...Object.keys(latestComponentCodes),
        ])

        allComponentTypes.forEach((type) => {
          const currentCode = currentComponentCodes[type]
          const latestCode = latestComponentCodes[type]

          if (!isEqual(currentCode, latestCode)) {
            // 从组件类型中提取组件名称
            const componentName = type.split('.').pop() || type
            // 检查是否已经在上面的组件实例比较中添加了这个变化
            const alreadyAdded = componentsChanges.some(
              (change) =>
                change.includes(componentName) && change.includes('代码配置'),
            )

            if (!alreadyAdded) {
              componentsChanges.push(
                `组件类型「${componentName}」的代码配置有变化`,
              )
            }
          }
        })

        const componentsEqual = changedComponents.length === 0
        const componentCodesEqual = isEqual(
          currentComponentCodes,
          latestComponentCodes,
        )
        const configsEqual = componentsEqual && componentCodesEqual

        console.log('组件配置是否相同:', configsEqual)
        console.log('变化的组件数量:', changedComponents.length)
        console.log('变化的组件详情:', changedComponents)

        if (!configsEqual) {
          // 检查是否所有变更都是本地变更
          const allLocalChanges = changedComponents.every(
            (component) =>
              component.type === 'LOCAL_ADD' ||
              component.type === 'LOCAL_DELETE',
          )

          if (allLocalChanges) {
            console.log('所有变更都是本地变更，直接保存')
            return true // 如果所有变更都是本地的，直接返回true允许保存
          }

          // 如果有远程变更或冲突，显示组件差异对比Modal
          setComponentDiffs(changedComponents)
          setDiffModalVisible(true)
          return false // 返回false，阻止保存操作，直到用户在差异对比Modal中做出选择
        }

        return configsEqual
      } catch (error) {
        console.error('比较最新schema失败:', error)
        return false
      }
    }

    // The checkNoNewCommits function is already defined above

    const handleBtnClick = useCallback(async () => {
      const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
      if (!schemaService.schema) {
        message.error('保存失败，Schema 不存在')
        console.error('schemaService', schemaService)
        return
      }
      // 检查是否有新的提交
      const currentSchema = toJS(schemaService.schema)
      const initialSchema = schemaStore.moduleDetailInit?.content
        ? JSON.parse(schemaStore.moduleDetailInit?.content)
        : null
      console.log('initialSchema', initialSchema)
      setLoading(true)

      // 获取远程日志并设置基准日志ID
      const res = await findModuleVersionLogBrief({
        moduleId: DESIGNER_QUERY.moduleId,
        moduleVersion: +DESIGNER_QUERY.moduleVersion,
      })

      if (res.data?.logs?.length > 0) {
        setBaselineLogId(res.data.logs[0].id)
        console.log('设置初始基准日志ID:', res.data.logs[0].id)
      }
      const noNewCommits = await checkNoNewCommits(
        f2bE2ESchema(currentSchema),
        initialSchema,
      )
      setLoading(false)

      if (!noNewCommits) {
        // 如果有新的提交且组件配置有变化，compareLatestSchema函数已经显示了提示信息
        // 这里不需要再显示提示，直接返回
        return
      }

      // 没有新的提交或组件配置没有变化，执行正常的保存流程
      save()
    }, [save, checkNoNewCommits])
    return (
      <>
        <NavActionButton
          {...props}
          type="primary"
          loading={loading}
          disabled={!schemaStore.savable}
          onClick={handleBtnClick}
        >
          保存
        </NavActionButton>

        {/* 组件差异对比Modal */}
        <ComponentDiffModal
          visible={diffModalVisible}
          diffs={componentDiffs}
          onCancel={(): void => {
            setDiffModalVisible(false)
            setComponentDiffs([])
            setBaselineLogId(null)
          }}
          onOk={handleDiffSelection}
        />
      </>
    )
  })
