import { type E2ESchemaService } from '@/pages/designer/services'
import { useLatestFn } from '@ad/canal-shared-ui'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { Form, InputNumber, Switch } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { type FC } from 'react'
import styled from 'styled-components'

/**
 * 设置面板
 */
export const SettingPanel: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService | null>(SchemaServiceSymbol)
  const px2vw = schemaService?.schema?.px2vw
  const px2rem = schemaService?.schema?.px2rem
  const handlePx2vwSwitchChange = useLatestFn((checked: boolean) => {
    if (checked) {
      schemaService?.enablePx2vw()
    } else {
      schemaService?.disablePx2vw()
    }
  })
  const handlePx2remSwitchChange = useLatestFn((checked: boolean) => {
    if (checked) {
      schemaService?.enablePx2rem()
    } else {
      schemaService?.disablePx2rem()
    }
  })
  const handlePx2vwInputNumberBlur = useLatestFn(
    (ev: React.FocusEvent<HTMLInputElement, Element>) => {
      const value = parseFloat(ev.target.value)
      if (isNaN(value) || value <= 0) return
      schemaService?.updatePx2vwDesignWidth(value)
    },
  )
  const handlePx2remInputNumberBlur = useLatestFn(
    (ev: React.FocusEvent<HTMLInputElement, Element>) => {
      const value = parseFloat(ev.target.value)
      if (isNaN(value) || value <= 0) return
      schemaService?.updatePx2remRootElementFontSize(value)
    },
  )
  return (
    <Container>
      <h3>配置</h3>
      <div>
        <div className="switchs">
          <Form.Item label="px2vw">
            <Switch checked={!!px2vw} onChange={handlePx2vwSwitchChange} />
          </Form.Item>
          <Form.Item label="px2rem">
            <Switch checked={!!px2rem} onChange={handlePx2remSwitchChange} />
          </Form.Item>
        </div>
        {!!px2vw && (
          <Form.Item label="设计宽度">
            <InputNumber
              value={px2vw?.designWidth || 0}
              min={1}
              onBlur={handlePx2vwInputNumberBlur}
            />
          </Form.Item>
        )}
        {!!px2rem && (
          <Form.Item label="根元素字体大小">
            <InputNumber
              value={px2rem?.rootElementFontSize || 0}
              min={1}
              onBlur={handlePx2remInputNumberBlur}
            />
          </Form.Item>
        )}
      </div>
    </Container>
  )
})

const Container = styled.div`
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .switchs {
    display: flex;
    justify-content: space-between;
  }
`
