import { OLD_BIZ_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import { AssetsService, AssetsServiceSymbol, init } from '@kael/designer-assets'
import { getInject } from '@kael/di'

/**
 * 初始化资产面板
 */
export function initAssets(): void {
  init({
    expanded: true,
    search: true,
    display: 'tab',
    getMenus(...args) {
      const assetsService = getInject<AssetsService>(AssetsServiceSymbol)
      const ret = assetsService.getMenus(...args)
      delete ret[OLD_BIZ_COMPONENT_LIB_NAME]
      return ret
    },
  })
}
