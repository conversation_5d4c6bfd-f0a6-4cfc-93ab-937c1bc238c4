import styled, { css } from 'styled-components'

/**
 * 导航栏图标按钮属性
 */
export interface NavIconButtonProps {
  /**
   * 是否禁用
   */
  $isDisabled?: boolean
}

/**
 * 导航栏图标按钮
 */
export const NavIconButton = styled.div<NavIconButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  width: 18px;
  cursor: pointer;

  ${
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    ({ $isDisabled }) =>
      $isDisabled
        ? css`
            color: #c0c0c0;
            cursor: not-allowed;
          `
        : css`
            &:hover {
              color: #0075ff;
            }
          `
  }
`
