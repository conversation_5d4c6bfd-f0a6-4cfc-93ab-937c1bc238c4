import { ControlLabel } from '@/components/control-label'
import { FrontJSExprEditorModalBtn } from '@/pages/designer/components'
import {
  DEFAULT_IIFE_EFFECT_JS_EXPR_STR,
  DEFAULT_IIFE_EFFECT_TS_EXPR_STR,
} from '@/pages/designer/constants'
import type { FrontExpressionScene } from '@ad/canal-ai'
import { useLatestFn } from '@ad/canal-shared-ui'
import type {
  BindIIFEActionExecEffect,
  E2ESchemaExpressionJS,
} from '@ad/e2e-schema'
import { Input } from '@m-ui/react'
import { useCallback, type ChangeEvent, type FC } from 'react'
import { FieldAsPath } from './field-as-path'
import type { BindIIFEActionEditorCommonProps } from './types'

/**
 * 执行副作用动作编辑器属性
 */
export interface BindIIFEActionExecEffectEditorProps
  extends BindIIFEActionEditorCommonProps<BindIIFEActionExecEffect> {}

/**
 * 执行副作用动作编辑器
 */
export const BindIIFEActionExecEffectEditor: FC<
  BindIIFEActionExecEffectEditorProps
> = ({ value, onChange, index }) => {
  const handleExprChange = useCallback(
    (expr?: E2ESchemaExpressionJS) => {
      if (expr) {
        onChange({
          ...value,
          expr,
        })
      }
    },
    [onChange, value],
  )
  const handleGetScene = useLatestFn((): FrontExpressionScene | null => {
    return {
      type: 'iifeActionEffect',
      index,
    }
  })
  const handleArg0IsPathChange = useCallback(
    (v: boolean) => {
      onChange({
        ...value,
        arg0IsPath: v,
      })
    },
    [onChange, value],
  )
  const handleArg0Change = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        arg0: e.target.value,
      })
    },
    [onChange, value],
  )
  return (
    <div>
      <div>
        <ControlLabel>副作用：</ControlLabel>
        <FrontJSExprEditorModalBtn
          value={value.expr}
          onChange={handleExprChange}
          modalTitle="编辑副作用"
          formItemLabel="副作用"
          defaultCodeES={DEFAULT_IIFE_EFFECT_JS_EXPR_STR}
          defaultCodeTS={DEFAULT_IIFE_EFFECT_TS_EXPR_STR}
          getScene={handleGetScene}
        />
      </div>
      <div>
        <ControlLabel>
          参数（字符串）：
          <FieldAsPath
            value={value.arg0IsPath}
            onChange={handleArg0IsPathChange}
          />
        </ControlLabel>
        <Input value={value.arg0} onChange={handleArg0Change} />
      </div>
    </div>
  )
}
