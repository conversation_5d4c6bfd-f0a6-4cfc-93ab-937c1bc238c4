import { IconButton } from '@/components/icon-button'
import type { E2ESchemaService } from '@/pages/designer/services'
import { getApiLabel } from '@/pages/designer/tools'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { PropertyCollapse } from '@kael/designer-property'
import {
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { SystemAddLine, SystemDeleteLine, SystemEditLine } from '@m-ui/icons'
import { Button, Popconfirm, Tooltip, Typography } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import {
  useCallback,
  type ComponentProps,
  type FC,
  type PropsWithChildren,
} from 'react'
import styled from 'styled-components'
import { ApiDetailModal, apiDetailStore } from '../api-detail-modal'

/**
 * PropertyCollapse.PropertyPanel 兼容 React 18
 */
export const Panel = PropertyCollapse.PropertyPanel as FC<
  PropsWithChildren<ComponentProps<typeof PropertyCollapse.PropertyPanel>>
>

/**
 * 获取内部组件版本
 * @param materialSchema 组件物料 Schema
 */
export function getInternalComponentVersion(
  materialSchema: E2ERemoteComponentMaterialSchema | null,
): string {
  let ret = '运行时内置'
  if (materialSchema?.runtimeVersion) {
    ret += ` ≥ ${materialSchema.runtimeVersion}`
  }
  return ret
}

/**
 * 属性基础面板接口列表
 */
export const PropertyBaseTabAPIList: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const stateService = getInject<StateService>(StateServiceSymbol)
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const comp = schemaService.findComponentById(
    stateService.selectedComponentId,
  )!
  const handleAddApiBtnClick = useCallback(() => {
    apiDetailStore.openModal()
  }, [])
  // 低代码引擎实现有问题，不监听 schema 字段，undo/redo 视图不会刷新
  void schemaService?.schema
  return (
    <>
      {(comp.apis || []).map((api) => (
        <APIItem key={api.id}>
          <APIItemTitle ellipsis>{getApiLabel(api)}</APIItemTitle>
          <Tooltip title="编辑" mouseEnterDelay={0.5}>
            <APIItemIconWrapper
              onClick={(): void => {
                apiDetailStore.openModal(api)
              }}
            >
              <SystemEditLine />
            </APIItemIconWrapper>
          </Tooltip>
          <Tooltip title="删除" mouseEnterDelay={0.5}>
            <Popconfirm
              title="确认删除？"
              placement="left"
              onConfirm={(): void => {
                schemaService.deleteComponentAPI(comp.id, api.id)
              }}
            >
              <APIItemIconWrapper>
                <SystemDeleteLine />
              </APIItemIconWrapper>
            </Popconfirm>
          </Tooltip>
        </APIItem>
      ))}
      <div>
        <Button
          shape="circle"
          icon={<SystemAddLine />}
          onClick={handleAddApiBtnClick}
        />
      </div>
      <ApiDetailModal />
    </>
  )
})

export const Item = styled.div`
  display: flex;
`

export const Label = styled.div`
  width: 72px;
  height: 26px;
  margin-right: 8px;
  flex-shrink: 0;
`

export const Content = styled.div`
  width: 0;
  flex-grow: 1;
  flex-shrink: 1;
`

export const LatestVersionTip = styled.span`
  font-weight: bold;
`

export const APIItem = styled.div`
  display: flex;
`

export const APIItemTitle = styled(Typography.Text)`
  color: #0075ff;
  width: 0;
  flex-grow: 1;
`

export const APIItemIconWrapper = styled(IconButton)`
  display: none;

  ${APIItem}:hover & {
    display: block;
    padding: 0 4px;
  }
`

export const HeaderWithIcon = styled.div`
  display: flex;
  align-items: center;

  .anticon {
    margin-left: 4px;
  }
`
