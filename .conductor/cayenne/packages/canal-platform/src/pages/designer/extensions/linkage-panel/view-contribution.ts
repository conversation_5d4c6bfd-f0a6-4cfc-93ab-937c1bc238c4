import {
  AbstractViewContribution,
  type ApplicationContribution,
} from '@kael/designer-core'
import { injectable, register } from '@kael/di'
import { LinkagePanelWidget } from './widget'

/**
 * 联动面板视图贡献点
 */
@injectable()
export class LinkagePanelViewContribution
  extends AbstractViewContribution<LinkagePanelWidget>
  implements ApplicationContribution
{
  public constructor() {
    super({
      widgetId: LinkagePanelWidget.ID,
      widgetName: LinkagePanelWidget.LABEL,
      widgetOptions: { area: 'left' },
    })
  }

  public async initializeLayout(): Promise<void> {
    await this.openView()
  }
}

/**
 * 注册联动面板视图贡献点
 */
export function registerLinkagePanelViewContribution(): void {
  register({
    token: LinkagePanelViewContribution.token,
    useClass: LinkagePanelViewContribution,
  })
}
