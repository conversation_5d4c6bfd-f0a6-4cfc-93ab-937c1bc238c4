import { useLatestFn } from '@ad/canal-shared-ui'
import type { E2ESchemaNormalizedExpression } from '@ad/e2e-schema'
import { type SetterRenderProps } from '@kael/designer-property'
import { normalizeExpression } from '@kael/schema-utils'
import { Input } from '@m-ui/react'
import { isString } from 'lodash'
import { useMemo, type ChangeEvent, type FC } from 'react'
import { cacheSetterValue } from '../../hocs'

/**
 * 字符串设置器属性
 */
export interface StringSetterProps {}

/**
 * 字符串设置器
 */
export const StringSetter: FC<SetterRenderProps<StringSetterProps>> =
  cacheSetterValue(({ value: expr, onChange }) => {
    const ne = normalizeExpression(expr) as E2ESchemaNormalizedExpression
    const finalValue = useMemo(() => {
      if (ne.type === 'static' && isString(ne.value)) {
        return ne.value
      }
      return ''
    }, [ne])
    const finalOnChange = useLatestFn((ev: ChangeEvent<HTMLInputElement>) => {
      // click 表示点击清除按钮
      const isClear = ev.type === 'click'
      const str = ev.target.value
      return onChange(!str && isClear ? undefined : str)
    })

    return (
      <Input
        placeholder="请输入"
        // 与内部 StringSetter 保持一致
        className="property-input"
        value={finalValue}
        onChange={finalOnChange}
        allowClear
      />
    )
  })
