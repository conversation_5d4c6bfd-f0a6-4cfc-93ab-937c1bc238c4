import { DESIGNER_QUERY } from '@/pages/designer/constants'
import { expressionTemplateStore } from '@/pages/designer/stores/expression-template-store'
import { updateExpressionTemplateUsingPost } from '@/services/backend/expression-template'
import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import { action, makeObservable, observable } from 'mobx'

/**
 * 保存表达式模板仓库
 */
export class SaveExpressionTemplateStore {
  /**
   * 模态框是否可见
   */
  private _isModalVisible = false

  /**
   * 模态框是否可见
   */
  public get isModalVisible(): boolean {
    return this._isModalVisible
  }

  /**
   * 编辑中的表达式模板信息
   */
  private _editingExpressionTemplateInfo: ExpressionTemplateInfo | null = null

  /**
   * 编辑中的表达式模板信息
   */
  public get editingExpressionTemplateInfo(): ExpressionTemplateInfo | null {
    return this._editingExpressionTemplateInfo
  }

  public constructor() {
    makeObservable<
      SaveExpressionTemplateStore,
      '_isModalVisible' | '_editingExpressionTemplateInfo'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isModalVisible: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _editingExpressionTemplateInfo: observable.ref,
      openModal: action,
      cancelModal: action,
      submitModal: action,
    })
  }

  /**
   * 打开对话框
   * @param editingExpressionTemplateInfo 编辑中的表达式模板信息
   */
  public openModal(
    editingExpressionTemplateInfo: Omit<ExpressionTemplateInfo, 'name'>,
  ): void {
    this._isModalVisible = true
    this._editingExpressionTemplateInfo = {
      ...editingExpressionTemplateInfo,
      // 类型一样时，保留 name
      name:
        editingExpressionTemplateInfo.expressionType ===
        this._editingExpressionTemplateInfo?.expressionType
          ? this._editingExpressionTemplateInfo.name
          : '',
    }
  }

  /**
   * 取消对话框
   */
  public cancelModal(): void {
    this._isModalVisible = false
  }

  /**
   * 提交对话框
   */
  public submitModal(info: Pick<ExpressionTemplateInfo, 'name'>): void {
    console.log('SaveExpressionTemplateStore::submitModal formValues', info)
    this._isModalVisible = false
    if (!this._editingExpressionTemplateInfo) {
      return
    }
    this._editingExpressionTemplateInfo.name = info.name
    const { expression, expressionType } = this._editingExpressionTemplateInfo
    ;(async (): Promise<void> => {
      await updateExpressionTemplateUsingPost({
        name: info.name,
        expression,
        domainCode: DESIGNER_QUERY.domainCode,
        expressionType,
      })
      await expressionTemplateStore.updateByType(expressionType)
    })()
  }
}

/**
 * 单例：保存表达式模板仓库
 */
export const saveExpressionTemplateStore = new SaveExpressionTemplateStore()

/**
 * 表达式模板信息
 */
export interface ExpressionTemplateInfo {
  /**
   * 名称
   */
  name: string
  /**
   * 表达式
   */
  expression: string
  /**
   * 类型
   */
  expressionType: BackExpressionType | FrontExpressionType
}
