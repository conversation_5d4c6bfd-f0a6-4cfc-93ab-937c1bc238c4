import type { E2ESchemaService } from '@/pages/designer/services'
import { CommandRegistry, CommandRegistrySymbol } from '@kael/designer-core'
import {
  NavigationContribution,
  type NavigationContributionPosition,
} from '@kael/designer-navigation'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject, injectable } from '@kael/di'
import { Tooltip } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, type ReactNode } from 'react'
import styled from 'styled-components'
import { HOTKEY_DISPLAY, HOTKEY_NAME, HotkeyType } from '../../../keybindings'
import { NavIconButton } from '../../components'
import { ReactComponent as SVGRedo } from './redo.svg'

/**
 * 重做贡献点
 */
@injectable()
export class RedoContribution extends NavigationContribution {
  public id = 'navigation-redo'
  public rank = 3
  public position: NavigationContributionPosition = 'right'

  public render(): ReactNode {
    return <RedoIconBtn />
  }
}

/**
 * 重做图标按钮
 */
const RedoIconBtn = observer((): JSX.Element => {
  const commandRegistry = getInject<CommandRegistry>(CommandRegistrySymbol)
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  /**
   * 处理点击事件
   */
  const handleClick = useCallback(() => {
    commandRegistry.executeCommand(HotkeyType.REDO)
  }, [commandRegistry])
  return (
    <Tooltip
      title={`${HOTKEY_NAME[HotkeyType.REDO]}（${
        HOTKEY_DISPLAY[HotkeyType.REDO]
      }）`}
    >
      <StyledNavIconButton
        $isDisabled={!schemaService.canRedo}
        onClick={handleClick}
      >
        <SVGRedo />
      </StyledNavIconButton>
    </Tooltip>
  )
})

const StyledNavIconButton = styled(NavIconButton)`
  margin-right: 18px;
`
