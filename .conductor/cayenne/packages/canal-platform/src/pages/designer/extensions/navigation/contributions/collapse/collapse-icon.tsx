import { IconButton } from '@/components/icon-button'
import { NormalFullScreenLine, NormalFullscreenExitLine } from '@m-ui/icons'
import { Tooltip } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback } from 'react'
import { collapseIconStore } from './collapse-icon-store'

/**
 * 折叠图标
 */
export const CollapseIcon = observer(() => {
  const { isCollapsed } = collapseIconStore
  const handleClick = useCallback(() => {
    collapseIconStore.toggle()
  }, [])
  return (
    <Tooltip title={isCollapsed ? '展开侧边栏' : '收起侧边栏'}>
      <IconButton onClick={handleClick}>
        {isCollapsed ? <NormalFullscreenExitLine /> : <NormalFullScreenLine />}
      </IconButton>
    </Tooltip>
  )
})
