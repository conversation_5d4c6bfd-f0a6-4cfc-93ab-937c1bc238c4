import { JSONEditor } from '@/components/json-editor'
import { jsonObjRule } from '@/utils'
import { Form, Modal, Radio, Switch } from '@m-ui/react'
import { mapValues } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useCallback, type FC, type ReactNode } from 'react'
import { useUpdateEffect } from 'react-use'
import { canvasPreviewerStore } from './canvas-previewer-store'
import {
  PREVIEW_ENVS,
  PREVIEW_ENV_OPTIONS,
  type PreviewEnv,
  type PreviewSetting,
} from './preview-setting'

/**
 * 预览配置
 */
export const PreviewSettingModal: FC = observer(() => {
  const [form] = Form.useForm<PreviewSettingFormValues>()
  const { isPreviewSettingVisible } = canvasPreviewerStore
  const handleModalCancel = useCallback(() => {
    canvasPreviewerStore.finishEditPreviewSetting()
  }, [])
  const handleModalOk = useCallback(() => {
    form.submit()
  }, [form])
  const handleFormFinish = useCallback(async () => {
    const formValues = form.getFieldsValue()
    const s: PreviewSetting = {
      env: formValues.env,
      headers: mapValues(formValues.headers, (hs) => JSON.parse(hs)),
      params: JSON.parse(formValues.params),
      componentJsUrlMap: JSON.parse(formValues.componentJsUrlMap),
      mock: formValues.mock,
    }
    canvasPreviewerStore.finishEditPreviewSetting(s)
  }, [form])
  useUpdateEffect(() => {
    if (isPreviewSettingVisible) {
      const s = canvasPreviewerStore.getPreviewSetting()
      const formValues: PreviewSettingFormValues = {
        env: s.env,
        headers: mapValues(s.headers, (hs) => JSON.stringify(hs, null, 2)),
        params: JSON.stringify(s.params, null, 2),
        componentJsUrlMap: JSON.stringify(s.componentJsUrlMap, null, 2),
        mock: s.mock,
      }
      // console.error('formValues init', formValues)
      form.setFieldsValue(formValues)
    }
  }, [form, isPreviewSettingVisible])

  return (
    <Modal
      visible={isPreviewSettingVisible}
      title="预览配置"
      okText="提交"
      onCancel={handleModalCancel}
      onOk={handleModalOk}
      maskClosable={false}
      width={800}
    >
      <Form form={form} onFinish={handleFormFinish}>
        <Form.Item label="预览环境" name="env" rules={[{ required: true }]}>
          <Radio.Group options={PREVIEW_ENV_OPTIONS} />
        </Form.Item>
        <Form.Item dependencies={['env']} noStyle>
          {({ getFieldValue }): ReactNode =>
            PREVIEW_ENVS.map((env) => (
              <Form.Item
                key={env}
                label="请求头"
                name={['headers', env]}
                hidden={getFieldValue('env') !== env}
                preserve
                rules={[{ required: true }, jsonObjRule]}
              >
                <JSONEditor />
              </Form.Item>
            ))
          }
        </Form.Item>
        <Form.Item
          label="参数（params）"
          name="params"
          rules={[{ required: true }, jsonObjRule]}
        >
          <JSONEditor />
        </Form.Item>
        <Form.Item
          label="组件 js url 对照表"
          tooltip="可用于将组件 js url 替换为本地链接，用于调试组件代码"
          name="componentJsUrlMap"
          rules={[{ required: true }, jsonObjRule]}
        >
          <JSONEditor />
        </Form.Item>
        <Form.Item label="开启 Mock" name="mock" valuePropName="checked">
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  )
})

/**
 * 预览配置表单值
 */
export interface PreviewSettingFormValues {
  /**
   * 环境
   */
  env: PreviewEnv
  /**
   * 请求头，按环境
   */
  headers: Record<PreviewEnv, string>
  /**
   * 参数
   */
  params: string
  /**
   * 组件 js url 对照表
   */
  componentJsUrlMap: string
  /**
   * 开启 Mock
   */
  mock: boolean
}
