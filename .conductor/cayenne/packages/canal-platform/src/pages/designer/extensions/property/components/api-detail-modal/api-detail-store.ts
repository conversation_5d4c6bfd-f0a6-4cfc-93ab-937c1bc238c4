import { DESIGNER_QUERY } from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import { getAllListByDomain } from '@/services/backend/data_source'
import { type DataSourceConfigDto } from '@/services/backend/models'
import { getBFFBuilderApis } from '@/services/bff/builder'
import { preloadBabel } from '@/utils'
import { createDataSourceLabel } from '@ad/canal-shared'
import type { E2EAPI } from '@ad/e2e-schema'
import {
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { action, makeObservable, observable, runInAction } from 'mobx'

export type { E2EAPI }

/**
 * 接口详情仓库
 */
export class ApiDetailStore {
  /**
   * 模态框是否可见
   */
  private _isModalVisible = false

  /**
   * 模态框是否可见
   */
  public get isModalVisible(): boolean {
    return this._isModalVisible
  }

  /**
   * 编辑中的接口
   */
  private _editingAPI: E2EAPI | null = null

  /**
   * 编辑中的接口
   */
  public get editingAPI(): E2EAPI | null {
    return this._editingAPI
  }

  /**
   * 所有服务的函数，服务 -> 所有函数
   */
  private _allServiceMethods: Record<string, string[]> = {}

  /**
   * 所有服务选项
   */
  public get allServicesOptions(): { label: string; value: string }[] {
    return Object.keys(this._allServiceMethods).map((v) => ({
      label: v,
      value: v,
    }))
  }

  /**
   * 所有数据源
   */
  private _allDataSource: DataSourceConfigDto[] = []

  /**
   * 所有数据源
   */
  public get allDataSource(): DataSourceConfigDto[] {
    return this._allDataSource
  }

  /**
   * 该业务域下所有数据源
   */
  public get dataSourceSelections(): { label: string; value: string }[] {
    return this._allDataSource
      .filter(
        (
          item: DataSourceConfigDto,
        ): item is DataSourceConfigDto & { id: string } => {
          return item.id !== undefined
        },
      )
      .map((dataSource) => ({
        value: dataSource.id,
        label: createDataSourceLabel(dataSource),
      }))
  }
  public getSingleDataSource(
    dataSourceId: string,
  ): DataSourceConfigDto | undefined {
    return this._allDataSource.find((item) => item.id === dataSourceId)
  }

  public constructor() {
    makeObservable<
      ApiDetailStore,
      | '_isModalVisible'
      | '_editingAPI'
      | '_allServiceMethods'
      | '_allDataSource'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isModalVisible: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _editingAPI: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _allServiceMethods: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _allDataSource: observable.ref,
      openModal: action,
      cancelModal: action,
      submitModal: action,
    })
    this.refreshAllServiceMethods()
    this.getAllServiceConfigByDomainCode()

    if (process.env.NODE_ENV === 'development') {
      Object.assign(window, {
        debugApiDetailStore: this,
      })
    }
  }

  /**
   * 打开对话框
   * @param editingAPI 编辑中的接口
   */
  public openModal(editingAPI: E2EAPI | null = null): void {
    this._isModalVisible = true
    this._editingAPI = editingAPI
    this.refreshAllServiceMethods()
    this.getAllServiceConfigByDomainCode()
    preloadBabel()
  }

  /**
   * 取消对话框
   */
  public cancelModal(): void {
    this._isModalVisible = false
  }

  /**
   * 提交对话框
   */
  public submitModal(api: Omit<E2EAPI, 'id'>): void {
    console.log('ApiDetailStore::submitModal formValues', api)
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    const stateService = getInject<StateService>(StateServiceSymbol)
    if (stateService.selectedComponentId) {
      if (this._editingAPI) {
        schemaService.updateComponentAPI(stateService.selectedComponentId, {
          ...api,
          id: this._editingAPI.id,
        })
      } else {
        schemaService.addComponentAPI(stateService.selectedComponentId, api)
      }
    }
    this._isModalVisible = false
  }

  /**
   * 刷新所有服务的函数
   */
  public async refreshAllServiceMethods(): Promise<void> {
    const { data } = await getBFFBuilderApis()
    const newObj: typeof this._allServiceMethods = {}
    for (const { service, method } of data) {
      newObj[service] = newObj[service] || []
      newObj[service].push(method)
    }
    runInAction(() => {
      this._allServiceMethods = newObj
    })
  }

  public async getAllServiceConfigByDomainCode(): Promise<void> {
    const data = await getAllListByDomain({
      domainCode: DESIGNER_QUERY.domainCode,
    })
    if (data.result === 1 && Array.isArray(data.data)) {
      runInAction(() => {
        this._allDataSource = data.data
      })
    }
  }

  /**
   * 根据服务获取方法选项
   * @param service 服务
   */
  public getMethodOptionsByService(
    service: string,
  ): { label: string; value: string }[] {
    console.log('getMethodOptionsByService')
    return (this._allServiceMethods[service] || []).map((v) => ({
      label: v,
      value: v,
    }))
  }
}

/**
 * 接口详情仓库，单例
 */
export const apiDetailStore = new ApiDetailStore()
