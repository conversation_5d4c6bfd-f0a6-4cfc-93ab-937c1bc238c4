import { ControlLabel } from '@/components/control-label'
import { FrontJSExprEditorModalBtn } from '@/pages/designer/components'
import {
  DEFAULT_ACTION_EFFECT_JS_EXPR_STR,
  DEFAULT_ACTION_EFFECT_TS_EXPR_STR,
} from '@/pages/designer/constants'
import type { CanalMaterialService } from '@/pages/designer/services'
import { getSimplifyComponent } from '@/pages/designer/tools'
import type { FrontExpressionScene } from '@ad/canal-ai'
import { useLatestFn } from '@ad/canal-shared-ui'
import type {
  E2ESchemaExpressionBind,
  E2ESchemaExpressionJS,
} from '@ad/e2e-schema'
import { isJsExpression } from '@ad/e2e-schema-utils'
import { MaterialServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { useCallback, useMemo, type FC } from 'react'
import type { ActionEditorCommonProps } from './types'

/**
 * 动作：执行副作用编辑器属性
 */
export interface ActionExecEffectEditorProps
  extends ActionEditorCommonProps<E2ESchemaExpressionBind> {}

/**
 * 动作：执行副作用编辑器
 */
export const ActionExecEffectEditor: FC<ActionExecEffectEditorProps> = ({
  value,
  onChange,
  index,
  componentId,
  fieldPropSchema,
}) => {
  const jsExpr: E2ESchemaExpressionJS = useMemo(() => {
    if (isJsExpression(value.fn)) {
      return value.fn
    }
    return {
      type: 'js',
      code: '',
      codeES: '',
    }
  }, [value.fn])
  const handleExprChange = useCallback(
    (expr?: E2ESchemaExpressionJS) => {
      if (expr) {
        onChange({
          ...value,
          fn: expr,
        })
      }
    },
    [onChange, value],
  )
  const handleGetScene = useLatestFn((): FrontExpressionScene | null => {
    const canalMaterialService = getInject<CanalMaterialService>(
      MaterialServiceSymbol,
    )
    const simplifyComponent = getSimplifyComponent(componentId)
    const logicPath =
      canalMaterialService.getLogicPathByFieldProp(fieldPropSchema)
    if (!simplifyComponent || !logicPath) {
      return null
    }
    return {
      type: 'eventActionEffect',
      component: simplifyComponent,
      propPath: logicPath,
      propName: `${fieldPropSchema.name}`,
      index,
    }
  })
  return (
    <div>
      <div>
        <ControlLabel>副作用：</ControlLabel>
        <FrontJSExprEditorModalBtn
          value={jsExpr}
          onChange={handleExprChange}
          modalTitle="编辑副作用"
          formItemLabel="副作用"
          defaultCodeES={DEFAULT_ACTION_EFFECT_JS_EXPR_STR}
          defaultCodeTS={DEFAULT_ACTION_EFFECT_TS_EXPR_STR}
          getScene={handleGetScene}
        />
      </div>
    </div>
  )
}
