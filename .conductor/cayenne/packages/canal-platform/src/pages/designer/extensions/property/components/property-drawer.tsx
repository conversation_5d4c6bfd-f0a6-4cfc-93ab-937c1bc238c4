import { Drawer } from '@m-ui/react'
import styled from 'styled-components'

/**
 * 属性抽屉容器
 */
export const PropertyDrawerContainer = styled.div`
  position: fixed;
  top: 48px;
  right: 280px;
  bottom: 0;
  left: 0;
  pointer-events: none;
  z-index: 1000;
  overflow: hidden;

  .ant-drawer-mask::after {
    content: ' ';
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    height: 48px;
    width: 100vw;
  }
`

/**
 * 属性抽屉
 */
export const PropertyDrawer = styled(Drawer)`
  position: absolute;
  pointer-events: auto;
`
