import { ControlLabel } from '@/components/control-label'
import { LongSelect } from '@/components/long-select'
import type {
  JSONRuleSpecialString,
  SpecialStringType,
  ValueOnChangeProps,
} from '@ad/canal-shared-ui'
import { Input } from '@m-ui/react'
import { useCallback, type ChangeEvent, type FC } from 'react'

/**
 * JSON 规则：特殊字符串，编辑器属性
 */
export interface JSONRuleSpecialStringEditorProps
  extends Required<ValueOnChangeProps<JSONRuleSpecialString>> {}

/**
 * JSON 规则：特殊字符串，编辑器
 */
export const JSONRuleSpecialStringEditor: FC<
  JSONRuleSpecialStringEditorProps
> = ({ value, onChange }) => {
  const handleStrTypeChange = useCallback(
    (v: SpecialStringType) => {
      onChange({
        ...value,
        strType: v,
        errMsg: getErrMsgBySpecialStringType(v),
      })
    },
    [onChange, value],
  )
  const handleErrMsgChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        errMsg: e.target.value,
      })
    },
    [onChange, value],
  )
  return (
    <div>
      <div>
        <ControlLabel>字符串类型：</ControlLabel>
        <LongSelect
          size="small"
          options={STR_TYPE_OPTIONS}
          value={value.strType}
          onChange={handleStrTypeChange}
        />
      </div>
      <div>
        <ControlLabel>错误提示：</ControlLabel>
        <Input
          size="small"
          value={value.errMsg}
          onChange={handleErrMsgChange}
        />
      </div>
    </div>
  )
}

/**
 * 字符串类型选项
 */
const STR_TYPE_OPTIONS: {
  label: string
  value: SpecialStringType
}[] = [
  {
    label: '邮箱',
    value: 'mail',
  },
  {
    label: '中国手机号',
    value: 'china-mobile',
  },
  {
    label: '链接',
    value: 'url',
  },
]

/**
 * 通过特殊字符串类型获取错误信息
 * @param t 特殊字符串类型
 */
export function getErrMsgBySpecialStringType(t: SpecialStringType): string {
  return `${STR_TYPE_OPTIONS.find((o) => o.value === t)?.label || ''}格式不正确`
}
