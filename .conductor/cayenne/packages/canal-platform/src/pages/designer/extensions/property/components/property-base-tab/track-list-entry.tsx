import { IconButton } from '@/components/icon-button'
import { JSONEditor } from '@/components/json-editor'
import type { E2ESchemaService } from '@/pages/designer/services'
import { jsonObjRule } from '@/utils'
import type { Track } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { SystemDeleteLine } from '@m-ui/icons'
import {
  Button,
  Form,
  Modal,
  Select,
  Switch,
  Tooltip,
  Typography,
} from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { nanoid } from 'nanoid'
import {
  useCallback,
  useEffect,
  useState,
  type FC,
  type ReactNode,
} from 'react'
import styled from 'styled-components'

/**
 * 埋点列表入口引用
 */
export interface TrackListEntryRef {
  /**
   * 打开
   */
  open(): void
}

/**
 * 埋点列表入口引用
 */
export let trackListEntryRef: TrackListEntryRef | null = null

/**
 * 埋点列表入口
 */
export const TrackListEntry: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const schema = schemaService.schema!
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm<TrackListFormValues>()
  const handleEditBtnClick = useCallback(() => {
    setIsModalVisible(true)
    form.setFieldsValue({
      tracks: (schemaService.schema?.tracks || []).map((track) => ({
        id: track.id,
        eventType: track.eventType,
        eventOptions: JSON.stringify(track.eventOptions, null, 2),
        canRepeat: track.canRepeat ?? true,
      })),
    })
  }, [form, schemaService])
  const handleModalCancel = useCallback(() => {
    setIsModalVisible(false)
  }, [])
  const handleModalOk = useCallback(() => {
    form.submit()
  }, [form])
  const handleFormFinish = useCallback(async () => {
    const formValues = form.getFieldsValue()
    // console.error('formValues', formValues)
    schemaService.updateTracks(
      formValues.tracks.map((track) => ({
        id: track.id,
        eventType: track.eventType,
        eventOptions: JSON.parse(track.eventOptions),
        canRepeat: track.canRepeat,
      })),
    )
    setIsModalVisible(false)
  }, [form, schemaService])
  useEffect(() => {
    trackListEntryRef = {
      open(): void {
        handleEditBtnClick()
      },
    }
  }, [handleEditBtnClick])

  return (
    <>
      <Button
        type={schema.tracks?.length ? 'primary' : 'secondary'}
        onClick={handleEditBtnClick}
      >
        编辑
        {schema.tracks?.length ? `（已有 ${schema.tracks.length} 个）` : ''}
      </Button>
      <Modal
        visible={isModalVisible}
        title="编辑埋点列表"
        okText="提交"
        onCancel={handleModalCancel}
        onOk={handleModalOk}
        maskClosable={false}
        width={800}
      >
        <Form form={form} onFinish={handleFormFinish}>
          <Form.List name="tracks">
            {(fields, { add, remove }): ReactNode => (
              <>
                {fields.map(({ key, name, ...restField }, index) => (
                  <div key={key}>
                    <Title>
                      <div>
                        <h3>埋点 {index}</h3>
                        <Typography.Text copyable>
                          {form.getFieldValue(['tracks', name, 'id'])}
                        </Typography.Text>
                      </div>

                      <Tooltip title="删除" placement="left">
                        <IconButton onClick={(): void => remove(name)}>
                          <SystemDeleteLine />
                        </IconButton>
                      </Tooltip>
                    </Title>
                    <Form.Item
                      {...restField}
                      label="事件类型"
                      name={[name, 'eventType']}
                      rules={[{ required: true }]}
                    >
                      <Select options={EVENT_TYPE_OPTIONS} />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      label="可重复上报"
                      name={[name, 'canRepeat']}
                      rules={[{ required: true }]}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      label="事件参数"
                      name={[name, 'eventOptions']}
                      rules={[{ required: true }, jsonObjRule]}
                    >
                      <JSONEditor />
                    </Form.Item>
                  </div>
                ))}
                <Form.Item>
                  <Button
                    type="primary"
                    onClick={(): void =>
                      add({
                        id: nanoid(),
                        eventType: 'CLICK',
                        eventOptions: JSON.stringify(
                          {
                            action: 'CLICK_ACTION',
                            params: {
                              paramA: 3,
                            },
                          },
                          null,
                          2,
                        ),
                        canRepeat: true,
                      } satisfies TrackListFormValuesTrack)
                    }
                  >
                    新增埋点
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </>
  )
})

/**
 * 埋点列表表单值
 */
interface TrackListFormValues {
  /**
   * 埋点
   */
  tracks: TrackListFormValuesTrack[]
}

/**
 * 埋点列表表单值埋点
 */
interface TrackListFormValuesTrack extends Pick<Track, 'id' | 'eventType'> {
  /**
   * 事件参数，JSON 字符串
   */
  eventOptions: string
  /**
   * 可重复上报
   */
  canRepeat: boolean
}

/**
 * 事件类型
 */
const EVENT_TYPES: Track['eventType'][] = [
  'PV',
  'SHOW',
  'VIDEO',
  'CUSTOM',
  'CUSTOM_EVENT',
  'RADAR',
  'CLICK',
  'DOUBLE_CLICK',
  'TRIPLE_CLICK',
  'LONG_PRESS',
  'PULL',
  'DRAG',
  'SCALE',
  'PULL_DOWN',
  'PULL_UP',
  'AUTO',
]

/**
 * 事件类型选项
 */
const EVENT_TYPE_OPTIONS = EVENT_TYPES.map((t) => ({
  label: t,
  value: t,
}))

const Title = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;

  h3 {
    display: inline-block;
    margin-bottom: 0;
    margin-right: 10px;
  }
`
