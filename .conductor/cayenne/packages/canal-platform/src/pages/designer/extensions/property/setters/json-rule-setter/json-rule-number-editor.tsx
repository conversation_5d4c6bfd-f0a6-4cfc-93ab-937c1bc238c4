import { ControlLabel } from '@/components/control-label'
import { LongInputNumber } from '@/components/long-input-number'
import type { JSONRuleNumber, ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Input, Select } from '@m-ui/react'
import { isNumber } from 'lodash'
import { useCallback, type ChangeEvent, type FC } from 'react'
import styled from 'styled-components'

/**
 * JSON 规则：数字，编辑器属性
 */
export interface JSONRuleNumberEditorProps
  extends Required<ValueOnChangeProps<JSONRuleNumber>> {}

/**
 * JSON 规则：数字，编辑器
 */
export const JSONRuleNumberEditor: FC<JSONRuleNumberEditorProps> = ({
  value,
  onChange,
}) => {
  const handleEqualMinChange = useCallback(
    (v: MinEqual) => {
      onChange({
        ...value,
        equalMin: v === MinEqual.EQUAL_MIN || undefined,
      })
    },
    [onChange, value],
  )
  const handleMinChange = useCallback(
    (v: number | null) => {
      onChange({
        ...value,
        min: isNumber(v) ? v : undefined,
      })
    },
    [onChange, value],
  )
  const handleEqualMaxChange = useCallback(
    (v: MaxEqual) => {
      onChange({
        ...value,
        equalMax: v === MaxEqual.EQUAL_MAX || undefined,
      })
    },
    [onChange, value],
  )
  const handleMaxChange = useCallback(
    (v: number | null) => {
      onChange({
        ...value,
        max: isNumber(v) ? v : undefined,
      })
    },
    [onChange, value],
  )
  const handlePrecisionChange = useCallback(
    (v: number | null) => {
      onChange({
        ...value,
        precision: isNumber(v) ? v : undefined,
      })
    },
    [onChange, value],
  )
  const handleErrMsgChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        errMsg: e.target.value || undefined,
      })
    },
    [onChange, value],
  )
  return (
    <div>
      <div>
        <ControlLabel>范围：</ControlLabel>
        <MinMaxContainer>
          <Select
            size="small"
            options={MIN_EQUAL_OPTIONS}
            value={value.equalMin ? MinEqual.EQUAL_MIN : MinEqual.MIN}
            onChange={handleEqualMinChange}
          />
          <LongInputNumber
            size="small"
            value={value.min}
            onChange={handleMinChange}
          />
        </MinMaxContainer>
        <MinMaxContainer>
          <Select
            size="small"
            options={MAX_EQUAL_OPTIONS}
            value={value.equalMax ? MaxEqual.EQUAL_MAX : MaxEqual.MAX}
            onChange={handleEqualMaxChange}
          />
          <LongInputNumber
            size="small"
            value={value.max}
            onChange={handleMaxChange}
          />
        </MinMaxContainer>
      </div>
      <div>
        <ControlLabel>精度（小数位数）：</ControlLabel>
        <LongInputNumber
          size="small"
          min={0}
          precision={0}
          value={value.precision}
          onChange={handlePrecisionChange}
        />
      </div>
      <div>
        <ControlLabel>错误提示：</ControlLabel>
        <Input
          size="small"
          value={value.errMsg || ''}
          onChange={handleErrMsgChange}
        />
      </div>
    </div>
  )
}

/**
 * 最小值或等于最小值
 */
enum MinEqual {
  MIN = '>',
  EQUAL_MIN = '≥',
}

/**
 * 最小值或等于最小值选项
 */
const MIN_EQUAL_OPTIONS = [
  {
    label: MinEqual.MIN,
    value: MinEqual.MIN,
  },
  {
    label: MinEqual.EQUAL_MIN,
    value: MinEqual.EQUAL_MIN,
  },
]

/**
 * 最大值或等于最大值
 */
enum MaxEqual {
  MAX = '<',
  EQUAL_MAX = '≤',
}

/**
 * 最大值或等于最大值选项
 */
const MAX_EQUAL_OPTIONS = [
  {
    label: MaxEqual.MAX,
    value: MaxEqual.MAX,
  },
  {
    label: MaxEqual.EQUAL_MAX,
    value: MaxEqual.EQUAL_MAX,
  },
]

const MinMaxContainer = styled.div`
  display: flex;
`
