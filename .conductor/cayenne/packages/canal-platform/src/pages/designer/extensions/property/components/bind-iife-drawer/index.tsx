import { LongLabelFormItem } from '@/components/long-label-form-item'
import { SpaceBetweenDiv } from '@/components/space-between-div'
import { BackJSExprEditor } from '@/pages/designer/components'
import {
  BACK_JS_EXPR_LABEL,
  BACK_TS_EXPR_LABEL,
  BIG_JS_LINK,
  DEFAULT_IIFE_JS_EXPR_STR,
  DEFAULT_IIFE_TS_EXPR_STR,
} from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import { getApiLabel, getApiLabelsInComponent } from '@/pages/designer/tools'
import type { ExpressionTemplate } from '@/services/backend/models'
import { jsRule, tsRule, useMoneyRule } from '@/utils'
import type { BackExpressionScene } from '@ad/canal-ai'
import {
  BACK_EXPRESSION_TYPE_JS_TS_OPTIONS,
  BackExpressionType,
  FrontExpressionType,
  arrayToArrayable,
  arrayableToArray,
} from '@ad/canal-shared'
import { useLatestFn } from '@ad/canal-shared-ui'
import type { E2ESchemaExpressionAPI } from '@ad/e2e-schema'
import { getBindIIFEActionLabel, isJsExpression } from '@ad/e2e-schema-utils'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { normalizeExpression } from '@kael/schema-utils'
import { Button, Form, Select } from '@m-ui/react'
import { isUndefined } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useCallback, useEffect, type FC, type ReactNode } from 'react'
import styled from 'styled-components'
import { ExpressionTemplateActions } from '..'
import {
  AiGenExprCtxFc,
  AiGenerateExpression,
} from '../../../../components/ai-generate-expression'
import { PropertyDrawer, PropertyDrawerContainer } from '../property-drawer'
import { BindIIFEActionsEditor } from './bind-iife-actions-editor'
import { bindIIFEStore } from './bind-iife-store'
import {
  decodeJSExpr2BindIIFEFormValues,
  encodeBindIIFEFormValues2JSExpr,
  type BindIIFEFormValues,
} from './utils'

export * from './bind-iife-store'

/**
 * 绑定立即执行（前端动作）抽屉
 */
export const BindIIFEDrawer: FC = observer(() => {
  const { isDrawerVisible } = bindIIFEStore
  const [form] = Form.useForm<BindIIFEFormValues>()
  const handleRemoveBtnClick = useCallback(() => {
    bindIIFEStore.submitDrawer()
  }, [])
  const handleCancelBtnClick = useCallback(() => {
    bindIIFEStore.cancelDrawer()
  }, [])
  const handleSubmitBtnClick = useCallback(() => {
    form.submit()
  }, [form])
  const handleFormFinish = useCallback(async () => {
    const formValues = form.getFieldsValue()
    const apiExpr: E2ESchemaExpressionAPI = {
      type: 'api',
      apiId: arrayToArrayable(formValues.apiId || []),
    }
    apiExpr.transform = await encodeBindIIFEFormValues2JSExpr(formValues)
    bindIIFEStore.submitDrawer(apiExpr)
  }, [form])
  const handleApplyExpressionTemplate = useCallback(
    (tpl: ExpressionTemplate) => {
      form.setFieldValue(
        tpl.expressionType === BackExpressionType.JAVASCRIPT
          ? 'jsExpr'
          : 'tsExpr',
        tpl.expression,
      )
    },
    [form],
  )
  const schemaService = getInject<E2ESchemaService | null>(SchemaServiceSymbol)
  useEffect(() => {
    if (!isDrawerVisible) {
      return
    }
    const formValues: Partial<BindIIFEFormValues> = {
      apiId: undefined,
      exprType: BackExpressionType.JAVASCRIPT,
      jsExpr: DEFAULT_IIFE_JS_EXPR_STR,
      tsExpr: DEFAULT_IIFE_TS_EXPR_STR,
      actions: [],
    }
    const iife = schemaService?.schema?.iife
    const normalizedIIFE = isUndefined(iife) ? iife : normalizeExpression(iife)
    if (normalizedIIFE?.type === 'api') {
      formValues.apiId = arrayableToArray(normalizedIIFE.apiId)
      if (isJsExpression(normalizedIIFE.transform)) {
        Object.assign(
          formValues,
          decodeJSExpr2BindIIFEFormValues(normalizedIIFE.transform),
        )
      }
    }
    form.setFieldsValue(formValues)
  }, [isDrawerVisible, schemaService, form])
  const rootApis =
    (schemaService && schemaService.getComponentAPIs(schemaService.rootId)) ||
    []
  const apiOptions = [
    ...rootApis.map((api) => ({
      label: getApiLabel(api, 'module'),
      value: api.id,
    })),
  ]
  const handleGetScene = useLatestFn(() => {
    const apiIds: string[] = form.getFieldValue('apiId') || []
    const { actions } = form.getFieldsValue()
    return {
      type: 'iife',
      apis: getApiLabelsInComponent(undefined, apiIds),
      actions: actions.map(getBindIIFEActionLabel),
    } satisfies BackExpressionScene
  })
  const handleGenerateCode = useLatestFn(
    (
      code: string,
      expressionType: BackExpressionType | FrontExpressionType,
    ) => {
      if (expressionType === BackExpressionType.TYPESCRIPT) {
        const oldCode = form.getFieldValue('tsExpr')
        form.setFieldValue('tsExpr', code)
        return oldCode
      } else {
        const oldCode = form.getFieldValue('jsExpr')
        form.setFieldValue('jsExpr', code)
        return oldCode
      }
    },
  )
  const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
  // 低代码引擎实现有问题，不监听 schema 字段，undo/redo 视图不会刷新
  void schemaService?.schema
  return (
    <AiGenExprCtxFc>
      <PropertyDrawerContainer>
        <PropertyDrawer
          title="绑定前端动作"
          getContainer={false}
          visible={isDrawerVisible}
          closable={false}
          maskClosable={false}
          destroyOnClose
          extra={
            <>
              <ActionBtn danger onClick={handleRemoveBtnClick}>
                移除绑定
              </ActionBtn>
              <ActionBtn onClick={handleCancelBtnClick}>取消</ActionBtn>
              <ActionBtn type="primary" onClick={handleSubmitBtnClick}>
                提交
              </ActionBtn>
            </>
          }
          width={1200}
        >
          <Form form={form} onFinish={handleFormFinish}>
            <Form.Item label="接口" name="apiId">
              <Select options={apiOptions} mode="multiple" />
            </Form.Item>
            <Form.Item
              label="动作列表"
              name="actions"
              rules={[{ required: true }]}
            >
              <BindIIFEActionsEditor />
            </Form.Item>
            <Form.Item
              label="表达式类型"
              name="exprType"
              rules={[{ required: true }]}
            >
              <Select options={BACK_EXPRESSION_TYPE_JS_TS_OPTIONS} />
            </Form.Item>
            <Form.Item dependencies={['exprType', 'jsExpr', 'tsExpr']} noStyle>
              {({ getFieldValue }): ReactNode => (
                <>
                  {getFieldValue('exprType') ===
                    BackExpressionType.JAVASCRIPT && (
                    <LongLabelFormItem
                      label={
                        <SpaceBetweenDiv>
                          <div>生成动作编号（{BACK_JS_EXPR_LABEL}）:</div>
                          <AiGenerateExpression
                            expressionType={BackExpressionType.JAVASCRIPT}
                            getScene={handleGetScene}
                            onGenerate={handleGenerateCode}
                          />
                          <ExpressionTemplateActions
                            expression={getFieldValue('jsExpr')}
                            expressionType={BackExpressionType.JAVASCRIPT}
                            onApply={handleApplyExpressionTemplate}
                          />
                        </SpaceBetweenDiv>
                      }
                      labelCol={{
                        span: 24,
                      }}
                      wrapperCol={{
                        span: 24,
                      }}
                      name="jsExpr"
                      rules={[{ required: true }, jsRule, moneyRule]}
                      extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                    >
                      <BackJSExprEditor height="calc(100vh - 337px)" />
                    </LongLabelFormItem>
                  )}
                  {getFieldValue('exprType') ===
                    BackExpressionType.TYPESCRIPT && (
                    <LongLabelFormItem
                      label={
                        <SpaceBetweenDiv>
                          <div>生成动作编号（{BACK_TS_EXPR_LABEL}）:</div>
                          <AiGenerateExpression
                            expressionType={BackExpressionType.TYPESCRIPT}
                            getScene={handleGetScene}
                            onGenerate={handleGenerateCode}
                          />
                          <ExpressionTemplateActions
                            expression={getFieldValue('tsExpr')}
                            expressionType={BackExpressionType.TYPESCRIPT}
                            onApply={handleApplyExpressionTemplate}
                          />
                        </SpaceBetweenDiv>
                      }
                      labelCol={{
                        span: 24,
                      }}
                      wrapperCol={{
                        span: 24,
                      }}
                      name="tsExpr"
                      rules={[{ required: true }, tsRule, moneyRule]}
                      extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                    >
                      <BackJSExprEditor
                        language="typescript"
                        height="calc(100vh - 337px)"
                      />
                    </LongLabelFormItem>
                  )}
                </>
              )}
            </Form.Item>
          </Form>
        </PropertyDrawer>
      </PropertyDrawerContainer>
    </AiGenExprCtxFc>
  )
})

const ActionBtn = styled(Button)`
  margin-left: 8px;
`
