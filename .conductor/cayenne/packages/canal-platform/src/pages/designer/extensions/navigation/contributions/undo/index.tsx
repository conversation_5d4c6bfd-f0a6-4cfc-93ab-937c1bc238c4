import type { E2ESchemaService } from '@/pages/designer/services'
import { CommandRegistry, CommandRegistrySymbol } from '@kael/designer-core'
import {
  NavigationContribution,
  type NavigationContributionPosition,
} from '@kael/designer-navigation'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject, injectable } from '@kael/di'
import { Tooltip } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, type ReactNode } from 'react'
import { HOTKEY_DISPLAY, HOTKEY_NAME, HotkeyType } from '../../../keybindings'
import { NavIconButton } from '../../components'
import { ReactComponent as SVGUndo } from './undo.svg'

/**
 * 撤销贡献点
 */
@injectable()
export class UndoContribution extends NavigationContribution {
  public id = 'navigation-undo'
  public rank = 2
  public position: NavigationContributionPosition = 'right'

  public render(): ReactNode {
    return <UndoIconBtn />
  }
}

/**
 * 撤销图标按钮
 */
const UndoIconBtn = observer((): JSX.Element => {
  const commandRegistry = getInject<CommandRegistry>(CommandRegistrySymbol)
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  /**
   * 处理点击事件
   */
  const handleClick = useCallback(() => {
    commandRegistry.executeCommand(HotkeyType.UNDO)
  }, [commandRegistry])
  return (
    <Tooltip
      title={`${HOTKEY_NAME[HotkeyType.UNDO]}（${
        HOTKEY_DISPLAY[HotkeyType.UNDO]
      }）`}
    >
      <NavIconButton $isDisabled={!schemaService.canUndo} onClick={handleClick}>
        <SVGUndo />
      </NavIconButton>
    </Tooltip>
  )
})
