import { IconButton } from '@/components/icon-button'
import { LongSelect } from '@/components/long-select'
import { DEFAULT_IIFE_EFFECT_JS_EXPR_STR } from '@/pages/designer/constants'
import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import type { BindIIFEAction, BindIIFEActionType } from '@ad/e2e-schema'
import { SystemDeleteLine } from '@m-ui/icons'
import { Button, Tooltip } from '@m-ui/react'
import { cloneDeep } from 'lodash'
import { useCallback, useMemo, type FC } from 'react'
import styled from 'styled-components'
import {
  BindIIFEActionExecEffectEditor,
  BindIIFEActionExecExternalFnEditor,
  BindIIFEActionOpenUrlEditor,
} from './bind-iife-action-editors'
import type { BindIIFEActionEditorCommonProps } from './bind-iife-action-editors/types'

/**
 * 绑定立即执行（前端动作）动作编辑器属性
 */
export interface BindIIFEActionsEditorProps
  extends ValueOnChangeProps<BindIIFEAction[]> {}

/**
 * 绑定立即执行（前端动作）动作编辑器
 */
export const BindIIFEActionsEditor: FC<BindIIFEActionsEditorProps> = ({
  value: rawValue,
  onChange,
}) => {
  const actions = useMemo(() => rawValue || [], [rawValue])
  const handleAddActionBtnClick = useCallback(() => {
    onChange?.([
      ...actions,
      cloneDeep(DEFAULT_BIND_IIFE_ACTION_BY_TYPE['open-url']),
    ])
  }, [onChange, actions])
  const handleDeleteActionBtnClick = useCallback(
    (idx: number) => {
      const newActions = [...actions]
      newActions.splice(idx, 1)
      onChange?.(newActions)
    },
    [onChange, actions],
  )
  const handleActionTypeChange = useCallback(
    (idx: number, type: BindIIFEActionType) => {
      const newActions = [...actions]
      newActions[idx] = cloneDeep(DEFAULT_BIND_IIFE_ACTION_BY_TYPE[type])
      onChange?.(newActions)
    },
    [onChange, actions],
  )
  const handleActionChange = useCallback(
    (idx: number, a: BindIIFEAction) => {
      const newActions = [...actions]
      newActions[idx] = a
      onChange?.(newActions)
    },
    [onChange, actions],
  )
  return (
    <div>
      <Button type="primary" onClick={handleAddActionBtnClick}>
        新增动作
      </Button>
      {actions.map((rule, index) => {
        const Editor = BIND_IIFE_ACTION_EDITOR_BY_TYPE[rule.type]
        return (
          <Item key={index}>
            <ItemHeader>
              <b>动作 {index}</b>
              <Tooltip title="删除" placement="left">
                <IconButton
                  onClick={handleDeleteActionBtnClick.bind(null, index)}
                >
                  <SystemDeleteLine />
                </IconButton>
              </Tooltip>
            </ItemHeader>
            <TypeSelect
              options={BIND_IIFE_ACTION_TYPE_OPTIONS}
              value={rule.type}
              onChange={handleActionTypeChange.bind(null, index)}
            />
            <Editor
              value={rule}
              onChange={handleActionChange.bind(null, index)}
              index={index}
            />
          </Item>
        )
      })}
    </div>
  )
}

/**
 * 按类型的默认动作
 */
const DEFAULT_BIND_IIFE_ACTION_BY_TYPE: Record<
  BindIIFEActionType,
  BindIIFEAction
> = {
  ['open-url']: {
    type: 'open-url',
    url: 'https://www.kuaishou.com',
    urlIsPath: false,
    inPlace: true,
  },
  ['exec-external-fn']: {
    type: 'exec-external-fn',
    fnPath: 'customFn',
    arg0: '',
    arg0IsPath: false,
  },
  ['exec-effect']: {
    type: 'exec-effect',
    expr: {
      type: 'js',
      code: `(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = void 0;\n/**\n* @param {Container} ctx 上下文\n* @param {any} arg 参数\n*/\nvar _default = exports["default"] = function _default(ctx, arg) {\n  console.log('模块级前端动作副作用', ctx, arg);\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)`,
      codeES: DEFAULT_IIFE_EFFECT_JS_EXPR_STR,
    },
    arg0: '',
    arg0IsPath: false,
  },
}

/**
 * 动作类型选项
 */
const BIND_IIFE_ACTION_TYPE_OPTIONS = [
  {
    label: '打开链接',
    value: 'open-url',
  },
  {
    label: '执行外部函数',
    value: 'exec-external-fn',
  },
  {
    label: '执行副作用',
    value: 'exec-effect',
  },
]

/**
 * 按类型的动作编辑器
 */
const BIND_IIFE_ACTION_EDITOR_BY_TYPE = {
  ['open-url']: BindIIFEActionOpenUrlEditor,
  ['exec-external-fn']: BindIIFEActionExecExternalFnEditor,
  ['exec-effect']: BindIIFEActionExecEffectEditor,
} as Record<
  BindIIFEActionType,
  FC<BindIIFEActionEditorCommonProps<BindIIFEAction>>
>

const Item = styled.div`
  margin-top: 5px;
`

const ItemHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const TypeSelect = styled(LongSelect)`
  margin-top: 5px;
`
