import { ControlLabel } from '@/components/control-label'
import { LongInputNumber } from '@/components/long-input-number'
import type { JSONRuleLength, ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Input } from '@m-ui/react'
import { isNumber } from 'lodash'
import { useCallback, type ChangeEvent, type FC } from 'react'

/**
 * JSON 规则：长度，编辑器属性
 */
export interface JSONRuleLengthEditorProps
  extends Required<ValueOnChangeProps<JSONRuleLength>> {}

/**
 * JSON 规则：长度，编辑器
 */
export const JSONRuleLengthEditor: FC<JSONRuleLengthEditorProps> = ({
  value,
  onChange,
}) => {
  const handleMinChange = useCallback(
    (v: number | null) => {
      onChange({
        ...value,
        min: isNumber(v) ? v : undefined,
      })
    },
    [onChange, value],
  )
  const handleMaxChange = useCallback(
    (v: number | null) => {
      onChange({
        ...value,
        max: isNumber(v) ? v : undefined,
      })
    },
    [onChange, value],
  )
  const handleErrMsgChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        errMsg: e.target.value || undefined,
      })
    },
    [onChange, value],
  )
  return (
    <div>
      <div>
        <ControlLabel>最小长度（包括）：</ControlLabel>
        <LongInputNumber
          size="small"
          min={0}
          precision={0}
          value={value.min}
          onChange={handleMinChange}
        />
      </div>
      <div>
        <ControlLabel>最大长度（包括）：</ControlLabel>
        <LongInputNumber
          size="small"
          min={0}
          precision={0}
          value={value.max}
          onChange={handleMaxChange}
        />
      </div>
      <div>
        <ControlLabel>错误提示：</ControlLabel>
        <Input
          size="small"
          value={value.errMsg}
          onChange={handleErrMsgChange}
        />
      </div>
    </div>
  )
}
