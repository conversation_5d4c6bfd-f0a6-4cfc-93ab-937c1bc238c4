import { J<PERSON><PERSON><PERSON>or } from '@/components/json-editor'
import { SpaceBetweenDiv } from '@/components/space-between-div'
import { BackJSExprEditor } from '@/pages/designer/components'
import {
  AiGenerateExpression,
  AiGenExprCtxFc,
} from '@/pages/designer/components/ai-generate-expression'
import {
  BACK_JS_EXPR_LABEL,
  BACK_JSONATA_EXPR_LABEL,
  BACK_TS_EXPR_LABEL,
  BIG_JS_LINK,
  DEFAULT_ARG_JS_EXPR_STR,
  DEFAULT_ARG_JSONATA_EXPR_STR,
  DEFAULT_ARG_TS_EXPR_STR,
  DEFAULT_IF_JS_EXPR_STR,
  DEFAULT_IF_JSONATA_EXPR_STR,
  DEFAULT_IF_TS_EXPR_STR,
  DESIGNER_QUERY,
  INTERNAL_COMPONENT_TYPE_ROOT,
} from '@/pages/designer/constants'
import {
  getDataSourceLabelBy<PERSON><PERSON>,
  getSelectedSimplifyComponent,
} from '@/pages/designer/tools'
import type { ExpressionTemplate } from '@/services/backend/models'
import {
  asyncBackJSExprToES5,
  asyncBackTSExprToJS,
  jsonObjRule,
  jsRule,
  tsRule,
  useMoneyRule,
} from '@/utils'
import type {
  BackExpressionScene,
  BackExpressionSceneApiArgs,
  BackExpressionSceneApiIf,
} from '@ad/canal-ai'
import {
  BACK_EXPRESSION_TYPE_OPTIONS,
  BackExpressionType,
  FrontExpressionType,
} from '@ad/canal-shared'
import { useLatestFn } from '@ad/canal-shared-ui'
import type { E2EAPI } from '@ad/e2e-schema'
import { normalizeExpression } from '@kael/schema-utils'
import { Alert, Form, Input, Modal, Select, Tag } from '@m-ui/react'
import type { FormInstance } from '@m-ui/react/es/form/Form'
import { observer } from 'mobx-react-lite'
import { useCallback, type FC, type ReactNode } from 'react'
import { useUpdateEffect } from 'react-use'
import styled from 'styled-components'
import { ExpressionTemplateActions } from '../expression-template-actions'
import { apiDetailStore } from './api-detail-store'

export * from './api-detail-store'

/**
 * 接口详情模态框
 */
export const ApiDetailModal: FC = observer(() => {
  const [form] = Form.useForm<APIDetailFormValues>()
  const { isModalVisible, editingAPI } = apiDetailStore
  const handleModalCancel = useCallback(() => {
    apiDetailStore.cancelModal()
  }, [])
  const handleModalOk = useCallback(() => {
    form.submit()
  }, [form])
  const handleFormFinish = useCallback(async () => {
    const formValues = form.getFieldsValue()
    let api: Omit<E2EAPI, 'id'> = {}
    if (formValues.dataSourceId) {
      api = {
        dataSourceId: formValues.dataSourceId,
      }
    } else {
      if (formValues.service && formValues.method) {
        api = {
          service: formValues.service,
          method: formValues.method,
        }
      } else {
        Modal.error({
          title: '错误',
          content: '请选择服务和方法或数据源',
        })
        return
      }
    }
    if (formValues.name) {
      api.name = formValues.name
    }
    switch (formValues.argType) {
      case BackExpressionType.JSONATA: {
        api.args = [
          {
            type: 'degraded-jsonata',
            value: JSON.parse(formValues.jsonataExpr),
          },
        ]
        break
      }
      case BackExpressionType.JAVASCRIPT: {
        api.args = [
          {
            type: 'js',
            code: await asyncBackJSExprToES5(formValues.jsExpr),
            codeES: formValues.jsExpr,
          },
        ]
        break
      }
      case BackExpressionType.TYPESCRIPT: {
        api.args = [
          {
            type: 'js',
            code: await asyncBackTSExprToJS(formValues.tsExpr),
            codeTS: formValues.tsExpr,
          },
        ]
        break
      }
    }
    switch (formValues.ifType) {
      case BackExpressionType.JSONATA: {
        api.if = {
          type: 'degraded-jsonata',
          value: JSON.parse(formValues.ifJSONataExpr),
        }

        break
      }
      case BackExpressionType.JAVASCRIPT: {
        api.if = {
          type: 'js',
          code: await asyncBackJSExprToES5(formValues.ifJSExpr),
          codeES: formValues.ifJSExpr,
        }
        break
      }
      case BackExpressionType.TYPESCRIPT: {
        api.if = {
          type: 'js',
          code: await asyncBackTSExprToJS(formValues.ifTSExpr),
          codeTS: formValues.ifTSExpr,
        }
        break
      }
    }
    if (formValues.mockRes) {
      api.mockRes = JSON.parse(formValues.mockRes)
    }
    apiDetailStore.submitModal(api)
  }, [form])
  const handleApplyExpressionTemplateForArgs = useCallback(
    (tpl: ExpressionTemplate) => {
      form.setFieldValue(
        tpl.expressionType === BackExpressionType.JAVASCRIPT
          ? 'jsExpr'
          : tpl.expressionType === BackExpressionType.TYPESCRIPT
          ? 'tsExpr'
          : 'jsonataExpr',
        tpl.expression,
      )
    },
    [form],
  )
  const handleApplyExpressionTemplateForIf = useCallback(
    (tpl: ExpressionTemplate) => {
      form.setFieldValue(
        tpl.expressionType === BackExpressionType.JAVASCRIPT
          ? 'ifJSExpr'
          : tpl.expressionType === BackExpressionType.TYPESCRIPT
          ? 'ifTSExpr'
          : 'ifJSONataExpr',
        tpl.expression,
      )
    },
    [form],
  )
  useUpdateEffect(() => {
    const formValues: Partial<APIDetailFormValues> = {
      name: undefined,
      service: undefined,
      method: undefined,
      argType: BackExpressionType.NO,
      jsonataExpr: DEFAULT_ARG_JSONATA_EXPR_STR,
      jsExpr: DEFAULT_ARG_JS_EXPR_STR,
      tsExpr: DEFAULT_ARG_TS_EXPR_STR,
      ifType: BackExpressionType.NO,
      ifJSONataExpr: DEFAULT_IF_JSONATA_EXPR_STR,
      ifJSExpr: DEFAULT_IF_JS_EXPR_STR,
      ifTSExpr: DEFAULT_IF_TS_EXPR_STR,
      mockRes: '', // JSONEditor 对 undefined 的处理有问题
    }
    if (isModalVisible && editingAPI) {
      formValues.name = editingAPI.name
      formValues.service = editingAPI.service
      formValues.method = editingAPI.method
      if (editingAPI.dataSourceId) {
        formValues.dataSourceId = editingAPI.dataSourceId
      }
      if (editingAPI.args?.length) {
        const expr = normalizeExpression(editingAPI.args[0])
        switch (expr.type) {
          case 'degraded-jsonata': {
            formValues.argType = BackExpressionType.JSONATA
            // 兼容一些不规范的写法
            formValues.jsonataExpr =
              JSON.stringify(expr.value, null, 2) || formValues.jsonataExpr
            break
          }
          case 'jsonata': {
            formValues.argType = BackExpressionType.JSONATA
            // 兼容一些不规范的写法
            formValues.jsonataExpr =
              JSON.stringify(expr, null, 2) || formValues.jsonataExpr
            break
          }
          case 'js': {
            formValues.argType = expr.codeTS
              ? BackExpressionType.TYPESCRIPT
              : BackExpressionType.JAVASCRIPT
            // 兼容一些不规范的写法
            formValues.jsExpr = expr.codeES || formValues.jsExpr
            formValues.tsExpr = expr.codeTS || formValues.tsExpr
            break
          }
        }
      }
      if (editingAPI.if) {
        const expr = normalizeExpression(editingAPI.if)
        switch (expr.type) {
          case 'degraded-jsonata': {
            formValues.ifType = BackExpressionType.JSONATA
            // 兼容一些不规范的写法
            formValues.ifJSONataExpr =
              JSON.stringify(expr.value, null, 2) || formValues.ifJSONataExpr
            break
          }
          case 'jsonata': {
            formValues.ifType = BackExpressionType.JSONATA
            // 兼容一些不规范的写法
            formValues.ifJSONataExpr =
              JSON.stringify(expr, null, 2) || formValues.ifJSONataExpr
            break
          }
          case 'js': {
            formValues.ifType = expr.codeTS
              ? BackExpressionType.TYPESCRIPT
              : BackExpressionType.JAVASCRIPT
            // 兼容一些不规范的写法
            formValues.ifJSExpr = expr.codeES || formValues.ifJSExpr
            formValues.ifTSExpr = expr.codeTS || formValues.ifTSExpr
            break
          }
        }
      }
      if (editingAPI.mockRes) {
        formValues.mockRes = JSON.stringify(editingAPI.mockRes, null, 2)
      }
    }
    form.setFieldsValue(formValues)
  }, [editingAPI, form, isModalVisible])
  const handleServiceSelectChange = useCallback(() => {
    form.resetFields(['method'])
  }, [form])
  const hanldeDataSourceSelectChange = useCallback(() => {
    const { dataSourceId } = form.getFieldsValue()
    const ds = apiDetailStore.allDataSource.find((s) => s.id === dataSourceId)
    if (ds) {
      form.setFieldsValue({
        mockRes: ds.mockRes || '',
      })
    }
  }, [form])
  const handleGetArgsScene = useLatestFn(() => getScene(form, 'apiArgs'))
  const handleGenerateArgsCode = useLatestFn(
    (
      code: string,
      expressionType: BackExpressionType | FrontExpressionType,
    ) => {
      if (expressionType === BackExpressionType.TYPESCRIPT) {
        const oldCode = form.getFieldValue('tsExpr')
        form.setFieldValue('tsExpr', code)
        return oldCode
      } else {
        const oldCode = form.getFieldValue('jsExpr')
        form.setFieldValue('jsExpr', code)
        return oldCode
      }
    },
  )
  const handleGetIfScene = useLatestFn(() => getScene(form, 'apiIf'))
  const handleGenerateIfCode = useLatestFn(
    (
      code: string,
      expressionType: BackExpressionType | FrontExpressionType,
    ) => {
      if (expressionType === BackExpressionType.TYPESCRIPT) {
        const oldCode = form.getFieldValue('ifTSExpr')
        form.setFieldValue('ifTSExpr', code)
        return oldCode
      } else {
        const oldCode = form.getFieldValue('ifJSExpr')
        form.setFieldValue('ifJSExpr', code)
        return oldCode
      }
    },
  )
  const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
  return (
    <Modal
      visible={isModalVisible}
      title={editingAPI ? '编辑接口' : '新增接口'}
      okText="提交"
      onCancel={handleModalCancel}
      onOk={handleModalOk}
      maskClosable={false}
      width={800}
    >
      <Alert
        message={
          <>
            数据源已支持平台配置，请在业务域下配置数据源，并在此页面刷新并在面板选择
            <a
              href={`/business-domain/config?domainCode=${DESIGNER_QUERY.domainCode}`}
              target="_blank"
              style={{ paddingLeft: '8px' }}
            >
              +新增数据源
            </a>
          </>
        }
        type="info"
        style={{ marginBottom: '16px' }}
      />
      <Form form={form} onFinish={handleFormFinish}>
        {editingAPI && !!editingAPI.service && (
          <Form.Item label="服务【老】" name="service">
            <Select
              options={apiDetailStore.allServicesOptions}
              placeholder="请选择服务"
              showSearch
              onChange={handleServiceSelectChange}
              disabled
            />
          </Form.Item>
        )}
        {editingAPI && !!editingAPI.method && (
          <Form.Item dependencies={['service']} noStyle>
            {({ getFieldValue }): ReactNode => (
              <Form.Item label="方法【老】" name="method">
                <Select
                  options={apiDetailStore.getMethodOptionsByService(
                    getFieldValue('service'),
                  )}
                  placeholder="请选择方法"
                  showSearch
                  disabled
                />
              </Form.Item>
            )}
          </Form.Item>
        )}
        <Form.Item label="名称" name="name">
          <Input placeholder="可选，缺省时展示数据源信息" />
        </Form.Item>
        <Form.Item
          label={
            <>
              数据源&nbsp;&nbsp;<Tag color="green">推荐</Tag>
            </>
          }
          tooltip="该数据源列表可在业务域下进行配置"
          name="dataSourceId"
        >
          <Select
            options={apiDetailStore.dataSourceSelections}
            placeholder="请选择数据源"
            showSearch
            filterOption={(inputValue, option): boolean => {
              if (option && typeof option.label === 'string') {
                return option.label
                  ?.toLowerCase?.()
                  .includes?.(inputValue.toLowerCase())
              }
              return false
            }}
            allowClear
            onChange={hanldeDataSourceSelectChange}
          />
        </Form.Item>
        <Form.Item
          label="参数表达式类型"
          name="argType"
          rules={[{ required: true }]}
        >
          <Select options={BACK_EXPRESSION_TYPE_OPTIONS} />
        </Form.Item>
        <AiGenExprCtxFc>
          <Form.Item
            dependencies={['argType', 'jsonataExpr', 'jsExpr', 'tsExpr']}
            noStyle
          >
            {({ getFieldValue }): ReactNode => (
              <>
                {getFieldValue('argType') === BackExpressionType.JSONATA && (
                  <ArgFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>参数（{BACK_JSONATA_EXPR_LABEL}）:</div>
                        <ExpressionTemplateActions
                          expression={getFieldValue('jsonataExpr')}
                          expressionType={BackExpressionType.JSONATA}
                          onApply={handleApplyExpressionTemplateForArgs}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="jsonataExpr"
                    rules={[{ required: true }, jsonObjRule]}
                  >
                    <JSONEditor height="calc(100vh - 566px)" />
                  </ArgFormItem>
                )}
                {getFieldValue('argType') === BackExpressionType.JAVASCRIPT && (
                  <ArgFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>参数（{BACK_JS_EXPR_LABEL}）:</div>
                        <AiGenerateExpression
                          expressionType={BackExpressionType.JAVASCRIPT}
                          getScene={handleGetArgsScene}
                          onGenerate={handleGenerateArgsCode}
                        />
                        <ExpressionTemplateActions
                          expression={getFieldValue('jsExpr')}
                          expressionType={BackExpressionType.JAVASCRIPT}
                          onApply={handleApplyExpressionTemplateForArgs}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="jsExpr"
                    rules={[{ required: true }, jsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <BackJSExprEditor height="calc(100vh - 566px)" />
                  </ArgFormItem>
                )}
                {getFieldValue('argType') === BackExpressionType.TYPESCRIPT && (
                  <ArgFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>参数（{BACK_TS_EXPR_LABEL}）:</div>
                        <AiGenerateExpression
                          expressionType={BackExpressionType.TYPESCRIPT}
                          getScene={handleGetArgsScene}
                          onGenerate={handleGenerateArgsCode}
                        />
                        <ExpressionTemplateActions
                          expression={getFieldValue('tsExpr')}
                          expressionType={BackExpressionType.TYPESCRIPT}
                          onApply={handleApplyExpressionTemplateForArgs}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="tsExpr"
                    rules={[{ required: true }, tsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <BackJSExprEditor
                      language="typescript"
                      height="calc(100vh - 566px)"
                    />
                  </ArgFormItem>
                )}
              </>
            )}
          </Form.Item>
        </AiGenExprCtxFc>
        <Form.Item
          label="执行条件表达式类型"
          tooltip="生成执行条件的方式，选择无或下方表达式返回 true，才会继续请求接口"
          name="ifType"
          rules={[{ required: true }]}
        >
          <Select options={BACK_EXPRESSION_TYPE_OPTIONS} />
        </Form.Item>
        <AiGenExprCtxFc>
          <Form.Item
            dependencies={['ifType', 'ifJSONataExpr', 'ifJSExpr', 'ifTSExpr']}
            noStyle
          >
            {({ getFieldValue }): ReactNode => (
              <>
                {getFieldValue('ifType') === BackExpressionType.JSONATA && (
                  <ArgFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>执行条件（{BACK_JSONATA_EXPR_LABEL}）:</div>
                        <ExpressionTemplateActions
                          expression={getFieldValue('ifJSONataExpr')}
                          expressionType={BackExpressionType.JSONATA}
                          onApply={handleApplyExpressionTemplateForIf}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="ifJSONataExpr"
                    rules={[{ required: true }, jsonObjRule]}
                  >
                    <JSONEditor height="calc(100vh - 566px)" />
                  </ArgFormItem>
                )}
                {getFieldValue('ifType') === BackExpressionType.JAVASCRIPT && (
                  <ArgFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>执行条件（{BACK_JS_EXPR_LABEL}）:</div>
                        <AiGenerateExpression
                          expressionType={BackExpressionType.JAVASCRIPT}
                          getScene={handleGetIfScene}
                          onGenerate={handleGenerateIfCode}
                        />
                        <ExpressionTemplateActions
                          expression={getFieldValue('ifJSExpr')}
                          expressionType={BackExpressionType.JAVASCRIPT}
                          onApply={handleApplyExpressionTemplateForIf}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="ifJSExpr"
                    rules={[{ required: true }, jsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <BackJSExprEditor height="calc(100vh - 566px)" />
                  </ArgFormItem>
                )}
                {getFieldValue('ifType') === BackExpressionType.TYPESCRIPT && (
                  <ArgFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>执行条件（{BACK_TS_EXPR_LABEL}）:</div>
                        <AiGenerateExpression
                          expressionType={BackExpressionType.TYPESCRIPT}
                          getScene={handleGetIfScene}
                          onGenerate={handleGenerateIfCode}
                        />
                        <ExpressionTemplateActions
                          expression={getFieldValue('ifTSExpr')}
                          expressionType={BackExpressionType.TYPESCRIPT}
                          onApply={handleApplyExpressionTemplateForIf}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="ifTSExpr"
                    rules={[{ required: true }, tsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <BackJSExprEditor
                      language="typescript"
                      height="calc(100vh - 566px)"
                    />
                  </ArgFormItem>
                )}
              </>
            )}
          </Form.Item>
        </AiGenExprCtxFc>
        <Form.Item label="Mock 响应" name="mockRes" rules={[jsonObjRule]}>
          <JSONEditor height={400} />
        </Form.Item>
      </Form>
    </Modal>
  )
})

/**
 * 接口详情表单值
 */
export interface APIDetailFormValues {
  /**
   * 名称
   */
  name?: string
  /**
   * 服务
   */
  service?: string
  /**
   * 方法
   */
  method?: string
  /**
   * 数据源id
   */
  dataSourceId?: string
  /**
   * 参数类型
   */
  argType: BackExpressionType
  /**
   * 参数 JSONata 表达式
   */
  jsonataExpr: string
  /**
   * 参数 js 表达式
   */
  jsExpr: string
  /**
   * 参数 ts 表达式
   */
  tsExpr: string
  /**
   * 执行条件类型
   */
  ifType: BackExpressionType
  /**
   * 执行条件 JSONata 表达式
   */
  ifJSONataExpr: string
  /**
   * 执行条件 js 表达式
   */
  ifJSExpr: string
  /**
   * 执行条件 ts 表达式
   */
  ifTSExpr: string
  /**
   * Mock 响应
   */
  mockRes?: string
}

const ArgFormItem = styled(Form.Item)`
  label {
    width: 100%;
  }
`

function getScene(
  form: FormInstance<APIDetailFormValues>,
  type: (BackExpressionSceneApiArgs | BackExpressionSceneApiIf)['type'],
): BackExpressionScene | null {
  const simplifyComponent = getSelectedSimplifyComponent()
  if (!simplifyComponent) return null
  const isRoot = simplifyComponent.type === INTERNAL_COMPONENT_TYPE_ROOT
  return {
    type,
    component: isRoot ? undefined : simplifyComponent,
    dataSource: getDataSourceLabelByApi(form.getFieldsValue()),
  } satisfies BackExpressionScene
}
