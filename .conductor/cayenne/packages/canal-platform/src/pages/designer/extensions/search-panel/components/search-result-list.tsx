import { SearchResultTree } from '@/components/search-result-tree'
import { SearchResultTreeNonLeafNode } from '@/components/search-result-tree-non-leaf-node'
import { DESIGNER_QUERY } from '@/pages/designer/constants'
import { anchorStore } from '@/pages/designer/stores/anchor-store'
import { designerSearchStore } from '@/pages/designer/stores/designer-search-store'
import {
  SearchTaskStatus,
  groupSearchResultItems,
  type SearchResultItem,
} from '@/stores/search-store'
import { Spin } from '@m-ui/react'
import type { DataNode } from '@m-ui/react/lib/tree'
import { first, values } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useMemo, useState, type FC } from 'react'
import styled from 'styled-components'
import { SearchResultTreeLeafNode } from '../../../../../components/search-result-tree-leaf-node'

/**
 * 搜索结果列表属性
 */
export interface SearchResultListProps {
  className?: string
}

/**
 * 搜索结果列表
 */
export const SearchResultList: FC<SearchResultListProps> = observer(
  ({ className }) => {
    const { currentTask } = designerSearchStore
    const rawResultItems = currentTask?.resultItems
    const resultItems = useMemo(() => rawResultItems || [], [rawResultItems])
    const [selectedKeys, setSelectedKeys] = useState<string[]>([])
    const treeData: DataNode[] = useMemo(() => {
      const currentModuleGroup = first(
        values(groupSearchResultItems(resultItems)),
      ) || {
        moduleId: DESIGNER_QUERY.moduleId,
        items: [],
      }
      return currentModuleGroup.items.map(
        (categoryGroup, categoryGroupIndex) =>
          ({
            key: JSON.stringify([categoryGroup.category, categoryGroupIndex]),
            selectable: false,
            title: (
              <SearchResultTreeNonLeafNode title={categoryGroup.category} />
            ),
            children: categoryGroup.items.map(
              (item: SearchResultItem, itemIndex: number) => {
                const key = JSON.stringify([
                  categoryGroup.category,
                  categoryGroupIndex,
                  itemIndex,
                ])
                return {
                  key,
                  isLeaf: true,
                  title: (
                    <SearchResultTreeLeafNode
                      item={item}
                      onClick={(): void => {
                        anchorStore.goToAnchor(item.anchor)
                        setSelectedKeys([key])
                      }}
                    />
                  ),
                }
              },
            ),
          }) satisfies DataNode,
      )
    }, [resultItems])
    return (
      <Container className={className}>
        {currentTask ? (
          <div className="search-summary">
            {currentTask.status === SearchTaskStatus.RUNNING ? <Spin /> : null}
            {resultItems.length ? (
              <span>有 {resultItems.length} 个结果</span>
            ) : null}
            {currentTask.status === SearchTaskStatus.FINISHED &&
            !resultItems.length ? (
              <span>未找到结果</span>
            ) : null}
          </div>
        ) : null}
        <div className="search-body">
          <SearchResultTree
            key={currentTask?.id}
            treeData={treeData}
            selectedKeys={selectedKeys}
          />
        </div>
      </Container>
    )
  },
)

const Container = styled.div`
  display: flex;
  flex-direction: column;

  .search-body {
    margin-top: 10px;
    height: 0;
    flex: auto;
  }
`
