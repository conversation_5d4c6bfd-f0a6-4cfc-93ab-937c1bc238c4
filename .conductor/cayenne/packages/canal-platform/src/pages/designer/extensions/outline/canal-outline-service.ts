import {
  OutlineService,
  type OutlineServiceDropTreeNode,
} from '@kael/designer-outline'
import {
  NodeControlServiceSymbol,
  SchemaServiceSymbol,
  type BaseSchemaComponent,
  type ChildComponentPosition,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { cloneDeep, isObject } from 'lodash'
import { CanalNodeControlService, type E2ESchemaService } from '../../services'

/**
 * 大运河大纲树服务
 */
export class CanalOutlineService extends OutlineService {
  public static token = OutlineService.token

  /**
   * 正在拖拽
   */
  private _isDragging = false

  /**
   * 正在拖拽
   */
  public get isDragging(): boolean {
    return this._isDragging
  }

  public constructor(...args: ConstructorParameters<typeof OutlineService>) {
    super(...args)
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(window as any).debugCanalOutlineService = this
    }
  }

  /**
   * 设置正在拖拽
   * @param isDragging 正在拖拽
   */
  public setIsDragging(isDragging: boolean): void {
    this._isDragging = isDragging
  }

  /**
   * 放置树节点
   * @param options 选项
   */
  public async dropTreeNode(
    options: OutlineServiceDropTreeNode,
  ): Promise<void> {
    // console.log('debug CanalOutlineService.dropTreeNode', options)
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    const nodeControlService = getInject<CanalNodeControlService>(
      NodeControlServiceSymbol,
    )
    const { componentId, toComponentId } = options
    let { isToSibling, position } = options
    const toComponent = schemaService.findComponentById(toComponentId)
    const toParent = schemaService.findParentComponentById(toComponentId).data
    if (!isToSibling && !this.mapKeyToIsContainer[toComponentId]) {
      isToSibling = true
      position = 1
    }
    let targetComponent = toComponent
    let targetIndex: ChildComponentPosition | number = position
    if (isToSibling) {
      if (!toComponent || !toParent) {
        return
      }
      // toComponent在它父节点中的位置
      const calcToIndex = nodeControlService.calcComponentPositionInParent(
        toComponent as BaseSchemaComponent,
        toParent as BaseSchemaComponent,
      )
      targetIndex = cloneDeep(calcToIndex)
      if (position === 0 && this.mapKeyToIsContainer[toComponentId]) {
        targetIndex = 0
      } else {
        targetIndex.index = position + targetIndex?.index
        targetComponent = toParent
      }
    } else {
      targetIndex = {
        type: 'children',
        index: Math.max(0, position),
      }
    }
    this.updateNearBayComponentIds(toComponentId, 'drop')
    // nodeControlService.calcComponentPositionInParent 有点问题，会多一个 propPath: []，导致后面的 isEqual 对比出问题
    if (isObject(targetIndex) && targetIndex.type === 'children') {
      targetIndex = targetIndex.index
    }
    nodeControlService.moveComponent(
      componentId,
      targetComponent?.id as string,
      targetIndex,
    )
  }

  /**
   * 设置节点是否展开
   * @param key 键值
   * @param isExpanded 是否展开
   */
  public setKeyExpanded(key: string, isExpanded: boolean): void {
    if (this._isDragging) return
    super.setKeyExpanded(key, isExpanded)
  }
}
