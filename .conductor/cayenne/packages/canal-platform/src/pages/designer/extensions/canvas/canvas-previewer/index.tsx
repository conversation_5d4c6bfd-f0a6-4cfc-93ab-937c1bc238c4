import { PREVIEWER_URL } from '@/pages/designer/constants'
import { useEffect, useRef, type FC } from 'react'
import { PreviewSettingModal } from './preview-setting-modal'

/**
 * 创建预览器 iframe
 */
function createPreviewerIframe(): HTMLIFrameElement {
  const iframe = document.createElement('iframe')

  iframe.src = PREVIEWER_URL

  iframe.style.display = 'none'
  iframe.style.position = 'absolute'
  iframe.style.outline = 'none'
  iframe.style.border = 'none'
  iframe.style.userSelect = 'none'
  iframe.style.background = '#edeff2'

  return iframe
}

/**
 * 画布预览器 iframe
 */
export const canvasPreviewerIframe = createPreviewerIframe()

/**
 * 画布预览器
 */
export const CanvasPreviewer: FC = () => {
  const refPreviewerContainer = useRef<HTMLDivElement>(null)
  useEffect(() => {
    refPreviewerContainer.current?.appendChild(canvasPreviewerIframe)
  }, [])
  return (
    <>
      <div ref={refPreviewerContainer}></div>
      <PreviewSettingModal />
    </>
  )
}
