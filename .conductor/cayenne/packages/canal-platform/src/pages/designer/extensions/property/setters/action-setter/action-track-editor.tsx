import { ControlLabel } from '@/components/control-label'
import { LongSelect } from '@/components/long-select'
import type { E2ESchemaService } from '@/pages/designer/services'
import type { E2ESchemaExpressionTrack, Track } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { get } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useCallback, type FC } from 'react'
import type { ActionEditorCommonProps } from './types'

/**
 * 动作：触发埋点编辑器属性
 */
export interface ActionTrackEditorProps
  extends ActionEditorCommonProps<E2ESchemaExpressionTrack> {}

/**
 * 动作：触发埋点编辑器
 */
export const ActionTrackEditor: FC<ActionTrackEditorProps> = observer(
  ({ value, onChange }) => {
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    const options = (schemaService.schema?.tracks || []).map(
      (track, index) => ({
        label: trackToLabel(track, index),
        value: track.id,
      }),
    )
    const handleActionIdChange = useCallback(
      (id: string) => {
        onChange({
          ...value,
          trackId: id,
        })
      },
      [onChange, value],
    )
    return (
      <div>
        <div>
          <ControlLabel>埋点：</ControlLabel>
          <LongSelect
            size="small"
            value={value.trackId}
            options={options}
            onChange={handleActionIdChange}
          />
        </div>
      </div>
    )
  },
)

/**
 * 埋点转标签
 * @param track 埋点
 * @param index 下标
 */
function trackToLabel(track: Track, index: number): string {
  for (const key of ['action', 'page', 'key']) {
    const value = get(track.eventOptions, key)
    if (value) {
      return `#${index} ${value} ${track.eventType}`
    }
  }
  return `#${index} ${track.eventType}`
}
