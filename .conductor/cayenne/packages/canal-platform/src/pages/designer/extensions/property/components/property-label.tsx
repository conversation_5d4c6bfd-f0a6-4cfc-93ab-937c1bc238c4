import { InlineMarkdownPreview } from '@/components/inline-markdown-preview'
import { LongTooltip } from '@/components/long-tooltip'
import type {
  CanalMaterialService,
  E2ESchemaService,
} from '@/pages/designer/services'
import { px2rem, px2vw } from '@ad/canal-shared'
import type { E2ECMSFieldProp } from '@ad/e2e-material-schema'
import { isPx2Prop } from '@ad/e2e-material-schema-utils'
import { isBindableExpression } from '@ad/e2e-schema-utils'
import type { LabelRenderProps } from '@kael/designer-property'
import {
  MaterialServiceSymbol,
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { normalizeExpression } from '@kael/schema-utils'
import { Typography } from '@m-ui/react'
import { isUndefined } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useMemo, type FC } from 'react'

/**
 * 属性面板标签
 */
export const PropertyLabel: FC<LabelRenderProps> = observer(
  ({ schema: rawFieldProp }) => {
    const fieldProp = rawFieldProp as E2ECMSFieldProp
    const { name, tip: fieldPropTip } = fieldProp
    const isPx2 = isPx2Prop(fieldProp)
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    const designWidth = schemaService.schema?.px2vw?.designWidth
    const rootElementFontSize =
      schemaService.schema?.px2rem?.rootElementFontSize
    const canalMaterialService = getInject<CanalMaterialService>(
      MaterialServiceSymbol,
    )
    const stateService = getInject<StateService>(StateServiceSymbol)
    const selectedComponentId = stateService.selectedComponentId
    const logicPath = useMemo(
      () => canalMaterialService.getLogicPathByFieldProp(fieldProp),
      [canalMaterialService, fieldProp],
    )
    const expr =
      selectedComponentId &&
      logicPath &&
      schemaService.getComponentPropExpression(selectedComponentId, logicPath)
    const px2Tip = useMemo(() => {
      let t = ''
      if (isPx2) {
        const ex = isBindableExpression(expr) ? expr.defaultValue : expr
        const ne = isUndefined(ex) ? ex : normalizeExpression(ex)
        const value = ne?.type === 'static' ? ne.value : undefined
        if (designWidth) {
          const vmValue = px2vw(value, designWidth)
          t = vmValue === value ? 'px2vw 未生效' : `px2vw 已生效：${vmValue}`
        } else if (rootElementFontSize) {
          const remValue = px2rem(value, rootElementFontSize)
          t =
            remValue === value ? 'px2rem 未生效' : `px2rem 已生效：${remValue}`
        }
      }
      return t
    }, [isPx2, expr, designWidth, rootElementFontSize])
    const tip = useMemo(() => {
      return px2Tip || fieldPropTip ? (
        <>
          {px2Tip && <div>{px2Tip}</div>}
          {fieldPropTip && <InlineMarkdownPreview source={`${fieldPropTip}`} />}
        </>
      ) : null
    }, [fieldPropTip, px2Tip])
    return (
      <Typography.Text ellipsis className={tip ? 'ellipsis tip' : 'ellipsis'}>
        {tip ? <LongTooltip title={tip}>{`${name}`}</LongTooltip> : `${name}`}
      </Typography.Text>
    )
  },
)
