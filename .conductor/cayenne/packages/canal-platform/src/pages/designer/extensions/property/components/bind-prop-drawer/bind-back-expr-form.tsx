import { <PERSON><PERSON><PERSON><PERSON>or } from '@/components/json-editor'
import { LongLabelNoAnimationFormItem } from '@/components/long-label-form-item'
import { SpaceBetweenDiv } from '@/components/space-between-div'
import { BackJSExprEditor } from '@/pages/designer/components'
import {
  BACK_JSONATA_EXPR_LABEL,
  BACK_JS_EXPR_LABEL,
  BACK_TS_EXPR_LABEL,
  BIG_JS_LINK,
  DEFAULT_RES_JSONATA_EXPR_STR,
  DEFAULT_RES_JS_EXPR_STR,
  DEFAULT_RES_TS_EXPR_STR,
} from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import {
  getApiLabelsInComponent,
  getApiOptions,
  getSimplifyComponent,
} from '@/pages/designer/tools'
import type { ExpressionTemplate } from '@/services/backend/models'
import {
  asyncBackJSExprToES5,
  asyncBackTSExprToJS,
  js<PERSON><PERSON>,
  jsonObj<PERSON>ule,
  tsRule,
  useMoneyRule,
} from '@/utils'
import type { BackExpressionScene } from '@ad/canal-ai'
import {
  BACK_EXPRESSION_TYPE_VALID_OPTIONS,
  BackExpressionType,
  FrontExpressionType,
  arrayToArrayable,
  arrayableToArray,
  type BackExpressionTypeValid,
} from '@ad/canal-shared'
import { useLatestFn } from '@ad/canal-shared-ui'
import type { E2ESchemaExpressionAPI } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { normalizeExpression } from '@kael/schema-utils'
import { Form, Select, type FormInstance } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { fillRef } from 'rc-util/es/ref'
import { forwardRef, useCallback, useEffect, type ReactNode } from 'react'
import {
  AiGenExprCtxFc,
  AiGenerateExpression,
} from '../../../../components/ai-generate-expression'
import { ExpressionTemplateActions } from '../expression-template-actions'
import { bindPropStore } from './bind-prop-store'

/**
 * 绑定后端表达式表单
 */
export const BindBackExprForm = observer(
  forwardRef<FormInstance<BindBackExprFormValues>>((...[, ref]) => {
    const { drawerConfig, editingPropExpression } = bindPropStore
    const [form] = Form.useForm<BindBackExprFormValues>()
    useEffect(() => fillRef(ref, form), [form, ref])
    const handleFormFinish = useCallback(async () => {
      const formValues = form.getFieldsValue()
      const apiExpr: E2ESchemaExpressionAPI = {
        type: 'api',
        apiId: arrayToArrayable(formValues.apiId || []),
        defaultValue:
          editingPropExpression?.type === 'js' ||
          editingPropExpression?.type === 'api'
            ? editingPropExpression.defaultValue
            : editingPropExpression,
      }
      switch (formValues.exprType) {
        case BackExpressionType.JSONATA: {
          apiExpr.transform = {
            type: 'degraded-jsonata',
            value: JSON.parse(formValues.jsonataExpr),
          }
          break
        }
        case BackExpressionType.JAVASCRIPT: {
          apiExpr.transform = {
            type: 'js',
            code: await asyncBackJSExprToES5(formValues.jsExpr),
            codeES: formValues.jsExpr,
          }
          break
        }
        case BackExpressionType.TYPESCRIPT: {
          apiExpr.transform = {
            type: 'js',
            code: await asyncBackTSExprToJS(formValues.tsExpr),
            codeTS: formValues.tsExpr,
          }
          break
        }
      }
      bindPropStore.submitDrawer(apiExpr)
    }, [editingPropExpression, form])
    const handleApplyExpressionTemplate = useLatestFn(
      (tpl: ExpressionTemplate) => {
        form.setFieldValue(
          tpl.expressionType === BackExpressionType.JAVASCRIPT
            ? 'jsExpr'
            : tpl.expressionType === BackExpressionType.TYPESCRIPT
            ? 'tsExpr'
            : 'jsonataExpr',
          tpl.expression,
        )
      },
    )
    useEffect(() => {
      void drawerConfig?.propPath
      const formValues: Partial<BindBackExprFormValues> = {
        apiId: undefined,
        exprType: BackExpressionType.JAVASCRIPT,
        jsonataExpr: DEFAULT_RES_JSONATA_EXPR_STR,
        jsExpr: drawerConfig?.defaultBackJsExpr || DEFAULT_RES_JS_EXPR_STR,
        tsExpr: drawerConfig?.defaultBackTsExpr || DEFAULT_RES_TS_EXPR_STR,
      }
      if (editingPropExpression?.type === 'api') {
        formValues.apiId = arrayableToArray(editingPropExpression.apiId)
        if (editingPropExpression.transform) {
          const expr = normalizeExpression(editingPropExpression.transform)
          switch (expr.type) {
            case 'degraded-jsonata': {
              formValues.exprType = BackExpressionType.JSONATA
              // 兼容一些不规范的写法
              formValues.jsonataExpr =
                JSON.stringify(expr.value, null, 2) || formValues.jsonataExpr
              break
            }
            case 'jsonata': {
              formValues.exprType = BackExpressionType.JSONATA
              // 兼容一些不规范的写法
              formValues.jsonataExpr =
                JSON.stringify(expr, null, 2) || formValues.jsonataExpr
              break
            }
            case 'js': {
              formValues.exprType = expr.codeTS
                ? BackExpressionType.TYPESCRIPT
                : BackExpressionType.JAVASCRIPT
              // 兼容一些不规范的写法
              formValues.jsExpr = expr.codeES || formValues.jsExpr
              formValues.tsExpr = expr.codeTS || formValues.tsExpr
              break
            }
          }
        }
      }
      form.setFieldsValue(formValues)
    }, [
      drawerConfig?.defaultBackJsExpr,
      drawerConfig?.defaultBackTsExpr,
      drawerConfig?.propPath,
      editingPropExpression,
      form,
    ])
    const apiOptions = getApiOptions(drawerConfig?.componentId)
    const handleGetScene = useLatestFn(() => {
      const simplifyComponent = getSimplifyComponent(drawerConfig?.componentId)
      const apiIds: string[] = form.getFieldValue('apiId') || []
      if (!drawerConfig || !simplifyComponent) {
        return null
      }
      return {
        type: 'prop',
        component: simplifyComponent,
        propPath: drawerConfig.propPath,
        propName: drawerConfig.propName,
        apis: getApiLabelsInComponent(simplifyComponent.id, apiIds),
      } satisfies BackExpressionScene
    })
    const handleGenerateCode = useLatestFn(
      (
        code: string,
        expressionType: BackExpressionType | FrontExpressionType,
      ) => {
        if (expressionType === BackExpressionType.TYPESCRIPT) {
          const oldCode = form.getFieldValue('tsExpr')
          form.setFieldValue('tsExpr', code)
          return oldCode
        } else {
          const oldCode = form.getFieldValue('jsExpr')
          form.setFieldValue('jsExpr', code)
          return oldCode
        }
      },
    )
    const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
    // 低代码引擎实现有问题，不监听 schema 字段，undo/redo 视图不会刷新
    const schemaService = getInject<E2ESchemaService | null>(
      SchemaServiceSymbol,
    )
    void schemaService?.schema
    return (
      <AiGenExprCtxFc>
        <Form form={form} onFinish={handleFormFinish}>
          <Form.Item label="接口" name="apiId">
            <Select options={apiOptions} mode="multiple" />
          </Form.Item>
          <Form.Item
            label="表达式类型"
            name="exprType"
            rules={[{ required: true }]}
          >
            <Select options={BACK_EXPRESSION_TYPE_VALID_OPTIONS} />
          </Form.Item>
          <Form.Item
            dependencies={['exprType', 'jsonataExpr', 'jsExpr', 'tsExpr']}
            noStyle
          >
            {({ getFieldValue }): ReactNode => (
              <>
                {getFieldValue('exprType') === BackExpressionType.JSONATA && (
                  <LongLabelNoAnimationFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>生成属性（{BACK_JSONATA_EXPR_LABEL}）:</div>
                        <ExpressionTemplateActions
                          expression={getFieldValue('jsonataExpr')}
                          expressionType={BackExpressionType.JSONATA}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="jsonataExpr"
                    rules={[{ required: true }, jsonObjRule]}
                  >
                    <JSONEditor height="calc(100vh - 351px)" />
                  </LongLabelNoAnimationFormItem>
                )}
                {getFieldValue('exprType') ===
                  BackExpressionType.JAVASCRIPT && (
                  <LongLabelNoAnimationFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>生成属性（{BACK_JS_EXPR_LABEL}）:</div>
                        <AiGenerateExpression
                          expressionType={BackExpressionType.JAVASCRIPT}
                          getScene={handleGetScene}
                          onGenerate={handleGenerateCode}
                        />
                        <ExpressionTemplateActions
                          expression={getFieldValue('jsExpr')}
                          expressionType={BackExpressionType.JAVASCRIPT}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="jsExpr"
                    rules={[{ required: true }, jsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <BackJSExprEditor
                      height={`calc(100vh - 351px${
                        moneyRuleHasErrBefore ? ' - 24px' : ''
                      })`}
                    />
                  </LongLabelNoAnimationFormItem>
                )}
                {getFieldValue('exprType') ===
                  BackExpressionType.TYPESCRIPT && (
                  <LongLabelNoAnimationFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>生成属性（{BACK_TS_EXPR_LABEL}）:</div>
                        <AiGenerateExpression
                          expressionType={BackExpressionType.TYPESCRIPT}
                          getScene={handleGetScene}
                          onGenerate={handleGenerateCode}
                        />
                        <ExpressionTemplateActions
                          expression={getFieldValue('tsExpr')}
                          expressionType={BackExpressionType.TYPESCRIPT}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="tsExpr"
                    rules={[{ required: true }, tsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <BackJSExprEditor
                      language="typescript"
                      height={`calc(100vh - 351px${
                        moneyRuleHasErrBefore ? ' - 24px' : ''
                      })`}
                    />
                  </LongLabelNoAnimationFormItem>
                )}
              </>
            )}
          </Form.Item>
        </Form>
      </AiGenExprCtxFc>
    )
  }),
)

/**
 * 绑定后端表达式表单值
 */
export interface BindBackExprFormValues {
  /**
   * 接口 ID
   */
  apiId?: string[]
  /**
   * 表达式类型
   */
  exprType: BackExpressionTypeValid
  /**
   * JSONata 表达式
   */
  jsonataExpr: string
  /**
   * js 表达式
   */
  jsExpr: string
  /**
   * ts 表达式
   */
  tsExpr: string
}
