import type { FetchSchemaEnv } from '@ad/canal-react-runtime'
import type { AnyObject } from '@kael/shared'

/**
 * 预览环境
 */
export const PREVIEW_ENVS = [
  'staging',
  'prt',
  'beta',
] satisfies FetchSchemaEnv[]

/**
 * 预览环境
 */
export type PreviewEnv = (typeof PREVIEW_ENVS)[number]

/**
 * 预览环境选项
 */
export const PREVIEW_ENV_OPTIONS = PREVIEW_ENVS.map((value) => ({
  value,
  label: value,
}))

/**
 * 预览配置
 */
export interface PreviewSetting {
  /**
   * 环境
   */
  env: PreviewEnv
  /**
   * 请求头，按环境
   */
  headers: Record<PreviewEnv, AnyObject>
  /**
   * 参数
   */
  params: AnyObject
  /**
   * 组件 js url 对照表
   */
  componentJsUrlMap: AnyObject
  /**
   * 开启 Mock
   */
  mock: boolean
}

/**
 * 默认预览配置
 */
export const DEFAULT_PREVIEW_SETTING: PreviewSetting = {
  env: 'staging',
  headers: {
    staging: {
      Cookie: '',
    },
    prt: {
      Cookie: '',
    },
    beta: {
      Cookie: '',
    },
  },
  params: {},
  componentJsUrlMap: {},
  mock: false,
}
