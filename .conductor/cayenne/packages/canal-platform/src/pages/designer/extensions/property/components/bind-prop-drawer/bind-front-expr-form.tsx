import { LongLabelNoAnimationFormItem } from '@/components/long-label-form-item'
import { SpaceBetweenDiv } from '@/components/space-between-div'
import { FrontJSExprEditor } from '@/pages/designer/components'
import {
  AiGenerateExpression,
  AiGenExprCtxFc,
} from '@/pages/designer/components/ai-generate-expression'
import {
  BIG_JS_LINK,
  DEFAULT_PROP_FRONT_JS_EXPR_STR,
  DEFAULT_PROP_FRONT_TS_EXPR_STR,
  FRONT_JS_EXPR_LABEL,
  FRONT_TS_EXPR_LABEL,
} from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import { getSimplifyComponent } from '@/pages/designer/tools'
import type { ExpressionTemplate } from '@/services/backend/models'
import {
  asyncFrontJSExprEvalByContainerToES5,
  asyncFrontTSExprEvalByContainerToJS,
  jsRule,
  tsRule,
  useMoneyRule,
} from '@/utils'
import type { FrontExpressionScene } from '@ad/canal-ai'
import {
  BackExpressionType,
  FRONT_EXPRESSION_TYPE_OPTIONS,
  FrontExpressionType,
} from '@ad/canal-shared'
import { useLatestFn } from '@ad/canal-shared-ui'
import type { E2ESchemaExpressionJS } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { Form, Select, type FormInstance } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { fillRef } from 'rc-util/es/ref'
import { forwardRef, useCallback, useEffect, type ReactNode } from 'react'
import { ExpressionTemplateActions } from '../expression-template-actions'
import { bindPropStore } from './bind-prop-store'

/**
 * 绑定前端表达式表单
 */
export const BindFrontExprForm = observer(
  forwardRef<FormInstance<BindFrontExprFormValues>>((...[, ref]) => {
    const { drawerConfig, editingPropExpression } = bindPropStore
    const [form] = Form.useForm<BindFrontExprFormValues>()
    useEffect(() => fillRef(ref, form), [form, ref])
    const handleFormFinish = useCallback(async () => {
      const formValues = form.getFieldsValue()
      const expr: E2ESchemaExpressionJS = {
        type: 'js',
        code:
          formValues.exprType === FrontExpressionType.JAVASCRIPT
            ? await asyncFrontJSExprEvalByContainerToES5(formValues.jsExpr)
            : await asyncFrontTSExprEvalByContainerToJS(formValues.tsExpr),
        defaultValue:
          editingPropExpression?.type === 'js' ||
          editingPropExpression?.type === 'api'
            ? editingPropExpression.defaultValue
            : editingPropExpression,
      }
      if (formValues.exprType === FrontExpressionType.JAVASCRIPT) {
        expr.codeES = formValues.jsExpr
      } else {
        expr.codeTS = formValues.tsExpr
      }
      bindPropStore.submitDrawer(expr)
    }, [editingPropExpression, form])
    useEffect(() => {
      void drawerConfig?.propPath
      const formValues: Partial<BindFrontExprFormValues> = {
        exprType: FrontExpressionType.JAVASCRIPT,
        jsExpr:
          drawerConfig?.defaultFrontJsExpr || DEFAULT_PROP_FRONT_JS_EXPR_STR,
        tsExpr:
          drawerConfig?.defaultFrontTsExpr || DEFAULT_PROP_FRONT_TS_EXPR_STR,
      }
      if (editingPropExpression?.type === 'js') {
        if (editingPropExpression.codeES) {
          formValues.jsExpr = editingPropExpression.codeES
        } else if (editingPropExpression.codeTS) {
          formValues.exprType = FrontExpressionType.TYPESCRIPT
          formValues.tsExpr = editingPropExpression.codeTS
        }
      }
      form.setFieldsValue(formValues)
    }, [
      drawerConfig?.defaultFrontJsExpr,
      drawerConfig?.defaultFrontTsExpr,
      drawerConfig?.propPath,
      editingPropExpression,
      form,
    ])
    const handleApplyExpressionTemplate = useCallback(
      (tpl: ExpressionTemplate) => {
        form.setFieldValue(
          tpl.expressionType === FrontExpressionType.JAVASCRIPT
            ? 'jsExpr'
            : 'tsExpr',
          tpl.expression,
        )
      },
      [form],
    )
    const handleGetScene = useLatestFn(() => {
      const simplifyComponent = getSimplifyComponent(drawerConfig?.componentId)
      if (!drawerConfig || !simplifyComponent) {
        return null
      }
      return {
        type: 'prop',
        component: simplifyComponent,
        propPath: drawerConfig.propPath,
        propName: drawerConfig.propName,
      } satisfies FrontExpressionScene
    })
    const handleGenerateCode = useLatestFn(
      (
        code: string,
        expressionType: BackExpressionType | FrontExpressionType,
      ) => {
        if (expressionType === FrontExpressionType.TYPESCRIPT) {
          const oldCode = form.getFieldValue('tsExpr')
          form.setFieldValue('tsExpr', code)
          return oldCode
        } else {
          const oldCode = form.getFieldValue('jsExpr')
          form.setFieldValue('jsExpr', code)
          return oldCode
        }
      },
    )
    const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
    // 低代码引擎实现有问题，不监听 schema 字段，undo/redo 视图不会刷新
    const schemaService = getInject<E2ESchemaService | null>(
      SchemaServiceSymbol,
    )
    void schemaService?.schema
    return (
      <AiGenExprCtxFc>
        <Form form={form} onFinish={handleFormFinish}>
          <Form.Item
            label="表达式类型"
            name="exprType"
            rules={[{ required: true }]}
          >
            <Select options={FRONT_EXPRESSION_TYPE_OPTIONS} />
          </Form.Item>
          <Form.Item dependencies={['exprType', 'jsExpr', 'tsExpr']} noStyle>
            {({ getFieldValue }): ReactNode => (
              <>
                {getFieldValue('exprType') ===
                  FrontExpressionType.JAVASCRIPT && (
                  <LongLabelNoAnimationFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>生成属性（{FRONT_JS_EXPR_LABEL}）:</div>
                        <AiGenerateExpression
                          expressionType={FrontExpressionType.JAVASCRIPT}
                          getScene={handleGetScene}
                          onGenerate={handleGenerateCode}
                        />
                        <ExpressionTemplateActions
                          expression={getFieldValue('jsExpr')}
                          expressionType={FrontExpressionType.JAVASCRIPT}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="jsExpr"
                    rules={[{ required: true }, jsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <FrontJSExprEditor
                      height={`calc(100vh - 351px + 56px${
                        moneyRuleHasErrBefore ? ' - 24px' : ''
                      })`}
                    />
                  </LongLabelNoAnimationFormItem>
                )}
                {getFieldValue('exprType') ===
                  FrontExpressionType.TYPESCRIPT && (
                  <LongLabelNoAnimationFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>生成属性（{FRONT_TS_EXPR_LABEL}）:</div>
                        <AiGenerateExpression
                          expressionType={FrontExpressionType.TYPESCRIPT}
                          getScene={handleGetScene}
                          onGenerate={handleGenerateCode}
                        />
                        <ExpressionTemplateActions
                          expression={getFieldValue('tsExpr')}
                          expressionType={FrontExpressionType.TYPESCRIPT}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="tsExpr"
                    rules={[{ required: true }, tsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <FrontJSExprEditor
                      language="typescript"
                      height={`calc(100vh - 351px + 56px${
                        moneyRuleHasErrBefore ? ' - 24px' : ''
                      })`}
                    />
                  </LongLabelNoAnimationFormItem>
                )}
              </>
            )}
          </Form.Item>
        </Form>
      </AiGenExprCtxFc>
    )
  }),
)

/**
 * 绑定前端表达式表单值
 */
export interface BindFrontExprFormValues {
  /**
   * 表达式类型
   */
  exprType: FrontExpressionType
  /**
   * js 表达式
   */
  jsExpr: string
  /**
   * ts 表达式
   */
  tsExpr: string
}
