import { DESIGNER_QUERY } from '@/pages/designer/constants'
import { LocalStorageDataLoader } from '@/utils'
import { waitByFrame } from '@ad/canal-shared'
import { isNumber } from 'lodash'
import { action, makeObservable, observable, reaction } from 'mobx'

/**
 * 画布宽度
 */
export type CanvasWidth =
  | number // 自定义宽度
  | 'pc' // PC 宽度
  | undefined // PC 宽度
  | 'tablet' // 平板宽度
  | 'phone' // 手机宽度

/**
 * 画布切换仓库
 */
export class CanvasSwitchStore {
  /**
   * 画布宽度加载器
   */
  private _canvasWidthLoader = new LocalStorageDataLoader<CanvasWidth>(
    `canvas-width:${DESIGNER_QUERY.moduleId}`,
    undefined,
  )

  /**
   * 画布宽度
   */
  private _canvasWidth: CanvasWidth = this._canvasWidthLoader.load()

  /**
   * 画布宽度
   */
  public get canvasWidth(): CanvasWidth {
    return this._canvasWidth
  }

  /**
   * kael-canvas 元素的宽度
   */
  private _kaelCanvasWidth = 0

  /**
   * 画布宽度像素
   */
  public get canvasWidthPx(): number {
    return this.getNumberWidth(this._canvasWidth)
  }

  public constructor() {
    makeObservable<this, '_canvasWidth' | '_kaelCanvasWidth'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _canvasWidth: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _kaelCanvasWidth: observable,
      setCanvasWidth: action,
    })
    reaction(
      () => this._canvasWidth,
      (width) => {
        this._canvasWidthLoader.save(width)
      },
    )
    waitByFrame(() => {
      const el = document.getElementById('kael-canvas')
      if (!el) return false
      const fn = action(() => {
        this._kaelCanvasWidth = el.getBoundingClientRect().width
      })
      fn()
      const resizeObserver = new ResizeObserver(fn)
      resizeObserver.observe(el)
      void this._kaelCanvasWidth
      return true
    })
  }

  /**
   * 获取数字宽度
   * @param canvasWidth 画布宽度
   */
  public getNumberWidth(canvasWidth: CanvasWidth): number {
    if (isNumber(canvasWidth)) return canvasWidth
    switch (canvasWidth) {
      case 'tablet': {
        return 768
      }
      case 'phone': {
        return 414
      }
    }
    return this._kaelCanvasWidth
  }

  /**
   * 设置画布宽度
   * @param width 画布宽度
   */
  public setCanvasWidth(width: CanvasWidth): void {
    this._canvasWidth = width
  }
}

/**
 * 单例：画布切换仓库
 */
export const canvasSwitchStore = new CanvasSwitchStore()
