import { IconButton } from '@/components/icon-button'
import type { E2ESchemaService } from '@/pages/designer/services'
import { isBindableExpression } from '@ad/e2e-schema-utils'
import { type PropertyActionProps } from '@kael/designer-property'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { Tooltip } from '@m-ui/react'
import { useCallback, type FC } from 'react'
import styled, { css } from 'styled-components'
import { bindPropStore } from '../bind-prop-drawer'
import { ReactComponent as SVGBind } from './bind.svg'

/**
 * 绑定属性图标
 */
export const BindPropIcon: FC<PropertyActionProps> = ({
  componentId,
  fieldPath,
  propertySchema,
}) => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const expr = schemaService.getComponentPropExpression(componentId, fieldPath)
  const propName = `${propertySchema.name}`
  const handleIconClick = useCallback(() => {
    bindPropStore.openDrawer({
      componentId,
      propPath: fieldPath,
      propName,
    })
  }, [componentId, fieldPath, propName])
  return (
    <Tooltip title="绑定" placement="left">
      <IconWrapper
        $isBound={isBindableExpression(expr)}
        onClick={handleIconClick}
      >
        <SVGBind />
      </IconWrapper>
    </Tooltip>
  )
}

interface IconWrapperProps {
  /**
   * 是否已绑定
   */
  $isBound: boolean
}

const IconWrapper = styled(IconButton)<IconWrapperProps>`
  flex-shrink: 0;
  transform: translate(-16px, 2%);
  width: 16px;
  height: 100%;
  margin-right: -16px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;

  .property-item:hover & {
    opacity: 1;
  }

  ${
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    ({ $isBound }) =>
      $isBound &&
      css`
        opacity: 1;
      `
  }
`
