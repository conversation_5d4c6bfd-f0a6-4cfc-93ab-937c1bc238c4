import { CommandRegistry, CommandRegistrySymbol } from '@kael/designer-core'
import {
  NavigationContribution,
  type NavigationContributionPosition,
} from '@kael/designer-navigation'
import { getInject, injectable } from '@kael/di'
import {
  SystemPlayCircleLine,
  SystemSettingLine,
  SystemStopCircleLine,
} from '@m-ui/icons'
import { Tooltip } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, type ReactNode } from 'react'
import styled, { css } from 'styled-components'
import { canvasPreviewerStore } from '../../../canvas/canvas-previewer/canvas-previewer-store'
import { HOTKEY_DISPLAY, HOTKEY_NAME, HotkeyType } from '../../../keybindings'
import { NavIconButton } from '../../components'

/**
 * 预览贡献点
 */
@injectable()
export class PreviewContribution extends NavigationContribution {
  public id = 'navigation-preview'
  public rank = 1
  public position: NavigationContributionPosition = 'right'

  public render(): ReactNode {
    return <PreviewIconBtn />
  }
}

/**
 * 预览图标按钮
 */
const PreviewIconBtn = observer((): JSX.Element => {
  const commandRegistry = getInject<CommandRegistry>(CommandRegistrySymbol)
  /**
   * 处理点击预览配置按钮事件
   */
  const handleClickSettingBtn = useCallback(() => {
    canvasPreviewerStore.startEditPreviewSetting()
  }, [])
  /**
   * 处理点击预览按钮事件
   */
  const handleClickPreviewBtn = useCallback(() => {
    commandRegistry.executeCommand(HotkeyType.PREVIEW)
  }, [commandRegistry])

  return (
    <Container $isPreviewing={canvasPreviewerStore.isPreviewing}>
      <Tooltip title="预览配置">
        <SettingBtn onClick={handleClickSettingBtn}>
          <SystemSettingLine />
        </SettingBtn>
      </Tooltip>
      <Tooltip
        title={`${
          canvasPreviewerStore.isPreviewing
            ? '停止'
            : HOTKEY_NAME[HotkeyType.PREVIEW]
        }（${HOTKEY_DISPLAY[HotkeyType.PREVIEW]}）`}
      >
        <PreviewBtn onClick={handleClickPreviewBtn}>
          {canvasPreviewerStore.isPreviewing ? (
            <StopIcon />
          ) : (
            <SystemPlayCircleLine />
          )}
        </PreviewBtn>
      </Tooltip>
    </Container>
  )
})

const SettingBtn = styled(NavIconButton)`
  position: relative;
  margin-right: 6px;
  transform: translateX(calc(100% + 6px)) rotate(90deg);
  opacity: 0;
  transition-property: opacity transform;
  transition-duration: 300ms;
`

const Container = styled.div<{
  $isPreviewing: boolean
}>`
  position: relative;
  line-height: 0;

  &:hover {
    ${
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      (props) =>
        props.$isPreviewing
          ? null
          : css`
              ${SettingBtn} {
                transform: none;
                opacity: 1;
              }
            `
    }
  }
`

const PreviewBtn = styled(NavIconButton)`
  position: relative;
  z-index: 1;
`

const StopIcon = styled(SystemStopCircleLine)`
  color: red;
`
