import {
  AbstractViewContribution,
  type ApplicationContribution,
} from '@kael/designer-core'
import { injectable, register } from '@kael/di'
import { SaveLogPanelWidget } from './widget'

/**
 * 保存日志面板视图贡献点
 */
@injectable()
export class SaveLogPanelViewContribution
  extends AbstractViewContribution<SaveLogPanelWidget>
  implements ApplicationContribution
{
  public constructor() {
    super({
      widgetId: SaveLogPanelWidget.ID,
      widgetName: SaveLogPanelWidget.LABEL,
      widgetOptions: { area: 'left' },
    })
  }

  public async initializeLayout(): Promise<void> {
    await this.openView()
  }
}

/**
 * 注册保存日志面板视图贡献点
 */
export function registerSaveLogPanelViewContribution(): void {
  register({
    token: SaveLogPanelViewContribution.token,
    useClass: SaveLogPanelViewContribution,
  })
}
