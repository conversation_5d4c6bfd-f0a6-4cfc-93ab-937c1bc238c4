import { type E2ESchemaExpression } from '@ad/e2e-schema'
import { type SetterRenderProps } from '@kael/designer-property'
import { normalizeExpression } from '@kael/schema-utils'
import { Input } from '@m-ui/react'
import { isString } from 'lodash'
import React, { useCallback, useMemo, type FC } from 'react'
import styled from 'styled-components'
import { cacheSetterValue } from '../../hocs/cache-setter-value'

/**
 * 边简写设置器属性
 */
export interface EdgeShorthandSetterProps {}

/**
 * 5 个输入框的值
 */
type InputValues = [string, string, string, string, string]

/**
 * 解码简写属性
 * @param p 简写属性
 */
function decodeShorthandProp(p?: string): InputValues {
  if (p) {
    const parts: string[] = p.split(' ')
    if (parts.length === 1) {
      return [parts[0], parts[0], parts[0], parts[0], parts[0]]
    } else if (parts.length === 2) {
      return ['', parts[0], parts[1], parts[0], parts[1]]
    } else if (parts.length === 3) {
      return ['', parts[0], parts[1], parts[2], parts[1]]
    } else if (parts.length === 4) {
      return ['', parts[0], parts[1], parts[2], parts[3]]
    }
  }
  return ['', '', '', '', '']
}

/**
 * 编码简写属性
 * @param parts 解码后的各个部分
 */
function encodeShorthandProp(parts: InputValues): string {
  if (parts[1] === parts[2] && parts[1] === parts[3] && parts[1] === parts[4]) {
    return parts[1]
  }
  return parts
    .slice(1)
    .map((v) => v || '0')
    .join(' ')
}

/**
 * 修改简写属性部分
 * @param parts 解码后的各个部分
 * @param index 修改的下标
 * @param value 修改的值
 */
function modifyShorthandPropPart(
  parts: InputValues,
  index: number,
  value: string,
): InputValues {
  if (index === 0) {
    return parts.map(() => value) as InputValues
  } else {
    return parts.map((v, i) => {
      if (i === 0) {
        return ''
      } else if (index === i) {
        return value
      } else {
        return v
      }
    }) as InputValues
  }
}

/**
 * 边简写设置器
 */
export const EdgeShorthandSetter: FC<
  SetterRenderProps<EdgeShorthandSetterProps>
> = cacheSetterValue((props) => {
  const { value: expr, onChange } = props
  /**
   * 输入框的值
   */
  const inputValues: InputValues = useMemo(() => {
    let str: string | undefined
    const e = normalizeExpression(expr as E2ESchemaExpression)
    if (e.type === 'static' && isString(e.value)) {
      str = e.value
    }
    return decodeShorthandProp(str)
  }, [expr])
  /**
   * 处理输入框修改事件
   */
  const handleInputChange = useCallback(
    (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
      const newInputValues = modifyShorthandPropPart(
        inputValues,
        index,
        e.target.value,
      )
      onChange(encodeShorthandProp(newInputValues))
    },
    [inputValues, onChange],
  )

  return (
    <Container>
      <ShorthandInputWrapper>
        <Input
          size="small"
          value={inputValues[0]}
          onChange={handleInputChange.bind(null, 0)}
        />
      </ShorthandInputWrapper>
      <Row>
        <Col>上</Col>
        <Col>右</Col>
        <Col>下</Col>
        <Col>左</Col>
      </Row>
      <Row>
        <Col>
          <Input
            size="small"
            value={inputValues[1]}
            onChange={handleInputChange.bind(null, 1)}
          />
        </Col>
        <Col>
          <Input
            size="small"
            value={inputValues[2]}
            onChange={handleInputChange.bind(null, 2)}
          />
        </Col>
        <Col>
          <Input
            size="small"
            value={inputValues[3]}
            onChange={handleInputChange.bind(null, 3)}
          />
        </Col>
        <Col>
          <Input
            size="small"
            value={inputValues[4]}
            onChange={handleInputChange.bind(null, 4)}
          />
        </Col>
      </Row>
    </Container>
  )
})

const Container = styled.div``

const ShorthandInputWrapper = styled.div`
  position: absolute;
  top: 26px;
  left: -68px;
  width: 50px;
  height: 26px;

  .ant-input {
    width: 100%;
    height: 100%;
  }
`

const Row = styled.div`
  display: flex;
  font-size: 12px;
  height: 26px;
  line-height: 26px;
`

const Col = styled.div`
  width: 25%;
  text-align: center;

  .ant-input {
    width: 90%;
    height: 100%;
  }
`
