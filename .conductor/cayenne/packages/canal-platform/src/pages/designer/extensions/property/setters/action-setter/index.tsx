import { I<PERSON><PERSON>utton } from '@/components/icon-button'
import { DEFAULT_ACTION_EFFECT_JS_EXPR_STR } from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import type {
  E2ESchemaExpression,
  E2ESchemaExpressionActions,
  E2ESchemaExpressionBind,
  E2ESchemaExpressionJS,
  E2ESchemaExpressionRefresh,
  E2ESchemaExpressionTrack,
} from '@ad/e2e-schema'
import type { SetterRenderProps } from '@kael/designer-property'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { normalizeExpression } from '@kael/schema-utils'
import { SystemDeleteLine } from '@m-ui/icons'
import { Button, Tooltip, message } from '@m-ui/react'
import { cloneDeep } from 'lodash'
import { useCallback, useMemo, type FC } from 'react'
import { cacheSetterValue } from '../../hocs'
import {
  SetterList,
  SetterListItem,
  SetterListItemHeader,
  SetterListTypeSelect,
} from '../common'
import { ActionExecEffectEditor } from './action-exec-effect-editor'
import { ActionRefreshSubmitEditor } from './action-refresh-submit-editor'
import { ActionTrackEditor } from './action-track-editor'
import type { ActionEditorCommonProps } from './types'

/**
 * 操作设置器属性
 */
export interface ActionSetterProps {}

/**
 * 操作设置器
 */
export const ActionSetter: FC<SetterRenderProps<ActionSetterProps>> =
  cacheSetterValue(
    ({ value: rawValue, onChange, componentId, schema: fieldPropSchema }) => {
      const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
      const expr: E2ESchemaExpressionActions = useMemo(() => {
        const rawExpr = normalizeExpression(rawValue as E2ESchemaExpression)
        if (rawExpr.type === 'actions') {
          return rawExpr
        }
        if (!getActionByExpr(rawExpr)) {
          return {
            type: 'actions',
            fns: [],
          }
        }
        return {
          type: 'actions',
          fns: [rawExpr],
        }
      }, [rawValue])
      const handleAddActionBtnClick = useCallback(() => {
        const newExpr: E2ESchemaExpressionActions = {
          ...expr,
          fns: [
            ...expr.fns,
            cloneDeep(DEFAULT_EXPR_BY_ACTION[Action.REFRESH_SUBMIT]),
          ],
        }
        onChange(newExpr)
      }, [onChange, expr])
      const handleDeleteActionBtnClick = useCallback(
        (idx: number) => {
          const newExpr: E2ESchemaExpressionActions = {
            ...expr,
            fns: [...expr.fns],
          }
          newExpr.fns.splice(idx, 1)
          onChange(newExpr.fns.length ? newExpr : undefined)
        },
        [onChange, expr],
      )
      const handleActionTypeChange = useCallback(
        (idx: number, action: Action) => {
          const fnExpr = cloneDeep(DEFAULT_EXPR_BY_ACTION[action])
          if (action === Action.TRACK) {
            const firstTrackId = schemaService.schema?.tracks?.[0]?.id
            if (!firstTrackId) {
              message.info('请先点击根组件，配置埋点列表')
              return
            }
            ;(fnExpr as E2ESchemaExpressionTrack).trackId = firstTrackId
          }
          const newExpr: E2ESchemaExpressionActions = {
            ...expr,
            fns: [...expr.fns],
          }
          newExpr.fns[idx] = fnExpr
          onChange(newExpr)
        },
        [expr, onChange, schemaService],
      )
      const handleActionChange = useCallback(
        (idx: number, fnExpr: E2ESchemaExpression) => {
          const newExpr: E2ESchemaExpressionActions = {
            ...expr,
            fns: [...expr.fns],
          }
          newExpr.fns[idx] = fnExpr
          onChange(newExpr)
        },
        [onChange, expr],
      )
      return (
        <SetterList>
          <Button type="primary" size="small" onClick={handleAddActionBtnClick}>
            新增动作
          </Button>
          {expr.fns.map((fnExpr, index) => {
            const action = getActionByExpr(fnExpr)
            if (!action) {
              return null
            }
            const Editor = ACTION_EDITOR_MAP[action]
            return (
              <SetterListItem key={index}>
                <SetterListItemHeader>
                  <b>动作 {index}</b>
                  <Tooltip title="删除" placement="left">
                    <IconButton
                      onClick={handleDeleteActionBtnClick.bind(null, index)}
                    >
                      <SystemDeleteLine />
                    </IconButton>
                  </Tooltip>
                </SetterListItemHeader>
                <SetterListTypeSelect
                  size="small"
                  options={ACTION_OPTIONS}
                  value={action}
                  onChange={handleActionTypeChange.bind(null, index)}
                />
                <Editor
                  value={fnExpr}
                  onChange={handleActionChange.bind(null, index)}
                  index={index}
                  componentId={componentId}
                  fieldPropSchema={fieldPropSchema}
                />
              </SetterListItem>
            )
          })}
        </SetterList>
      )
    },
  )

/**
 * 操作
 */
enum Action {
  /**
   * 提交刷新
   */
  REFRESH_SUBMIT = 1,
  /**
   * 触发埋点
   */
  TRACK,
  /**
   * 执行副作用
   */
  EXEC_EFFECT,
}

/**
 * 操作选项
 */
const ACTION_OPTIONS = [
  {
    label: '提交刷新',
    value: Action.REFRESH_SUBMIT,
  },
  {
    label: '触发埋点',
    value: Action.TRACK,
  },
  {
    label: '执行副作用',
    value: Action.EXEC_EFFECT,
  },
]

/**
 * 按操作的默认表达式
 */
const DEFAULT_EXPR_BY_ACTION = {
  [Action.REFRESH_SUBMIT]: {
    type: 'refresh',
    refreshType: 'submit',
  } satisfies E2ESchemaExpressionRefresh,
  [Action.TRACK]: {
    type: 'track',
    trackId: '',
  } satisfies E2ESchemaExpressionTrack,
  [Action.EXEC_EFFECT]: {
    type: 'bind',
    fn: {
      type: 'js',
      code: `(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = void 0;\n/**\n* @param {Container} ctx 上下文\n* @param {any} args 组件调用事件函数时传递的参数\n*/\nvar _default = exports["default"] = function _default(ctx) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  console.log('事件级前端动作副作用', ctx, args);\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)`,
      codeES: DEFAULT_ACTION_EFFECT_JS_EXPR_STR,
    },
    args: [
      {
        type: 'js',
        code: 'ctx.runtime.container',
      } as E2ESchemaExpressionJS,
    ],
  } satisfies E2ESchemaExpressionBind,
}

/**
 * 通过表达式获取动作
 * @param expr 表达式
 */
function getActionByExpr(expr: E2ESchemaExpression): Action | null {
  const normalizedExpr = normalizeExpression(expr)
  switch (normalizedExpr.type) {
    case 'refresh': {
      if (normalizedExpr.refreshType === 'submit') {
        return Action.REFRESH_SUBMIT
      }
      break
    }
    case 'track': {
      return Action.TRACK
    }
    case 'bind': {
      return Action.EXEC_EFFECT
    }
  }
  return null
}

/**
 * 操作编辑器对照表
 */
const ACTION_EDITOR_MAP = {
  [Action.REFRESH_SUBMIT]: ActionRefreshSubmitEditor,
  [Action.TRACK]: ActionTrackEditor,
  [Action.EXEC_EFFECT]: ActionExecEffectEditor,
} as Record<Action, FC<ActionEditorCommonProps<E2ESchemaExpression>>>
