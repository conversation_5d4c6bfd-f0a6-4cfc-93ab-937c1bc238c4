import {
  DESIGNER_QUERY,
  IS_DEBUG_MODE,
  IS_READONLY_MODE,
} from '@/pages/designer/constants'
import { designerCoeditStore } from '@/pages/designer/stores/designer-coedit-store'
import { schemaStore } from '@/pages/designer/stores/schema-store'
import LogDiffModel from '@/pages/diff/log-diff-model'
import { CommandRegistry, CommandRegistrySymbol } from '@kael/designer-core'
import { getInject } from '@kael/di'
import { Popconfirm, Spin, Typography } from '@m-ui/react'
import dayjs from 'dayjs'
import { once } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useEffect, useRef, type FC } from 'react'
import styled from 'styled-components'
import { HotkeyType } from '../../keybindings'
import { saveLogStore, type ModuleVersionSaveLogState } from '../save-log-store'

/**
 * 模块保存日志属性
 */
export interface ModuleSaveLogProps {
  /**
   * 状态
   */
  state: ModuleVersionSaveLogState
}

/**
 * 模块保存日志
 */
export const ModuleSaveLog: FC<ModuleSaveLogProps> = observer(
  ({ state: { moduleVersion, checkoutModuleVersion, loading, logs } }) => {
    const refTitle = useRef<HTMLDivElement>(null)
    useEffect(() => {
      const el = refTitle.current
      if (
        !checkoutModuleVersion ||
        checkoutModuleVersion === moduleVersion ||
        !el
      ) {
        return
      }
      const load = once(() => saveLogStore.loadLogs(checkoutModuleVersion))
      const ob = new IntersectionObserver((entries) => {
        for (const entry of entries) {
          if (entry.isIntersecting && entry.target === el) {
            load()
          }
        }
      })
      ob.observe(el)
      return () => ob.disconnect()
    }, [checkoutModuleVersion, moduleVersion])
    const resumable =
      designerCoeditStore.isCurrentRoomEditing &&
      schemaStore.editable &&
      !IS_DEBUG_MODE &&
      !IS_READONLY_MODE
    return (
      <Container>
        <Title ref={refTitle}>
          模块版本：{moduleVersion}
          {`${moduleVersion}` === DESIGNER_QUERY.moduleVersion
            ? '（当前）'
            : ''}
          {loading && <Spin />}
        </Title>
        {!logs.length && !loading && <NoLog>无记录</NoLog>}
        {logs.map((log, index) => (
          <Log key={log.id}>
            <div className="log-content">
              {log.creator} {dayjs(log.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </div>
            <div className="log-actions">
              <div
                className="log-text-btn log-view"
                onClick={(): void => {
                  const url = new URL(window.location.href)
                  url.searchParams.set('mode', 'readonly')
                  url.searchParams.set('moduleVersionLogId', log.id)
                  window.open(url)
                }}
              >
                查看↗
              </div>
              {resumable && (
                <>
                  <Popconfirm
                    title="确认覆盖当前的内容，恢复为该保存记录的内容吗？"
                    onConfirm={(): void => {
                      const commandRegistry = getInject<CommandRegistry>(
                        CommandRegistrySymbol,
                      )
                      commandRegistry.executeCommand(HotkeyType.RESUME, log.id)
                    }}
                  >
                    <div className="log-text-btn log-resume">恢复</div>
                  </Popconfirm>
                </>
              )}
              <LogDiffModel
                currenLogId={log.id}
                baseLogId={logs[index + 1]?.id ?? ''}
                domainCode={DESIGNER_QUERY.domainCode}
              />
            </div>
          </Log>
        ))}
      </Container>
    )
  },
)

const Container = styled.div`
  margin: 10px;
`

const Title = styled.h3`
  line-height: 30px;
  margin-bottom: 10px;
`

const NoLog = styled(Typography.Text).attrs({
  type: 'secondary',
})`
  margin-left: 10px;
`

const Log = styled.div`
  position: relative;
  margin: 10px 0 10px 10px;

  .log-content {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .log-actions {
    position: absolute;
    top: 0;
    right: 0;
    display: none;
    background: #fff;
    padding: 0 10px;
    gap: 10px;
  }

  .log-text-btn {
    cursor: pointer;
    display: inline-block;
  }

  .log-view {
    color: rgb(0, 117, 255);
  }

  .log-resume {
    color: rgb(255, 101, 74);
  }

  &:hover {
    .log-actions {
      display: flex;
    }
  }
`
