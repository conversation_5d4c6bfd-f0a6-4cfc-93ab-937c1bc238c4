import { PropertyCategoryActionContribution } from '@kael/designer-property'
import type { ReactNode } from 'react'
import { PropertyBaseTab } from '../components'

/**
 * 属性分类操作，基础面板
 */
export class PropertyCategoryActionBase extends PropertyCategoryActionContribution {
  public tab = '基础'

  public key = BASE_TAB_KEY

  public index = 0

  public render(): ReactNode {
    return <PropertyBaseTab />
  }
}

/**
 * 基础面板键值
 */
export const BASE_TAB_KEY = 'BASE'
