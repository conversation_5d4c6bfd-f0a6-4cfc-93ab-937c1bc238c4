import { observer } from 'mobx-react-lite'
import { type FC } from 'react'
import styled from 'styled-components'
import { saveLogStore } from '../save-log-store'
import { ModuleSaveLog } from './module-save-log'

/**
 * 保存日志面板
 */
export const SaveLogPanel: FC = observer(() => {
  const { states } = saveLogStore
  return (
    <Container>
      {states.map((state) => (
        <ModuleSaveLog state={state} key={state.moduleVersion} />
      ))}
    </Container>
  )
})

const Container = styled.div`
  height: 100%;
  overflow: auto;
`
