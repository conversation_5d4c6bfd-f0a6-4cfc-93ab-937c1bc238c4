import {
  NavigationContribution,
  type NavigationContributionPosition,
} from '@kael/designer-navigation'
import { injectable } from '@kael/di'
import { type ReactNode } from 'react'
import { CollapseIcon } from './collapse-icon'

/**
 * 折叠贡献点
 */
@injectable()
export class CollapseContribution extends NavigationContribution {
  public id = 'navigation-collapse'
  public rank = 2
  public position: NavigationContributionPosition = 'middle'

  public render(): ReactNode {
    return <CollapseIcon />
  }
}
