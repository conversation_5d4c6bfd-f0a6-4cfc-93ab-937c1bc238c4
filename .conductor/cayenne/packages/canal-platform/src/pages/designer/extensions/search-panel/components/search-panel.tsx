import { designerSearchStore } from '@/pages/designer/stores/designer-search-store'
import { useLatestFn } from '@ad/canal-shared-ui'
import { Input } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useEffect, useRef, type ChangeEvent, type FC } from 'react'
import styled from 'styled-components'
import { SearchResultList } from './search-result-list'

/**
 * 搜索面板
 */
export const SearchPanel: FC = observer(() => {
  const { keyword, searchPanelIntersectionObserver } = designerSearchStore
  const handleInputChange = useLatestFn((ev: ChangeEvent<HTMLInputElement>) => {
    designerSearchStore.setKeyword(ev.target.value)
  })
  const refContainer = useRef<HTMLDivElement>(null)
  useEffect(() => {
    const el = refContainer.current
    if (el) {
      searchPanelIntersectionObserver.observe(el)
      return () => searchPanelIntersectionObserver.unobserve(el)
    }
  }, [searchPanelIntersectionObserver])
  return (
    <Container ref={refContainer}>
      <Input
        size="small"
        placeholder="搜索模块内容"
        allowClear
        autoFocus
        value={keyword}
        onChange={handleInputChange}
      />
      <StyledSearchResultList />
    </Container>
  )
})

const Container = styled.div`
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
`

const StyledSearchResultList = styled(SearchResultList)`
  height: 0;
  flex: 1;
  margin-top: 10px;
`
