import { INTERNAL_COMPONENT_TYPE_ROOT } from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import { getPropertyService } from '@kael/designer-property'
import {
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { observer } from 'mobx-react-lite'
import { type FC } from 'react'
import { PropertyBaseTabOtherComp } from './property-base-tab-other-comp'
import { PropertyBaseTabRootComp } from './property-base-tab-root-comp'

/**
 * 属性基础面板
 */
export const PropertyBaseTab: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const stateService = getInject<StateService>(StateServiceSymbol)
  const propertyService = getPropertyService()
  const materialSchema = propertyService.materialSchema
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const comp = schemaService.findComponentById(
    stateService.selectedComponentId,
  )!
  const isRoot = comp.type === INTERNAL_COMPONENT_TYPE_ROOT

  if (!comp || !materialSchema) {
    return null
  }
  return isRoot ? <PropertyBaseTabRootComp /> : <PropertyBaseTabOtherComp />
})
