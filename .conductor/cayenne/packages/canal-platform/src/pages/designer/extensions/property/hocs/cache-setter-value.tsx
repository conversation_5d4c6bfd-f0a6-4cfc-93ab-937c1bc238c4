import { type SetterRenderProps } from '@kael/designer-property'
import SetterWrapper from '@kael/designer-setter/esm/common/setter-wrapper'
import { type FC } from 'react'

/**
 * 缓存 Setter 的值
 * @param S Setter
 */
export function cacheSetterValue<T>(
  S: FC<SetterRenderProps<T>>,
): FC<SetterRenderProps<T>> {
  return (props) => {
    return (
      <SetterWrapper
        value={props.value}
        onChange={props.onChange}
        component={<S {...props} />}
      />
    )
  }
}
