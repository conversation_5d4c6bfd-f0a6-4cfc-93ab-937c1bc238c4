import {
  PropertyActionContribution,
  type PropertyActionProps,
  type PropertyContributionPosition,
} from '@kael/designer-property'
import { injectable } from '@kael/di'
import { BindPropIcon } from '../components'

/**
 * 属性绑定操作贡献点
 */
@injectable()
export class PropertyBindActionContribution extends PropertyActionContribution {
  public align: PropertyContributionPosition = 'left'

  public render(props: PropertyActionProps): JSX.Element {
    return <BindPropIcon {...props} />
  }
}
