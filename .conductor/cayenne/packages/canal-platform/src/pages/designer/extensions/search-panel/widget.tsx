import { ReactWidget } from '@kael/designer-core'
import { injectable, postConstruct, register } from '@kael/di'
import React from 'react'
import { createGlobalStyle } from 'styled-components'
import { SearchPanel } from './components/search-panel'
import IMG_SEARCH_PANEL from './search-panel.svg'

/**
 * 搜索面板控件
 */
@injectable()
export class SearchPanelWidget extends ReactWidget {
  public static readonly ID = 'search-panel'
  public static readonly LABEL = '搜索面板'

  @postConstruct()
  public init(): void {
    this.id = SearchPanelWidget.ID
    this.title.label = SearchPanelWidget.LABEL
    this.title.caption = SearchPanelWidget.LABEL
    this.title.iconClass = 'tab-icon-search-panel'
    this.title.closable = true
  }

  public render(): React.ReactNode {
    return (
      <>
        <GlobalStyle />
        <SearchPanel />
      </>
    )
  }
}

/**
 * 注册搜索面板控件
 */
export function registerSearchPanelWidget(): void {
  register({
    token: SearchPanelWidget.token,
    useClass: SearchPanelWidget,
  })
}

const GlobalStyle = createGlobalStyle`
  .tab-icon-search-panel {
    mask: url('${IMG_SEARCH_PANEL}');
    -webkit-mask: url('${IMG_SEARCH_PANEL}');
  }
`
