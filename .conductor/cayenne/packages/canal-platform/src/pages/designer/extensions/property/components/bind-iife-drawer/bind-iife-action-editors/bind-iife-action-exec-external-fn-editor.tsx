import { ControlLabel } from '@/components/control-label'
import type { BindIIFEActionExecExternalFn } from '@ad/e2e-schema'
import { Input } from '@m-ui/react'
import { useCallback, type ChangeEvent, type FC } from 'react'
import { FieldAsPath } from './field-as-path'
import type { BindIIFEActionEditorCommonProps } from './types'

/**
 * 执行外部函数动作编辑器属性
 */
export interface BindIIFEActionExecExternalFnEditorProps
  extends BindIIFEActionEditorCommonProps<BindIIFEActionExecExternalFn> {}

/**
 * 执行外部函数动作编辑器
 */
export const BindIIFEActionExecExternalFnEditor: FC<
  BindIIFEActionExecExternalFnEditorProps
> = ({ value, onChange }) => {
  const handleFnPathChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        fnPath: e.target.value,
      })
    },
    [onChange, value],
  )
  const handleArg0Change = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        arg0: e.target.value,
      })
    },
    [onChange, value],
  )
  const handleArg0IsPathChange = useCallback(
    (v: boolean) => {
      onChange({
        ...value,
        arg0IsPath: v,
      })
    },
    [onChange, value],
  )

  return (
    <div>
      <div>
        <ControlLabel>函数路径（在运行时 data 里的路径）：</ControlLabel>
        <Input value={value.fnPath} onChange={handleFnPathChange} />
      </div>
      <div>
        <ControlLabel>
          参数（字符串）：
          <FieldAsPath
            value={value.arg0IsPath}
            onChange={handleArg0IsPathChange}
          />
        </ControlLabel>
        <Input value={value.arg0} onChange={handleArg0Change} />
      </div>
    </div>
  )
}
