import { expressionTemplateStore } from '@/pages/designer/stores/expression-template-store'
import type { ExpressionTemplate } from '@/services/backend/models'
import { action, makeObservable, observable } from 'mobx'
import type { ExpressionTemplateInfo } from '../save-expression-template-modal/save-expression-template-store'

/**
 * 应用表达式模板仓库
 */
export class ApplyExpressionTemplateStore {
  /**
   * 模态框是否可见
   */
  private _isModalVisible = false

  /**
   * 模态框是否可见
   */
  public get isModalVisible(): boolean {
    return this._isModalVisible
  }

  /**
   * 编辑中的表达式模板信息
   */
  private _editingExpressionTemplateInfo: ExpressionTemplateInfoForApply | null =
    null

  /**
   * 编辑中的表达式模板信息
   */
  public get editingExpressionTemplateInfo(): ExpressionTemplateInfoForApply | null {
    return this._editingExpressionTemplateInfo
  }

  public constructor() {
    makeObservable<
      ApplyExpressionTemplateStore,
      '_isModalVisible' | '_editingExpressionTemplateInfo'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isModalVisible: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _editingExpressionTemplateInfo: observable.ref,
      openModal: action,
      cancelModal: action,
      submitModal: action,
    })

    if (process.env.NODE_ENV === 'development') {
      Object.assign(window, {
        debugApplyExpressionTemplateStore: this,
      })
    }
  }

  /**
   * 打开对话框
   * @param editingExpressionTemplateInfo 编辑中的表达式模板信息
   */
  public openModal(
    editingExpressionTemplateInfo: Omit<ExpressionTemplateInfoForApply, 'name'>,
  ): void {
    this._isModalVisible = true
    this._editingExpressionTemplateInfo = {
      ...editingExpressionTemplateInfo,
      // 类型一样时，保留 name
      name:
        editingExpressionTemplateInfo.expressionType ===
        this._editingExpressionTemplateInfo?.expressionType
          ? this._editingExpressionTemplateInfo.name
          : '',
    }
  }

  /**
   * 取消对话框
   */
  public cancelModal(): void {
    this._isModalVisible = false
  }

  /**
   * 提交对话框
   */
  public submitModal(info: Pick<ExpressionTemplateInfo, 'name'>): void {
    console.log('ApplyExpressionTemplateStore::submitModal formValues', info)
    this._isModalVisible = false
    if (!this._editingExpressionTemplateInfo) {
      return
    }
    this._editingExpressionTemplateInfo.name = info.name
    const tpl = expressionTemplateStore.expressionTemplateByType[
      this._editingExpressionTemplateInfo.expressionType
    ].find((t) => t.name === info.name)
    if (!tpl) {
      return
    }
    this._editingExpressionTemplateInfo.onFinish(tpl)
  }
}

/**
 * 单例：应用表达式模板仓库
 */
export const applyExpressionTemplateStore = new ApplyExpressionTemplateStore()

/**
 * 表达式模板信息
 */
export interface ExpressionTemplateInfoForApply
  extends Omit<ExpressionTemplateInfo, 'expression'> {
  /**
   * 完成事件
   * @param tpl 表达式模板
   */
  onFinish: (tpl: ExpressionTemplate) => void
}
