import { IS_DEBUG_MODE, IS_READONLY_MODE } from '@/pages/designer/constants'
import {
  NavigationContribution,
  type NavigationContributionPosition,
} from '@kael/designer-navigation'
import { injectable } from '@kael/di'
import { type ReactNode } from 'react'
import { DesignerCoeditSave, SaveBtn } from './components'

/**
 * 保存贡献点
 */
@injectable()
export class SaveContribution extends NavigationContribution {
  public id = 'navigation-save'
  public rank = 4
  public position: NavigationContributionPosition = 'right'

  public render(): ReactNode {
    return IS_READONLY_MODE ? null : IS_DEBUG_MODE ? (
      <SaveBtn />
    ) : (
      <DesignerCoeditSave />
    )
  }
}
