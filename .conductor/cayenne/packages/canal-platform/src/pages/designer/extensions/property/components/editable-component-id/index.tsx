import type { E2ESchemaService } from '@/pages/designer/services'
import {
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { Typography, message } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, type FC } from 'react'
import CopyToClipboard from 'react-copy-to-clipboard'

/**
 * 可编辑的组件 ID
 */
export const EditableComponentId: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const stateService = getInject<StateService>(StateServiceSymbol)
  const selectedComponentId = stateService.selectedComponentId
  const handleCopy = useCallback(() => {
    message.info('组件 ID 已复制')
  }, [])
  const handleTextChange = useCallback(
    (newId: string) => {
      if (!selectedComponentId || !newId || newId === selectedComponentId) {
        return
      }
      if (schemaService.isComponentIdExists(newId)) {
        message.error('组件 ID 已存在')
        return
      }
      schemaService.updateComponentId(selectedComponentId, newId)
    },
    [schemaService, selectedComponentId],
  )
  if (!selectedComponentId) return null
  return (
    <Typography.Text
      editable={{
        text: selectedComponentId,
        onChange: handleTextChange,
      }}
    >
      <CopyToClipboard text={selectedComponentId} onCopy={handleCopy}>
        <span>{selectedComponentId}</span>
      </CopyToClipboard>
    </Typography.Text>
  )
})
