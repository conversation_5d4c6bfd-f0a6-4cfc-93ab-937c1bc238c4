import { ControlLabel } from '@/components/control-label'
import type { BindIIFEActionOpenUrl } from '@ad/e2e-schema'
import { Input, Switch } from '@m-ui/react'
import { useCallback, type ChangeEvent, type FC } from 'react'
import { FieldAsPath } from './field-as-path'
import type { BindIIFEActionEditorCommonProps } from './types'

/**
 * 打开链接动作编辑器属性
 */
export interface BindIIFEActionOpenUrlEditorProps
  extends BindIIFEActionEditorCommonProps<BindIIFEActionOpenUrl> {}

/**
 * 打开链接动作编辑器
 */
export const BindIIFEActionOpenUrlEditor: FC<
  BindIIFEActionOpenUrlEditorProps
> = ({ value, onChange }) => {
  const handleUrlChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        url: e.target.value,
      })
    },
    [onChange, value],
  )
  const handleUrlIsPathChange = useCallback(
    (v: boolean) => {
      onChange({
        ...value,
        urlIsPath: v,
      })
    },
    [onChange, value],
  )
  const handleInPlaceChange = useCallback(
    (v: boolean) => {
      onChange({
        ...value,
        inPlace: v,
      })
    },
    [onChange, value],
  )

  return (
    <div>
      <div>
        <ControlLabel>
          链接：
          <FieldAsPath
            value={value.urlIsPath}
            onChange={handleUrlIsPathChange}
          />
        </ControlLabel>
        <Input value={value.url} onChange={handleUrlChange} />
      </div>
      <div>
        <ControlLabel>原地打开（不新开浏览器标签页）：</ControlLabel>
        <Switch checked={value.inPlace} onChange={handleInPlaceChange} />
      </div>
    </div>
  )
}
