import type { E2ESchemaService } from '@/pages/designer/services'
import { preloadBabel } from '@/utils'
import type { E2ESchemaExpression } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { action, makeObservable, observable } from 'mobx'

/**
 * 绑定立即执行（前端动作）仓库
 */
export class BindIIFEStore {
  /**
   * 抽屉是否可见
   */
  private _isDrawerVisible = false

  /**
   * 抽屉是否可见
   */
  public get isDrawerVisible(): boolean {
    return this._isDrawerVisible
  }

  public constructor() {
    makeObservable<BindIIFEStore, '_isDrawerVisible'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isDrawerVisible: observable,
      openDrawer: action,
      submitDrawer: action,
    })
  }

  /**
   * 打开抽屉
   */
  public openDrawer(): void {
    this._isDrawerVisible = true
    preloadBabel()
  }

  /**
   * 取消抽屉
   */
  public cancelDrawer(): void {
    this._isDrawerVisible = false
  }

  /**
   * 提交抽屉
   * @param iife 立即执行的函数表达式
   */
  public submitDrawer(iife?: E2ESchemaExpression): void {
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    schemaService.updateIIFE(iife)
    this._isDrawerVisible = false
  }
}

/**
 * 绑定立即执行（前端动作）仓库，单例
 */
export const bindIIFEStore = new BindIIFEStore()
