import { ExpressionEnv } from '@ad/canal-shared'
import { Button, Tabs, type FormInstance } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, useRef, type FC } from 'react'
import styled from 'styled-components'
import { PropertyDrawer, PropertyDrawerContainer } from '../property-drawer'
import { BindBackExprForm } from './bind-back-expr-form'
import { BindFrontExprForm } from './bind-front-expr-form'
import { bindPropStore } from './bind-prop-store'

export * from './bind-back-expr-form'
export * from './bind-prop-store'

/**
 * 绑定属性抽屉
 */
export const BindPropDrawer: FC = observer(() => {
  const { isDrawerVisible, drawerConfig, expressionEnv } = bindPropStore
  const { frontExprEnabled = true } = drawerConfig || {}
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const refForm = useRef<FormInstance<any>>(null)
  const handleRemoveBtnClick = useCallback(() => {
    bindPropStore.submitDrawer()
  }, [])
  const handleCancelBtnClick = useCallback(() => {
    bindPropStore.cancelDrawer()
  }, [])
  const handleSubmitBtnClick = useCallback(() => {
    refForm.current?.submit()
  }, [])
  const handleTabsChange = useCallback((key: string) => {
    bindPropStore.setExpressionEnv(Number(key) as ExpressionEnv)
  }, [])
  return (
    <PropertyDrawerContainer>
      <PropertyDrawer
        title={
          <ExpressionEnvTabs
            activeKey={`${expressionEnv}`}
            onChange={handleTabsChange}
          >
            <Tabs.TabPane key={ExpressionEnv.BACK} tab="后端表达式" />
            {frontExprEnabled && (
              <Tabs.TabPane key={ExpressionEnv.FRONT} tab="前端表达式" />
            )}
          </ExpressionEnvTabs>
        }
        getContainer={false}
        visible={isDrawerVisible}
        closable={false}
        maskClosable={false}
        destroyOnClose
        extra={
          <>
            <ActionBtn danger onClick={handleRemoveBtnClick}>
              移除绑定
            </ActionBtn>
            <ActionBtn onClick={handleCancelBtnClick}>取消</ActionBtn>
            <ActionBtn type="primary" onClick={handleSubmitBtnClick}>
              提交
            </ActionBtn>
          </>
        }
        width={1200}
      >
        {expressionEnv === ExpressionEnv.FRONT ? (
          <BindFrontExprForm ref={refForm} />
        ) : (
          <BindBackExprForm ref={refForm} />
        )}
      </PropertyDrawer>
    </PropertyDrawerContainer>
  )
})

const ActionBtn = styled(Button)`
  margin-left: 8px;
`

const ExpressionEnvTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 0;
  }
`
