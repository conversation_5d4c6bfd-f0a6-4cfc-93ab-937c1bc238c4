import type { ExpressionTemplate } from '@/services/backend/models'
import type { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import { Button } from '@m-ui/react'
import { useCallback, type FC } from 'react'
import { applyExpressionTemplateStore } from './apply-expression-template-modal /apply-expression-template-store'
import { saveExpressionTemplateStore } from './save-expression-template-modal/save-expression-template-store'

/**
 * 表达式模板操作属性
 */
export interface ExpressionTemplateActionsProps {
  /**
   * 表达式
   */
  expression: string
  /**
   * 表达式类型
   */
  expressionType: BackExpressionType | FrontExpressionType
  /**
   * 应用事件
   * @param tpl 表达式模板
   */
  onApply: (tpl: ExpressionTemplate) => void
}

/**
 * 表达式模板操作
 */
export const ExpressionTemplateActions: FC<ExpressionTemplateActionsProps> = ({
  expressionType,
  expression,
  onApply,
}) => {
  const handleClickApplyExpressionTemplate = useCallback(() => {
    applyExpressionTemplateStore.openModal({
      expressionType,
      onFinish: onApply,
    })
  }, [expressionType, onApply])
  const handleClickSaveExpressionTemplate = useCallback(() => {
    saveExpressionTemplateStore.openModal({
      expression,
      expressionType,
    })
  }, [expression, expressionType])
  return (
    <div>
      <Button type="link" onClick={handleClickApplyExpressionTemplate}>
        应用模板
      </Button>
      <Button type="link" onClick={handleClickSaveExpressionTemplate}>
        保存为模板
      </Button>
    </div>
  )
}
