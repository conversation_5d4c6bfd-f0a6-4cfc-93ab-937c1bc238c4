import { LongLabelFormItem } from '@/components/long-label-form-item'
import { SpaceBetweenDiv } from '@/components/space-between-div'
import { BackJSExprEditor } from '@/pages/designer/components'
import {
  BACK_JS_MODEL_LABEL,
  BACK_TS_MODEL_LABEL,
  BIG_JS_LINK,
  DEFAULT_JS_BACK_MODEL_STR,
  DEFAULT_TS_BACK_MODEL_STR,
} from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import type { ExpressionTemplate } from '@/services/backend/models'
import {
  asyncBackModelJSToES5,
  asyncBackModelTSToJS,
  jsRule,
  tsRule,
  useMoneyRule,
} from '@/utils'
import {
  BACK_EXPRESSION_TYPE_JS_TS_OPTIONS,
  BackExpressionType,
} from '@ad/canal-shared'
import type { Model } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { Button, Form, Modal, Select } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import {
  useCallback,
  useEffect,
  useState,
  type FC,
  type ReactNode,
} from 'react'
import styled from 'styled-components'
import { ExpressionTemplateActions } from '../expression-template-actions'

/**
 * 编辑后端模型模态框按钮引用
 */
export interface EditBackModelModalBtnRef {
  /**
   * 打开
   */
  open(): void
}

/**
 * 编辑后端模型模态框按钮引用
 */
export let editBackModelModalBtnRef: EditBackModelModalBtnRef | null = null

/**
 * 编辑后端模型模态框按钮
 */
export const EditBackModelModalBtn: FC = observer(() => {
  const [form] = Form.useForm<EditBackModelFormValues>()
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const handleEditBtnClick = useCallback(() => {
    setIsModalVisible(true)
    const formValues: Partial<EditBackModelFormValues> = {
      exprType: BackExpressionType.JAVASCRIPT,
      jsExpr: DEFAULT_JS_BACK_MODEL_STR,
      tsExpr: DEFAULT_TS_BACK_MODEL_STR,
    }
    const backModel = schemaService?.schema?.backModel
    if (backModel) {
      if (backModel.codeES) {
        formValues.jsExpr = backModel.codeES
      } else if (backModel.codeTS) {
        formValues.exprType = BackExpressionType.TYPESCRIPT
        formValues.tsExpr = backModel.codeTS
      }
    }
    form.setFieldsValue(formValues)
  }, [form, schemaService])
  const handleResetBtnClick = useCallback(() => {
    schemaService.updateBackModel()
  }, [schemaService])
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const schema = schemaService.schema!
  const handleModalCancel = useCallback(() => {
    setIsModalVisible(false)
  }, [])
  const handleModalOk = useCallback(() => {
    form.submit()
  }, [form])
  const handleFormFinish = useCallback(async () => {
    const formValues = form.getFieldsValue()
    let backModel: Model
    if (formValues.exprType === BackExpressionType.JAVASCRIPT) {
      backModel = {
        code: await asyncBackModelJSToES5(formValues.jsExpr),
        codeES: formValues.jsExpr,
      }
    } else {
      backModel = {
        code: await asyncBackModelTSToJS(formValues.tsExpr),
        codeTS: formValues.tsExpr,
      }
    }
    schemaService.updateBackModel(backModel)
    setIsModalVisible(false)
  }, [form, schemaService])
  const handleApplyExpressionTemplate = useCallback(
    (tpl: ExpressionTemplate) => {
      form.setFieldValue(
        tpl.expressionType === BackExpressionType.JAVASCRIPT
          ? 'jsExpr'
          : 'tsExpr',
        tpl.expression,
      )
    },
    [form],
  )
  useEffect(() => {
    editBackModelModalBtnRef = {
      open(): void {
        handleEditBtnClick()
      },
    }
  }, [handleEditBtnClick])
  const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
  return (
    <>
      <Btn
        type={schema.backModel ? 'primary' : 'secondary'}
        onClick={handleEditBtnClick}
      >
        编辑
      </Btn>
      <Btn type="secondary" onClick={handleResetBtnClick}>
        重置
      </Btn>
      <Modal
        visible={isModalVisible}
        title="编辑后端模型"
        okText="提交"
        onCancel={handleModalCancel}
        onOk={handleModalOk}
        maskClosable={false}
        width={1000}
      >
        <Form form={form} onFinish={handleFormFinish}>
          <Form.Item
            label="模型类型"
            name="exprType"
            rules={[{ required: true }]}
          >
            <Select options={BACK_EXPRESSION_TYPE_JS_TS_OPTIONS} />
          </Form.Item>
          <Form.Item dependencies={['exprType', 'jsExpr', 'tsExpr']} noStyle>
            {({ getFieldValue }): ReactNode => (
              <>
                {getFieldValue('exprType') ===
                  BackExpressionType.JAVASCRIPT && (
                  <LongLabelFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>模型（{BACK_JS_MODEL_LABEL}）:</div>
                        <ExpressionTemplateActions
                          expression={getFieldValue('jsExpr')}
                          expressionType={BackExpressionType.JAVASCRIPT}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="jsExpr"
                    rules={[{ required: true }, jsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <BackJSExprEditor height="calc(100vh - 454px)" />
                  </LongLabelFormItem>
                )}
                {getFieldValue('exprType') ===
                  BackExpressionType.TYPESCRIPT && (
                  <LongLabelFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>模型（{BACK_TS_MODEL_LABEL}）:</div>
                        <ExpressionTemplateActions
                          expression={getFieldValue('tsExpr')}
                          expressionType={BackExpressionType.TYPESCRIPT}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="tsExpr"
                    rules={[{ required: true }, tsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <BackJSExprEditor
                      language="typescript"
                      height="calc(100vh - 454px)"
                    />
                  </LongLabelFormItem>
                )}
              </>
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
})

const Btn = styled(Button)`
  margin-right: 6px;
`

/**
 * 编辑后端模型表单值
 */
export interface EditBackModelFormValues {
  /**
   * 表达式类型
   */
  exprType: BackExpressionType
  /**
   * js 表达式
   */
  jsExpr: string
  /**
   * ts 表达式
   */
  tsExpr: string
}
