import { expressionTemplateStore } from '@/pages/designer/stores/expression-template-store'
import { UNI_EXPRESSION_TYPE_TEXT } from '@ad/canal-shared'
import { Form, Modal, Select } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, useEffect, type FC } from 'react'
import { applyExpressionTemplateStore } from './apply-expression-template-store'

/**
 * 应用表达式模板模态框
 */
export const ApplyExpressionTemplateModal: FC = observer(() => {
  const { isModalVisible, editingExpressionTemplateInfo } =
    applyExpressionTemplateStore
  const [form] = Form.useForm<ApplyExpressionTemplateModalFormValues>()
  const handleModalCancel = useCallback(() => {
    applyExpressionTemplateStore.cancelModal()
  }, [])
  const handleModalOk = useCallback(() => {
    form.submit()
  }, [form])
  const handleFormFinish = useCallback(() => {
    const formValues = form.getFieldsValue()
    applyExpressionTemplateStore.submitModal(formValues)
  }, [form])
  useEffect(() => {
    if (isModalVisible && editingExpressionTemplateInfo) {
      form.setFieldsValue({
        name: editingExpressionTemplateInfo.name,
      })
    } else {
      form.resetFields()
    }
  }, [editingExpressionTemplateInfo, form, isModalVisible])
  return (
    <Modal
      visible={isModalVisible}
      title="请选择需要应用的模板"
      okText="应用"
      zIndex={1001}
      onCancel={handleModalCancel}
      onOk={handleModalOk}
    >
      <Form form={form} onFinish={handleFormFinish}>
        <Form.Item label="类型">
          {editingExpressionTemplateInfo &&
            UNI_EXPRESSION_TYPE_TEXT[
              editingExpressionTemplateInfo.expressionType
            ]}
        </Form.Item>
        <Form.Item label="名称" name="name" rules={[{ required: true }]}>
          <Select
            options={
              editingExpressionTemplateInfo
                ? expressionTemplateStore.getNameOptions(
                    editingExpressionTemplateInfo.expressionType,
                  )
                : []
            }
            showSearch
          />
        </Form.Item>
      </Form>
    </Modal>
  )
})

/**
 * 保存表达式模板模态框表单值
 */
interface ApplyExpressionTemplateModalFormValues {
  /**
   * 名称
   */
  name: string
}
