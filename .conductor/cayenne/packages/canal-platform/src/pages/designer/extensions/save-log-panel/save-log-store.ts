import type { ModuleVersionLogFindBriefResDto } from '@/services/backend/models'
import { findModuleVersionLogBrief } from '@/services/backend/module-version-log'
import { last } from 'lodash'
import { makeObservable, observable, runInAction } from 'mobx'
import { DESIGNER_QUERY } from '../../constants'

/**
 * 保存记录仓库
 */
export class SaveLogStore {
  /**
   * 状态
   */
  private _states: ModuleVersionSaveLogState[] = []

  /**
   * 状态
   */
  public get states(): ModuleVersionSaveLogState[] {
    return this._states
  }

  public constructor() {
    makeObservable<SaveLogStore, '_states'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _states: observable,
    })
    this.loadCurrentVersionLogs()

    if (process.env.NODE_ENV === 'development') {
      Object.assign(window, {
        debugSaveLogStore: this,
      })
    }
  }

  /**
   * 加载当前版本日志
   */
  public loadCurrentVersionLogs(): Promise<void> {
    return this.loadLogs(+DESIGNER_QUERY.moduleVersion)
  }

  /**
   * 加载日志
   * @param moduleVersion 模块版本
   */
  public async loadLogs(moduleVersion: number): Promise<void> {
    let state: ModuleVersionSaveLogState | null = null
    const lastState = last(this._states)
    if (
      !lastState ||
      (lastState.moduleVersion !== moduleVersion &&
        lastState.checkoutModuleVersion === moduleVersion)
    ) {
      runInAction(() => {
        this._states.push({
          moduleId: DESIGNER_QUERY.moduleId,
          moduleVersion,
          loading: true,
          logs: [],
          checkoutModuleVersion: null,
        })
      })
      state = last(this._states) || null
    } else {
      const s = this._states.find((ss) => ss.moduleVersion === moduleVersion)
      if (s) {
        state = s
        runInAction(() => {
          s.loading = true
        })
      }
    }
    if (!state) return
    const res = await findModuleVersionLogBrief({
      moduleId: DESIGNER_QUERY.moduleId,
      moduleVersion,
    })
    runInAction(() => {
      state.loading = false
      state.logs = res.data.logs
      state.checkoutModuleVersion = res.data.checkoutModuleVersion
    })
  }
}

/**
 * 模块版本保存记录状态
 */
export interface ModuleVersionSaveLogState
  extends Omit<ModuleVersionLogFindBriefResDto, 'checkoutModuleVersion'> {
  /**
   * 模块 id
   */
  moduleId: string
  /**
   * 模块版本
   */
  moduleVersion: number
  /**
   * 加载中
   */
  loading: boolean
  /**
   * 签出模块版本
   */
  checkoutModuleVersion: null | number
}

/**
 * 保存记录仓库：单例
 */
export const saveLogStore = new SaveLogStore()
