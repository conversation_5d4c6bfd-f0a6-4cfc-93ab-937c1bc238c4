import { ControlLabel } from '@/components/control-label'
import type { JSONRuleRequired, ValueOnChangeProps } from '@ad/canal-shared-ui'
import { Input } from '@m-ui/react'
import { useCallback, type ChangeEvent, type FC } from 'react'

/**
 * JSON 规则：必填，编辑器属性
 */
export interface JSONRuleRequiredEditorProps
  extends Required<ValueOnChangeProps<JSONRuleRequired>> {}

/**
 * JSON 规则：必填，编辑器
 */
export const JSONRuleRequiredEditor: FC<JSONRuleRequiredEditorProps> = ({
  value,
  onChange,
}) => {
  const handleErrMsgChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange({
        ...value,
        errMsg: e.target.value,
      })
    },
    [onChange, value],
  )
  return (
    <div>
      <ControlLabel>错误提示：</ControlLabel>
      <Input size="small" value={value.errMsg} onChange={handleErrMsgChange} />
    </div>
  )
}
