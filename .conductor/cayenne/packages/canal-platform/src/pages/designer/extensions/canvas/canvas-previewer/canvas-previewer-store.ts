import { DESIGNER_QUERY, PREVIEWER_URL } from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import type {
  DesignerForPreviewerRpcApis,
  PreviewerForDesignerRpcApis,
} from '@/pages/previewer/types'
import { getPreviewSettingKey } from '@/utils'
import type {
  DesignerForLocalPreviewerRpcApis,
  LocalPreviewerForDesignerRpcApis,
} from '@ad/canal-react-runtime'
import { createQuickEncoder } from '@ad/canal-shared'
import type { E2EServerSchema } from '@ad/e2e-schema'
import { f2bE2ESchema } from '@ad/e2e-schema-utils'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { type MaybePromiseApis } from '@kael/shared'
import { createEndpoint, fromIframe, type Endpoint } from '@remote-ui/rpc'
import { cloneDeep, get, mapValues, once } from 'lodash'
import { action, makeObservable, observable, toJS } from 'mobx'
import { canvasPreviewerIframe } from '.'
import { DEFAULT_PREVIEW_SETTING, type PreviewSetting } from './preview-setting'

/**
 * 画布预览器仓库
 */
export class CanvasPreviewerStore {
  /**
   * 内部 RPC 终端
   */
  private readonly _innerRpcEndpoint: Endpoint<PreviewerForDesignerRpcApis> | null =
    null

  /**
   * 本地 RPC 终端
   */
  private readonly _localRpcEndpoint: Endpoint<LocalPreviewerForDesignerRpcApis> | null =
    null

  /**
   * 正在预览
   */
  private _isPreviewing = false

  /**
   * 正在预览
   */
  public get isPreviewing(): typeof this._isPreviewing {
    return this._isPreviewing
  }

  /**
   * 预览配置可见
   */
  private _isPreviewSettingVisible = false

  /**
   * 预览配置可见
   */
  public get isPreviewSettingVisible(): typeof this._isPreviewSettingVisible {
    return this._isPreviewSettingVisible
  }

  /**
   * 预览器已卸载
   */
  private _isPreviewerUnloaded = false

  public constructor() {
    makeObservable<
      this,
      '_isPreviewing' | '_isPreviewSettingVisible' | '_syncSchema'
    >(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isPreviewing: observable,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isPreviewSettingVisible: observable,
      startPreview: action,
      stopPreview: action,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _syncSchema: action,
      startEditPreviewSetting: action,
      finishEditPreviewSetting: action,
    })

    if (DESIGNER_QUERY.localPreviewer) {
      this._localRpcEndpoint = createEndpoint<LocalPreviewerForDesignerRpcApis>(
        fromIframe(canvasPreviewerIframe),
        { createEncoder: createQuickEncoder },
      )
      const apis: MaybePromiseApis<DesignerForLocalPreviewerRpcApis> = {
        getRawSchemaFromDesigner: this._getRawSchemaFromDesigner.bind(this),
      }
      this._localRpcEndpoint.expose(apis)
    } else {
      this._innerRpcEndpoint = createEndpoint<PreviewerForDesignerRpcApis>(
        fromIframe(canvasPreviewerIframe),
        { createEncoder: createQuickEncoder },
      )
      const apis: MaybePromiseApis<DesignerForPreviewerRpcApis> = {
        beforeUnload: this._beforeUnload.bind(this),
      }
      this._innerRpcEndpoint.expose(apis)
    }
  }

  /**
   * 通过元素调整预览器 iframe
   */
  public adjustPreviewerIframeByElement(): void {
    const elCanvas = document.getElementById('canvas')
    const elCanvasIframeWrapper = document.getElementById(
      'canvas-iframe-wrapper',
    )
    if (!elCanvas || !elCanvasIframeWrapper) return
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (
          entry.target !== elCanvas &&
          entry.target !== elCanvasIframeWrapper
        ) {
          continue
        }
        const rectCanvas = elCanvas.getBoundingClientRect()
        const rectCanvasIframeWrapper =
          elCanvasIframeWrapper.getBoundingClientRect()
        canvasPreviewerIframe.style.top = `${rectCanvas.top}px`
        canvasPreviewerIframe.style.right = `${rectCanvas.right}px`
        canvasPreviewerIframe.style.bottom = `${rectCanvas.bottom}px`
        canvasPreviewerIframe.style.left = `${rectCanvas.left}px`
        canvasPreviewerIframe.style.width = `${rectCanvas.width}px`
        canvasPreviewerIframe.style.height = `${rectCanvas.height}px`
        canvasPreviewerIframe.style.padding = `0 ${
          (rectCanvas.width - rectCanvasIframeWrapper.width) / 2
        }px`
      }
    })
    resizeObserver.observe(elCanvas)
    resizeObserver.observe(elCanvasIframeWrapper)
  }

  /**
   * 开始预览
   */
  public startPreview(): void {
    this._isPreviewing = true
    canvasPreviewerIframe.style.display = 'block'
    if (DESIGNER_QUERY.localPreviewer) {
      this._loadLocalPreviewer()
    } else {
      this._syncSchema()
    }
  }

  /**
   * 停止预览
   */
  public stopPreview(): void {
    this._isPreviewing = false
    canvasPreviewerIframe.style.display = 'none'
    if (DESIGNER_QUERY.localPreviewer) {
      // 换一个空白页面
      canvasPreviewerIframe.src = PREVIEWER_URL
    } else {
      if (this._isPreviewerUnloaded) {
        this._isPreviewerUnloaded = false
        canvasPreviewerIframe.src = PREVIEWER_URL
        this._innerRpcEndpoint?.replace(fromIframe(canvasPreviewerIframe))
      }
    }
  }

  /**
   * 切换预览
   */
  public togglePreview(): void {
    if (this._isPreviewing) {
      this.stopPreview()
    } else {
      this.startPreview()
    }
  }

  /**
   * 预览器卸载前
   */
  private _beforeUnload(): void {
    this._isPreviewerUnloaded = true
  }

  /**
   * 从设计器获取原始 Schema 文件
   */
  private async _getRawSchemaFromDesigner(): Promise<E2EServerSchema> {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return this._getCurrentServerSchema()!
  }

  /**
   * 加载本地预览器
   */
  private _loadLocalPreviewer(): void {
    canvasPreviewerIframe.src = this._getLocalPreviewerUrl()
    this._localRpcEndpoint?.replace(fromIframe(canvasPreviewerIframe))
  }

  /**
   * 同步 Schema 文件
   */
  private _syncSchema(): void {
    const serverSchema = this._getCurrentServerSchema()
    if (!serverSchema) return
    this._innerRpcEndpoint?.call.setServerSchema(
      serverSchema,
      this.getPreviewSetting(),
    )
  }

  /**
   * 获取当前端到端 Schema 文件
   */
  private _getCurrentServerSchema(): E2EServerSchema | null {
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    const schema = toJS(schemaService.schema)
    return schema && f2bE2ESchema(schema)
  }

  /**
   * 开始编辑预览配置
   */
  public startEditPreviewSetting(): void {
    this._isPreviewSettingVisible = true
  }

  /**
   * 完成编辑预览配置
   */
  public finishEditPreviewSetting(s?: PreviewSetting): void {
    this._isPreviewSettingVisible = false
    if (s) {
      this._setPreviewSetting(s)
    }
  }

  /**
   * 获取预览配置
   */
  public getPreviewSetting(): PreviewSetting {
    let s = cloneDeep(DEFAULT_PREVIEW_SETTING)
    const item =
      localStorage.getItem(
        getPreviewSettingKey(
          DESIGNER_QUERY.moduleId,
          DESIGNER_QUERY.moduleVersion,
        ),
      ) ||
      localStorage.getItem(getPreviewSettingKey(DESIGNER_QUERY.moduleId, '*'))
    if (item) {
      try {
        const itemJson = JSON.parse(item)
        s = {
          ...s,
          ...itemJson,
          headers: mapValues(s.headers, (hs, env) => ({
            ...hs,
            ...get(itemJson, ['headers', env]),
          })),
        }
      } catch (err) {
        console.log('getPreviewSetting err', err)
      }
    }
    return s
  }

  /**
   * 设置预览配置
   * @param s 预览配置
   */
  private _setPreviewSetting(s: PreviewSetting): void {
    const str = JSON.stringify(s)
    localStorage.setItem(
      getPreviewSettingKey(
        DESIGNER_QUERY.moduleId,
        DESIGNER_QUERY.moduleVersion,
      ),
      str,
    )
    localStorage.setItem(
      getPreviewSettingKey(DESIGNER_QUERY.moduleId, '*'),
      str,
    )
  }

  /**
   * 获取本地预览器 URL，添加 __canalModuleId
   */
  private _getLocalPreviewerUrl = once(() => {
    const { localPreviewer, moduleId } = DESIGNER_QUERY
    if (!localPreviewer) return ''
    const url = new URL(localPreviewer)
    url.searchParams.set('__canalModuleId', moduleId)
    return url.toString()
  })
}

/**
 * 单例：画布预览器仓库
 */
export const canvasPreviewerStore = new CanvasPreviewerStore()
