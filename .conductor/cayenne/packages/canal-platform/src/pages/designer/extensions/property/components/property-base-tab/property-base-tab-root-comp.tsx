import KoncallHelp from '@/components/koncall-help'
import { QuestionTip } from '@/components/question-tip'
import {
  DEFAULT_RES_DEFAULT_VALUE_JS_EXPR_STR,
  DEFAULT_RES_DEFAULT_VALUE_TS_EXPR_STR,
} from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import type { E2ESchema } from '@ad/e2e-schema'
import { CanalRootComponentField } from '@ad/e2e-schema-utils'
import { PropertyCollapse } from '@kael/designer-property'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { getComponentPropExpression } from '@kael/schema-utils'
import { Button } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, useMemo, type FC } from 'react'
import { useUnmount } from 'react-use'
import { apiDetailStore } from '../api-detail-modal'
import { bindIIFEStore } from '../bind-iife-drawer'
import { bindPropStore } from '../bind-prop-drawer'
import { Panel, PropertyBaseTabAPIList } from './common'
import { EditBackModelModalBtn } from './edit-back-model-modal-btn'
import { EditFrontModelModalBtn } from './edit-front-model-modal-btn'
import { TrackListEntry } from './track-list-entry'

/**
 * 属性基础面板，根组件
 */
export const PropertyBaseTabRootComp: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const schema = schemaService.schema!
  const handleBindIIFEBtnClick = useCallback(() => {
    bindIIFEStore.openDrawer()
  }, [])
  const activeTabKeys = useMemo(
    () => ['接口列表', '埋点列表', '前端动作', '数据', '前端模型', '后端模型'],
    [],
  )
  const comp = schema.view
  const handleBindBackDataBtnClick = useCallback(() => {
    if (!comp) {
      return
    }
    bindPropStore.openDrawer({
      componentId: comp.id,
      propPath: [CanalRootComponentField.BACK_DATA],
      propName: '数据',
      frontExprEnabled: false,
      defaultBackJsExpr: DEFAULT_RES_DEFAULT_VALUE_JS_EXPR_STR,
      defaultBackTsExpr: DEFAULT_RES_DEFAULT_VALUE_TS_EXPR_STR,
    })
  }, [comp])
  useUnmount(() => {
    apiDetailStore.cancelModal()
  })
  const backDataExpr = getComponentPropExpression<E2ESchema>(comp, [
    CanalRootComponentField.BACK_DATA,
  ])
  return (
    <PropertyCollapse defaultActiveKey={activeTabKeys}>
      <Panel
        key="接口列表"
        header={
          <>
            模块级接口列表
            <KoncallHelp question="模块级数据源" />
          </>
        }
      >
        <PropertyBaseTabAPIList />
      </Panel>
      <Panel
        key="埋点列表"
        header={
          <>
            埋点列表
            <KoncallHelp question="埋点" />
          </>
        }
      >
        <TrackListEntry />
      </Panel>
      <Panel
        key="前端动作"
        header={
          <QuestionTip
            text="前端动作"
            tip="通过绑定表达式指定模块本次刷新内容下发到前端后，前端运行时需要执行的动作"
          />
        }
      >
        <Button
          type={schema.iife ? 'primary' : 'secondary'}
          onClick={handleBindIIFEBtnClick}
        >
          {schema.iife ? '已绑定' : '绑定'}
        </Button>
      </Panel>
      <Panel
        key="数据"
        header={
          <QuestionTip
            text="数据"
            tip="表达式返回的非 null 的值，会按键值覆盖前端运行时里的数据"
          />
        }
      >
        <Button
          type={backDataExpr.type === 'api' ? 'primary' : 'secondary'}
          onClick={handleBindBackDataBtnClick}
        >
          {backDataExpr.type === 'api' ? '已绑定' : '绑定'}
        </Button>
      </Panel>
      <Panel
        key="前端模型"
        header={
          <QuestionTip
            text="前端模型"
            tip="全局的前端模型对象，可以用于收拢一些模块内能够复用的数据和函数，只能在前端表达式里通过 ctx.model 访问到"
          />
        }
      >
        <EditFrontModelModalBtn />
      </Panel>
      <Panel
        key="后端模型"
        header={
          <QuestionTip
            text="后端模型"
            tip="全局的后端模型对象，可以用于收拢一些模块内能够复用的数据和函数，只能在后端表达式里通过 ctx.model 访问到"
          />
        }
      >
        <EditBackModelModalBtn />
      </Panel>
    </PropertyCollapse>
  )
})
