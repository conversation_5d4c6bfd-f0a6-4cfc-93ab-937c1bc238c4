import { isMacOS } from '@ad/canal-shared-ui'

/**
 * 热键类型
 */
export enum HotkeyType {
  /**
   * 预览
   */
  PREVIEW = 'PREVIEW',
  /**
   * 撤销
   */
  UNDO = 'UNDO',
  /**
   * 重做
   */
  REDO = 'REDO',
  /**
   * 保存
   */
  SAVE = 'SAVE',
  /**
   * 折叠
   */
  COLLAPSE = 'COLLAPSE',
  /**
   * 恢复
   */
  RESUME = 'RESUME',
  /**
   * 合并保存
   */
  MERGE_SAVE = 'MERGE_SAVE',
}

/**
 * 热键里的 CMD
 */
const HOTKEY_CMD = isMacOS ? 'command' : 'ctrl'

/**
 * 热键对照表
 */
export const HOTKEY_MAP = {
  [HotkeyType.PREVIEW]: `${HOTKEY_CMD}+p`,
  [HotkeyType.UNDO]: `${HOTKEY_CMD}+z`,
  [HotkeyType.REDO]: isMacOS ? `${HOTKEY_CMD}+shift+z` : `${HOTKEY_CMD}+y`,
  [HotkeyType.SAVE]: `${HOTKEY_CMD}+s`,
  [HotkeyType.COLLAPSE]: `${HOTKEY_CMD}+m`,
} as const

/**
 * 热键名称
 */
export const HOTKEY_NAME = {
  [HotkeyType.PREVIEW]: `预览`,
  [HotkeyType.UNDO]: `撤销`,
  [HotkeyType.REDO]: `重做`,
  [HotkeyType.SAVE]: `保存`,
  [HotkeyType.COLLAPSE]: `折叠`,
} as const

/**
 * 热键里的 CMD 展示
 */
const HOTKEY_CMD_DISPLAY = isMacOS ? '⌘' : 'Ctrl'

/**
 * 热键里的 Shift 展示
 */
const HOTKEY_SHIFT_DISPLAY = isMacOS ? '⇧' : 'Shift'

/**
 * 热键展示
 */
export const HOTKEY_DISPLAY = {
  [HotkeyType.PREVIEW]: `${HOTKEY_CMD_DISPLAY}+P`,
  [HotkeyType.UNDO]: `${HOTKEY_CMD_DISPLAY}+Z`,
  [HotkeyType.REDO]: isMacOS
    ? `${HOTKEY_CMD_DISPLAY}+${HOTKEY_SHIFT_DISPLAY}+Z`
    : `${HOTKEY_CMD_DISPLAY}+Y`,
  [HotkeyType.SAVE]: `${HOTKEY_CMD_DISPLAY}+S`,
} as const
