import { ControlLabel } from '@/components/control-label'
import { TextAreaJsonEditorWithFormItem } from '@/components/textarea-editor'
import type { E2ESchemaExpressionRefresh } from '@ad/e2e-schema'
import { useCallback, useMemo, type FC } from 'react'
import type { ActionEditorCommonProps } from './types'

/**
 * 动作：提交刷新编辑器属性
 */
export interface ActionRefreshSubmitEditorProps
  extends ActionEditorCommonProps<E2ESchemaExpressionRefresh> {}

/**
 * 动作：提交刷新编辑器
 */
export const ActionRefreshSubmitEditor: FC<ActionRefreshSubmitEditorProps> = ({
  value,
  onChange,
}) => {
  const params = useMemo(() => value.params || {}, [value.params])
  const handleParamsChange = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (v: any) => {
      onChange({
        ...value,
        params: v,
      })
    },
    [onChange, value],
  )
  return (
    <div>
      <div>
        <ControlLabel>刷新参数：</ControlLabel>
        <TextAreaJsonEditorWithFormItem
          size="small"
          value={params}
          onChange={handleParamsChange}
        />
      </div>
    </div>
  )
}
