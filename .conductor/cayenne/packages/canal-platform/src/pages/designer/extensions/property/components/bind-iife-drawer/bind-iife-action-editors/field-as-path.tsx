import { IconCheckbox } from '@/components/icon-checkbox'
import type { ValueOnChangeProps } from '@ad/canal-shared-ui'
import { ShareAssemblylineLine } from '@m-ui/icons'
import { Tooltip } from '@m-ui/react'
import type { FC } from 'react'

/**
 * 字段作为路径属性
 */
export interface FieldAsPathProps
  extends Required<ValueOnChangeProps<boolean>> {}

/**
 * 字段作为路径
 */
export const FieldAsPath: FC<FieldAsPathProps> = ({ value, onChange }) => {
  return (
    <Tooltip
      title={
        <div>
          <div>{value ? '取消' : '启用'}</div>
          作为键值，从代码里的 output 里获取
        </div>
      }
    >
      <IconCheckbox value={value} onChange={onChange}>
        <ShareAssemblylineLine />
      </IconCheckbox>
    </Tooltip>
  )
}
