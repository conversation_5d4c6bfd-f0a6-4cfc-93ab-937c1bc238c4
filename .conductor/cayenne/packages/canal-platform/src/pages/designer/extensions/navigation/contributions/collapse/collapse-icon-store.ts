import { getSkeleton } from '@kael/designer-core'
import { action, makeObservable, observable } from 'mobx'

/**
 * 折叠图标仓库
 */
export class CollapseIconStore {
  /**
   * 已折叠
   */
  private _isCollapsed = false

  /**
   * 已折叠
   */
  public get isCollapsed(): boolean {
    return this._isCollapsed
  }

  public constructor() {
    makeObservable<this, '_isCollapsed'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _isCollapsed: observable,
      toggle: action,
    })
  }

  /**
   * 切换
   */
  public toggle(): void {
    const skeleton = getSkeleton()
    if (skeleton.rightPanelHandler?.container.isHidden) {
      skeleton.expandPanel('right')
      skeleton.expandPanel('left')
      this._isCollapsed = false
    } else {
      skeleton.collapsePanel('right')
      skeleton.collapsePanel('left')
      this._isCollapsed = true
    }
  }
}

/**
 * 单例：折叠图标仓库
 */
export const collapseIconStore = new CollapseIconStore()
