import { IconButton } from '@/components/icon-button'
import type {
  JSONRule,
  JSONRuleType,
  ValueOnChangeProps,
} from '@ad/canal-shared-ui'
import type { SetterRenderProps } from '@kael/designer-property'
import { SystemDeleteLine } from '@m-ui/icons'
import { <PERSON><PERSON>, Toolt<PERSON> } from '@m-ui/react'
import { cloneDeep } from 'lodash'
import { useCallback, useMemo, type FC } from 'react'
import { cacheSetterValue } from '../../hocs'
import {
  SetterList,
  SetterListItem,
  SetterListItemHeader,
  SetterListTypeSelect,
} from '../common'
import { JSONRuleLengthEditor } from './json-rule-length-editor'
import { JSONRuleNumberEditor } from './json-rule-number-editor'
import { JSONRuleRegExpEditor } from './json-rule-reg-exp-editor'
import { JSONRuleRequiredEditor } from './json-rule-required-editor'
import {
  JSONRuleSpecialStringEditor,
  getErrMsgBySpecialStringType,
} from './json-rule-special-string-editor'

/**
 * JSON 规则设置器属性
 */
export interface JSONRuleSetterProps {}

/**
 * JSON 规则设置器
 */
export const JSONRuleSetter: FC<SetterRenderProps<JSONRuleSetterProps>> =
  cacheSetterValue(({ value: rawValue, onChange }) => {
    const rules: JSONRule[] = useMemo(() => rawValue || [], [rawValue])
    const handleAddRuleBtnClick = useCallback(() => {
      const rs: JSONRule[] = [
        ...rules,
        cloneDeep(DEFAULT_JSON_RULE_BY_TYPE.required),
      ]
      onChange(rs)
    }, [onChange, rules])
    const handleDeleteRuleBtnClick = useCallback(
      (idx: number) => {
        const rs: JSONRule[] = [...rules]
        rs.splice(idx, 1)
        onChange(rs)
      },
      [onChange, rules],
    )
    const handleRuleTypeChange = useCallback(
      (idx: number, type: JSONRuleType) => {
        const rs: JSONRule[] = [...rules]
        rs[idx] = cloneDeep(DEFAULT_JSON_RULE_BY_TYPE[type])
        onChange(rs)
      },
      [onChange, rules],
    )
    const handleRuleChange = useCallback(
      (idx: number, r: JSONRule) => {
        const rs: JSONRule[] = [...rules]
        rs[idx] = r
        onChange(rs)
      },
      [onChange, rules],
    )
    return (
      <SetterList>
        <Button type="primary" size="small" onClick={handleAddRuleBtnClick}>
          新增校验项
        </Button>
        {rules.map((rule, idx) => {
          const Editor = JSON_RULE_EDITOR_BY_TYPE[rule.type]
          return (
            <SetterListItem key={idx}>
              <SetterListItemHeader>
                <b>校验项 {idx}</b>
                <Tooltip title="删除" placement="left">
                  <IconButton
                    onClick={handleDeleteRuleBtnClick.bind(null, idx)}
                  >
                    <SystemDeleteLine />
                  </IconButton>
                </Tooltip>
              </SetterListItemHeader>
              <SetterListTypeSelect
                size="small"
                options={JSON_RULE_TYPE_OPTIONS}
                value={rule.type}
                onChange={handleRuleTypeChange.bind(null, idx)}
              />
              <Editor
                value={rule}
                onChange={handleRuleChange.bind(null, idx)}
              />
            </SetterListItem>
          )
        })}
      </SetterList>
    )
  })

/**
 * 按类型的默认 JSON 规则
 */
const DEFAULT_JSON_RULE_BY_TYPE: Record<JSONRuleType, JSONRule> = {
  required: {
    type: 'required',
    errMsg: '必填',
  },
  number: {
    type: 'number',
  },
  length: {
    type: 'length',
  },
  ['special-string']: {
    type: 'special-string',
    errMsg: getErrMsgBySpecialStringType('mail'),
    strType: 'mail',
  },
  ['reg-exp']: {
    type: 'reg-exp',
    errMsg: '不符合要求',
    expr: '',
  },
}

/**
 * JSON 规则类型选项
 */
const JSON_RULE_TYPE_OPTIONS = [
  {
    label: '必填',
    value: 'required',
  },
  {
    label: '数字',
    value: 'number',
  },
  {
    label: '长度',
    value: 'length',
  },
  {
    label: '特殊字符串',
    value: 'special-string',
  },
  {
    label: '正则表达式',
    value: 'reg-exp',
  },
]

/**
 * 按类型的 JSON 规则编辑器
 */
const JSON_RULE_EDITOR_BY_TYPE = {
  required: JSONRuleRequiredEditor,
  number: JSONRuleNumberEditor,
  length: JSONRuleLengthEditor,
  ['special-string']: JSONRuleSpecialStringEditor,
  ['reg-exp']: JSONRuleRegExpEditor,
} as Record<JSONRuleType, FC<Required<ValueOnChangeProps<JSONRule>>>>
