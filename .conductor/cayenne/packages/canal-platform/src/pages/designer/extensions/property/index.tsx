import {
  DOMAIN_CODE_AD_ESP_CREATE,
  DOMAIN_CODE_AD_ESP_MOBILE_CREATE,
} from '@/constants/domain-codes'
import { init, registerService } from '@kael/designer-property'
import { DESIGNER_QUERY } from '../../constants'
import { CanalPropertyService } from '../../services'
import { PropertyLabel, PropertyTitle } from './components'
import {
  BASE_TAB_KEY,
  PropertyBindActionContribution,
  PropertyCategoryActionBase,
} from './contributions'
import {
  ActionSetter,
  DynamicEnumSetter,
  EdgeShorthandSetter,
  JSONRuleSetter,
  StringSetter,
} from './setters'

export * from './components'

/**
 * 初始化属性面板
 */
export function initProperty(): void {
  init({
    layout: 'horizental',
    validators: {},
    stylePanelProps: { showModuleList: [] },
    setters: {
      EdgeShorthand: EdgeShorthandSetter,
      Action: ActionSetter,
      JSONRule: JSONRuleSetter,
      DynamicEnum: DynamicEnumSetter,

      // 覆盖，因为 kael 内部是小写开头，所以这里需要保持一致
      string: StringSetter,
    },
    titleRender() {
      return <PropertyTitle />
    },
    labelRender(props) {
      return <PropertyLabel {...props} />
    },
    getDefaultTabActiveKey: [
      DOMAIN_CODE_AD_ESP_CREATE,
      DOMAIN_CODE_AD_ESP_MOBILE_CREATE,
    ].includes(DESIGNER_QUERY.domainCode)
      ? undefined
      : (): string => BASE_TAB_KEY,
    contributions: [PropertyBindActionContribution, PropertyCategoryActionBase],
  })
  registerService(CanalPropertyService)
}
