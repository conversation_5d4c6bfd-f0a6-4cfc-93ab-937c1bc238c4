import {
  AbstractViewContribution,
  type ApplicationContribution,
} from '@kael/designer-core'
import { injectable, register } from '@kael/di'
import { SettingPanelWidget } from './widget'

/**
 * 设置面板视图贡献点
 */
@injectable()
export class SettingPanelViewContribution
  extends AbstractViewContribution<SettingPanelWidget>
  implements ApplicationContribution
{
  public constructor() {
    super({
      widgetId: SettingPanelWidget.ID,
      widgetName: SettingPanelWidget.LABEL,
      widgetOptions: { area: 'left' },
    })
  }

  public async initializeLayout(): Promise<void> {
    await this.openView()
  }
}

/**
 * 注册设置面板视图贡献点
 */
export function registerSettingPanelViewContribution(): void {
  register({
    token: SettingPanelViewContribution.token,
    useClass: SettingPanelViewContribution,
  })
}
