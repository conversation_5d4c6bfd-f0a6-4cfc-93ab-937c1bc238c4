import { I<PERSON><PERSON><PERSON>on } from '@/components/icon-button'
import { InlineMarkdownPreview } from '@/components/inline-markdown-preview'
import KoncallHelp from '@/components/koncall-help'
import { LongTooltip } from '@/components/long-tooltip'
import {
  FrontJSExprEditorModalBtn,
  type FrontJSExprEditorModalBtnRef,
} from '@/pages/designer/components'
import {
  DEFAULT_COMPONENT_EFFECT_JS_EXPR_STR,
  DEFAULT_COMPONENT_EFFECT_TS_EXPR_STR,
  DEFAULT_RES_DEFAULT_VALUE_JS_EXPR_STR,
  DEFAULT_RES_DEFAULT_VALUE_TS_EXPR_STR,
} from '@/pages/designer/constants'
import type {
  CanalMaterialService,
  E2ESchemaService,
} from '@/pages/designer/services'
import { getSelectedSimplifyComponent } from '@/pages/designer/tools'
import type { FrontExpressionScene } from '@ad/canal-ai'
import { useLatestFn } from '@ad/canal-shared-ui'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import {
  INTERNAL_COMPONENT_LIB_NAME,
  LOCAL_COMPONENT_LIB_NAME,
} from '@ad/e2e-material-schema-utils'
import type { E2ESchema, E2ESchemaExpressionJS } from '@ad/e2e-schema'
import { PropertyCollapse, getPropertyService } from '@kael/designer-property'
import {
  MaterialServiceSymbol,
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import {
  decodeComponentType,
  getComponentPropExpression,
} from '@kael/schema-utils'
import { SystemArrowDoubleUpLine } from '@m-ui/icons'
import { Button, Checkbox, Popconfirm, Tooltip, Typography } from '@m-ui/react'
import { get, xor } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useCallback, useMemo, type FC } from 'react'
import { useUnmount } from 'react-use'
import styled from 'styled-components'
import { apiDetailStore } from '../api-detail-modal'
import { bindPropStore } from '../bind-prop-drawer'
import { EditableComponentId } from '../editable-component-id'
import {
  Content,
  HeaderWithIcon,
  Item,
  Label,
  LatestVersionTip,
  Panel,
  PropertyBaseTabAPIList,
  getInternalComponentVersion,
} from './common'

/**
 * 其他组件副作用编辑器引用
 */
export let otherComponentEffectEditorRef: FrontJSExprEditorModalBtnRef | null =
  null

/**
 * 属性基础面板，其他组件
 */
export const PropertyBaseTabOtherComp: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const stateService = getInject<StateService>(StateServiceSymbol)
  const canalMaterialService = getInject<CanalMaterialService>(
    MaterialServiceSymbol,
  )
  const propertyService = getPropertyService()
  const materialSchema: E2ERemoteComponentMaterialSchema =
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    propertyService.materialSchema!
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const comp = schemaService.findComponentById(
    stateService.selectedComponentId,
  )!
  const latestMaterialSchema: E2ERemoteComponentMaterialSchema | null =
    (comp &&
      canalMaterialService.getComponentSchemaByType(comp.type, 'latest')) ||
    null
  const [libName] = decodeComponentType(comp.type)
  const currentVersion =
    schemaService.schema?.componentCodes?.[comp.type]?.version
  const activeTabKeys = useMemo(
    () => [
      '组件实例信息',
      '组件物料信息',
      '接口列表',
      '组件值',
      '副作用',
      '联动',
    ],
    [],
  )
  const handleUpgradeBtnClick = useCallback(() => {
    schemaService.upgrateComponentCode(comp.type)
  }, [comp.type, schemaService])
  const handleBindComponentValueBtnClick = useCallback(() => {
    if (!comp) {
      return
    }
    bindPropStore.openDrawer({
      componentId: comp.id,
      propPath: ['value'],
      propName: '值',
      frontExprEnabled: false,
      defaultBackJsExpr: DEFAULT_RES_DEFAULT_VALUE_JS_EXPR_STR,
      defaultBackTsExpr: DEFAULT_RES_DEFAULT_VALUE_TS_EXPR_STR,
    })
  }, [comp])
  useUnmount(() => {
    apiDetailStore.cancelModal()
  })
  const effect = schemaService.getComponentEffect(comp.id)
  const handleEffectChange = useCallback(
    (eff?: E2ESchemaExpressionJS) => {
      schemaService.updateComponentEffect(comp.id, eff)
    },
    [comp.id, schemaService],
  )
  const handleEffectGetScene = useLatestFn((): FrontExpressionScene | null => {
    const simplifyComponent = getSelectedSimplifyComponent()
    if (!simplifyComponent) return null
    return {
      type: 'componentValueEffect',
      component: simplifyComponent,
    }
  })
  const handleEffectRef = useCallback(
    (ref: FrontJSExprEditorModalBtnRef | null) => {
      otherComponentEffectEditorRef = ref
    },
    [],
  )
  //#region 联动
  const linkage = schemaService.schema?.linkage
  const isAutoRefresh =
    linkage?.autoRefreshByComponent?.includes(comp.id) || false
  const handleAutoRefreshChange = useCallback(() => {
    schemaService.updatePartialLinkage({
      autoRefreshByComponent: xor(linkage?.autoRefreshByComponent, [comp.id]),
    })
  }, [comp.id, linkage?.autoRefreshByComponent, schemaService])
  const isComponentDataByAuto =
    linkage?.componentDataParams?.byRefreshType?.auto?.includes(comp.id) ||
    false
  const handleComponentDataByAutoChange = useCallback(() => {
    schemaService.updatePartialLinkage({
      componentDataParams: {
        byRefreshType: {
          auto: xor(linkage?.componentDataParams?.byRefreshType?.auto, [
            comp.id,
          ]),
        },
      },
    })
  }, [
    comp.id,
    linkage?.componentDataParams?.byRefreshType?.auto,
    schemaService,
  ])
  const isComponentDataBySubmit =
    linkage?.componentDataParams?.byRefreshType?.submit?.includes(comp.id) ||
    false
  const handleComponentDataBySubmitChange = useCallback(() => {
    schemaService.updatePartialLinkage({
      componentDataParams: {
        byRefreshType: {
          submit: xor(linkage?.componentDataParams?.byRefreshType?.submit, [
            comp.id,
          ]),
        },
      },
    })
  }, [
    comp.id,
    linkage?.componentDataParams?.byRefreshType?.submit,
    schemaService,
  ])
  const byRefreshTypeOutsideParams =
    linkage?.componentDataParams?.byRefreshType?.['outside-params']
  const isComponentDataByOutsideParams =
    byRefreshTypeOutsideParams?.includes(comp.id) || false
  const handleComponentDataByOutsideParamsChange = useCallback(() => {
    schemaService.updatePartialLinkage({
      componentDataParams: {
        byRefreshType: {
          ['outside-params']: xor(byRefreshTypeOutsideParams, [comp.id]),
        },
      },
    })
  }, [byRefreshTypeOutsideParams, comp.id, schemaService])
  const isComponentDataByAll =
    linkage?.componentDataParams?.common?.includes(comp.id) || false
  const handleComponentDataByAllChange = useCallback(() => {
    schemaService.updatePartialLinkage({
      componentDataParams: {
        common: xor(linkage?.componentDataParams?.common, [comp.id]),
      },
    })
  }, [comp.id, linkage?.componentDataParams?.common, schemaService])
  //#endregion
  const valuePropTip = canalMaterialService.getValueTipByComponentType(
    comp.type,
  )

  const valueExpr = getComponentPropExpression<E2ESchema>(comp, ['value'])
  return (
    <PropertyCollapse defaultActiveKey={activeTabKeys}>
      <Panel key="组件实例信息" header="组件实例信息">
        <Item>
          <Label>组件 ID</Label>
          <Content>
            <EditableComponentId />
          </Content>
        </Item>
      </Panel>
      <Panel key="组件物料信息" header="组件物料信息">
        <Item>
          <Label>组件库</Label>
          <Content>
            <Typography.Text ellipsis>
              {canalMaterialService.getLibDisplayNameByComponentType(comp.type)}
            </Typography.Text>
          </Content>
        </Item>
        <Item>
          <Label>组件</Label>
          <Content>
            <Typography.Text ellipsis copyable>
              {materialSchema.name as string}
            </Typography.Text>
          </Content>
        </Item>
        <Item>
          <Label>JS</Label>
          <Content>
            <Typography.Text
              ellipsis={{
                tooltip: true,
              }}
              copyable
            >
              {`${get(materialSchema, 'code.code.js')}`}
            </Typography.Text>
          </Content>
        </Item>
        <Item>
          <Label>版本</Label>
          <Content>
            <Typography.Text ellipsis>
              {libName === INTERNAL_COMPONENT_LIB_NAME
                ? getInternalComponentVersion(materialSchema)
                : libName === LOCAL_COMPONENT_LIB_NAME
                ? '本地'
                : currentVersion}
              {libName !== INTERNAL_COMPONENT_LIB_NAME &&
              libName !== LOCAL_COMPONENT_LIB_NAME &&
              latestMaterialSchema?.version &&
              latestMaterialSchema.version !== currentVersion ? (
                <LatestVersionTip>
                  （最新版本：{latestMaterialSchema.version}
                  <Tooltip title="升级">
                    <Popconfirm
                      title="当前模块中这个组件的所有实例都会升级版本？"
                      onConfirm={handleUpgradeBtnClick}
                    >
                      <IconButton>
                        <SystemArrowDoubleUpLine />
                      </IconButton>
                    </Popconfirm>
                  </Tooltip>
                  ）
                </LatestVersionTip>
              ) : null}
            </Typography.Text>
          </Content>
        </Item>
      </Panel>
      <Panel
        key="接口列表"
        header={
          <>
            接口列表
            <KoncallHelp question="数据源" />
          </>
        }
      >
        <PropertyBaseTabAPIList />
      </Panel>
      <Panel
        key="组件值"
        header={
          <LongTooltip
            title={
              <>
                <div>表达式返回的非 null 的值，会覆盖前端组件当前的值</div>
                {valuePropTip && (
                  <InlineMarkdownPreview source={valuePropTip} />
                )}
              </>
            }
          >
            <HeaderWithIcon>
              组件值
              <KoncallHelp question="属性配置面板里组件值" />
            </HeaderWithIcon>
          </LongTooltip>
        }
      >
        <Button
          type={valueExpr.type === 'api' ? 'primary' : 'secondary'}
          onClick={handleBindComponentValueBtnClick}
        >
          {valueExpr.type === 'api' ? '已绑定' : '绑定'}
        </Button>
      </Panel>
      <Panel
        key="副作用"
        header={
          <Tooltip title="纯前端，组件值变化时立即执行">
            <HeaderWithIcon>
              副作用
              <KoncallHelp question="副作用" />
            </HeaderWithIcon>
          </Tooltip>
        }
      >
        <FrontJSExprEditorModalBtn
          value={effect}
          onChange={handleEffectChange}
          modalTitle="编辑副作用"
          formItemLabel="副作用"
          defaultCodeES={DEFAULT_COMPONENT_EFFECT_JS_EXPR_STR}
          defaultCodeTS={DEFAULT_COMPONENT_EFFECT_TS_EXPR_STR}
          resettable
          getScene={handleEffectGetScene}
          ref={handleEffectRef}
        />
      </Panel>
      <Panel
        key="联动"
        header={
          <Tooltip
            title={
              <div>
                参考
                <a
                  href="https://docs.corp.kuaishou.com/k/home/<USER>/fcABRI4T0AFSjqroVwUKxf_eR"
                  rel="noopener"
                  target="_blank"
                >
                  端到端联动
                </a>
              </div>
            }
          >
            <HeaderWithIcon>
              联动
              <KoncallHelp question="端到端联动" />
            </HeaderWithIcon>
          </Tooltip>
        }
      >
        <Item>
          <Tooltip title="组件值变化后会自动刷新模块，默认携带当前组件值">
            <LinkageLabel>自动刷新</LinkageLabel>
          </Tooltip>
          <Content>
            <Checkbox
              checked={isAutoRefresh}
              onChange={handleAutoRefreshChange}
            />
          </Content>
        </Item>
        <Item>
          <Tooltip title="所有组件触发的自动刷新，都会携带当前组件值">
            <LinkageLabel>自动携带</LinkageLabel>
          </Tooltip>
          <Content>
            <Checkbox
              checked={isComponentDataByAuto}
              onChange={handleComponentDataByAutoChange}
            />
          </Content>
        </Item>
        <Item>
          <Tooltip title="所有组件触发的提交刷新，都会携带当前组件值">
            <LinkageLabel>提交携带</LinkageLabel>
          </Tooltip>
          <Content>
            <Checkbox
              checked={isComponentDataBySubmit}
              onChange={handleComponentDataBySubmitChange}
            />
          </Content>
        </Item>
        <Item>
          <Tooltip title="所有外部参数变化触发的刷新，都会携带当前组件值">
            <LinkageLabel>外部参数携带</LinkageLabel>
          </Tooltip>
          <Content>
            <Checkbox
              checked={isComponentDataByOutsideParams}
              onChange={handleComponentDataByOutsideParamsChange}
            />
          </Content>
        </Item>
        <Item>
          <Tooltip title="所有刷新，都会携带当前组件值">
            <LinkageLabel>全部携带</LinkageLabel>
          </Tooltip>
          <Content>
            <Checkbox
              checked={isComponentDataByAll}
              onChange={handleComponentDataByAllChange}
            />
          </Content>
        </Item>
      </Panel>
    </PropertyCollapse>
  )
})

const LinkageLabel = styled(Label)`
  width: 90px;
`
