import { ReactWidget } from '@kael/designer-core'
import { injectable, postConstruct, register } from '@kael/di'
import React from 'react'
import { createGlobalStyle } from 'styled-components'
import { SettingPanel } from './components/setting-panel'
import IMG_SETTING_PANEL from './setting-panel.svg'

/**
 * 设置面板控件
 */
@injectable()
export class SettingPanelWidget extends ReactWidget {
  public static readonly ID = 'setting-panel'
  public static readonly LABEL = '设置面板'

  @postConstruct()
  public init(): void {
    this.id = SettingPanelWidget.ID
    this.title.label = SettingPanelWidget.LABEL
    this.title.caption = SettingPanelWidget.LABEL
    this.title.iconClass = `tab-icon-${SettingPanelWidget.ID}`
    this.title.closable = true
  }

  public render(): React.ReactNode {
    return (
      <>
        <GlobalStyle />
        <SettingPanel />
      </>
    )
  }
}

/**
 * 注册设置面板控件
 */
export function registerSettingPanelWidget(): void {
  register({
    token: SettingPanelWidget.token,
    useClass: SettingPanelWidget,
  })
}

const GlobalStyle = createGlobalStyle`
  .tab-icon-${SettingPanelWidget.ID} {
    mask: url('${IMG_SETTING_PANEL}');
    -webkit-mask: url('${IMG_SETTING_PANEL}');
  }
`
