import {
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { observer } from 'mobx-react-lite'
import type { FC } from 'react'
import styled from 'styled-components'
import type { E2ESchemaService } from '../../../services'

/**
 * 属性面板标题
 */
export const PropertyTitle: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const stateService = getInject<StateService>(StateServiceSymbol)
  const comp =
    stateService.selectedComponentId &&
    schemaService.findComponentById(stateService.selectedComponentId)

  if (!comp) {
    return null
  }
  return <Container>{comp.name}属性配置</Container>
})

/**
 * 容器
 */
const Container = styled.div`
  font-weight: bold;
`
