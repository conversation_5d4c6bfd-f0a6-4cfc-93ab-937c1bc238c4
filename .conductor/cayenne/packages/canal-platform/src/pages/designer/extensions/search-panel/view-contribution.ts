import {
  AbstractViewContribution,
  type ApplicationContribution,
} from '@kael/designer-core'
import { injectable, register } from '@kael/di'
import { SearchPanelWidget } from './widget'

/**
 * 搜索面板视图贡献点
 */
@injectable()
export class SearchPanelViewContribution
  extends AbstractViewContribution<SearchPanelWidget>
  implements ApplicationContribution
{
  public constructor() {
    super({
      widgetId: SearchPanelWidget.ID,
      widgetName: SearchPanelWidget.LABEL,
      widgetOptions: { area: 'left' },
    })
  }

  public async initializeLayout(): Promise<void> {
    await this.openView()
  }
}

/**
 * 注册搜索面板视图贡献点
 */
export function registerSearchPanelViewContribution(): void {
  register({
    token: SearchPanelViewContribution.token,
    useClass: SearchPanelViewContribution,
  })
}
