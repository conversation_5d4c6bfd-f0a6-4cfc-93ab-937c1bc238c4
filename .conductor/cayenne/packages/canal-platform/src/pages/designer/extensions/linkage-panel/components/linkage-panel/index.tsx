import { TextAreaJsonEditorWithFormItem } from '@/components/textarea-editor'
import type { E2ESchemaService } from '@/pages/designer/services'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { dfsGenComponentDetailBySchema } from '@kael/schema-utils'
import { Form, Select } from '@m-ui/react'
import { isArray, isObject } from 'lodash'
import { observer } from 'mobx-react-lite'
import { useCallback, useMemo, type FC } from 'react'
import styled from 'styled-components'

/**
 * 联动面板
 */
export const LinkagePanel: FC = observer(() => {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const schema = schemaService.schema
  const linkage = schema?.linkage
  const autoRefreshByComponent = linkage?.autoRefreshByComponent || []
  const handleAutoRefreshByComponentSelectChange = useCallback(
    (value: string[]) => {
      schemaService.updatePartialLinkage({
        autoRefreshByComponent: value,
      })
    },
    [schemaService],
  )
  const componentDataByAuto =
    linkage?.componentDataParams?.byRefreshType?.auto || []
  const handleComponentDataByAutoSelectChange = useCallback(
    (value: string[]) => {
      schemaService.updatePartialLinkage({
        componentDataParams: {
          byRefreshType: {
            auto: value,
          },
        },
      })
    },
    [schemaService],
  )
  const componentDataBySubmit =
    linkage?.componentDataParams?.byRefreshType?.submit || []
  const handleComponentDataBySubmitSelectChange = useCallback(
    (value: string[]) => {
      schemaService.updatePartialLinkage({
        componentDataParams: {
          byRefreshType: {
            submit: value,
          },
        },
      })
    },
    [schemaService],
  )
  const componentDataByOutsideParams =
    linkage?.componentDataParams?.byRefreshType?.['outside-params'] || []
  const handleComponentDataByOutsideParamsSelectChange = useCallback(
    (value: string[]) => {
      schemaService.updatePartialLinkage({
        componentDataParams: {
          byRefreshType: {
            ['outside-params']: value,
          },
        },
      })
    },
    [schemaService],
  )
  const componentDataCommon = linkage?.componentDataParams?.common || []
  const handleComponentDataCommonSelectChange = useCallback(
    (value: string[]) => {
      schemaService.updatePartialLinkage({
        componentDataParams: {
          common: value,
        },
      })
    },
    [schemaService],
  )
  const commonParams = linkage?.commonParams
  const commonParamsEditorValue = useMemo(
    () =>
      isObject(commonParams) && !isArray(commonParams) ? commonParams : {},
    [commonParams],
  )
  const handleCommonParamsEditorValueChange = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (newValue: any) => {
      schemaService.updatePartialLinkage({
        commonParams: newValue,
      })
    },
    [schemaService],
  )
  const allComponentOptions = useMemo(() => {
    return schema
      ? Array.from(dfsGenComponentDetailBySchema(schema), (cd) => ({
          label: cd.component.name,
          value: cd.component.id,
        }))
      : []
  }, [schema])

  return (
    <Container>
      <Form layout="vertical">
        <Form.Item
          label="通过组件（值变化）自动刷新"
          tooltip="这些组件的值变化后会自动刷新模块，默认携带触发刷新的组件的值"
        >
          <Select
            mode="multiple"
            options={allComponentOptions}
            value={autoRefreshByComponent}
            onChange={handleAutoRefreshByComponentSelectChange}
          />
        </Form.Item>
        <Form.Item
          label="自动刷新需要携带的组件值"
          tooltip="所有组件触发的自动刷新，都会携带这些组件的值"
        >
          <Select
            mode="multiple"
            options={allComponentOptions}
            value={componentDataByAuto}
            onChange={handleComponentDataByAutoSelectChange}
          />
        </Form.Item>
        <Form.Item
          label="提交刷新需要携带的组件值"
          tooltip="所有组件触发的提交刷新，都会携带这些组件的值"
        >
          <Select
            mode="multiple"
            options={allComponentOptions}
            value={componentDataBySubmit}
            onChange={handleComponentDataBySubmitSelectChange}
          />
        </Form.Item>
        <Form.Item
          label="外部参数刷新需要携带的组件值"
          tooltip="所有外部参数变化触发的刷新，都会携带这些组件的值"
        >
          <Select
            mode="multiple"
            options={allComponentOptions}
            value={componentDataByOutsideParams}
            onChange={handleComponentDataByOutsideParamsSelectChange}
          />
        </Form.Item>
        <Form.Item
          label="所有刷新都需要携带的组件值"
          tooltip="所有刷新，都会携带这些组件的值"
        >
          <Select
            mode="multiple"
            options={allComponentOptions}
            value={componentDataCommon}
            onChange={handleComponentDataCommonSelectChange}
          />
        </Form.Item>
        <TextAreaJsonEditorWithFormItem
          label="所有刷新都需要携带的静态数据"
          value={commonParamsEditorValue}
          onChange={handleCommonParamsEditorValueChange}
        />
      </Form>
      <div>
        参考
        <a
          href="https://docs.corp.kuaishou.com/k/home/<USER>/fcABRI4T0AFSjqroVwUKxf_eR"
          rel="noopener"
          target="_blank"
        >
          端到端联动
        </a>
      </div>
    </Container>
  )
})

const Container = styled.div`
  padding: 10px;
  height: 100%;
  overflow: auto;
`
