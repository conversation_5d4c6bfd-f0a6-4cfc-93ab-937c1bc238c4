import { LongLabelFormItem } from '@/components/long-label-form-item'
import { SpaceBetweenDiv } from '@/components/space-between-div'
import { FrontJSExprEditor } from '@/pages/designer/components'
import {
  BIG_JS_LINK,
  DEFAULT_JS_FRONT_MODEL_STR,
  DEFAULT_TS_FRONT_MODEL_STR,
  FRONT_JS_MODEL_LABEL,
  FRONT_TS_MODEL_LABEL,
} from '@/pages/designer/constants'
import type { E2ESchemaService } from '@/pages/designer/services'
import type { ExpressionTemplate } from '@/services/backend/models'
import {
  asyncFrontJSExprToES5,
  asyncFrontTSExprToJS,
  jsRule,
  tsRule,
  useMoneyRule,
} from '@/utils'
import {
  FRONT_EXPRESSION_TYPE_OPTIONS,
  FrontExpressionType,
} from '@ad/canal-shared'
import type { Model } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { Button, Form, Modal, Select } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import {
  useCallback,
  useEffect,
  useState,
  type FC,
  type ReactNode,
} from 'react'
import styled from 'styled-components'
import { ExpressionTemplateActions } from '../expression-template-actions'

/**
 * 编辑前端模型模态框按钮引用
 */
export interface EditFrontModelModalBtnRef {
  /**
   * 打开
   */
  open(): void
}

/**
 * 编辑前端模型模态框按钮引用
 */
export let editFrontModelModalBtnRef: EditFrontModelModalBtnRef | null = null

/**
 * 编辑前端模型模态框按钮
 */
export const EditFrontModelModalBtn: FC = observer(() => {
  const [form] = Form.useForm<EditFrontModelFormValues>()
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const handleEditBtnClick = useCallback(() => {
    setIsModalVisible(true)
    const formValues: Partial<EditFrontModelFormValues> = {
      exprType: FrontExpressionType.JAVASCRIPT,
      jsExpr: DEFAULT_JS_FRONT_MODEL_STR,
      tsExpr: DEFAULT_TS_FRONT_MODEL_STR,
    }
    const model = schemaService?.schema?.model
    if (model) {
      if (model.codeES) {
        formValues.jsExpr = model.codeES
      } else if (model.codeTS) {
        formValues.exprType = FrontExpressionType.TYPESCRIPT
        formValues.tsExpr = model.codeTS
      }
    }
    form.setFieldsValue(formValues)
  }, [form, schemaService])
  const handleResetBtnClick = useCallback(() => {
    schemaService.updateFrontModel()
  }, [schemaService])
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const schema = schemaService.schema!
  const handleModalCancel = useCallback(() => {
    setIsModalVisible(false)
  }, [])
  const handleModalOk = useCallback(() => {
    form.submit()
  }, [form])
  const handleFormFinish = useCallback(async () => {
    const formValues = form.getFieldsValue()
    let model: Model
    if (formValues.exprType === FrontExpressionType.JAVASCRIPT) {
      model = {
        code: await asyncFrontJSExprToES5(formValues.jsExpr),
        codeES: formValues.jsExpr,
      }
    } else {
      model = {
        code: await asyncFrontTSExprToJS(formValues.tsExpr),
        codeTS: formValues.tsExpr,
      }
    }
    schemaService.updateFrontModel(model)
    setIsModalVisible(false)
  }, [form, schemaService])
  const handleApplyExpressionTemplate = useCallback(
    (tpl: ExpressionTemplate) => {
      form.setFieldValue(
        tpl.expressionType === FrontExpressionType.JAVASCRIPT
          ? 'jsExpr'
          : 'tsExpr',
        tpl.expression,
      )
    },
    [form],
  )
  useEffect(() => {
    editFrontModelModalBtnRef = {
      open(): void {
        handleEditBtnClick()
      },
    }
  }, [handleEditBtnClick])
  const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
  return (
    <>
      <Btn
        type={schema.model ? 'primary' : 'secondary'}
        onClick={handleEditBtnClick}
      >
        编辑
      </Btn>
      <Btn type="secondary" onClick={handleResetBtnClick}>
        重置
      </Btn>
      <Modal
        visible={isModalVisible}
        title="编辑前端模型"
        okText="提交"
        onCancel={handleModalCancel}
        onOk={handleModalOk}
        maskClosable={false}
        width={1000}
      >
        <Form form={form} onFinish={handleFormFinish}>
          <Form.Item
            label="模型类型"
            name="exprType"
            rules={[{ required: true }]}
          >
            <Select options={FRONT_EXPRESSION_TYPE_OPTIONS} />
          </Form.Item>
          <Form.Item dependencies={['exprType', 'jsExpr', 'tsExpr']} noStyle>
            {({ getFieldValue }): ReactNode => (
              <>
                {getFieldValue('exprType') ===
                  FrontExpressionType.JAVASCRIPT && (
                  <LongLabelFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>模型（{FRONT_JS_MODEL_LABEL}）:</div>
                        <ExpressionTemplateActions
                          expression={getFieldValue('jsExpr')}
                          expressionType={FrontExpressionType.JAVASCRIPT}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="jsExpr"
                    rules={[{ required: true }, jsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <FrontJSExprEditor height="calc(100vh - 454px)" />
                  </LongLabelFormItem>
                )}
                {getFieldValue('exprType') ===
                  FrontExpressionType.TYPESCRIPT && (
                  <LongLabelFormItem
                    label={
                      <SpaceBetweenDiv>
                        <div>模型（{FRONT_TS_MODEL_LABEL}）:</div>
                        <ExpressionTemplateActions
                          expression={getFieldValue('tsExpr')}
                          expressionType={FrontExpressionType.TYPESCRIPT}
                          onApply={handleApplyExpressionTemplate}
                        />
                      </SpaceBetweenDiv>
                    }
                    labelCol={{
                      span: 24,
                    }}
                    wrapperCol={{
                      span: 24,
                    }}
                    name="tsExpr"
                    rules={[{ required: true }, tsRule, moneyRule]}
                    extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                  >
                    <FrontJSExprEditor
                      language="typescript"
                      height="calc(100vh - 454px)"
                    />
                  </LongLabelFormItem>
                )}
              </>
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
})

const Btn = styled(Button)`
  margin-right: 6px;
`

/**
 * 编辑前端模型表单值
 */
export interface EditFrontModelFormValues {
  /**
   * 表达式类型
   */
  exprType: FrontExpressionType
  /**
   * js 表达式
   */
  jsExpr: string
  /**
   * ts 表达式
   */
  tsExpr: string
}
