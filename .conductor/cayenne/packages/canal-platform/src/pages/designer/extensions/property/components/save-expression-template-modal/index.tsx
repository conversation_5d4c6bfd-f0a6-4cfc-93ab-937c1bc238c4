import { expressionTemplateStore } from '@/pages/designer/stores/expression-template-store'
import { UNI_EXPRESSION_TYPE_TEXT } from '@ad/canal-shared'
import { AutoComplete, Form, Modal } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useCallback, useEffect, type FC } from 'react'
import { saveExpressionTemplateStore } from './save-expression-template-store'

/**
 * 保存表达式模板模态框
 */
export const SaveExpressionTemplateModal: FC = observer(() => {
  const { isModalVisible, editingExpressionTemplateInfo } =
    saveExpressionTemplateStore
  const [form] = Form.useForm<SaveExpressionTemplateModalFormValues>()
  const handleModalCancel = useCallback(() => {
    saveExpressionTemplateStore.cancelModal()
  }, [])
  const handleModalOk = useCallback(() => {
    form.submit()
  }, [form])
  const handleFormFinish = useCallback(() => {
    const formValues = form.getFieldsValue()
    saveExpressionTemplateStore.submitModal(formValues)
  }, [form])
  useEffect(() => {
    if (isModalVisible && editingExpressionTemplateInfo) {
      form.setFieldsValue({
        name: editingExpressionTemplateInfo.name,
      })
    } else {
      form.resetFields()
    }
  }, [editingExpressionTemplateInfo, form, isModalVisible])
  return (
    <Modal
      visible={isModalVisible}
      title="保存为模板"
      okText="保存"
      zIndex={1001}
      onCancel={handleModalCancel}
      onOk={handleModalOk}
    >
      <Form form={form} onFinish={handleFormFinish}>
        <Form.Item label="类型">
          {editingExpressionTemplateInfo &&
            UNI_EXPRESSION_TYPE_TEXT[
              editingExpressionTemplateInfo.expressionType
            ]}
        </Form.Item>
        <Form.Item label="名称" name="name" rules={[{ required: true }]}>
          <AutoComplete
            options={
              editingExpressionTemplateInfo
                ? expressionTemplateStore.getNameOptions(
                    editingExpressionTemplateInfo.expressionType,
                  )
                : []
            }
          />
        </Form.Item>
      </Form>
    </Modal>
  )
})

/**
 * 保存表达式模板模态框表单值
 */
interface SaveExpressionTemplateModalFormValues {
  /**
   * 名称
   */
  name: string
}
