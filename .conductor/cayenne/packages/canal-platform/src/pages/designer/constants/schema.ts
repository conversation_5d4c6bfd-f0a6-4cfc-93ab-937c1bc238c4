import { INTERNAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import type { E2EServerSchema } from '@ad/e2e-schema'
import { encodeComponentType } from '@kael/schema-utils'

/**
 * 内部组件类型: Root
 */
export const INTERNAL_COMPONENT_TYPE_ROOT = encodeComponentType(
  INTERNAL_COMPONENT_LIB_NAME,
  'Root',
)

/**
 * 默认 Schema
 */
export const DEFAULT_SCHEMA: E2EServerSchema = {
  schemaVersion: '0.0.1',
  flattenedView: {
    rootComponentId: 'root',
    components: [
      {
        type: INTERNAL_COMPONENT_TYPE_ROOT,
        id: 'root',
        name: '根组件',
      },
    ],
    childComponentIdMap: {},
  },
}

/**
 * 默认的参数 JSONata 表达式字符串
 */
export const DEFAULT_ARG_JSONATA_EXPR_STR = JSON.stringify(
  {
    a: 3,
    b: {
      type: 'jsonata',
      code: '$.request.params.x',
    },
  },
  null,
  2,
)

/**
 * 默认的执行条件 JSONata 表达式字符串
 */
export const DEFAULT_IF_JSONATA_EXPR_STR = JSON.stringify(
  {
    type: 'jsonata',
    code: '$.request.params.x',
  },
  null,
  2,
)

/**
 * 默认的响应 JSONata 表达式字符串
 */
export const DEFAULT_RES_JSONATA_EXPR_STR = JSON.stringify(
  {
    a: 3,
    b: {
      type: 'jsonata',
      code: '$.response.data.x',
    },
  },
  null,
  2,
)

/**
 * 默认的参数 JS 表达式字符串
 */
export const DEFAULT_ARG_JS_EXPR_STR = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return {
    a: 3,
    b: ctx.request.params.x,
  }
}
`

/**
 * 默认的参数 TS 表达式字符串
 */
export const DEFAULT_ARG_TS_EXPR_STR = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  return {
    a: 3,
    b: ctx.request.params.x,
  }
}
`

/**
 * 默认的执行条件 JS 表达式字符串
 */
export const DEFAULT_IF_JS_EXPR_STR = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return ctx.request.params.x === 3
}
`

/**
 * 默认的执行条件 TS 表达式字符串
 */
export const DEFAULT_IF_TS_EXPR_STR = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  return ctx.request.params.x === 3
}
`

/**
 * 默认的响应 JS 表达式字符串
 */
export const DEFAULT_RES_JS_EXPR_STR = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return {
    a: 3,
    b: ctx.response.data.x,
  }
}
`

/**
 * 默认的响应处理默认值 JS 表达式字符串
 */
export const DEFAULT_RES_DEFAULT_VALUE_JS_EXPR_STR = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  if (ctx.request.refreshType !== 'default') {
    // 后续获取 Schema，null 会被忽略
    return null
  }
  // 首次获取 Schema，即默认值
  return {
    a: 3,
    b: ctx.response.data.x,
  }
}
`

/**
 * 默认的响应 TS 表达式字符串
 */
export const DEFAULT_RES_TS_EXPR_STR = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  return {
    a: 3,
    b: ctx.response.data.x,
  }
}
`

/**
 * 默认的响应处理默认值 TS 表达式字符串
 */
export const DEFAULT_RES_DEFAULT_VALUE_TS_EXPR_STR = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  if (ctx.request.refreshType !== 'default') {
    // 后续获取 Schema，null 会被忽略
    return null
  }
  // 首次获取 Schema，即默认值
  return {
    a: 3,
    b: ctx.response.data.x,
  }
}
`

/**
 * 默认的执行前端动作 JS 表达式字符串
 */
export const DEFAULT_IIFE_JS_EXPR_STR = `/**
 * @param {Ctx} ctx 上下文
 * @returns {number | number[]} 动作编号
 */
export default (ctx) => {
  if (ctx.response.data.x) {
    return 0
  }
  return [1, 2]
}

/**
 * 输出，用于动作列表里按键值取用，可以在上面函数里修改
 */
export const output = {}
`

/**
 * 默认的执行前端动作 TS 表达式字符串
 */
export const DEFAULT_IIFE_TS_EXPR_STR = `/**
 * @param ctx 上下文
 * @returns 动作编号
 */
export default (ctx: Ctx): number | number[] => {
  if (ctx.response.data.x) {
    return 0
  }
  return [1, 2]
}

/**
 * 输出，用于动作列表里按键值取用，可以在上面函数里修改
 */
export const output: Record<string, any> = {}
`

/**
 * 默认的组件副作用 JS 表达式字符串
 */
export const DEFAULT_COMPONENT_EFFECT_JS_EXPR_STR = `/**
 * @param {any} value 当前值
 * @param {any} prevValue 上一次值
 * @param {Container} ctx 上下文
 */
export default (value, prevValue, ctx) => {
  console.log('组件副作用', value, prevValue, ctx)
}
`

/**
 * 默认的组件副作用 TS 表达式字符串
 */
export const DEFAULT_COMPONENT_EFFECT_TS_EXPR_STR = `/**
 * @param value 当前值
 * @param prevValue 上一次值
 * @param ctx 上下文
 */
export default (value: any, prevValue: any, ctx: Container) => {
  console.log('组件副作用', value, prevValue, ctx)
}
`

/**
 * 默认的模块级前端动作副作用 JS 表达式字符串
 */
export const DEFAULT_IIFE_EFFECT_JS_EXPR_STR = `/**
 * @param {Container} ctx 上下文
 * @param {any} arg 参数
 */
export default (ctx, arg) => {
  console.log('模块级前端动作副作用', ctx, arg)
}
`

/**
 * 默认的模块级前端动作副作用 TS 表达式字符串
 */
export const DEFAULT_IIFE_EFFECT_TS_EXPR_STR = `/**
 * @param ctx 上下文
 * @param arg 参数
 */
export default (ctx: Container, arg: any) => {
  console.log('模块级前端动作副作用', ctx, arg)
}
`

/**
 * 默认的事件级前端动作副作用 JS 表达式字符串
 */
export const DEFAULT_ACTION_EFFECT_JS_EXPR_STR = `/**
 * @param {Container} ctx 上下文
 * @param {any[]} args 组件调用事件函数时传递的参数
 */
export default (ctx, ...args) => {
  console.log('事件级前端动作副作用', ctx, args)
}
`

/**
 * 默认的事件级前端动作副作用 TS 表达式字符串
 */
export const DEFAULT_ACTION_EFFECT_TS_EXPR_STR = `/**
 * @param ctx 上下文
 * @param args 组件调用事件函数时传递的参数
 */
export default (ctx: Container, ...args: any[]) => {
  console.log('事件级前端动作副作用', ctx, args)
}
`

/**
 * 默认属性前端 JS 表达式字符串
 */
export const DEFAULT_PROP_FRONT_JS_EXPR_STR = `/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return ctx.data.x
}
`

/**
 * 默认属性前端 TS 表达式字符串
 */
export const DEFAULT_PROP_FRONT_TS_EXPR_STR = `/**
 * @param ctx 上下文
 */
export default (ctx: Container) => {
  return ctx.data.x
}
`

/**
 * 默认 JS 前端模型字符串
 */
export const DEFAULT_JS_FRONT_MODEL_STR = `/**
 * 前端模型
 */
export default class FrontModel {
  /**
   * @param {Container} ctx 上下文
   */
  constructor(ctx) {
    this.ctx = ctx
    console.log('FrontModel init', ctx)
  }
}
`

/**
 * 默认 TS 前端模型字符串
 */
export const DEFAULT_TS_FRONT_MODEL_STR = `/**
 * 前端模型
 */
export default class FrontModel {
  /**
   * @param ctx 上下文
   */
  public constructor(private ctx: Container) {
    console.log('FrontModel init', ctx)
  }
}
`

/**
 * 默认 JS 后端模型字符串
 */
export const DEFAULT_JS_BACK_MODEL_STR = `/**
 * 后端模型
 */
export default class BackModel {
  /**
   * @param {Ctx} ctx 上下文
   */
  constructor(ctx) {
    this.ctx = ctx
    console.log('BackModel init', ctx)
  }
}
`

/**
 * 默认 TS 后端模型字符串
 */
export const DEFAULT_TS_BACK_MODEL_STR = `/**
 * 后端模型
 */
export default class BackModel {
  /**
   * @param ctx 上下文
   */
  public constructor(private ctx: Ctx) {
    console.log('BackModel init', ctx)
  }
}
`

/**
 * 默认 JS 全局前端模型字符串
 */
export const DEFAULT_JS_GLOBAL_FRONT_MODEL_STR = `/**
 * 全局前端模型
 */
export default class GlobalFrontModel {
  constructor(ctx) {
    this.ctx = ctx
    console.log('GlobalFrontModel init', ctx)
  }
}
`

/**
 * 默认 TS 全局前端模型字符串
 */
export const DEFAULT_TS_GLOBAL_FRONT_MODEL_STR = `/**
 * 全局前端模型
 */
export default class GlobalFrontModel {
  public constructor(private ctx: Container) {
    console.log('GlobalFrontModel init', ctx)
  }
}
`

/**
 * 默认 JS 全局后端模型字符串
 */
export const DEFAULT_JS_GLOBAL_BACK_MODEL_STR = `/**
 * 全局后端模型
 */
export default class GlobalBackModel {
  constructor(ctx) {
    this.ctx = ctx
    console.log('GlobalBackModel init', ctx)
  }
}
`

/**
 * 默认 TS 全局后端模型字符串
 */
export const DEFAULT_TS_GLOBAL_BACK_MODEL_STR = `/**
 * 全局后端模型
 */
export default class GlobalBackModel {
  public constructor(private ctx: Ctx) {
    console.log('GlobalBackModel init', ctx)
  }
}
`
