import { createApp } from '@kael/designer-core'
import { canvasPreviewerStore } from './extensions/canvas/canvas-previewer/canvas-previewer-store'
import { initDesignerExtensions } from './init-designer-extensions'

/**
 * 创建设计器应用
 * @param host 挂载点
 */
export function createDesignerApp(
  host = document.body,
): ReturnType<typeof createApp> {
  initDesignerExtensions()
  const app = createApp()
  ;(async (): Promise<void> => {
    try {
      await app.start({
        host,
      })

      canvasPreviewerStore.adjustPreviewerIframeByElement()
    } catch (err) {
      console.error('createDesignerApp 启动失败', err)
    }
  })()
  return app
}
