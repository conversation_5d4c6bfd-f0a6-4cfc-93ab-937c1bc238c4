import { getModuleDetail } from '@/services/backend/module'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import type { E2ESchema, E2EServerSchema } from '@ad/e2e-schema'
import { b2fE2ESchema, f2bE2ESchema } from '@ad/e2e-schema-utils'
import { MaterialServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { cloneDeep } from 'lodash'
import { DEFAULT_SCHEMA } from '../constants'
import type { CanalMaterialService } from '../services'

/**
 * 模块内容转端到端 Schema
 * @param moduleContent 模块内容
 */
export function moduleContentToE2ESchema(moduleContent: string): E2ESchema {
  const serverSchema: E2EServerSchema = moduleContent
    ? JSON.parse(moduleContent)
    : cloneDeep(DEFAULT_SCHEMA)
  const schema = b2fE2ESchema(serverSchema, {
    keepBackDataProp: true,
    keepValueProp: true,
    keepIfProp: true,
  })
  return schema
}

/**
 * 获取模块端到端 Schema
 * @param moduleId 模块 ID
 * @param moduleVersion 模块 版本
 */
export async function getModuleSchema(
  moduleId: string,
  moduleVersion?: string | number,
): Promise<E2ESchema> {
  const {
    data: { content: moduleContent },
  } = await getModuleDetail({
    id: moduleId,
    version: moduleVersion as string | undefined,
  })
  const schema = moduleContentToE2ESchema(moduleContent)
  return schema
}

/**
 * 端到端 Schema 转模块内容
 * @param schema 端到端 Schema
 */
export function e2eSchemaToModuleContent(schema: E2ESchema): string {
  return JSON.stringify(f2bE2ESchema(schema))
}

/**
 * 获取组件物料 Schema
 * @param type 组件类型
 * @param version 版本
 */
export function getMergedComponentMaterialSchema(
  type: string,
  version?: string,
): E2ERemoteComponentMaterialSchema | null {
  const canalMaterialService = getInject<CanalMaterialService>(
    MaterialServiceSymbol,
  )
  return canalMaterialService.getMergedComponentMaterialSchema(type, version)
}
