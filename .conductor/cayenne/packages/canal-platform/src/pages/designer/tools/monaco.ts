import {
  getBackCtxDts,
  getCtxDataFieldsBySchema,
  getFrontCtxDts,
  type CtxDataField,
} from '@ad/canal-shared'
import {
  MaterialServiceSymbol,
  SchemaServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import type { CanalMaterialService, E2ESchemaService } from '../services'
import { schemaStore } from '../stores/schema-store'

/**
 * 获取最新的后端上下文类型定义
 */
export function getLatestBackCtxDts(): string {
  const schemaService = getInject<E2ESchemaService | null>(SchemaServiceSymbol)
  return getBackCtxDts(
    getLatestDataFields(),
    schemaService?.schema?.backModel,
    schemaStore.globalSchema?.globalBackModel,
  )
}

/**
 * 获取最新的前端上下文类型定义
 */
export function getLatestFrontCtxDts(): string {
  const schemaService = getInject<E2ESchemaService | null>(SchemaServiceSymbol)
  return getFrontCtxDts(
    getLatestDataFields(),
    schemaService?.schema?.model,
    schemaStore.globalSchema?.globalFrontModel,
  )
}

/**
 * 获取最新的数据字段
 */
function getLatestDataFields(): CtxDataField[] {
  const schemaService = getInject<E2ESchemaService | null>(SchemaServiceSymbol)
  const canalMaterialService = getInject<CanalMaterialService>(
    MaterialServiceSymbol,
  )
  const schema = schemaService?.schema
  return getCtxDataFieldsBySchema(
    schema,
    canalMaterialService.getValueProp.bind(canalMaterialService),
  )
}
