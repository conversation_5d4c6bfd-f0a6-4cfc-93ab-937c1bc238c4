import type { SimplifyComponent } from '@ad/canal-ai'
import { waitByFrame } from '@ad/canal-shared'
import type { E2ESchemaComponent } from '@ad/e2e-schema'
import {
  PropertyServiceSymbol,
  type PropertyService,
} from '@kael/designer-property'
import {
  SchemaServiceSymbol,
  StateService,
  StateServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { pick } from 'lodash'
import type { E2ESchemaService } from '../services'

/**
 * 选择组件并等待渲染
 * @param componentId 组件 ID
 */
export async function selectComponentAndWait(
  componentId: string,
): Promise<E2ESchemaComponent | undefined> {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const comp = schemaService.findComponentById(componentId)
  if (!comp) return
  const stateService = getInject<StateService>(StateServiceSymbol)
  stateService.setSelectedComponentId(componentId)
  const propertyService = getInject<PropertyService>(PropertyServiceSymbol)
  await waitByFrame(() =>
    propertyService.activeTab.startsWith(`${componentId}.`),
  )
  return comp
}

/**
 * 获取组件
 * @param componentId 组件 ID
 */
export function getComponent(componentId?: string): null | E2ESchemaComponent {
  if (!componentId) return null
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  return schemaService.findComponentById(componentId) || null
}

/**
 * 获取选中的组件
 */
export function getSelectedComponent(): null | E2ESchemaComponent {
  const stateService = getInject<StateService>(StateServiceSymbol)
  return getComponent(stateService.selectedComponentId)
}

/**
 * 获取简化组件
 * @param componentId 组件 ID
 */
export function getSimplifyComponent(
  componentId?: string,
): null | E2ESchemaComponent {
  const comp = getComponent(componentId)
  return comp && pick(comp, 'id', 'type', 'name')
}

/**
 * 获取选中的简化组件
 */
export function getSelectedSimplifyComponent(): null | SimplifyComponent {
  const comp = getSelectedComponent()
  return comp && pick(comp, 'id', 'type', 'name')
}
