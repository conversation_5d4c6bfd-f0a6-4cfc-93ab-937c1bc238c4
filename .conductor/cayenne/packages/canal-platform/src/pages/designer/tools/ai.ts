import { aiSimpleChat } from '@/services/backend/ai'
import type { BackExpressionScene, FrontExpressionScene } from '@ad/canal-ai'
import {
  createBackExprCodePrompt,
  createFrontExprCodePrompt,
  getCodeFromCompletion,
} from '@ad/canal-ai'
import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import type { E2ESchemaService } from '../services'
import { getApiLabelsInComponent, getDataSourceLabelByApi } from './api'
import { getLatestBackCtxDts, getLatestFrontCtxDts } from './monaco'
import { getMergedComponentMaterialSchema } from './schema'

/**
 * AI 生成表达式代码选项
 */
export type AiGenerateExpressionCodeOptions =
  | {
      /**
       * 表达式类型
       */
      expressionType: BackExpressionType
      /**
       * 场景
       */
      scene: BackExpressionScene
    }
  | {
      /**
       * 表达式类型
       */
      expressionType: FrontExpressionType
      /**
       * 场景
       */
      scene: FrontExpressionScene
    }

/**
 * 生成表达式代码结果
 */
export interface AiGenerateExpressionCodeResult {
  /**
   * UUID
   */
  uuid: string
  /**
   * 提示
   */
  prompt: string
  /**
   * 代码
   */
  code: string
}

/**
 * AI 生成表达式代码
 * @param options 选项
 */
export async function aiGenerateExpressionCode({
  expressionType,
  scene,
}: AiGenerateExpressionCodeOptions): Promise<AiGenerateExpressionCodeResult> {
  const schemaService = getInject<E2ESchemaService | null>(SchemaServiceSymbol)
  const schema = schemaService?.schema
  if (!schema) {
    throw new Error('schema 不存在')
  }
  let prompt: string
  if (
    expressionType === BackExpressionType.JAVASCRIPT ||
    expressionType === BackExpressionType.TYPESCRIPT
  ) {
    prompt = createBackExprCodePrompt({
      expressionType,
      scene,
      ctxDts: getLatestBackCtxDts(),
      schema,
      getComponentMaterialSchema: getMergedComponentMaterialSchema,
      getApiLabelsInComponent,
      getDataSourceLabelByApi,
    })
  } else if (
    expressionType === FrontExpressionType.JAVASCRIPT ||
    expressionType === FrontExpressionType.TYPESCRIPT
  ) {
    prompt = createFrontExprCodePrompt({
      expressionType,
      scene,
      ctxDts: getLatestFrontCtxDts(),
      schema,
      getComponentMaterialSchema: getMergedComponentMaterialSchema,
    })
  } else {
    throw new Error(`不支持的表达式类型，expressionType: ${expressionType}`)
  }
  const {
    data: { uuid, completion },
  } = await aiSimpleChat({ prompt })
  return { uuid, prompt, code: getCodeFromCompletion(completion) }
}
