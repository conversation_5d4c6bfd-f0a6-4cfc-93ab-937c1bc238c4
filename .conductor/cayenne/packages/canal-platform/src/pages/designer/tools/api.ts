import { createApiLabel, createDataSourceLabelByApi } from '@ad/canal-shared'
import type { E2EAPI } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { apiDetailStore } from '../extensions/property'
import type { E2ESchemaService } from '../services'

/**
 * 获取接口标签
 * @param api 接口
 * @param pos 位置
 */
export function getApiLabel(api: E2EAPI, pos?: 'module' | 'component'): string {
  return createApiLabel(
    api,
    pos,
    apiDetailStore.getSingleDataSource.bind(apiDetailStore),
  )
}

/**
 * 获取接口选项
 * @param componentId 组件 ID
 */
export function getApiOptions(componentId?: string): {
  value: string
  label: string
}[] {
  const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
  const apis =
    (componentId &&
      componentId !== schemaService?.rootId &&
      schemaService?.getComponentAPIs(componentId)) ||
    []
  const rootApis =
    (schemaService && schemaService.getComponentAPIs(schemaService.rootId)) ||
    []
  const apiOptions = [
    ...apis.map((api) => ({
      label: getApiLabel(api, 'component'),
      value: api.id,
    })),
    ...rootApis.map((api) => ({
      label: getApiLabel(api, 'module'),
      value: api.id,
    })),
  ]
  return apiOptions
}

/**
 * 在组件里获取多个接口标签
 * @param componentId 组件 ID
 * @param apiIds 接口 ID
 */
export function getApiLabelsInComponent(
  componentId: string | undefined,
  apiIds: string[],
): string[] {
  const apiIdSet = new Set(apiIds)
  return getApiOptions(componentId)
    .filter((api) => apiIdSet.has(api.value))
    .map((api) => api.label)
}

/**
 * 通过接口获取数据源标签
 * @param api 接口
 */
export function getDataSourceLabelByApi(
  api: Pick<E2EAPI, 'dataSourceId' | 'service' | 'method'>,
): string {
  return createDataSourceLabelByApi(
    api,
    apiDetailStore.getSingleDataSource.bind(apiDetailStore),
  )
}
