import { AiAnimationIcon } from '@/components/ai-animation-icon'
import { IconButton } from '@/components/icon-button'
import { aiUpdateLog } from '@/services/backend/ai'
import {
  AiLogStatus,
  type BackExpressionScene,
  type FrontExpressionScene,
} from '@ad/canal-ai'
import type { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import { useLatestFn } from '@ad/canal-shared-ui'
import { NormalPreviousLine } from '@m-ui/icons'
import { Tooltip, message } from '@m-ui/react'
import { useContext, useState, type FC } from 'react'
import styled from 'styled-components'
import {
  aiGenerateExpressionCode,
  type AiGenerateExpressionCodeOptions,
} from '../../tools'
import { aiGenerateExpressionContext } from './ai-generate-expression-context'

export * from './ai-generate-expression-context'

/**
 * AI 生成表达式属性
 */
export type AiGenerateExpressionProps = (
  | {
      /**
       * 表达式类型
       */
      expressionType: BackExpressionType
      /**
       * 获取场景
       */
      getScene(): BackExpressionScene | null
    }
  | {
      /**
       * 表达式类型
       */
      expressionType: FrontExpressionType
      /**
       * 获取场景
       */
      getScene(): FrontExpressionScene | null
    }
) & {
  /**
   * 生成事件
   * @param code 代码
   * @param expressionType 表达式类型
   * @param generateType 生成类型
   * @returns 旧的代码
   */
  onGenerate(
    code: string,
    expressionType: BackExpressionType | FrontExpressionType,
    generateType: AiGenerateType,
  ): string
}

/**
 * AI 生成类型
 */
export enum AiGenerateType {
  /**
   * 生成
   */
  GENERATE = 'GENERATE',
  /**
   * 恢复
   */
  RESTORE = 'RESTORE',
}

/**
 * AI 生成表达式
 */
export const AiGenerateExpression: FC<AiGenerateExpressionProps> = ({
  expressionType,
  getScene,
  onGenerate,
}) => {
  const aiCtxValue = useContext(aiGenerateExpressionContext)
  const [restoreState, setRestoreState] = useState<[string, string] | null>(
    null,
  )
  const handleClickAiAnimationIcon = useLatestFn(async () => {
    if (aiCtxValue?.isLoading) return
    const scene = getScene()
    if (!scene) return
    aiCtxValue?.setIsLoading(true)
    try {
      const { uuid, code } = await aiGenerateExpressionCode({
        expressionType,
        scene,
      } as AiGenerateExpressionCodeOptions)
      const oldCode = onGenerate(code, expressionType, AiGenerateType.GENERATE)
      setRestoreState([uuid, oldCode])
    } catch (err) {
      console.error(' ai generate code err', err)
      message.error('AI 生成代码失败')
    } finally {
      aiCtxValue?.setIsLoading(false)
    }
  })
  const handleClickRestoreBtn = useLatestFn(() => {
    if (!restoreState) return
    const [uuid, oldCode] = restoreState
    onGenerate(oldCode, expressionType, AiGenerateType.RESTORE)
    setRestoreState(null)
    aiUpdateLog({ uuid, status: AiLogStatus.REJECTED })
  })
  return (
    <Container>
      {restoreState && (
        <div className="restore-btn">
          <Tooltip title="恢复表达式代码">
            <IconButton onClick={handleClickRestoreBtn}>
              <NormalPreviousLine />
            </IconButton>
          </Tooltip>
        </div>
      )}
      <Tooltip title="AI 生成表达式代码">
        <AiAnimationIcon
          onClick={handleClickAiAnimationIcon}
          $isActive={aiCtxValue?.isLoading}
        />
      </Tooltip>
    </Container>
  )
}

const Container = styled.div`
  display: flex;

  .restore-btn {
    width: 0;

    ${IconButton} {
      font-size: 20px;
      margin-left: -20px;
    }
  }
`
