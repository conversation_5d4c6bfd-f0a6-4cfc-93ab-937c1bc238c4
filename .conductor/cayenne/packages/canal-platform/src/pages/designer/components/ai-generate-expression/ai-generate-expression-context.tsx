import {
  createContext,
  memo,
  useState,
  type FC,
  type PropsWithChildren,
} from 'react'

/**
 * AI 生成表达式值
 */
export interface AiGenerateExpressionContextValue {
  /**
   * 正在加载
   */
  isLoading: boolean
  /**
   * 设置正在加载
   * @param isLoading 正在加载
   */
  setIsLoading(isLoading: boolean): void
}

/**
 * AI 生成表达式上下文
 */
export const aiGenerateExpressionContext =
  createContext<AiGenerateExpressionContextValue | null>(null)

/**
 * AI 生成表达式上下文函数组件
 */
export const AiGenExprCtxFc: FC<PropsWithChildren> = memo(({ children }) => {
  const [isLoading, setIsLoading] = useState(false)
  return (
    <aiGenerateExpressionContext.Provider value={{ isLoading, setIsLoading }}>
      {children}
    </aiGenerateExpressionContext.Provider>
  )
})
