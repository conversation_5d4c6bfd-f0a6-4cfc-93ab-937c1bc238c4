import { Long<PERSON>abelFormItem } from '@/components/long-label-form-item'
import { SpaceBetweenDiv } from '@/components/space-between-div'
import type { ExpressionTemplate } from '@/services/backend/models'
import {
  asyncFrontJSExprToES5,
  asyncFrontTSExprToJS,
  jsRule,
  tsRule,
  useMoneyRule,
} from '@/utils'
import type { FrontExpressionScene } from '@ad/canal-ai'
import {
  BackExpressionType,
  FRONT_EXPRESSION_TYPE_OPTIONS,
  FrontExpressionType,
} from '@ad/canal-shared'
import { useLatestFn, type ValueOnChangeProps } from '@ad/canal-shared-ui'
import type { E2ESchemaExpressionJS } from '@ad/e2e-schema'
import { Button, Form, Modal, Select } from '@m-ui/react'
import { fillRef } from 'rc-util/es/ref'
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useState,
  type ComponentProps,
  type ReactNode,
} from 'react'
import styled from 'styled-components'
import {
  BIG_JS_LINK,
  FRONT_JS_EXPR_LABEL,
  FRONT_TS_EXPR_LABEL,
} from '../constants'
import { ExpressionTemplateActions } from '../extensions/property'
import { AiGenExprCtxFc, AiGenerateExpression } from './ai-generate-expression'
import { FrontJSExprEditor } from './front-js-expr-editor'

/**
 * 前端 JS 表达式编辑器带模态框按钮属性
 */
export interface FrontJSExprEditorModalBtnProps
  extends Omit<ComponentProps<typeof FrontJSExprEditor>, 'value' | 'onChange'>,
    ValueOnChangeProps<E2ESchemaExpressionJS | undefined> {
  /**
   * 模态框标题
   */
  modalTitle: string
  /**
   * 表单项标签
   */
  formItemLabel: string
  /**
   * 默认的 ES 代码
   */
  defaultCodeES?: string
  /**
   * 默认的 TS 代码
   */
  defaultCodeTS?: string
  /**
   * 可重置
   */
  resettable?: boolean
  /**
   * 获取场景
   */
  getScene(): FrontExpressionScene | null
}

/**
 * 前端 JS 表达式编辑器带模态框按钮引用
 */
export interface FrontJSExprEditorModalBtnRef {
  /**
   * 打开
   */
  open(): void
}

/**
 * 前端 JS 表达式编辑器带模态框按钮
 */
export const FrontJSExprEditorModalBtn = memo(
  forwardRef<FrontJSExprEditorModalBtnRef, FrontJSExprEditorModalBtnProps>(
    (
      {
        value,
        onChange,
        modalTitle,
        formItemLabel,
        defaultCodeES,
        defaultCodeTS,
        resettable,
        getScene,
        ...restProps
      },
      ref,
    ) => {
      const [isModalVisible, setIsModalVisible] = useState(false)
      const [form] = Form.useForm<FrontJSExprEditorModalBtnFormValues>()
      const handleEditBtnClick = useCallback(() => {
        setIsModalVisible(true)
        const formValues: Partial<FrontJSExprEditorModalBtnFormValues> = {
          exprType: value?.codeTS
            ? FrontExpressionType.TYPESCRIPT
            : FrontExpressionType.JAVASCRIPT,
          jsExpr: value?.codeES || defaultCodeES,
          tsExpr: value?.codeTS || defaultCodeTS,
        }
        form.setFieldsValue(formValues)
      }, [defaultCodeES, defaultCodeTS, form, value?.codeES, value?.codeTS])
      const handleResetBtnClick = useCallback(() => {
        onChange?.(undefined)
      }, [onChange])
      const handleModalCancel = useCallback(() => {
        setIsModalVisible(false)
      }, [])
      const handleModalOk = useCallback(() => {
        form.submit()
      }, [form])
      const handleFormFinish = useCallback(async () => {
        const formValues = form.getFieldsValue()
        const expr: E2ESchemaExpressionJS = {
          type: 'js',
          code:
            formValues.exprType === FrontExpressionType.JAVASCRIPT
              ? await asyncFrontJSExprToES5(formValues.jsExpr)
              : await asyncFrontTSExprToJS(formValues.tsExpr),
        }
        if (formValues.exprType === FrontExpressionType.JAVASCRIPT) {
          expr.codeES = formValues.jsExpr
        } else {
          expr.codeTS = formValues.tsExpr
        }
        onChange?.(expr)
        setIsModalVisible(false)
      }, [form, onChange])
      const handleApplyExpressionTemplate = useCallback(
        (tpl: ExpressionTemplate) => {
          form.setFieldValue(
            tpl.expressionType === FrontExpressionType.JAVASCRIPT
              ? 'jsExpr'
              : 'tsExpr',
            tpl.expression,
          )
        },
        [form],
      )
      const handleGenerateCode = useLatestFn(
        (
          code: string,
          expressionType: BackExpressionType | FrontExpressionType,
        ) => {
          if (expressionType === FrontExpressionType.TYPESCRIPT) {
            const oldCode = form.getFieldValue('tsExpr')
            form.setFieldValue('tsExpr', code)
            return oldCode
          } else {
            const oldCode = form.getFieldValue('jsExpr')
            form.setFieldValue('jsExpr', code)
            return oldCode
          }
        },
      )
      useEffect(() => {
        fillRef(ref, {
          open() {
            handleEditBtnClick()
          },
        })
      }, [handleEditBtnClick, ref])
      const [moneyRule, moneyRuleHasErrBefore] = useMoneyRule()
      return (
        <>
          <Btn
            type={value ? 'primary' : 'secondary'}
            onClick={handleEditBtnClick}
          >
            编辑
          </Btn>
          {resettable && (
            <Btn type="secondary" onClick={handleResetBtnClick}>
              重置
            </Btn>
          )}
          <AiGenExprCtxFc>
            <Modal
              visible={isModalVisible}
              title={modalTitle}
              okText="提交"
              onCancel={handleModalCancel}
              onOk={handleModalOk}
              maskClosable={false}
              width={800}
            >
              <Form form={form} onFinish={handleFormFinish}>
                <Form.Item
                  label="表达式类型"
                  name="exprType"
                  rules={[{ required: true }]}
                >
                  <Select options={FRONT_EXPRESSION_TYPE_OPTIONS} />
                </Form.Item>

                <Form.Item
                  dependencies={['exprType', 'jsExpr', 'tsExpr']}
                  noStyle
                >
                  {({ getFieldValue }): ReactNode => (
                    <>
                      {getFieldValue('exprType') ===
                        FrontExpressionType.JAVASCRIPT && (
                        <LongLabelFormItem
                          label={
                            <SpaceBetweenDiv>
                              <div>
                                {formItemLabel}（{FRONT_JS_EXPR_LABEL}）:
                              </div>
                              <AiGenerateExpression
                                expressionType={FrontExpressionType.JAVASCRIPT}
                                getScene={getScene}
                                onGenerate={handleGenerateCode}
                              />
                              <ExpressionTemplateActions
                                expression={getFieldValue('jsExpr')}
                                expressionType={FrontExpressionType.JAVASCRIPT}
                                onApply={handleApplyExpressionTemplate}
                              />
                            </SpaceBetweenDiv>
                          }
                          labelCol={{
                            span: 24,
                          }}
                          wrapperCol={{
                            span: 24,
                          }}
                          name="jsExpr"
                          rules={[{ required: true }, jsRule, moneyRule]}
                          extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                        >
                          <FrontJSExprEditor
                            height="calc(100vh - 456px)"
                            {...restProps}
                          />
                        </LongLabelFormItem>
                      )}
                      {getFieldValue('exprType') ===
                        FrontExpressionType.TYPESCRIPT && (
                        <LongLabelFormItem
                          label={
                            <SpaceBetweenDiv>
                              <div>
                                {formItemLabel}（{FRONT_TS_EXPR_LABEL}）:
                              </div>
                              <AiGenerateExpression
                                expressionType={FrontExpressionType.TYPESCRIPT}
                                getScene={getScene}
                                onGenerate={handleGenerateCode}
                              />
                              <ExpressionTemplateActions
                                expression={getFieldValue('tsExpr')}
                                expressionType={FrontExpressionType.TYPESCRIPT}
                                onApply={handleApplyExpressionTemplate}
                              />
                            </SpaceBetweenDiv>
                          }
                          labelCol={{
                            span: 24,
                          }}
                          wrapperCol={{
                            span: 24,
                          }}
                          name="tsExpr"
                          rules={[{ required: true }, tsRule, moneyRule]}
                          extra={moneyRuleHasErrBefore && BIG_JS_LINK}
                        >
                          <FrontJSExprEditor
                            language="typescript"
                            height="calc(100vh - 456px)"
                            {...restProps}
                          />
                        </LongLabelFormItem>
                      )}
                    </>
                  )}
                </Form.Item>
              </Form>
            </Modal>
          </AiGenExprCtxFc>
        </>
      )
    },
  ),
)

/**
 * 前端 JS 表达式编辑器带模态框按钮表单值
 */
interface FrontJSExprEditorModalBtnFormValues {
  /**
   * 表达式类型
   */
  exprType: FrontExpressionType
  /**
   * js 表达式
   */
  jsExpr: string
  /**
   * ts 表达式
   */
  tsExpr: string
}

const Btn = styled(Button)`
  margin-right: 6px;
`
