import { JSEditor } from '@/components/js-editor'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import { Spin } from '@m-ui/react'
import { observer } from 'mobx-react-lite'
import { useContext, useMemo, type ComponentProps, type FC } from 'react'
import { JSExprEditor } from '../../../components/js-expr-editor'
import type { E2ESchemaService } from '../services'
import { schemaStore } from '../stores/schema-store'
import { getLatestFrontCtxDts } from '../tools'
import { aiGenerateExpressionContext } from './ai-generate-expression'

/**
 * 前端 JS 表达式编辑器属性
 */
export interface FrontJSExprEditorProps
  extends ComponentProps<typeof JSEditor> {
  /**
   * 正在加载
   */
  isLoading?: boolean
}

/**
 * 前端 JS 表达式编辑器
 */
export const FrontJSExprEditor: FC<FrontJSExprEditorProps> = observer(
  ({ isLoading, ...restProps }) => {
    const schemaService = getInject<E2ESchemaService | null>(
      SchemaServiceSymbol,
    )
    const schema = schemaService?.schema
    const { globalSchema } = schemaStore
    const ctxDts = useMemo(() => {
      void schema
      void globalSchema
      return getLatestFrontCtxDts()
    }, [schema, globalSchema])
    const aiCtxValue = useContext(aiGenerateExpressionContext)
    const finalIsLoading = !!(isLoading || aiCtxValue?.isLoading)
    return (
      <Spin spinning={finalIsLoading}>
        <JSExprEditor {...restProps} ctxDts={ctxDts} />
      </Spin>
    )
  },
)
