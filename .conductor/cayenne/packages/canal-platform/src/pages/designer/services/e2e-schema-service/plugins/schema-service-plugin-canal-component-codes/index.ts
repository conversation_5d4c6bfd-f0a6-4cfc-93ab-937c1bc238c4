import {} from '@ad/canal-shared'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import {
  INTERNAL_COMPONENT_LIB_NAME,
  upgradeComponentProps,
} from '@ad/e2e-material-schema-utils'
import type { E2ESchema, E2ESchemaComponent } from '@ad/e2e-schema'
import {
  MaterialServiceSymbol,
  SchemaCode,
  SchemaEffect,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaReturnType,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import {
  decodeComponentType,
  dfsGenComponentDetailBySchema,
} from '@kael/schema-utils'
import { action } from 'mobx'
import type { CanalMaterialService } from '../../..'

/**
 * SchemaService 大运河组件代码插件的扩展属性
 */
export type SchemaServicePluginCanalComponentCodesPropertiesExt = {
  /**
   * 重新生成组件代码
   */
  regenerateComponentCodes: () => void
  /**
   * 升级组件代码（到最新版本）
   * @param componentType 组件类型
   */
  upgrateComponentCode: (componentType: string) => SchemaReturnType<void>
  /**
   * 是否为最新的组件代码
   * @param componentType 组件类型
   */
  isLatestComponentCode: (componentType: string) => boolean
}

/**
 * SchemaService 大运河组件代码插件
 */
export const schemaServicePluginCanalComponentCodes: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalComponentCodesPropertiesExt
> = {
  id: 'CANAL_COMPONENT_CODES',
  extends(): void {
    this.regenerateComponentCodes = action(() => {
      if (!this._schemaData) {
        return
      }
      const canalMaterialService = getInject<CanalMaterialService>(
        MaterialServiceSymbol,
      )
      const componentCodes: NonNullable<E2ESchema['componentCodes']> = {}
      for (const cd of dfsGenComponentDetailBySchema(this._schemaData)) {
        const componentType = cd.component.type
        if (this._schemaData.componentCodes?.[componentType]) {
          componentCodes[componentType] =
            this._schemaData.componentCodes[componentType]
          continue
        }
        const [libName] = decodeComponentType(componentType)
        if (libName === INTERNAL_COMPONENT_LIB_NAME) {
          continue
        }
        const compMaterial: E2ERemoteComponentMaterialSchema | undefined =
          canalMaterialService.getComponentSchemaByType(componentType, 'latest')
        if (!compMaterial) {
          continue
        }
        componentCodes[componentType] = {
          ...compMaterial.code,
          version: compMaterial.version,
        }
      }
      this._schemaData.componentCodes = componentCodes
    }) as typeof this.regenerateComponentCodes
    this.upgrateComponentCode = action((componentType) => {
      if (!this._schemaData) {
        return resultFn(SchemaCode.SUCCESS)
      }
      const canalMaterialService = getInject<CanalMaterialService>(
        MaterialServiceSymbol,
      )
      const oldComponentMaterial: E2ERemoteComponentMaterialSchema | undefined =
        canalMaterialService.getComponentSchemaByType(componentType)
      const newComponentMaterial: E2ERemoteComponentMaterialSchema | undefined =
        canalMaterialService.getComponentSchemaByType(componentType, 'latest')
      if (
        !oldComponentMaterial ||
        !newComponentMaterial ||
        oldComponentMaterial === newComponentMaterial
      ) {
        return resultFn(SchemaCode.SUCCESS)
      }
      const componentCodes: NonNullable<E2ESchema['componentCodes']> = {
        ...this._schemaData.componentCodes,
      }
      componentCodes[componentType] = {
        ...newComponentMaterial.code,
        version: newComponentMaterial.version,
      }
      this._schemaData.componentCodes = componentCodes

      // 更新 props
      const upgradeComps: E2ESchemaComponent[] = []
      for (const cd of dfsGenComponentDetailBySchema(this._schemaData)) {
        if (cd.component.type === componentType) {
          upgradeComps.push(cd.component)
        }
      }
      upgradeComponentProps(
        upgradeComps,
        oldComponentMaterial,
        newComponentMaterial,
      )

      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
      }
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.upgrateComponentCode
    dynamicApplyMethodDecorator(this, 'upgrateComponentCode', SchemaEffect())
  },
}
