import type { E2ESchema, E2ESchemaComponent } from '@ad/e2e-schema'
import {
  createVarComponentId,
  createVarId,
  e2eSchemaTraverseOptions,
  getAllIdsOfSchema,
  isApiExpression,
  renameComponentById,
} from '@ad/e2e-schema-utils'
import {
  MaterialServiceSymbol,
  NodeControlService,
  SchemaServiceSymbol,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import type { ComponentMaterialSchema } from '@kael/material-schema'
import {
  dfsGenComponentDetailBySchemaComponent,
  dfsGenExpressionDetailBySchemaComponent,
} from '@kael/schema-utils'
import { cloneDeep, isArray } from 'lodash'
import type { CanalMaterialService, E2ESchemaService } from '.'
import { INTERNAL_COMPONENT_TYPE_ROOT } from '../constants'

/**
 * 大运河节点控制服务
 */
export class CanalNodeControlService extends NodeControlService {
  /**
   * 构造被粘贴组件 Schema
   */
  // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
  public constructPasteComponent(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    clipBoardCopiedComponent: any,
  ) {
    if (!isArray(clipBoardCopiedComponent)) return clipBoardCopiedComponent
    const components = cloneDeep(
      clipBoardCopiedComponent as E2ESchemaComponent[],
    )
    if (
      components.some(
        (component) => component.type === INTERNAL_COMPONENT_TYPE_ROOT,
      )
    ) {
      return null
    }
    const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
    const schema = schemaService.schema
    const allCompIds = getAllIdsOfSchema(schema)
    const usedCompIds = new Set(allCompIds)
    for (const component of components) {
      for (const cd of dfsGenComponentDetailBySchemaComponent<E2ESchema>(
        component,
        [],
        [],
      )) {
        if (usedCompIds.has(cd.component.id)) {
          cd.component.id = createVarComponentId()
          renameComponentById(cd.component)
        }
        usedCompIds.add(cd.component.id)
        // 接口 ID 替换
        if (cd.component.apis?.length) {
          const apiIdMap = new Map<string, string>()
          for (const api of cd.component.apis) {
            const newId = createVarId()
            apiIdMap.set(api.id, newId)
            api.id = newId
          }
          const replaceExprId = (oldId: string): string =>
            apiIdMap.get(oldId) || oldId
          for (const ed of dfsGenExpressionDetailBySchemaComponent(
            cd.component,
            [],
            [],
            {
              ...e2eSchemaTraverseOptions,
              componentDepth: 2,
            },
          )) {
            const { expression } = ed
            if (isApiExpression(expression)) {
              if (isArray(expression.apiId)) {
                expression.apiId = expression.apiId.map(replaceExprId)
              } else {
                expression.apiId = replaceExprId(expression.apiId)
              }
            }
          }
        }
      }
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return components as any
  }

  /**
   * 根据类型获取组件物料数据
   * @param type 组件类型
   */
  protected async getComponentSchemaByType(
    type: string,
  ): Promise<ComponentMaterialSchema | undefined> {
    const canalMaterialService = getInject<CanalMaterialService>(
      MaterialServiceSymbol,
    )
    return canalMaterialService.getComponentSchemaByType(type)
  }
}
