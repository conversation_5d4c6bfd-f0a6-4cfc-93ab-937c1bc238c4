import { schemaServicePluginCanalAPIs } from './schema-service-plugin-canal-apis'
import { schemaServicePluginCanalComponentCodes } from './schema-service-plugin-canal-component-codes'
import { schemaServicePluginCanalComponentCRUD } from './schema-service-plugin-canal-component-crud'
import { schemaServicePluginCanalComponentId } from './schema-service-plugin-canal-component-id'
import { schemaServicePluginCanalEffect } from './schema-service-plugin-canal-effect'
import { schemaServicePluginCanalIIFE } from './schema-service-plugin-canal-iife'
import { schemaServicePluginCanalLinkage } from './schema-service-plugin-canal-linkage'
import { schemaServicePluginCanalModel } from './schema-service-plugin-canal-model'
import { schemaServicePluginCanalProps } from './schema-service-plugin-canal-props'
import { schemaServicePluginCanalPx2 } from './schema-service-plugin-canal-px2'
import { schemaServicePluginCanalTracks } from './schema-service-plugin-canal-tracks'

export * from './schema-service-plugin-canal-apis'
export * from './schema-service-plugin-canal-component-codes'
export * from './schema-service-plugin-canal-component-crud'
export * from './schema-service-plugin-canal-component-id'
export * from './schema-service-plugin-canal-effect'
export * from './schema-service-plugin-canal-iife'
export * from './schema-service-plugin-canal-linkage'
export * from './schema-service-plugin-canal-model'
export * from './schema-service-plugin-canal-props'
export * from './schema-service-plugin-canal-px2'
export * from './schema-service-plugin-canal-tracks'

/**
 * 端到端 SchemaService 插件
 */
export const e2eSchemaServicePlugins = [
  schemaServicePluginCanalAPIs,
  schemaServicePluginCanalComponentCodes,
  schemaServicePluginCanalComponentCRUD,
  schemaServicePluginCanalComponentId,
  schemaServicePluginCanalEffect,
  schemaServicePluginCanalIIFE,
  schemaServicePluginCanalLinkage,
  schemaServicePluginCanalModel,
  schemaServicePluginCanalProps,
  schemaServicePluginCanalPx2,
  schemaServicePluginCanalTracks,
] as const
