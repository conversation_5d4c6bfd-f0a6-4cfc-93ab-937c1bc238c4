import type { E2ESchemaExpression } from '@ad/e2e-schema'
import { isBindableExpression } from '@ad/e2e-schema-utils'
import { PropertyService } from '@kael/designer-property'
import { postConstruct } from '@kael/di'
import { STATIC_EXPRESSION_TYPE, normalizeExpression } from '@kael/schema-utils'
import { isUndefined } from 'lodash'

/**
 * 大运河属性面板服务
 */
export class CanalPropertyService extends PropertyService {
  /**
   * 大运河初始化
   */
  @postConstruct()
  public canalInit(): void {
    const oldRender = this.setterManager.render.bind(this.setterManager)
    this.setterManager.render = ((...args) => {
      const expr = normalizeExpression(args[0].value as E2ESchemaExpression)
      if (isBindableExpression(expr)) {
        const defaultValueExpr = isUndefined(expr.defaultValue)
          ? undefined
          : normalizeExpression(expr.defaultValue)
        const { onChange } = args[0]
        args[0] = {
          ...args[0],
          // 低代码引擎目前混用 static 的 value 和其他表达式作为 value，这是有问题的，但这里还是兼容一下低代码引擎的现有逻辑
          value:
            defaultValueExpr?.type === STATIC_EXPRESSION_TYPE
              ? defaultValueExpr.value
              : expr.defaultValue,
          onChange: (v): void => {
            // value 是混用的，onChange 这里又不是混用的
            return onChange({
              ...expr,
              defaultValue: v,
            })
          },
        }
      }
      return oldRender(...args)
    }) as typeof this.setterManager.render
  }
}
