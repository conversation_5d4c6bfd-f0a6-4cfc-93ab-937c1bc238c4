import type { E2ESchema, Linkage } from '@ad/e2e-schema'
import {
  SchemaCode,
  SchemaEffect,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaReturnType,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import { dfsGenComponentDetailBySchema } from '@kael/schema-utils'
import { intersection, mapValues } from 'lodash'

/**
 * SchemaService 大运河联动插件的扩展属性
 */
export interface SchemaServicePluginCanalLinkagePropertiesExt {
  /**
   * 更新部分联动
   * @param linkage 联动
   */
  updatePartialLinkage: (linkage: Partial<Linkage>) => SchemaReturnType<void>
  /**
   * 删除组件后清洗联动
   */
  clearLinkageAfterDeleteComponent: () => void
}

/**
 * SchemaService 大运河联动插件
 */
export const schemaServicePluginCanalLinkage: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalLinkagePropertiesExt
> = {
  id: 'CANAL_LINKAGE',
  extends(): void {
    this.updatePartialLinkage = ((linkage) => {
      if (this._schemaData) {
        this._schemaData = {
          ...this._schemaData,
          linkage: {
            ...this._schemaData?.linkage,
            ...linkage,
            componentDataParams: {
              ...this._schemaData?.linkage?.componentDataParams,
              ...linkage.componentDataParams,
              byRefreshType: {
                ...this._schemaData?.linkage?.componentDataParams
                  ?.byRefreshType,
                ...linkage.componentDataParams?.byRefreshType,
              },
            },
          },
        }
      }
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.updatePartialLinkage
    dynamicApplyMethodDecorator(this, 'updatePartialLinkage', SchemaEffect())

    this.clearLinkageAfterDeleteComponent = (() => {
      if (!this._schemaData?.linkage) {
        return
      }
      const allCompIds = Array.from(
        dfsGenComponentDetailBySchema(this._schemaData),
        (cd) => cd.component.id,
      )
      this._schemaData = {
        ...this._schemaData,
        linkage: {
          ...this._schemaData.linkage,
          autoRefreshByComponent: intersection(
            this._schemaData.linkage.autoRefreshByComponent,
            allCompIds,
          ),
          componentDataParams: {
            common: intersection(
              this._schemaData.linkage.componentDataParams?.common,
              allCompIds,
            ),
            byRefreshType: mapValues(
              this._schemaData.linkage.componentDataParams?.byRefreshType,
              (v) => intersection(v, allCompIds),
            ),
          },
        },
      }
    }) as typeof this.clearLinkageAfterDeleteComponent
  },
}
