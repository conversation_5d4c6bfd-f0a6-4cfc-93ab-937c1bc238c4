import type { E2ESchema, Model } from '@ad/e2e-schema'
import {
  SchemaCode,
  SchemaEffect,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import { action } from 'mobx'

/**
 * SchemaService 大运河模型插件的扩展属性
 */
export interface SchemaServicePluginCanalModelPropertiesExt {
  /**
   * 更新前端模型
   */
  updateFrontModel(model?: Model): void
  /**
   * 更新后端模型
   */
  updateBackModel(model?: Model): void
}

/**
 * SchemaService 大运河模型插件
 */
export const schemaServicePluginCanalModel: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalModelPropertiesExt
> = {
  id: 'CANAL_MODEL',
  extends(): void {
    this.updateFrontModel = action((model) => {
      if (!this._schemaData) {
        return resultFn(SchemaCode.SUCCESS)
      }
      this._schemaData = {
        ...this._schemaData,
      }
      if (model) {
        this._schemaData.model = model
      } else {
        delete this._schemaData.model
      }
      return resultFn(SchemaCode.SUCCESS)
    })
    dynamicApplyMethodDecorator(this, 'updateFrontModel', SchemaEffect())
    this.updateBackModel = action((backModel) => {
      if (!this._schemaData) {
        return resultFn(SchemaCode.SUCCESS)
      }
      this._schemaData = {
        ...this._schemaData,
      }
      if (backModel) {
        this._schemaData.backModel = backModel
      } else {
        delete this._schemaData.backModel
      }
      return resultFn(SchemaCode.SUCCESS)
    })
    dynamicApplyMethodDecorator(this, 'updateBackModel', SchemaEffect())
  },
}
