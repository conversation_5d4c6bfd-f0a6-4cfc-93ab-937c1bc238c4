import type { E2ESchema } from '@ad/e2e-schema'
import type {
  SchemaService,
  SchemaServiceDefaultPropertiesExt,
  SchemaServicePropertiesExtOfPlugins,
} from '@kael/designer-service'
import type { e2eSchemaServicePlugins } from './plugins'

export * from './plugins'
export * from './utils'

/**
 * 端到端 Schema 服务
 */
export type E2ESchemaService = SchemaService<
  E2ESchema,
  SchemaServiceDefaultPropertiesExt<E2ESchema> &
    SchemaServicePropertiesExtOfPlugins<typeof e2eSchemaServicePlugins>
>
