import {
  genAllFieldPropDetailsInLibraries,
  isPx2Prop,
} from '@ad/e2e-material-schema-utils'
import type { E2ESchema, E2ESchemaComponent } from '@ad/e2e-schema'
import {
  MaterialServiceSymbol,
  SchemaCode,
  SchemaEffect,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import {
  decodeComponentType,
  dfsGenComponentDetailBySchema,
  type PropertyPath,
} from '@kael/schema-utils'
import { action } from 'mobx'
import type { CanalMaterialService } from '../../../canal-material-service'

/**
 * 默认的设计宽度
 */
const DEFAULT_DESIGN_WIDTH = 414

/**
 * 默认的根元素字体大小
 */
const DEFAULT_ROOT_ELEMENT_FONT_SIZE = 41.4

/**
 * SchemaService 大运河像素转换插件的扩展属性
 */
export type SchemaServicePluginCanalPx2PropertiesExt = {
  /**
   * 启用 px2vw
   */
  enablePx2vw(): void
  /**
   * 关闭 px2vw
   */
  disablePx2vw(): void
  /**
   * 更新 px2vw 设计宽度
   * @param designWidth 设计宽度
   */
  updatePx2vwDesignWidth(designWidth: number): void
  /**
   * 启用 px2rem
   */
  enablePx2rem(): void
  /**
   * 关闭 px2rem
   */
  disablePx2rem(): void
  /**
   * 更新 px2rem 根元素字体大小
   * @param rootElementFontSize 根元素字体大小
   */
  updatePx2remRootElementFontSize(rootElementFontSize: number): void
  /**
   * 收集像素转换路径
   * @param component 组件
   */
  collectPx2Paths(component: E2ESchemaComponent): void
}

/**
 * SchemaService 大运河像素转换插件
 */
export const schemaServicePluginCanalPx2: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalPx2PropertiesExt
> = {
  id: 'CANAL_PX2',
  extends(): void {
    // px2vw
    this.enablePx2vw = action(() => {
      if (!this._schemaData || this._schemaData.px2vw) {
        return resultFn(SchemaCode.SUCCESS)
      }
      this._schemaData.px2vw = {
        designWidth: DEFAULT_DESIGN_WIDTH,
        propLogicPaths: {},
      }
      delete this._schemaData.px2rem
      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
      }
      for (const cd of dfsGenComponentDetailBySchema(this._schemaData)) {
        this.collectPx2Paths(cd.component)
      }
      return resultFn(SchemaCode.SUCCESS)
    })
    dynamicApplyMethodDecorator(this, 'enablePx2vw', SchemaEffect())
    this.disablePx2vw = action(() => {
      if (!this._schemaData) {
        return resultFn(SchemaCode.SUCCESS)
      }
      delete this._schemaData.px2vw
      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
      }
      return resultFn(SchemaCode.SUCCESS)
    })
    this.updatePx2vwDesignWidth = action((designWidth) => {
      if (!this._schemaData?.px2vw) {
        return resultFn(SchemaCode.SUCCESS)
      }
      this._schemaData.px2vw.designWidth = designWidth
      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
      }
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.updatePx2vwDesignWidth
    dynamicApplyMethodDecorator(this, 'updatePx2vwDesignWidth', SchemaEffect())

    // px2rem
    this.enablePx2rem = action(() => {
      if (!this._schemaData || this._schemaData.px2rem) {
        return resultFn(SchemaCode.SUCCESS)
      }
      this._schemaData.px2rem = {
        rootElementFontSize: DEFAULT_ROOT_ELEMENT_FONT_SIZE,
        propLogicPaths: {},
      }
      delete this._schemaData.px2vw
      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
      }
      for (const cd of dfsGenComponentDetailBySchema(this._schemaData)) {
        this.collectPx2Paths(cd.component)
      }
      return resultFn(SchemaCode.SUCCESS)
    })
    dynamicApplyMethodDecorator(this, 'enablePx2rem', SchemaEffect())
    this.disablePx2rem = action(() => {
      if (!this._schemaData) {
        return resultFn(SchemaCode.SUCCESS)
      }
      delete this._schemaData.px2rem
      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
      }
      return resultFn(SchemaCode.SUCCESS)
    })
    dynamicApplyMethodDecorator(this, 'disablePx2rem', SchemaEffect())
    this.updatePx2remRootElementFontSize = action((rootElementFontSize) => {
      if (!this._schemaData?.px2rem) {
        return resultFn(SchemaCode.SUCCESS)
      }
      this._schemaData.px2rem.rootElementFontSize = rootElementFontSize
      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
      }
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.updatePx2remRootElementFontSize
    dynamicApplyMethodDecorator(
      this,
      'updatePx2remRootElementFontSize',
      SchemaEffect(),
    )

    // 其他
    this.collectPx2Paths = action((component) => {
      const propLogicPaths =
        this._schemaData?.px2vw?.propLogicPaths ||
        this._schemaData?.px2rem?.propLogicPaths
      if (!propLogicPaths || propLogicPaths[component.type]) {
        return
      }
      const canalMaterialService = getInject<CanalMaterialService>(
        MaterialServiceSymbol,
      )
      const materialSchema =
        canalMaterialService.getComponentSchemaByTypeVersion(
          component.type,
          this._schemaData?.componentCodes?.[component.type]?.version,
        )
      if (!materialSchema) return
      const props =
        canalMaterialService.getComponentMaterialPropsMergedByImplements(
          materialSchema,
        )
      const [libName] = decodeComponentType(component.type)
      const paths: PropertyPath[] = []
      for (const { filedProp, path } of genAllFieldPropDetailsInLibraries({
        [libName]: {
          ...canalMaterialService.getComponentLibraryMaterialSchema(libName),
          components: [
            {
              ...materialSchema,
              props,
            },
          ],
        },
      })) {
        if (isPx2Prop(filedProp)) {
          paths.push(path)
        }
      }
      propLogicPaths[component.type] = paths
    })
  },
}
