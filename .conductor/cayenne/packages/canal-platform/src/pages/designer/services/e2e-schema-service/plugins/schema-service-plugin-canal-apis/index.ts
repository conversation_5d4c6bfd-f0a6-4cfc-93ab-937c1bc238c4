import type { E2EAPI, E2ESchema } from '@ad/e2e-schema'
import {
  SchemaCode,
  SchemaEffect,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaReturnType,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import { nanoid } from 'nanoid'

/**
 * SchemaService 大运河接口插件的扩展属性
 */
export interface SchemaServicePluginCanalAPIsPropertiesExt {
  /**
   * 获取组件接口
   * @param componentId 组件 id
   */
  getComponentAPIs: (componentId: string) => E2EAPI[] | undefined
  /**
   * 添加组件接口
   * @param componentId 组件 id
   * @param api 接口
   */
  addComponentAPI: (
    componentId: string,
    api: Omit<E2EAPI, 'id'>,
  ) => SchemaReturnType<void>
  /**
   * 更新组件接口
   * @param componentId 组件 id
   * @param api 接口
   */
  updateComponentAPI: (
    componentId: string,
    api: E2EAPI,
  ) => SchemaReturnType<void>
  /**
   * 删除组件接口
   * @param componentId 组件 id
   * @param apiId 接口 id
   */
  deleteComponentAPI: (
    componentId: string,
    apiId: string,
  ) => SchemaReturnType<void>
}

/**
 * SchemaService 大运河接口插件
 */
export const schemaServicePluginCanalAPIs: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalAPIsPropertiesExt
> = {
  id: 'CANAL_APIS',
  extends(): void {
    this.getComponentAPIs = ((componentId) => {
      const component = this._schemaDataMap.get(componentId)
      return component?.apis
    }) as typeof this.getComponentAPIs
    this.addComponentAPI = ((componentId, api) => {
      const component = this._schemaDataMap.get(componentId)
      if (!component) {
        console.error('addComponentAPI 组件不存在', componentId, api)
        return resultFn(SchemaCode.INVALID_TARGETID)
      }
      if (!component.apis) {
        component.apis = []
      }
      component.apis.push({
        id: nanoid(),
        ...api,
      })
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.addComponentAPI
    dynamicApplyMethodDecorator(this, 'addComponentAPI', SchemaEffect())
    this.updateComponentAPI = ((componentId, api) => {
      const component = this._schemaDataMap.get(componentId)
      if (!component) {
        console.error('updateComponentAPI 组件不存在', componentId, api)
        return resultFn(SchemaCode.INVALID_TARGETID)
      }
      component.apis = component.apis?.map((a) => (a.id === api.id ? api : a))
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.updateComponentAPI
    dynamicApplyMethodDecorator(this, 'updateComponentAPI', SchemaEffect())
    this.deleteComponentAPI = ((componentId, apiId) => {
      const component = this._schemaDataMap.get(componentId)
      if (!component) {
        console.error('deleteComponentAPI 组件不存在', componentId, apiId)
        return resultFn(SchemaCode.INVALID_TARGETID)
      }
      component.apis = component.apis?.filter((a) => a.id !== apiId)
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.deleteComponentAPI
    dynamicApplyMethodDecorator(this, 'deleteComponentAPI', SchemaEffect())
  },
}
