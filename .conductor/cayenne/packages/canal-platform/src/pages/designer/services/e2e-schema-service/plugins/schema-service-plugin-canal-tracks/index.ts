import { deleteByPath } from '@ad/canal-shared'
import type { E2ESchema, Track } from '@ad/e2e-schema'
import { e2eSchemaTraverseOptions } from '@ad/e2e-schema-utils'
import {
  SchemaCode,
  SchemaEffect,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import {
  dfsGenExpressionDetailBySchema,
  normalizeExpression,
} from '@kael/schema-utils'
import { action } from 'mobx'

/**
 * SchemaService 大运河埋点插件的扩展属性
 */
export type SchemaServicePluginCanalTracksPropertiesExt = {
  /**
   * 更新埋点
   */
  updateTracks: (tracks?: Track[]) => void
}

/**
 * SchemaService 大运河埋点插件
 */
export const schemaServicePluginCanalTracks: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalTracksPropertiesExt
> = {
  id: 'CANAL_TRACKS',
  extends(): void {
    this.updateTracks = action((tracks) => {
      if (!this._schemaData) {
        return resultFn(SchemaCode.SUCCESS)
      }
      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
        tracks,
      }
      const trackIdSet = new Set((tracks || []).map((t) => t.id))
      for (const ed of dfsGenExpressionDetailBySchema(
        this._schemaData,
        e2eSchemaTraverseOptions,
      )) {
        const expr = normalizeExpression(ed.expression)
        if (expr.type === 'track' && !trackIdSet.has(expr.trackId)) {
          deleteByPath(this._schemaData, ed.path)
        }
      }
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.updateTracks
    dynamicApplyMethodDecorator(this, 'updateTracks', SchemaEffect())
  },
}
