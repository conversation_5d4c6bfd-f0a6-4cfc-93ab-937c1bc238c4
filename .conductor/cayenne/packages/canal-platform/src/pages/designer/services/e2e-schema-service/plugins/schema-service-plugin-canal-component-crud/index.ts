import type { E2ESchema, E2ESchemaComponent } from '@ad/e2e-schema'
import { renameAllComponentsAndFormatId } from '@ad/e2e-schema-utils'
import {
  AsyncSchemaEffect,
  MaterialServiceSymbol,
  SchemaCode,
  SchemaEffect,
  StateService,
  StateServiceSymbol,
  dynamicApplyMethodDecorator,
  resultFn,
  type BaseSchemaComponent,
  type ChildComponentPosition,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import type { NormalizeComponentPositionOptions } from '@kael/designer-service/esm/schema-service/types/component-library'
import { getInject } from '@kael/di'
import { cloneDeep, get, isEqual, isNumber } from 'lodash'
import { action, runInAction } from 'mobx'
import type { CanalMaterialService } from '../../../canal-material-service'
import { getSchemaServiceRawFn } from '../../utils'
import type { SchemaServicePluginCanalComponentCodesPropertiesExt } from '../schema-service-plugin-canal-component-codes'
import type { SchemaServicePluginCanalLinkagePropertiesExt } from '../schema-service-plugin-canal-linkage'
import type { SchemaServicePluginCanalPx2PropertiesExt } from '../schema-service-plugin-canal-px2'

/**
 * SchemaService 大运河组件增删改查插件的扩展属性
 */
export type SchemaServicePluginCanalComponentCRUDPropertiesExt =
  SchemaServicePluginCanalComponentCodesPropertiesExt &
    SchemaServicePluginCanalLinkagePropertiesExt &
    SchemaServicePluginCanalPx2PropertiesExt

/**
 * SchemaService 大运河组件增删改查插件
 */
export const schemaServicePluginCanalComponentCRUD: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalComponentCRUDPropertiesExt
> = {
  id: 'CANAL_COMPONENT_CRUD',
  extends(): void {
    const oldCreateComponent = getSchemaServiceRawFn(this.createComponent).bind(
      this,
    )
    this.createComponent = (async (...args) => {
      const ret = await oldCreateComponent(...args)
      renameAllComponentsAndFormatId(ret)
      this.collectPx2Paths(ret)
      return ret
    }) as typeof this.createComponent
    this._addComponent = (async (
      component,
      { parentId, toPosition, callback } = {},
    ) => {
      const clonedComp = cloneDeep(component)
      // 处理粘贴等场景
      renameAllComponentsAndFormatId(clonedComp)
      this.collectPx2Paths(clonedComp)
      const componentStore = this._rebuildSchemaData(clonedComp)
      parentId = parentId || this._rootId //默认根节点id
      const parentSchema = this._schemaDataMap.get(
        parentId,
      ) as BaseSchemaComponent
      if (callback && !callback(parentSchema as E2ESchemaComponent)) {
        return resultFn(SchemaCode.INVALID_PARENTID)
      }
      toPosition = toPosition ?? (parentSchema?.children?.length || 0)
      const position =
        await this._schemaLibrary.normalizeChildComponentPosition({
          childPosition: toPosition,
          parentType: parentSchema.type,
        })

      // 引擎关于 undo/redo、mobx 相关的扩展性比较差，这里复制了 _addComponent 的代码来修改
      const pId = parentId
      runInAction(() => {
        this._schemaLibrary.addComponent(
          componentStore as BaseSchemaComponent,
          parentSchema,
          position,
        )
        this._schemaIdMap.set(componentStore.id, pId)
        this._schemaDataMap.set(componentStore.id, componentStore)

        this.regenerateComponentCodes()

        // 强制触发联动面板更新
        if (this._schemaData) {
          this._schemaData = {
            ...this._schemaData,
          }
        }
      })

      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this._addComponent
    dynamicApplyMethodDecorator(this, '_addComponent', AsyncSchemaEffect())
    const oldDeleteComponent = getSchemaServiceRawFn(this.deleteComponent).bind(
      this,
    )
    this.deleteComponent = action((...args) => {
      const ret = oldDeleteComponent(...args)
      this.regenerateComponentCodes()
      this.clearLinkageAfterDeleteComponent()

      // 强制触发联动面板更新
      if (this._schemaData) {
        this._schemaData = {
          ...this._schemaData,
        }
      }
      const stateService = getInject<StateService>(StateServiceSymbol)
      const cmpId = args[0]
      if (stateService.selectedComponentId === cmpId) {
        stateService.setSelectedComponentId('')
      }
      return ret
    }) as typeof this.deleteComponent
    dynamicApplyMethodDecorator(this, 'deleteComponent', SchemaEffect())

    // this._schemaLibrary.moveComponent 的位置比对有问题，这里覆盖修一下
    this._schemaLibrary.moveComponent = async function (
      component: BaseSchemaComponent,
      fromParent: BaseSchemaComponent,
      toParent: BaseSchemaComponent,
      childPosition: ChildComponentPosition | number,
    ): Promise<boolean> {
      // console.log('debug _schemaLibrary.moveComponent', {
      //   component,
      //   fromParent,
      //   toParent,
      //   childPosition,
      // })
      let toPosition = await this.normalizeChildComponentPosition({
        childPosition,
        parentType: toParent.type,
      })
      const targetPosition = this.calcComponentPosition(component, fromParent)
      // 同一组件下移动
      if (fromParent.id === toParent.id) {
        if (isEqual(targetPosition, toPosition)) {
          return false
        }
        // 下标不同
        if (isSiblingPosition(targetPosition, toPosition, fromParent)) {
          if (targetPosition.index === toPosition.index - 1) {
            return false
          }
          if (targetPosition.index < toPosition.index) {
            toPosition = {
              ...toPosition,
              index: toPosition.index - 1,
            }
          }
        }
      }
      this._removeChildComponent(fromParent, targetPosition)
      this.addComponent(component, toParent, toPosition)
      return true
    }
    const oldNormalizeChildComponentPosition =
      this._schemaLibrary.normalizeChildComponentPosition.bind(
        this._schemaLibrary,
      )
    // this._schemaLibrary.normalizeChildComponentPosition 默认位置会设定到插槽里，导致 children 里的拖拽失效，这里覆盖修一下
    this._schemaLibrary.normalizeChildComponentPosition = async (
      options: NormalizeComponentPositionOptions,
    ): Promise<ChildComponentPosition> => {
      const { childPosition, parentType } = options
      if (!isNumber(childPosition)) {
        return childPosition
      }
      const canalMaterialService = getInject<CanalMaterialService>(
        MaterialServiceSymbol,
      )
      const materialSchema =
        canalMaterialService.getComponentSchemaByTypeVersion(
          parentType,
          this._schemaData?.componentCodes?.[parentType]?.version,
        )
      if (materialSchema?.implements?.container) {
        return {
          type: 'children',
          index: childPosition,
        }
      }
      return oldNormalizeChildComponentPosition(options)
    }
  },
}

/**
 * 是同级位置
 * @param pa 位置 A
 * @param pb 位置 B
 * @param parent 父组件
 */
function isSiblingPosition(
  pa: ChildComponentPosition,
  pb: ChildComponentPosition,
  parent: BaseSchemaComponent,
): boolean {
  if (pa.type === 'children' && pb.type === 'children') {
    return true
  } else if (pa.type === 'props' && pb.type === 'props') {
    let pathA = pa.propPath
    let pathB = pb.propPath
    if (isEqual(pathA, pathB)) {
      return true
    } else {
      // 理论上这里应该返回 false，但引擎的代码有点问题，这里会同时出现逻辑路径和 JSON 路径
      // 逻辑路径：['extra']
      // JSON 路径：['extra', 'value', 0, 'value']
      if (pathA.length > pathB.length) {
        ;[pathA, pathB] = [pathB, pathA]
      }
      if (
        pathA.length === pathB.length - 3 &&
        isEqual(pathA, pathB.slice(0, -3)) &&
        pathB[pathB.length - 1] === 'value' &&
        pathB[pathB.length - 3] === 'value' &&
        isNumber(pathB[pathB.length - 2]) &&
        get(parent.props, [...pathA, 'type']) === 'array' &&
        get(parent.props, [
          ...pathA,
          pathB[pathB.length - 3],
          pathB[pathB.length - 2],
          'type',
        ]) === 'component'
      ) {
        return true
      }
      return false
    }
  }
  return false
}
