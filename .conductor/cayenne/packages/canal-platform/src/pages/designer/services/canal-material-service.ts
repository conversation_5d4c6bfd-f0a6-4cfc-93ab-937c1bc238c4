import { BIZ_COMPONENT_LIB_NAME } from '@ad/canal-react-runtime'
import type {
  E2ECMSFieldProp,
  E2ERemoteComponentLibraryMaterialSchema,
  E2ERemoteComponentMaterialSchema,
} from '@ad/e2e-material-schema'
import {
  OLD_BIZ_COMPONENT_LIB_NAME,
  genAllFieldPropDetailsInLibraries,
  mergeComponentLibWithImplementProps,
} from '@ad/e2e-material-schema-utils'
import { MaterialService, SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import type {
  CMSFieldProp,
  CMSProp,
  CMSSetter,
  RemoteComponentLibraryMaterialSchema,
  RemoteComponentMaterialSchema,
} from '@kael/material-schema'
import {
  decodeLibType,
  encodeComponentType,
  encodeLibType,
  getMaterialLibsSchema,
  type MaterialLibsSchema,
} from '@kael/material-utils'
import type { Schema } from '@kael/schema'
import { decodeComponentType } from '@kael/schema-utils'
import { flatten, isEqual, mapValues } from 'lodash'
import type { E2ESchemaService } from './e2e-schema-service'

/**
 * 大运河物料服务
 */
export class CanalMaterialService extends MaterialService {
  public constructor(...args: ConstructorParameters<typeof MaterialService>) {
    super(...args)

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(window as any).debugCanalMaterialService = this
    }
  }

  /**
   * 物料 Schema 缓存
   */
  private _getMaterialLibsSchemaPromiseCache: Promise<MaterialLibsSchema> | null =
    null

  /**
   * 组件类型 -> 值属性
   */
  private _valuePropByComponentType = new Map<string, E2ECMSFieldProp>()

  /**
   * 获取物料库 Schema，重写 MaterialService 中的方法
   * @param schema 搭建 schema 信息
   * @param force 物料强制不缓存
   * @returns 物料库 Schema
   */
  public async getMaterialLibsSchema<T extends Schema>(
    schema: T,
    force = false,
  ): Promise<MaterialLibsSchema> {
    if (!this._getMaterialLibsSchemaPromiseCache) {
      this._getMaterialLibsSchemaPromiseCache = getMaterialLibsSchema(
        this.getMaterialLibInfo,
        schema,
        force ? {} : this._materialLibsSchemaCache,
      )
        .then((materialLibsSchema) => {
          this.storeValueTipAndClearValueProp(materialLibsSchema)
          this._materialLibsSchemaCache = materialLibsSchema
          this._getMaterialLibsSchemaPromiseCache = null
          return materialLibsSchema
        })
        .catch((error) => {
          console.error('获取物料 Schema 失败', error)
          throw new Error(error)
        })
    }

    return this._getMaterialLibsSchemaPromiseCache
  }

  /**
   * 通过组件类型获取 value 属性的提示信息
   * @param componentType 组件类型
   * @returns 提示信息
   */
  public getValueTipByComponentType(componentType: string): string | undefined {
    const tip = this.getValueProp(componentType)?.tip
    return tip && `${tip}`
  }

  /**
   * 获取值属性
   * @param componentType 组件类型
   */
  public getValueProp(componentType: string): E2ECMSFieldProp | null {
    return this._valuePropByComponentType.get(componentType) || null
  }

  /**
   * 存储 value 的提示信息，并删除物料 Schema 中的 value 属性
   * @param schema 物料 Schema
   * @returns
   */
  public storeValueTipAndClearValueProp(
    materialLibs: MaterialLibsSchema,
  ): void {
    const valuePropMap = new Map<string, E2ECMSFieldProp>()
    const valuePropPath = ['value']
    const storeAndClear = (
      props: CMSProp<CMSSetter>[],
      materialLibName: string,
      componentType: string,
      groupPath: string[],
    ): void => {
      const [libraryName, version] = decodeLibType(materialLibName)
      props?.forEach((field, index) => {
        if (
          field.type !== 'group' &&
          isEqual(flatten([...groupPath, field.path]), valuePropPath)
        ) {
          valuePropMap.set(
            encodeComponentType(libraryName, componentType, version),
            field,
          )
          props.splice(index, 1)
        }
      })
    }
    for (const {
      libraryName,
      component,
      groupProps,
      path,
    } of genAllFieldPropDetailsInLibraries(materialLibs)) {
      if (isEqual(path, valuePropPath)) {
        const parentProps = groupProps.length
          ? // 处理在属性在 group 中的情况
            groupProps[groupProps.length - 1].items
          : // 处理属性不在 group 中的情况，几乎不可能，但 Schema 定义上是允许的
            component.props || []
        storeAndClear(
          parentProps,
          libraryName,
          component.type,
          path.slice(0, -1),
        )
      }
    }
    this._valuePropByComponentType = valuePropMap
  }

  /**
   * 通过组件类型获取组件库展示名
   * @param componentType 组件类型
   */
  public getLibDisplayNameByComponentType(componentType: string): string {
    const [libName, , version] = decodeComponentType(componentType)
    const materialKey = encodeLibType(libName, version)
    const materialLibSchema = this._materialLibsSchemaCache[materialKey]
    return materialLibSchema.displayName as string
  }

  /**
   * 根据物料标识获取物料 Schema
   * @param type 物料标识
   * @param versionType 版本类型
   */
  public getComponentSchemaByType(
    type: string,
    versionType?: 'latest' | 'old',
  ): RemoteComponentMaterialSchema | undefined {
    if (!type) {
      return
    }
    const [libName, componentType] = decodeComponentType(type)
    let finalLibName = libName
    if (finalLibName === BIZ_COMPONENT_LIB_NAME && versionType !== 'latest') {
      if (versionType === 'old') {
        finalLibName = OLD_BIZ_COMPONENT_LIB_NAME
      } else {
        const schemaService = getInject<E2ESchemaService>(SchemaServiceSymbol)
        const currentComponentCode =
          schemaService._schemaData?.componentCodes?.[type]
        const compMateril = this.getComponentSchemaByName(
          this._materialLibsSchemaCache[BIZ_COMPONENT_LIB_NAME],
          componentType,
        ) as E2ERemoteComponentMaterialSchema | undefined
        if (
          currentComponentCode?.version &&
          currentComponentCode.version !== compMateril?.version
        ) {
          finalLibName = OLD_BIZ_COMPONENT_LIB_NAME
        }
      }
    }
    const materialLibSchema = this._materialLibsSchemaCache[finalLibName]
    if (!materialLibSchema) {
      console.error(`type=${type},根据物料标识获取物料${libName}失败`)
      return
    }
    return this.getComponentSchemaByName(materialLibSchema, componentType)
  }

  /**
   * 根据组件类型、版本获取组件物料 Schema
   * @param type 组件类型
   * @param version 版本
   */
  public getComponentSchemaByTypeVersion(
    type: string,
    version?: string,
  ): E2ERemoteComponentMaterialSchema | undefined {
    if (!type) {
      return
    }
    const [libName, componentType] = decodeComponentType(type)
    if (libName === BIZ_COMPONENT_LIB_NAME) {
      const compMateril = this.getComponentSchemaByName(
        this._materialLibsSchemaCache[BIZ_COMPONENT_LIB_NAME],
        componentType,
      ) as E2ERemoteComponentMaterialSchema | undefined
      return this.getComponentSchemaByType(
        type,
        !version || compMateril?.version === version ? 'latest' : 'old',
      )
    }
    return this.getComponentSchemaByType(type, 'latest')
  }

  /**
   * 获取组件库物料 Schema
   * @param libName 组件库名
   */
  public getComponentLibraryMaterialSchema(
    libName: string,
  ): RemoteComponentLibraryMaterialSchema {
    return this._materialLibsSchemaCache[libName]
  }

  /**
   * 缓存：字段属性 -> 逻辑路径
   */
  private _fieldPropToLogicPathCache: WeakMap<CMSFieldProp, string[]> | null =
    null

  /**
   * 根据字段属性获取逻辑路径
   * @param fieldProp 字段属性
   */
  public getLogicPathByFieldProp(fieldProp: CMSFieldProp): string[] | null {
    let cache = this._fieldPropToLogicPathCache
    if (!cache) {
      const c = (cache = this._fieldPropToLogicPathCache = new WeakMap())
      for (const { path, filedProp } of genAllFieldPropDetailsInLibraries(
        mapValues(
          this._materialLibsSchemaCache as Record<
            string,
            E2ERemoteComponentLibraryMaterialSchema
          >,
          mergeComponentLibWithImplementProps,
        ),
      )) {
        c.set(filedProp, path)
      }
    }
    return cache.get(fieldProp) || null
  }

  /**
   * 获取合并的组件物料 Schema
   * @param type 组件类型
   * @param version 版本
   */
  public getMergedComponentMaterialSchema(
    type: string,
    version?: string,
  ): E2ERemoteComponentMaterialSchema | null {
    const materialSchema = this.getComponentSchemaByTypeVersion(type, version)
    if (!materialSchema) return null
    const props =
      this.getComponentMaterialPropsMergedByImplements(materialSchema)
    return {
      ...materialSchema,
      props,
    }
  }
}
