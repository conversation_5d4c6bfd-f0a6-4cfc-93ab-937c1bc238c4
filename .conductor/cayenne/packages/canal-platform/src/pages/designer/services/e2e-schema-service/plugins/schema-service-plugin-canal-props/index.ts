import type { E2ESchema, E2ESchemaNormalizedExpression } from '@ad/e2e-schema'
import { type SchemaServicePlugin } from '@kael/designer-service'
import {
  getComponentPropExpression,
  type PropertyPath,
} from '@kael/schema-utils'

/**
 * SchemaService 大运河属性插件的扩展属性
 */
export interface SchemaServicePluginCanalPropsPropertiesExt {
  /**
   * 获取组件属性表达式
   * @param componentId 组件 id
   * @param logicPath 逻辑路径
   */
  getComponentPropExpression: (
    componentId: string,
    logicPath: PropertyPath,
  ) => E2ESchemaNormalizedExpression | null
}

/**
 * SchemaService 大运河属性插件
 */
export const schemaServicePluginCanalProps: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalPropsPropertiesExt
> = {
  id: 'CANAL_PROPS',
  extends(): void {
    this.getComponentPropExpression = ((componentId, logicPath) => {
      const comp = this.findComponentById(componentId)
      if (!comp) {
        return null
      }
      return getComponentPropExpression(comp, logicPath)
    }) as typeof this.getComponentPropExpression
  },
}
