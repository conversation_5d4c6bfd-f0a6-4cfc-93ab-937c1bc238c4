import { replaceLinkageComponentId } from '@/stores/search-store'
import type { E2ESchema } from '@ad/e2e-schema'
import {
  SchemaCode,
  SchemaEffect,
  StateService,
  StateServiceSymbol,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaReturnType,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import { getInject } from '@kael/di'
import { dfsGenComponentDetailBySchema } from '@kael/schema-utils'
import { cloneDeep } from 'lodash'
import { runInAction } from 'mobx'

/**
 * SchemaService 大运河组件ID插件的扩展属性
 */
export interface SchemaServicePluginCanalComponentIdPropertiesExt {
  /** 组件 ID 是否在 schema 中已经存在 */
  isComponentIdExists: (componentId: string) => boolean

  /** 更新组件ID */
  updateComponentId: (
    sourceId: string,
    targetId: string,
  ) => SchemaReturnType<void>

  /** 更新linkage中组件ID */
  updateLinkageComponentId: (sourceId: string, targetId: string) => void
}

/**
 * SchemaService 大运河联动插件
 */
export const schemaServicePluginCanalComponentId: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalComponentIdPropertiesExt
> = {
  id: 'CANAL_COMPONENT_ID',
  extends(): void {
    this.isComponentIdExists = (componentId: string): boolean => {
      let isExists = false
      if (this._schemaData) {
        for (const cd of dfsGenComponentDetailBySchema(this._schemaData)) {
          if (componentId === cd.component.id) {
            isExists = true
            break
          }
        }
      }
      return isExists
    }

    this.updateLinkageComponentId = (
      sourceId: string,
      targetId: string,
    ): void => {
      replaceLinkageComponentId(this._schemaData?.linkage, sourceId, targetId)
    }

    this.updateComponentId = (
      sourceId: string,
      targetId: string,
    ): SchemaReturnType<void> => {
      const stateService = getInject<StateService>(StateServiceSymbol)
      const component = this._schemaDataMap.get(sourceId)

      if (component && this._schemaData) {
        runInAction(() => {
          component.id = targetId
          this._rebuildSchemaData(component)
          this._schemaIdMap.set(
            targetId,
            this._schemaIdMap.get(sourceId) as string,
          )
          this._cleanDeleteComponent(sourceId)
          this.updateLinkageComponentId(sourceId, targetId)
          stateService.setSelectedComponentId(targetId)

          // 更新componentId后，强制触发联动面板更新
          if (this._schemaData) {
            this._schemaData = {
              ...this._schemaData,
              // 触发linkage面板更新选中项
              linkage: cloneDeep(this._schemaData.linkage),
            }
          }
        })
      }
      return resultFn(SchemaCode.SUCCESS)
    }

    dynamicApplyMethodDecorator(this, 'updateComponentId', SchemaEffect())
  },
}
