import { StateService } from '@kael/designer-service'
import { throttle } from 'lodash'

/**
 * 大运河状态服务
 */
export class CanalStateService extends StateService {
  /**
   * 模块工作量
   */
  private _moduleWorkload = 0

  /**
   * 节流发送操作心跳，默认以 60 秒节流
   */
  public throttleSendOperationHeartbeat = throttle(
    this.sendOperationHeartbeat.bind(this),
    60 * 1000,
  )

  /**
   * 发送操作心跳
   */
  public sendOperationHeartbeat(): void {
    this._moduleWorkload += 60 * 1000
    console.log(
      'CanalStateService::_moduleWorkload',
      this._moduleWorkload,
      this,
    )
  }

  /**
   * 获取并清空模块工作量
   */
  public getAndClearModuleWorkload(): number {
    const ret = this._moduleWorkload
    this._moduleWorkload = 0
    return ret
  }
}
