import type { E2ESchema, E2ESchemaExpressionJS } from '@ad/e2e-schema'
import {
  SchemaCode,
  SchemaEffect,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaReturnType,
  type SchemaServicePlugin,
} from '@kael/designer-service'

/**
 * SchemaService 大运河副作用插件的扩展属性
 */
export interface SchemaServicePluginCanalEffectPropertiesExt {
  /**
   * 获取组件副作用
   * @param componentId 组件 id
   */
  getComponentEffect: (componentId: string) => E2ESchemaExpressionJS | undefined
  /**
   * 更新组件副作用
   * @param componentId 组件 id
   * @param effect 副作用
   */
  updateComponentEffect: (
    componentId: string,
    effect?: E2ESchemaExpressionJS,
  ) => SchemaReturnType<void>
}

/**
 * SchemaService 大运河副作用插件
 */
export const schemaServicePluginCanalEffect: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalEffectPropertiesExt
> = {
  id: 'CANAL_EFFECT',
  extends(): void {
    this.getComponentEffect = ((componentId) => {
      const component = this._schemaDataMap.get(componentId)
      return component?.effect
    }) as typeof this.getComponentEffect
    this.updateComponentEffect = ((componentId, effect) => {
      const component = this._schemaDataMap.get(componentId)
      if (!component) {
        console.error('updateComponentEffect 组件不存在', componentId, effect)
        return resultFn(SchemaCode.INVALID_TARGETID)
      }
      component.effect = effect
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.updateComponentEffect
    dynamicApplyMethodDecorator(this, 'updateComponentEffect', SchemaEffect())
  },
}
