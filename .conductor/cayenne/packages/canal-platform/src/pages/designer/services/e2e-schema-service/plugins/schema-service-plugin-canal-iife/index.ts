import type { E2ESchema, E2ESchemaExpression } from '@ad/e2e-schema'
import {
  SchemaCode,
  SchemaEffect,
  dynamicApplyMethodDecorator,
  resultFn,
  type SchemaServicePlugin,
} from '@kael/designer-service'
import { action } from 'mobx'

/**
 * SchemaService 大运河立即执行的函数表达式插件的扩展属性
 */
export type SchemaServicePluginCanalIIFEPropertiesExt = {
  /**
   * 更新立即执行的函数表达式
   */
  updateIIFE: (iife?: E2ESchemaExpression) => void
}

/**
 * SchemaService 大运河立即执行的函数表达式插件
 */
export const schemaServicePluginCanalIIFE: SchemaServicePlugin<
  E2ESchema,
  SchemaServicePluginCanalIIFEPropertiesExt
> = {
  id: 'CANAL_IIFE',
  extends(): void {
    this.updateIIFE = action((iife) => {
      if (!this._schemaData) {
        return resultFn(SchemaCode.SUCCESS)
      }
      // 强制触发属性面板更新
      this._schemaData = {
        ...this._schemaData,
        iife,
      }
      return resultFn(SchemaCode.SUCCESS)
    }) as typeof this.updateIIFE
    dynamicApplyMethodDecorator(this, 'updateIIFE', SchemaEffect())
  },
}
