import { sleepFrame } from '@ad/canal-shared'
import type { E2ESchema } from '@ad/e2e-schema'
import { SchemaServiceSymbol } from '@kael/designer-service'
import { getInject } from '@kael/di'
import type { AnyFn } from '@kael/schema-utils'
import { action, observable } from 'mobx'
import type { E2ESchemaService } from '..'

/**
 * 获取 SchemaService 原始函数，装饰器作用前的
 * @param fn 函数
 */
export function getSchemaServiceRawFn<T extends AnyFn>(fn: T): T {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (fn as any).oldFn || fn
}

/**
 * 当 Schema 发生变化时，触发
 * @param cb 回调函数
 */
export async function onSchemaChange(
  cb: (schema: E2ESchema | null) => void,
): Promise<void> {
  let schemaService: E2ESchemaService | null
  while (
    !(schemaService = getInject<E2ESchemaService | null>(SchemaServiceSymbol))
  ) {
    await sleepFrame()
  }
  cb(schemaService.schema)
  schemaService.onChanged(cb)
}

const schemaBox = observable.box<E2ESchema | null>(null)
// 确保 schema 不会原地修改
onSchemaChange(action((schema) => schemaBox.set(schema ? { ...schema } : null)))

/**
 * 获取可观察的 Schema 文件
 */
export function getObSchema(): E2ESchema | null {
  return schemaBox.get()
}

/**
 * 等待初始化 Schema
 */
export async function waitInitSchema(): Promise<E2ESchema> {
  let schema: E2ESchema | null | undefined
  while (
    !(schema = getInject<E2ESchemaService | null>(SchemaServiceSymbol)?.schema)
  ) {
    await sleepFrame()
  }
  return schema
}
