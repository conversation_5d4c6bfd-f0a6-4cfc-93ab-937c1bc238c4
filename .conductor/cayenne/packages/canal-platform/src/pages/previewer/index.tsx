import {
  disableAssertWeblog,
  disableCaptureException,
} from '@ad/canal-react-runtime'
import { observer } from 'mobx-react-lite'
import { useMemo, useRef, type FC } from 'react'
import { ErrorBoundary, type FallbackProps } from 'react-error-boundary'
import { previewerStore } from './previewer-store'
import { RuntimeWrapper } from './runtime-wrapper'

disableCaptureException()
disableAssertWeblog()

/**
 * 预览器
 */
export const Previewer: FC = observer(() => {
  const FallbackComponent = useMemo(() => {
    const FallbackComp: FC<FallbackProps> = ({ error }) => {
      return <div>预览出错：{error instanceof Error ? error.message : ''}</div>
    }
    return FallbackComp
  }, [])
  const refErrorBoundary = useRef<ErrorBoundary>(null)
  const { serverSchema, setting } = previewerStore
  return (
    <ErrorBoundary FallbackComponent={FallbackComponent} ref={refErrorBoundary}>
      {serverSchema && setting && (
        <RuntimeWrapper serverSchema={serverSchema} setting={setting} />
      )}
    </ErrorBoundary>
  )
})

export default Previewer
