import {
  loadConfigTransformForDesignerRuntime,
  loadDependenciesForDesignerRuntime,
  loadExtraPluginsForDesignerRuntime,
  setRootElementFontSizeForPx2rem,
  type ConfigTransform,
} from '@/utils'
import { createQuickEncoder } from '@ad/canal-shared'
import { INTERNAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import type { E2EServerSchema } from '@ad/e2e-schema'
import type { PurePlugin } from '@kael/runtime'
import { type MaybePromiseApis } from '@kael/shared'
import { createEndpoint, fromInsideIframe } from '@remote-ui/rpc'
import { makeObservable, observable, runInAction } from 'mobx'
import type { PreviewSetting } from '../designer/extensions/canvas/canvas-previewer/preview-setting'
import { SubmoduleForSimulator } from '../simulator/plugins/plugin-dependencies-override/components'
import type {
  DesignerForPreviewerRpc<PERSON><PERSON>,
  PreviewerForDesignerRpcApis,
} from './types'

/**
 * 预览器仓库
 */
export class PreviewerStore {
  /**
   * RPC 终端
   */
  private readonly _rpcEndpoint = createEndpoint<DesignerForPreviewerRpcApis>(
    fromInsideIframe(),
    { createEncoder: createQuickEncoder },
  )

  /**
   * 端到端后端 Schema 文件
   */
  private _serverSchema: E2EServerSchema | null = null

  /**
   * 端到端后端 Schema 文件
   */
  public get serverSchema(): typeof this._serverSchema {
    return this._serverSchema
  }

  /**
   * 预览配置
   */
  private _setting: PreviewSetting | null = null

  /**
   * 预览配置
   */
  public get setting(): typeof this._setting {
    return this._setting
  }

  /**
   * 额外插件
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private _extraPlugins: PurePlugin<any, any>[] = []

  /**
   * 额外插件
   */
  public get extraPlugins(): typeof this._extraPlugins {
    return this._extraPlugins
  }

  /**
   * 依赖
   */
  private _dependencies: Record<string, unknown> = {}

  /**
   * 依赖
   */
  public get dependencies(): typeof this._dependencies {
    return this._dependencies
  }

  /**
   * 配置转换
   */
  private _configTransform!: ConfigTransform

  /**
   * 配置转换
   */
  public get configTransform(): typeof this._configTransform {
    return this._configTransform
  }

  /**
   * 预览器仓库
   */
  public constructor() {
    makeObservable<this, '_serverSchema' | '_setting'>(this, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _serverSchema: observable.ref,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _setting: observable.ref,
    })
    const apis: MaybePromiseApis<PreviewerForDesignerRpcApis> = {
      setServerSchema: this._setServerSchema.bind(this),
    }
    this._rpcEndpoint.expose(apis)
    window.addEventListener('beforeunload', () => {
      this._rpcEndpoint.call.beforeUnload()
    })
  }

  /**
   * 加载额外插件
   */
  private async _loadExtraPlugins(): Promise<void> {
    this._extraPlugins = await loadExtraPluginsForDesignerRuntime()
  }

  /**
   * 加载依赖
   */
  private async _loadDependencies(): Promise<void> {
    const dependencies = await loadDependenciesForDesignerRuntime()
    this._dependencies = {
      ...dependencies,
      [INTERNAL_COMPONENT_LIB_NAME]: {
        Submodule: SubmoduleForSimulator,
      },
    }
  }

  /**
   * 加载配置转换
   */
  private async _loadConfigTransform(): Promise<void> {
    this._configTransform = await loadConfigTransformForDesignerRuntime()
  }

  /**
   * 设置 Schema 文件
   * @param serverSchema 端到端后端 Schema 文件
   */
  private async _setServerSchema(
    serverSchema: E2EServerSchema,
    setting: PreviewSetting,
  ): Promise<void> {
    // console.log('PreviewerStore::_setServerSchema', serverSchema, setting)
    setRootElementFontSizeForPx2rem(serverSchema)
    await Promise.all([
      this._loadExtraPlugins(),
      this._loadDependencies(),
      this._loadConfigTransform(),
    ])
    runInAction(() => {
      this._serverSchema = serverSchema
      this._setting = setting
    })
  }
}

/**
 * 单例：预览器仓库
 */
export const previewerStore = new PreviewerStore()
