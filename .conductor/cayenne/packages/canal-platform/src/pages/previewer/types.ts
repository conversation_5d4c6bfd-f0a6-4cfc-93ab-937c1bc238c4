import type { E2EServerSchema } from '@ad/e2e-schema'
import type { PreviewSetting } from '../designer/extensions/canvas/canvas-previewer/preview-setting'

/**
 * 预览器为设计器提供的 RPC 接口
 */
export interface PreviewerForDesignerRpcApis {
  /**
   * 设置端到端后端 Schema 文件
   * @param serverSchema 端到端后端 Schema 文件
   */
  setServerSchema(serverSchema: E2EServerSchema, setting: PreviewSetting): void
}

/**
 * 设计器为预览器提供的 RPC 接口
 */
export interface DesignerForPreviewerRpcApis {
  /**
   * 预览器卸载前，页面跳转前
   */
  beforeUnload(): void
}
