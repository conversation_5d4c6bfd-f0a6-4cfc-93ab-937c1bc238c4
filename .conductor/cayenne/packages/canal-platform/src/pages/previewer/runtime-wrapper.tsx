import type { TransformedConfig } from '@/utils'
import {
  AutoLoading,
  CanalReactRuntimeFC,
  createPreviewFetchSchema,
} from '@ad/canal-react-runtime'
import { deleteUndefinedValues } from '@ad/canal-shared'
import type { E2EServerSchema } from '@ad/e2e-schema'
import { uniqueId } from 'lodash'
import { memo, useMemo, type ComponentProps, type FC } from 'react'
import styled from 'styled-components'
import { DESIGNER_QUERY, IS_DEBUG_MODE } from '../designer/constants'
import type { PreviewSetting } from '../designer/extensions/canvas/canvas-previewer/preview-setting'
import { debugComponentJsUrlMapLoader } from '../designer/stores/debug-mode-store/debug-component-js-url-map-loader'
import { previewerStore } from './previewer-store'

/**
 * 运行时包装属性
 */
export interface RuntimeWrapperProps {
  /**
   * 端到端后端 Schema 文件
   */
  serverSchema: E2EServerSchema
  /**
   * 预览配置
   */
  setting: PreviewSetting
}
/**
 * 运行时包装
 */
export const RuntimeWrapper: FC<RuntimeWrapperProps> = memo(
  ({ serverSchema, setting }) => {
    const fakeSchemaId = useMemo(() => {
      void serverSchema
      return uniqueId('preview-')
    }, [serverSchema])
    const fetchSchema = useMemo(() => {
      let headers = setting.headers[setting.env]
      headers = {
        ...headers,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'CanalP-Host': headers['Host'] || headers['host'],
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'CanalP-Origin': headers['Origin'] || headers['origin'],
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'CanalP-Referer': headers['Referer'] || headers['referer'],
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'CanalP-Cookie': headers['Cookie'] || headers['cookie'],
      }
      deleteUndefinedValues(headers)
      let componentJsUrlMap = setting.componentJsUrlMap
      if (IS_DEBUG_MODE && DESIGNER_QUERY.localCl) {
        componentJsUrlMap = {
          ...debugComponentJsUrlMapLoader.load(),
          // 手动配置的优先
          ...componentJsUrlMap,
        }
      }
      return createPreviewFetchSchema(serverSchema, {
        headers,
        componentJsUrlMap,
      })
    }, [serverSchema, setting])
    const { extraPlugins, dependencies, configTransform } = previewerStore
    const config = useMemo(
      () =>
        configTransform({
          dependencies,
          params: setting.params,
        } satisfies ComponentProps<
          typeof StyledCanalReactRuntimeFC
        >['config'] as TransformedConfig),
      [configTransform, dependencies, setting.params],
    )
    return (
      <StyledCanalReactRuntimeFC
        key={fakeSchemaId}
        schemaId={fakeSchemaId}
        fetchSchema={fetchSchema}
        env={setting.env}
        mock={setting.mock}
        Loading={AutoLoading}
        config={config}
        plugins={extraPlugins}
        params={config.params}
      />
    )
  },
)

const StyledCanalReactRuntimeFC = styled(CanalReactRuntimeFC)`
  min-height: 100px;
`
