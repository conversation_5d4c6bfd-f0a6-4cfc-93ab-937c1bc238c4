import DomainSelect from '@/components/domain-select'
import { copyComponent } from '@/services/backend/component'
import { NormalCopyLine } from '@m-ui/icons'
import { Button, message, Tooltip } from '@m-ui/react'
import React, { useState } from 'react'

interface ShareComponentProps {
  id: string
  domainCode: string
}

const ShareComponent: React.FC<ShareComponentProps> = (props) => {
  const [selectModelVisible, setSelectModelVisible] = useState(false)
  const openModal = (): void => {
    setSelectModelVisible(true)
  }
  const CopyToOtherDomain = async (targetDomainCode: string): Promise<void> => {
    console.log(targetDomainCode)
    const res = await copyComponent({
      targetDomainCode,
      components: [props.id],
    })
    if (res.result === 1) {
      message.info('执行成功')
    } else {
      message.error(res.msg || '拷贝失败')
    }
  }

  return (
    <>
      <Tooltip placement="top" title={'点击复制去其他业务域'}>
        <Button
          shape="circle"
          icon={<NormalCopyLine />}
          onClick={openModal}
        ></Button>
      </Tooltip>
      <DomainSelect
        visible={selectModelVisible}
        onClose={(): void => {
          setSelectModelVisible(false)
        }}
        onSelect={(selectedDomainCode: string): void => {
          if (!!selectedDomainCode && selectedDomainCode !== props.domainCode) {
            CopyToOtherDomain(selectedDomainCode)
          }
        }}
        msg={'请确认你有目标业务域的权限，若资源地址完全一致则不会执行复制'}
      />
    </>
  )
}

export default ShareComponent
