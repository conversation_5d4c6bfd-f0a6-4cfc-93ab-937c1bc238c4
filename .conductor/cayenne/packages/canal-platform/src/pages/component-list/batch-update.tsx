import {
  getComponentDetail,
  getStructureVersionInfo,
  updateComponentUsingPost,
} from '@/services/backend/component'
import {
  getMaterialLatestVersion,
  getMaterialPlatformComponentDetail,
} from '@/services/material/material-platform'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { getComponentGroupName } from '@ad/e2e-material-schema-utils'
import {
  Button,
  Divider,
  Form,
  Modal,
  Row,
  Typography,
  message,
} from '@m-ui/react'
import { get, isArray, pick } from 'lodash'
import React, { useState } from 'react'
import ComponentSelector from './component-selector'
import LibrarySelector, { type LibrarySelectorValue } from './library-selector'
const { Text, Title } = Typography
const { confirm } = Modal
interface BatchUpdateProps {
  domainCode: string | null
}

export interface associatedComponentItem {
  id: string
  associatedComponentVersion: string
  associatedComponentName: string
  version: string
  name: string
}

export interface BatchComponentItemInfo {
  associatedComponents: associatedComponentItem[]
  materialId: number
  packageName: string
  version: string
}

const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
}

const BatchUpdate: React.FC<BatchUpdateProps> = ({ domainCode }) => {
  const [batchUpdateLoading, setBatchUpdateLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [batchComponentInfo, setBatchComponentInfo] = useState<
    BatchComponentItemInfo[]
  >([])
  const [targetLibraryId, setTargetLibraryId] = useState<number>()
  const [targetLibraryVersion, setTargetLibraryVersion] = useState<string>()
  const [form] = Form.useForm()

  const handleBatchUpdate = async (): Promise<void> => {
    if (!domainCode) return
    // Add batch update logic here
    setBatchUpdateLoading(true)
    try {
      const res = await getStructureVersionInfo({
        domainCode: domainCode,
      })
      if (res.result === 1 && res.data) {
        const libraryArr = Object.keys(res.data)
        if (libraryArr?.length) {
          //获取到每个物料库最新的版本来判断是否需要升级
          const latestVersionRes = await getMaterialLatestVersion({
            materialParams: libraryArr.map((item) => ({
              materialId: Number(item),
            })) as { materialId: number }[],
          })
          if (
            latestVersionRes.result === 1 &&
            Array.isArray(latestVersionRes.data)
          ) {
            //组合数据
            const mergedData: BatchComponentItemInfo[] =
              latestVersionRes.data.map((material) => {
                const key = String(material.materialId)
                const associatedComponents = res.data[key] || []
                return {
                  ...material,
                  associatedComponents, // 添加组件列表
                }
              })
            setBatchComponentInfo(mergedData)
            setModalVisible(true)
          }
        } else {
          message.info('没有查到相关组件信息')
        }
      }
    } catch (error) {
      console.error(error)
    } finally {
      setBatchUpdateLoading(false)
    }
  }

  //   const onLibraryChange = (value: number): void => {
  //     setTargetLibraryId(value)
  //   }

  const closeModal = (): void => {
    setModalVisible(false)
    form.resetFields()
    setTargetLibraryId(undefined)
    setTargetLibraryVersion(undefined)
  }

  const onLibrarySelectorChange = (value: LibrarySelectorValue): void => {
    if (value.libraryId !== targetLibraryId) {
      form.setFieldsValue({ components: undefined })
    }
    value.libraryId && setTargetLibraryId(value.libraryId)
    value.version && setTargetLibraryVersion(value.version)
  }

  const submitForm = (): void => {
    form.validateFields().then((values) => {
      confirmInfo(values)
    })
  }

  const confirmInfo = (values: {
    library: {
      libraryId: number
      version: string
    }
    components: string[]
  }): void => {
    const target = batchComponentInfo.find(
      (item) => item.materialId === values.library.libraryId,
    )
    if (!target) return
    const arr = values?.components?.map?.((value) => {
      return target.associatedComponents?.find(
        (component) => component.id === value,
      ) as associatedComponentItem
    })

    if (target) {
      confirm({
        title:
          '请确认以下升级组件,将优先使用物料侧配置对组件进行升级，升级完成后可在组件详情页查看信息',
        content: (
          <div>
            {arr.map((component) => {
              if (component) {
                return (
                  <>
                    <Row>
                      <Title level={5}>
                        【{component.name}】-id：{component.id}
                      </Title>
                    </Row>
                    <Row>
                      <Text type="success">
                        从{component.associatedComponentVersion}升级至
                        {targetLibraryVersion}
                      </Text>
                    </Row>
                    <Row>
                      <Text type="secondary">
                        关联的物料名称：{component.associatedComponentName}
                      </Text>
                    </Row>
                    <Divider />
                  </>
                )
              }
            })}
          </div>
        ),
        okText: '确定',
        cancelText: '取消',
        width: 800,
        onOk() {
          closeModal()
          setBatchUpdateLoading(true)
          updateComponents(
            arr.map((item) => ({
              id: item.id,
              name: item.associatedComponentName,
              version: values.library.version,
              libraryId: target.materialId,
            })),
          )
        },
        onCancel() {
          console.log('Cancel')
        },
      })
    }
  }

  const updateComponents = async (
    components: Array<{
      id: string
      libraryId: number
      version: string
      name: string
    }>,
  ): Promise<void> => {
    // 存储所有的更新结果
    const promises = components.map((component) =>
      updateOne(
        component.id,
        component.libraryId,
        component.version,
        component.name,
      ),
    )

    const results = await Promise.allSettled(promises)

    // 收集失败的提示信息
    const failedUpdates = results
      .filter((result) => result.status === 'rejected')
      .map((result: PromiseRejectedResult) => {
        const { id, canalName } = result.reason // 提取 id 和 canalName
        return { id, canalName } // 返回一个对象
      })

    // 如果有失败的更新，展示提示
    if (failedUpdates.length > 0) {
      showFailModal(failedUpdates)
    } else {
      message.success('所有更新成功!')
    }
    setBatchUpdateLoading(false)
  }

  const showFailModal = (
    failedUpdates: Array<{ id: string; canalName?: string }>,
  ): void => {
    const modalContent = failedUpdates
      .map((update) => `ID: ${update.id}, 名称: ${update.canalName || '无'}`)
      .join('\n')

    Modal.error({
      title: '以下更新操作失败',
      content: modalContent,
    })
  }
  const updateOne = async (
    id: string,
    libraryId: number,
    version: string,
    name: string,
  ): Promise<{ id: string; canalName?: string } | undefined> => {
    const resp = await getComponentDetail({ id })
    if (resp.result === 1 && resp.data) {
      const ret = await getMaterialPlatformComponentDetail({
        materialId: libraryId,
        version: version,
        componentName: name,
      })
      if (ret.result !== 1 || !ret.data.schema) {
        return Promise.reject({
          id,
        })
      }
      const schema = ret.data.schema as E2ERemoteComponentMaterialSchema
      let jsUrl = get(schema, 'code.code.js') as string | string[] | undefined
      if (isArray(jsUrl)) {
        jsUrl = jsUrl[0]
      }
      const exportIdentifier = get(schema, 'code.exportIdentifier') as
        | string
        | undefined
      let group = resp.data.group
      if (ret.data.lowCodeData.groups && schema.type) {
        group =
          getComponentGroupName(ret.data.lowCodeData.groups, schema.type) ||
          undefined
      }

      const updateData = {
        coverUrl: schema.icon || resp.data.coverUrl,
        resourceUrl: jsUrl,
        propsConfig: JSON.stringify(
          {
            ...pick(schema, 'props', 'implements'),
            ...(exportIdentifier
              ? {
                  code: {
                    exportIdentifier,
                  },
                }
              : {}),
          },
          null,
          2,
        ),
        name: `${schema.name}`,
        type: resp.data.type,
        descs:
          (schema.description && `${schema.description}`) || resp.data.descs,
        isContainer: !!schema.implements?.container,
        group: group,
        associatedComponentId: JSON.stringify([libraryId, name]),
        associatedComponentVersion: version,
      }
      const updateRes = await updateComponentUsingPost({ ...updateData, id })
      if (updateRes?.result === 1) {
        return Promise.resolve({
          id,
        })
      } else {
        return Promise.reject({
          id,
          canalName: resp.data.name,
        })
      }
    }
  }

  return (
    <>
      <Modal
        title="批量升级"
        visible={modalVisible}
        onOk={(): void => {
          submitForm()
        }}
        onCancel={(): void => {
          closeModal()
        }}
        width={1300}
        maskClosable={false}
        destroyOnClose={true}
      >
        <>
          <Form {...layout} form={form} name="componentChoose">
            <Form.Item
              name="library"
              label="目标物料库和升级的目标版本"
              rules={[
                { required: true, message: '要升级的关联物料库和对应的版本' },
              ]}
            >
              <LibrarySelector
                batchComponentInfo={batchComponentInfo}
                onChange={onLibrarySelectorChange}
              />
            </Form.Item>
            {!!targetLibraryId && !!targetLibraryVersion && (
              <Form.Item
                name="components"
                label="选择组件"
                rules={[
                  { required: true, message: '需要至少一个可升级的组件' },
                ]}
              >
                <ComponentSelector
                  selectedVersion={targetLibraryVersion}
                  componentArr={
                    batchComponentInfo.find(
                      (item) => item.materialId === targetLibraryId,
                    )?.associatedComponents || []
                  }
                />
              </Form.Item>
            )}
          </Form>
        </>
      </Modal>
      <Button loading={batchUpdateLoading} onClick={handleBatchUpdate}>
        批量升级
      </Button>
    </>
  )
}

export default BatchUpdate
