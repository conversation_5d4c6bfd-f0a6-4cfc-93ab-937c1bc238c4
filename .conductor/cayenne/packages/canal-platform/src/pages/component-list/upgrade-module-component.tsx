import {
  doStagingDeploy,
  getGlobalModuleDetail,
  updateModuleUsingPost,
  uploadComponentVersionOfModule,
} from '@/services/backend/module'
import { BIZ_COMPONENT_LIB_NAME } from '@ad/canal-react-runtime'
import { upgradeComponentProps } from '@ad/e2e-material-schema-utils'
import type { E2EServerSchema } from '@ad/e2e-schema'
import { SyncOutlined, SystemArrowDoubleUpLine } from '@m-ui/icons'
import { Tooltip, message } from '@m-ui/react'
import React, { useState } from 'react'
interface Props {
  moduleId: string
  moduleVersion: number
  componentId: string
  latestVersion: number
  currentVersion: number
  callback: (moduleId: string) => void
  domainCode: string
}

const UpgradeModuleComponent: React.FC<Props> = (props) => {
  const [loading, setLoading] = useState<boolean>(false)
  const handleUpgrade = async (): Promise<void> => {
    // 处理升级逻辑
    setLoading(true)
    const { callback, ...restProps } = props
    const res = await uploadComponentVersionOfModule({ ...restProps })
    if (res.result === 1) {
      // 有人编辑
      if (res.data.editingRoom) {
        const user = res.data.editingRoom.user
        message.warning(
          user.userName + user.userCode + '正在编辑此页面，暂不能变更',
        )
      } else if (res.data.schema && res.data.newest && res.data.old) {
        const schema: E2EServerSchema = JSON.parse(
          res.data.schema?.content || '',
        )
        const componentCodes: E2EServerSchema['componentCodes'] = {
          ...schema.componentCodes,
        }
        /**
         * 更新 componentCode字段
         */
        const newestJsonPart = JSON.parse(res.data.newest.propsConfig)
        const componentType = BIZ_COMPONENT_LIB_NAME + '::' + props.componentId
        componentCodes[componentType] = {
          code: {
            js: res.data.newest.resourceUrl,
          },
          exportIdentifier:
            newestJsonPart?.code?.type === 'low-code'
              ? undefined
              : newestJsonPart.code?.exportIdentifier,
          version: `${res.data.newest.version}`,
        }
        schema.componentCodes = componentCodes
        // 更新 props
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const upgradeComps: any[] = []
        schema.flattenedView.components.forEach((item) => {
          if (item.type === componentType) {
            upgradeComps.push(item)
          }
        })
        upgradeComponentProps(
          upgradeComps,
          JSON.parse(res.data.old.propsConfig) || {},
          JSON.parse(res.data.newest.propsConfig) || {},
        )

        const updateRes = await updateModuleUsingPost({
          id: props.moduleId,
          version: props.moduleVersion + '',
          content: JSON.stringify(schema),
        })
        if (updateRes.result === 1) {
          // 加快提示
          message.success(
            '升级成功, 若想撤销该动作，可在编辑页的编辑记录中撤销',
            1,
          )
          callback && callback(props.moduleId)
          const globalModuleDetailRes = await getGlobalModuleDetail({
            domainCode: props.domainCode,
          })
          let globalModuleId: string | undefined =
            globalModuleDetailRes.data?.id // 可能不存在
          if (globalModuleId === props.moduleId) {
            globalModuleId = undefined
          }
          doStagingDeploy({
            module: {
              moduleId: props.moduleId,
              version: props.moduleVersion + '',
              content: JSON.stringify(schema),
              stage: 'staging',
              globalModuleId,
            },
            isNewDeploy: false,
          })
        } else {
          message.error(updateRes.msg || '更新失败')
        }
      }
    } else {
      message.error(res.msg || '执行失败')
    }
    setLoading(false)
  }

  if (props.currentVersion >= props.latestVersion) return null

  return (
    <>
      {!loading && (
        <Tooltip
          title={
            '点击升级到' +
            props.latestVersion +
            '版本, 一但升级该模块引用的该组件将全部升级'
          }
        >
          <SystemArrowDoubleUpLine
            style={{
              cursor: 'pointer',
              color: '#fa8c16',
              fontWeight: 'bold',
              fontSize: 16,
            }}
            onClick={handleUpgrade}
          />
        </Tooltip>
      )}
      {!!loading && (
        <Tooltip title={'升级中'}>
          <SyncOutlined
            spin
            style={{
              color: '#fa8c16',
              fontWeight: 'bold',
              fontSize: 16,
            }}
          />
        </Tooltip>
      )}
    </>
  )
}

export default UpgradeModuleComponent
