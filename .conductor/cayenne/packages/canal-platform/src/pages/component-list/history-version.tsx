import { getComponentDetail } from '@/services/backend/component'
import type { ComponentDetail } from '@/services/backend/models'
import {
  Button,
  Drawer,
  Form,
  Select,
  Spin,
  Tooltip,
  message,
} from '@m-ui/react'
import { useEffect, useState } from 'react'
import styled from 'styled-components'

interface HistoryVersionProps {
  componentId: string
  currentVersion: number
  buttonText?: string
}

/**
 * 组件历史版本展示
 * 提供一个按钮，点击后打开抽屉，可以通过下拉框选择不同版本查看组件历史信息
 */
const HistoryVersion: React.FC<HistoryVersionProps> = ({
  componentId,
  currentVersion,
  buttonText = '历史版本',
}) => {
  const [visible, setVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedVersion, setSelectedVersion] = useState<number>(currentVersion)
  const [versionOptions, setVersionOptions] = useState<
    { label: string; value: number }[]
  >([])
  const [componentDetail, setComponentDetail] =
    useState<ComponentDetail | null>(null)

  // 生成版本选项
  useEffect(() => {
    if (currentVersion && visible) {
      // 根据当前版本生成所有可能的历史版本选项
      const options = Array.from({ length: currentVersion }, (unused, i) => ({
        label: `版本 ${i + 1}${i + 1 === currentVersion ? ' (当前)' : ''}`,
        value: i + 1,
      }))
      setVersionOptions(options)

      // 默认选中当前版本
      setSelectedVersion(currentVersion)

      // 加载当前版本的组件详情
      fetchComponentDetail(currentVersion)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentVersion, visible])

  // 获取组件详情
  const fetchComponentDetail = async (version: number): Promise<void> => {
    if (!componentId) return

    setLoading(true)
    try {
      const response = await getComponentDetail({
        id: componentId,
        version: version.toString(),
      })

      if (response.result === 1) {
        setComponentDetail(response.data)
      } else {
        message.error(`获取组件版本详情失败: ${response.msg}`)
      }
    } catch (error) {
      console.error('获取组件版本详情出错:', error)
      message.error('获取组件版本详情失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理版本变更
  const handleVersionChange = (value: number): void => {
    setSelectedVersion(value)
    fetchComponentDetail(value)
  }

  // 打开抽屉
  const showDrawer = (): void => {
    setVisible(true)
  }

  // 关闭抽屉
  const onClose = (): void => {
    setVisible(false)
    setComponentDetail(null)
    setSelectedVersion(currentVersion)
    setVersionOptions([])
  }

  return (
    <>
      <Tooltip title="查看组件历史版本">
        <Button type="link" onClick={showDrawer} size="small">
          {buttonText}
        </Button>
      </Tooltip>

      <Drawer
        title="组件历史版本"
        placement="right"
        width={600}
        onClose={onClose}
        visible={visible}
        destroyOnClose
      >
        <Form layout="vertical">
          <Form.Item label="选择版本">
            <Select
              value={selectedVersion}
              onChange={handleVersionChange}
              options={versionOptions}
              style={{ width: '100%' }}
              placeholder="请选择版本"
            />
          </Form.Item>
        </Form>

        {loading ? (
          <LoadingContainer>
            <Spin size="large" tip="加载中..." />
          </LoadingContainer>
        ) : componentDetail ? (
          <DetailContainer>
            <DetailItem>
              <Label>组件名称:</Label>
              <Value>{componentDetail.name}</Value>
            </DetailItem>
            <DetailItem>
              <Label>组件类型:</Label>
              <Value>
                {componentDetail.type === 0
                  ? 'Web'
                  : componentDetail.type === 1
                  ? 'H5'
                  : componentDetail.type === 2
                  ? 'RN'
                  : 'Native'}
              </Value>
            </DetailItem>
            <DetailItem>
              <Label>组件描述:</Label>
              <Value>{componentDetail.descs || '-'}</Value>
            </DetailItem>
            <DetailItem>
              <Label>是否容器组件:</Label>
              <Value>{componentDetail.isContainer ? '是' : '否'}</Value>
            </DetailItem>
            <DetailItem>
              <Label>组件分类:</Label>
              <Value>{componentDetail.group || '-'}</Value>
            </DetailItem>
            <DetailItem>
              <Label>关联物料ID:</Label>
              <Value>{componentDetail.associatedComponentId || '-'}</Value>
            </DetailItem>
            <DetailItem>
              <Label>关联物料版本:</Label>
              <Value>{componentDetail.associatedComponentVersion || '-'}</Value>
            </DetailItem>
            <DetailItem>
              <Label>资源URL:</Label>
              <Value>
                {componentDetail.resourceUrl ? (
                  <a
                    href={componentDetail.resourceUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {componentDetail.resourceUrl}
                  </a>
                ) : (
                  '-'
                )}
              </Value>
            </DetailItem>
            <DetailItem>
              <Label>属性配置:</Label>
              <ConfigValue>
                <pre>{componentDetail.propsConfig || '-'}</pre>
              </ConfigValue>
            </DetailItem>
          </DetailContainer>
        ) : (
          <EmptyContainer>请选择一个版本查看详情</EmptyContainer>
        )}
      </Drawer>
    </>
  )
}

export default HistoryVersion

// 样式组件
const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
`

const EmptyContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  font-size: 16px;
`

const DetailContainer = styled.div`
  padding: 0 16px;
`

const DetailItem = styled.div`
  margin-bottom: 16px;
  display: flex;
`

const Label = styled.div`
  width: 120px;
  color: #666;
  flex-shrink: 0;
`

const Value = styled.div`
  flex: 1;
  word-break: break-all;
`

const ConfigValue = styled.div`
  flex: 1;

  pre {
    background-color: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
    max-height: 400px;
    overflow: auto;
    margin: 0;
  }
`
