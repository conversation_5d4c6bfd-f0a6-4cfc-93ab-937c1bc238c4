import {
  getMaterialVersions,
  type TreeNode,
} from '@/services/material/material-platform'
import { Col, Row, Select, TreeSelect } from '@m-ui/react'
import React, { useEffect, useState } from 'react'
import { type BatchComponentItemInfo } from './batch-update'

export interface LibrarySelectorValue {
  libraryId?: number
  version?: string
}

interface Props {
  value?: LibrarySelectorValue
  onChange?: (value: LibrarySelectorValue) => void
  batchComponentInfo: BatchComponentItemInfo[]
}

const LibrarySelector: React.FC<Props> = ({
  value,
  onChange,
  batchComponentInfo,
}) => {
  const [libraryId, setLibraryId] = useState<number>()
  const [libraryVersion, setLibraryVersion] = useState<string>()
  const [libraryVersionTree, setLibraryVersionTree] = useState<TreeNode[]>([])

  const triggerChange = (changedValue: {
    libraryId?: number
    version?: string
  }): void => {
    if (
      changedValue.libraryId !== undefined ||
      changedValue.version !== undefined
    ) {
      onChange?.({ ...value, ...changedValue })
    }
  }

  const onLibraryChange = (selectedLibraryId: number): void => {
    setLibraryId(selectedLibraryId)
    const selectedItem = batchComponentInfo.find(
      (item) => item.materialId === selectedLibraryId,
    )
    if (selectedItem) {
      setLibraryVersion(selectedItem.version)
      triggerChange({
        libraryId: selectedLibraryId,
        version: selectedItem?.version,
      })
    }
  }
  useEffect(() => {
    if (libraryId) {
      getVersions(libraryId)
    }
  }, [libraryId])
  const getVersions = async (id: number): Promise<void> => {
    const res = await getMaterialVersions({
      materialId: id,
      dataType: 'tree',
    })
    if (res.result === 1) {
      setLibraryVersionTree(res.data)
    }
  }

  // 递归处理 treeData，给非叶子节点加上 `disabled: true`
  const disableNonLeafNodes = (nodes: TreeNode[]): TreeNode[] => {
    if (nodes.length === 0) return []
    return nodes.map((node) => ({
      ...node,
      disabled: !!node.children?.length, // 如果有 children，则禁用
      children: node.children ? disableNonLeafNodes(node.children) : [],
    }))
  }

  return (
    <>
      <Row>
        <Col>
          <Select
            placeholder="选择要升级的关联物料库"
            onChange={onLibraryChange}
            style={{ width: 250 }}
            value={libraryId}
          >
            {batchComponentInfo.map((item) => (
              <Select.Option
                value={item.materialId}
                key={item.materialId + item.version}
              >
                {item.packageName}
              </Select.Option>
            ))}
          </Select>
        </Col>
        <Col>
          <TreeSelect
            placeholder="选择版本"
            treeData={disableNonLeafNodes(libraryVersionTree)}
            style={{ width: 350 }}
            value={libraryVersion}
            onChange={(version: string): void => {
              setLibraryVersion(version)
              triggerChange({
                version: version,
              })
            }}
            treeDefaultExpandAll
          />
        </Col>
      </Row>
      <span style={{ fontSize: 12 }}>
        选择物料库后默认填充当前最新版本值，请选择叶子节点
      </span>
    </>
  )
}

export default LibrarySelector
