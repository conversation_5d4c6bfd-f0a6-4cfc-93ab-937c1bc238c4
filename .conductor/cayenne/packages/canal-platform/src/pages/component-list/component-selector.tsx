import { Checkbox, Row, Typography } from '@m-ui/react'
import type { CheckboxChangeEvent } from '@m-ui/react/lib/checkbox'
import React, { useMemo } from 'react'
import semver from 'semver'
import { type associatedComponentItem } from './batch-update'
const { Text, Title } = Typography

interface CheckboxGroupFormItemProps {
  componentArr: associatedComponentItem[]
  selectedVersion: string
  value?: string[]
  onChange?: (value: string[]) => void
}

const ComponentSelector: React.FC<CheckboxGroupFormItemProps> = ({
  componentArr,
  selectedVersion,
  value,
  onChange,
}) => {
  const handleChange = (checkedValues: string[]): void => {
    console.log(checkedValues)
    onChange?.(checkedValues)
  }

  const handleSelectAll = (e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      onChange?.(
        componentArr
          ?.filter((component) => {
            return !(
              semver.compare(
                selectedVersion,
                component.associatedComponentVersion,
              ) <= 0
            )
          })
          .map?.((component) => component.id),
      )
    } else {
      onChange?.([])
    }
  }

  const selectAllDisabled = useMemo(() => {
    return (
      componentArr?.filter((component) => {
        return !(
          semver.compare(
            selectedVersion,
            component.associatedComponentVersion,
          ) <= 0
        )
      })?.length === 0
    )
  }, [componentArr])

  return (
    <>
      <div style={{ padding: '4px 0' }}>
        <Checkbox
          value={value?.length && value?.length === componentArr?.length}
          onChange={handleSelectAll}
          disabled={selectAllDisabled}
        >
          <Title level={5}>&nbsp;&nbsp;全选</Title>
        </Checkbox>
      </div>
      <Checkbox.Group value={value} onChange={handleChange}>
        {componentArr?.map?.((component) => {
          const versionCompareDisable =
            semver.compare(
              selectedVersion,
              component.associatedComponentVersion,
            ) <= 0
          return (
            <Row style={{ marginBottom: 8 }}>
              <Checkbox
                value={component.id}
                key={component.id}
                disabled={versionCompareDisable}
              >
                <Row>
                  <Title level={5}>
                    【{component.name}】-id：{component.id}
                  </Title>
                </Row>
                <Row>
                  <Text type="secondary">
                    关联的物料名称：{component.associatedComponentName}
                  </Text>
                </Row>
                <Row>
                  <Text type="secondary">
                    当前关联的物料版本：{component.associatedComponentVersion}
                    {versionCompareDisable && (
                      <Text type="warning">
                        &nbsp; 版本大于等于当前所选版本，不可选择
                      </Text>
                    )}
                  </Text>
                </Row>
              </Checkbox>
            </Row>
          )
        })}
      </Checkbox.Group>
    </>
  )
}

export default ComponentSelector
