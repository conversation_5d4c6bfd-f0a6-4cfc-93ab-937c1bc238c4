import {
  createComponentUsingPost,
  getStructureVersionInfo,
} from '@/services/backend/component'
import {
  getMaterialDetail,
  getMaterialPlatformComponentDetail,
  getMaterialVersions,
  searchMaterial,
  type Component,
  type TreeNode,
} from '@/services/material/material-platform'
import type { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { getComponentGroupName } from '@ad/e2e-material-schema-utils'
import {
  Button,
  Checkbox,
  Form,
  Modal,
  Row,
  Select,
  Spin,
  Switch,
  TreeSelect,
  message,
} from '@m-ui/react'
import { get, isArray, pick } from 'lodash'
import debounce from 'lodash/debounce'
import React, { useEffect, useRef, useState } from 'react'
interface Props {
  domainCode: string | null
  successCallback: () => void
}

const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
}
const BatchInit: React.FC<Props> = ({ domainCode, successCallback }) => {
  const [loading, setLoading] = useState(false)
  const [componentLoading, setComponentLoading] = useState(false)
  const [fetching, setFetching] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [targetLibraryId, setTargetLibraryId] = useState<number>()
  const [libraryVersion, setLibraryVersion] = useState<string>()
  const [libraryVersionTree, setLibraryVersionTree] = useState<TreeNode[]>([])
  const [materialDetailComponents, setMaterialDetailComponents] = useState<
    Component[]
  >([])
  const [options, setOptions] = useState<
    {
      value: number
      label: string
    }[]
  >([])
  const fetchRef = useRef(0)
  const [form] = Form.useForm()
  const openModal = (): void => {
    setModalVisible(true)
  }

  const debounceFetcher = React.useMemo(() => {
    const loadOptions = async (searchText: string): Promise<void> => {
      fetchRef.current += 1
      const fetchId = fetchRef.current
      setOptions([])
      setFetching(true)
      await searchMaterial({
        keyword: searchText,
        codeType: 'low-code',
      }).then((optionsRes) => {
        if (fetchId !== fetchRef.current) {
          return
        }
        setFetching(false)
        if (optionsRes.result === 1) {
          setOptions(
            optionsRes.data.list.map((option) => ({
              label: `【${option.packageName}】-${option.displayName}`,
              value: option.materialId,
            })) || [],
          )
        }
      })
    }
    return debounce(loadOptions, 800)
  }, [])

  useEffect(() => {
    if (targetLibraryId) {
      getVersions(targetLibraryId)
    }
  }, [targetLibraryId])

  const closeModal = (): void => {
    setModalVisible(false)
    setFetching(false)
    setLibraryVersion(undefined)
    setLibraryVersionTree([])
    setTargetLibraryId(undefined)
    setMaterialDetailComponents([])
    form.resetFields()
  }

  const getVersions = async (id: number): Promise<void> => {
    const res = await getMaterialVersions({
      materialId: id,
      dataType: 'tree',
    })
    if (res.result === 1) {
      setLibraryVersionTree(res.data)
    }
  }
  // 递归处理 treeData，给非叶子节点加上 `disabled: true`
  const disableNonLeafNodes = React.useMemo(() => {
    const recursiveDisable = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.map((node) => ({
        ...node,
        disabled: !!node.children?.length, // 如果有 children，则禁用
        children: node.children ? recursiveDisable(node.children) : [],
      }))
    }
    return recursiveDisable(libraryVersionTree)
  }, [libraryVersionTree])

  // 本业务域已经引用的物料库信息
  const getExistLibraryInfo = async (): Promise<any[]> => {
    if (!domainCode || !targetLibraryId) return []
    const res = await getStructureVersionInfo({
      domainCode: domainCode,
      libraryId: targetLibraryId,
    })
    if (res.result === 1 && res.data) {
      return res.data[targetLibraryId]
    }
    return []
  }
  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      if (targetLibraryId && libraryVersion) {
        setComponentLoading(true)
        const res = await getMaterialDetail({
          materialId: targetLibraryId,
          version: libraryVersion,
        })

        if (
          res.result === 1 &&
          res.data &&
          Array.isArray(res.data.components)
        ) {
          const existArr = await getExistLibraryInfo()

          // 过滤
          let componentArr = res.data.components
          if (Array.isArray(existArr) && existArr.length) {
            componentArr = res.data.components.filter(
              (component) =>
                !existArr.some(
                  (exist) => exist.associatedComponentName === component.name,
                ),
            )
          }

          setMaterialDetailComponents(componentArr)
          // 默认全选
          form?.setFieldValue(
            'selectedComponents',
            componentArr.map((item) => item.name),
          )
          setComponentLoading(false)
        } else {
          setMaterialDetailComponents([])
        }
      }
    }

    fetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [targetLibraryId, libraryVersion])

  const submit = (): void => {
    form.validateFields().then((values) => {
      createComponents(values)
    })
  }

  const create = async (
    name: string,
    libraryId: number,
    version: string,
  ): Promise<{
    id?: string
    name: string
  } | void> => {
    const ret = await getMaterialPlatformComponentDetail({
      materialId: libraryId,
      version: version,
      componentName: name,
    })
    if (ret.result !== 1 || !ret.data.schema) {
      return Promise.reject({
        name: name,
      })
    }
    const schema = ret.data.schema as E2ERemoteComponentMaterialSchema
    let jsUrl = get(schema, 'code.code.js') as string | string[] | undefined
    if (isArray(jsUrl)) {
      jsUrl = jsUrl[0]
    }
    if (!jsUrl) {
      return Promise.reject({
        name: name,
      })
    }
    const exportIdentifier = get(schema, 'code.exportIdentifier') as
      | string
      | undefined
    let group
    if (ret.data.lowCodeData.groups && schema.type) {
      group =
        getComponentGroupName(ret.data.lowCodeData.groups, schema.type) ||
        undefined
    }

    const data = {
      coverUrl: schema.icon,
      resourceUrl: jsUrl,
      propsConfig: JSON.stringify(
        {
          ...pick(schema, 'props', 'implements'),
          ...(exportIdentifier
            ? {
                code: {
                  exportIdentifier,
                },
              }
            : {}),
        },
        null,
        2,
      ),
      name: `${schema.name}`,
      type: 0,
      descs: schema.description && `${schema.description}`,
      isContainer: !!schema.implements?.container,
      group: group,
      associatedComponentId: JSON.stringify([libraryId, name]),
      associatedComponentVersion: version,
    }
    const resp = await createComponentUsingPost({
      ...data,
      businessDomainCode: domainCode as string,
    })
    if (resp?.result === 1) {
      return Promise.resolve({
        id: resp.data.id,
        name: data.name,
      })
    } else {
      return Promise.reject({
        name: data.name,
      })
    }
  }

  const createComponents = async (values: {
    selectedLibrary: number
    selectedVersion: string
    selectedComponents: string[]
  }): Promise<void> => {
    setLoading(true)
    const promises = values.selectedComponents.map((component) =>
      create(component, values.selectedLibrary, values.selectedVersion),
    )
    const results = await Promise.allSettled(promises)
    const failedUpdates = results
      .filter((result) => result.status === 'rejected')
      .map((result: PromiseRejectedResult) => {
        const { name } = result.reason // 提取 id 和 canalName
        return { name } // 返回一个对象
      })
    if (failedUpdates.length > 0) {
      showFailModal(failedUpdates)
    } else {
      message.success('导入成功')
      closeModal()
      successCallback?.()
    }
    setLoading(false)
  }

  const showFailModal = (failedUpdates: Array<{ name?: string }>): void => {
    const modalContent = failedUpdates
      .map((update) => ` ${update.name || ''}`)
      .join('\n')

    Modal.error({
      title: '以下组件导入失败,请检查物料的配置项是否缺失',
      content: modalContent,
    })
  }
  return (
    <>
      <Modal
        title="批量导入"
        visible={modalVisible}
        onOk={(): void => {
          submit()
        }}
        onCancel={(): void => {
          closeModal()
        }}
        confirmLoading={loading}
        width={1000}
        maskClosable={false}
        destroyOnClose={true}
      >
        <Form {...layout} form={form} name="componentSearch">
          <Form.Item
            label="选择组件库"
            name="selectedLibrary"
            rules={[{ required: true, message: '请搜索并选择' }]}
          >
            <Select
              showSearch
              filterOption={false}
              placeholder="请输入关键词进行搜索"
              style={{ width: 300 }}
              notFoundContent={fetching ? <Spin size="small" /> : null}
              onSearch={debounceFetcher}
              options={options}
              onChange={(selectLibraryId: number): void => {
                setTargetLibraryId(selectLibraryId)
                setLibraryVersion(undefined)
                setLibraryVersionTree([])
                setMaterialDetailComponents([])
                form.setFieldsValue({
                  selectedVersion: undefined,
                  selectedComponents: undefined,
                })
              }}
            />
          </Form.Item>
          {!!targetLibraryId && (
            <>
              <Form.Item
                label="选择版本"
                name="selectedVersion"
                rules={[{ required: true, message: '请选择版本' }]}
              >
                <TreeSelect
                  placeholder="选择版本"
                  treeData={disableNonLeafNodes}
                  style={{ width: 350 }}
                  value={libraryVersion}
                  onChange={(version: string): void => {
                    setLibraryVersion(version)
                  }}
                  treeDefaultExpandAll
                />
              </Form.Item>
              {!!libraryVersion && (
                <Spin spinning={componentLoading}>
                  <Form.Item
                    label="选择组件(已去掉已引用的组件）"
                    name="selectedComponents"
                    rules={[
                      { required: true, message: '必须选择至少一个组件' },
                    ]}
                    extra="如组件库已经全量导入，则可选组件为空"
                  >
                    <Checkbox.Group>
                      <Switch
                        checkedChildren="全选"
                        defaultChecked
                        onChange={(checked): void => {
                          if (!checked) {
                            form.setFieldsValue({
                              selectedComponents: [],
                            })
                          } else {
                            form?.setFieldValue(
                              'selectedComponents',
                              materialDetailComponents.map((item) => item.name),
                            )
                          }
                        }}
                      />
                      {materialDetailComponents?.map?.((component) => (
                        <Row>
                          <Checkbox
                            value={component.name}
                            key={component.id + component.name}
                          >
                            {component.name}
                          </Checkbox>
                        </Row>
                      ))}
                    </Checkbox.Group>
                  </Form.Item>
                </Spin>
              )}
            </>
          )}
        </Form>
      </Modal>

      <Button loading={loading} onClick={openModal}>
        批量导入
      </Button>
    </>
  )
}

export default BatchInit
