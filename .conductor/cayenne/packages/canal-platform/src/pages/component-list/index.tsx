import Reference from '@/components/reference'
import { getAllComponentsUsingPost } from '@/services/backend/component'
import { type ComponentDetail } from '@/services/backend/models'
import { history, useSearchParams } from '@kmi/react'
import { SystemArrowMediumRightLine } from '@m-ui/icons'
import {
  Button,
  Empty,
  Form,
  Input,
  Pagination,
  Row,
  Space,
  Tag,
} from '@m-ui/react'
import { useEffect, useState, type FC } from 'react'
import styled from 'styled-components'
import BatchInit from './batch-init'
import BatchUpdate from './batch-update'
import HistoryVersion from './history-version'
import ShareComponent from './share-component'
import UpgradeModuleComponent from './upgrade-module-component'
const TypeMap: {
  [key: string]: string
} = {
  0: 'Web',
  1: 'H5',
  2: 'RN',
  3: 'Native',
}

export const ComponentList: FC = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const domainId = searchParams.get('domainCode')
  const searchName = searchParams.get('name') || ''
  const pageParam = searchParams.get('page')
  const [list, setList] = useState<ComponentDetail[]>([])
  // 初始化页码，如果 URL 中有 page 参数，则使用它
  const [pageNum, setPageNum] = useState(() => {
    return pageParam ? parseInt(pageParam, 10) : 1
  })
  const [total, setTotal] = useState(0)
  const [form] = Form.useForm()
  // 初始化表单值状态，如果 URL 中有 name 参数，则使用它
  const [formValues, setFormValues] = useState(() => {
    return searchName ? { name: searchName } : {}
  })

  const fetchListData = async (): Promise<void> => {
    if (!domainId) return
    try {
      const res = await getAllComponentsUsingPost({
        businessDomainCode: domainId,
        pageSize: 15,
        pageNum,
        keepDeleted: false,
        ...formValues,
      })
      if (res?.result === 1) {
        setList(res.data.list)
        setTotal(res.data.pageInfo.total)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const handlePageChange = (page: number): void => {
    setPageNum(page)
    // 更新 URL 中的页码参数
    const newParams = new URLSearchParams(searchParams)
    if (page > 1) {
      newParams.set('page', page.toString())
    } else {
      newParams.delete('page') // 如果是第一页，删除页码参数以保持 URL 简洁
    }
    setSearchParams(newParams)
  }

  const renderTag = (type: number) => {
    let tagColor = '#87d068'
    switch (type) {
      case 1:
        tagColor = '#1D59F2'
        break
      case 2:
        tagColor = '#2db7f5'
        break
      case 3:
        tagColor = '#108ee9'
    }

    return (
      <Tag color={tagColor} style={{ marginRight: 8 }}>
        {TypeMap[type]}
      </Tag>
    )
  }

  const handleCreate = () =>
    history.push(`/business-domain/component/create?domainCode=${domainId}`)

  const handleEdit = (componentId: string) => {
    // 直接使用当前 URL 的查询参数，保留所有已有的参数
    const currentParams = new URLSearchParams(searchParams)
    // 移除 componentId 参数，因为我们会在新的 URL 中设置它
    currentParams.delete('componentId')
    // 构建基本 URL
    const baseUrl = `/business-domain/component/edit?domainCode=${domainId}&componentId=${componentId}`
    // 如果有其他参数，添加到 URL 中
    const otherParams = currentParams.toString()
    const queryString = otherParams ? `&${otherParams}` : ''

    history.push(`${baseUrl}${queryString}`)
  }

  // 初始化表单显示值
  useEffect(() => {
    // 如果 URL 中有搜索参数，则设置表单显示值
    if (searchName) {
      form.setFieldsValue({ name: searchName })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 监听页码、表单值和 domainId 变化，触发数据加载
  useEffect(() => {
    fetchListData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNum, formValues, domainId])

  const onSearch = (values: { name: string }): void => {
    setPageNum(1)
    setFormValues(values)
    // 更新URL参数
    const newParams = new URLSearchParams(searchParams)
    if (values.name) {
      newParams.set('name', values.name)
    } else {
      newParams.delete('name')
    }
    // 搜索时重置页码参数
    newParams.delete('page')
    setSearchParams(newParams)
  }

  return (
    <Container>
      <Header>
        组件列表
        <Space>
          <BatchInit
            domainCode={domainId}
            successCallback={() => {
              if (pageNum !== 1) {
                setPageNum(1)
              } else {
                fetchListData()
              }
            }}
          />
          <BatchUpdate domainCode={domainId} />
          <Button type="primary" onClick={handleCreate}>
            新增组件
          </Button>
        </Space>
      </Header>
      <Form
        name="component_search"
        layout="inline"
        onFinish={onSearch}
        form={form}
        style={{ marginBottom: '16px' }}
      >
        <Form.Item label="组件名称" name="name">
          <Input
            placeholder="请输入组件名称（模糊查找）"
            style={{ width: 300 }}
          ></Input>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            搜索
          </Button>
          <Button
            style={{ margin: '0 8px' }}
            onClick={(): void => {
              form.resetFields()
              setFormValues({})
              setPageNum(1)
              // 清除URL中的搜索参数和页码参数
              const newParams = new URLSearchParams(searchParams)
              newParams.delete('name')
              newParams.delete('page')
              setSearchParams(newParams)
            }}
          >
            清空
          </Button>
        </Form.Item>
      </Form>
      {list?.length ? (
        <ItemWrapper>
          {list.map((item) => {
            return (
              <Item key={item.id}>
                <Cover style={{ backgroundImage: `url(${item.coverUrl})` }} />
                <Info>
                  <InfoTitle>
                    <Space>{item.name}</Space>
                  </InfoTitle>
                  <InfoOps>
                    <Space style={{ color: '#8a9199' }}>
                      {renderTag(item.type)}
                      <span>ID:{item.id}</span>
                      <span>版本：{item.version}</span>
                    </Space>
                  </InfoOps>
                  <Row justify="end" align="bottom">
                    <Reference
                      componentId={item.id}
                      domainCode={item.businessDomainCode}
                      name={item.name}
                      upgradeComponent={(partProps: {
                        currentVersion: number
                        moduleId: string
                        moduleVersion: number
                        callback: (moduleId: string) => void
                      }): JSX.Element => {
                        return (
                          <UpgradeModuleComponent
                            {...partProps}
                            latestVersion={Number(item.version)}
                            componentId={item.id}
                            domainCode={domainId}
                          />
                        )
                      }}
                    />
                    <HistoryVersion
                      componentId={item.id}
                      currentVersion={Number(item.version)}
                    />
                    <Button
                      type="link"
                      size="small"
                      style={{ fontWeight: 'bold' }}
                      onClick={(): void => handleEdit(item.id)}
                    >
                      编辑 <SystemArrowMediumRightLine />
                    </Button>
                  </Row>
                  <CopyIcon>
                    <ShareComponent id={item.id} domainCode={domainId || ''} />
                  </CopyIcon>
                </Info>
              </Item>
            )
          })}
        </ItemWrapper>
      ) : (
        <EmptyWrapper>
          <Empty />
        </EmptyWrapper>
      )}
      <Footer>
        <Pagination
          current={pageNum}
          pageSize={15}
          total={total}
          showTotal={(total) => `总共${total}个组件`}
          showSizeChanger={false}
          onChange={handlePageChange}
          hideOnSinglePage
        />
      </Footer>
    </Container>
  )
}

export default ComponentList

const Container = styled.div`
  background: #fff;
  border-radius: 4px;
  padding: 16px 20px;
  margin-top: -8px;
`

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 600;
  color: #1d2126;
  margin-bottom: 16px;
`

const ItemWrapper = styled.div`
  display: grid;

  grid-template-columns: repeat(5, minmax(0, 1fr));
  column-gap: 16px;
  row-gap: 16px;
`

const Item = styled.div`
  border-radius: 4px;
  position: relative;
  border: 1px solid #edeff2;
  background-color: #edeff2;
  padding: 0 8px;
`

const Cover = styled.div`
  width: 100%;
  padding-top: 55%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
`

const Info = styled.div`
  background: #fff;
  padding: 12px 16px 4px;
  color: #434343;
  line-height: 22px;
  margin: 0 -8px;
`
const InfoTitle = styled.div`
  margin-bottom: 8px;
  font-weight: 500;
  margin-bottom: 8px;
`

const InfoOps = styled.div`
  display: flex;
  align-items: left;
  justify-content: space-between;
`
const Version = styled.span`
  font-size: 14px;
  color: #8a9199;
  line-height: 20px;
`
const Footer = styled.div`
  margin-top: 16px;
  text-align: right;
`

const EmptyWrapper = styled.div`
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
`
const CopyIcon = styled.div`
  position: absolute;
  top: 8px;
  right: 8px;
`
