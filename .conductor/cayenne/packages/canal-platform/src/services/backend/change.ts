import { type ChangeDeployType } from '@/pages/change/model'
import { type IGreySubmitConfig } from '@/pages/grey/grey-const'
import {
  type ChangePublish,
  type NewestPublish,
} from '@/pages/publish/model.type'
import { request } from '@kmi/react'
import type * as model from './models'
/** change  */
export function getCanalChangeList(
  payload?: model.ChangeListDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      list: any[]
      pageInfo: {
        total: number
        pageSize: number
        pageNum: number
      }
    }
  }>(`/rest/canal/change/list`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** change 创建变更 */
export function createChangeUsingPost(
  payload?: model.CreateChangeDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.CreateOrUpdateChangeResponse
  }>(`/rest/canal/change/create`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

// /** change  */
// export function changeControllerupdateStatus(
//   payload?: model.UpdateStatusChangeDto,
//   options?: { [key: string]: any },
// ) {
//   const data = payload
//   return request<void>(`/rest/canal/change/update/status`, {
//     method: 'post',
//     ...(options || {}),
//     data,
//   })
// }

/** change  */
export function changeControllergetDetail(
  payload: {
    changeId: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: model.ChangeDto
  }>(`/rest/canal/change/detail`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

/** change  */
export function getCanalProcessingChangeList(
  payload: {
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: model.ChangeDto }>(
    `/rest/canal/change/processing/change/list`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}

/** change  deploy list */
export function getCanalChangeDeployList(
  payload: {
    changeId: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: ChangeDeployType[] }>(
    `/rest/canal/change/deploy/list`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}

/** change  do deploy*/
export function doCanalDeploy(
  payload: {
    changeId: string
    stage: string
    lane?: string
    frontLane?: string
    status?: 0 | 1
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: ChangeDeployType }>(
    `/rest/canal/change/deploy/create`,
    {
      method: 'post',
      ...(options || {}),
      data: params,
    },
  )
}

/** change  do deploy*/
export function createPublishService(
  payload: {
    changeId: string
    domainCode: string
    type: number
    status: number
  } & IGreySubmitConfig,
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: { id: number } }>(
    `/rest/canal/change/publish/create`,
    {
      method: 'post',
      ...(options || {}),
      data: params,
    },
  )
}

/** change  */
export function getCanalPublishList(
  payload?: model.ChangeListDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      list: ChangePublish[]
      pageInfo: {
        total: number
        pageSize: number
        pageNum: number
      }
    }
  }>(`/rest/canal/change/publish/list`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** 获取最新的发布单 */
export function getNewestPublish(
  payload?: { domainCode: string },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: NewestPublish
  }>(`/rest/canal/change/publish/newest`, {
    method: 'get',
    ...(options || {}),
    params: data,
  })
}

/** change  */
export function quitPublish(
  payload?: {
    id: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/change/publish/quit`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** change  */
export function doPublish(
  payload?: {
    id: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/change/publish/doPublish`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}
/** change  */
export function startPublish(
  payload?: {
    id: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: ChangePublish
  }>(`/rest/canal/change/publish/start`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** 刷新  */
export async function refreshPublishFlowState(
  payload?: {
    id: number
    flowId: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      state: {
        state: string
        overState: string
      }
    }
  }>(`/rest/canal/change/publish/refresh/flow/state`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}
/**
 * 关闭变更
 * @param payload
 * @param options
 * @returns
 */
export function closeChange(
  payload?: {
    changeId: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/change/close`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** change  */
export function rollBackService(
  payload?: {
    id: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: number
  }>(`/rest/canal/change/publish/rollBack`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function rollBackCurrentService(
  payload?: {
    id: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: string
  }>(`/rest/canal/change/publish/rollBack/current`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** change  */
export function sendMessage(
  payload?: {
    changeAddress: string
    publisher: string
    changeName: string
    type: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: number
  }>(`/rest/canal/change/publish/send/message`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/**
 * 更新变更信息
 * @param payload
 * @param options
 * @returns
 */
export function doChangeInfoRequest(
  payload?: {
    changeId: string
    updateValue: string
    updateKey: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/change/update/info`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/**
 * 部署一个变更的模块的线上版本到prt和beta的主干，并返回线上版本信息
 * @param payload
 * @param options
 * @returns
 */
export function deployOnlineVersionToTrunk(
  payload: {
    changeId: string
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      moduleId: string
      version: number
    }[]
  }>(`/rest/canal/change/deploy/online/version`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}
