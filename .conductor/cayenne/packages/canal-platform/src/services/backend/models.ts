import type { CollaborativeModel } from '@/pages/designer/types'
import type { AiLogStatus } from '@ad/canal-ai'

export interface ComponentListDto {
  /** 业务域code */
  businessDomainCode: string
  /** 页码 */
  pageNum: number
  /** 分页大小 */
  pageSize: number
  /** 保留已删除的，默认为 true */
  keepDeleted?: boolean
}

export interface CreateComponentDto {
  /** 示例图url */
  coverUrl: string
  /** 关联物料中心组件id */
  associatedComponentId?: string
  /** 关联物料中心组件版本 */
  associatedComponentVersion?: string
  /** 资源url */
  resourceUrl?: string
  /** 属性配置 */
  propsConfig?: string
  /** 事件key */
  eventKey?: string
  /** 组件名称 */
  name: string
  /** 组件类型：web-0，H5-1，RN-2，Native-3 */
  type: number
  /** 业务域code */
  businessDomainCode: string
  /** 组件描述 */
  descs?: string
  /** 是否容器组件 */
  isContainer?: boolean
  /** 组件分类，即物料 Schema 里的 group */
  group?: string
}

export interface GetAllComponentsResponseDto {
  list: ComponentDetail[]
  pageInfo: {
    total: number
    ageNum: number
    pageSize: number
  }
}

export interface UpdateComponentDto {
  /** 示例图url */
  coverUrl: string
  /** 关联物料中心组件id */
  associatedComponentId?: string
  /** 关联物料中心组件版本 */
  associatedComponentVersion?: string
  /** 资源url */
  resourceUrl?: string
  /** 属性配置 */
  propsConfig?: string
  /** 事件key */
  eventKey?: string
  /** 组件id */
  id: string
  /** 组件分类，即物料 Schema 里的 group */
  group?: string
}

export interface DeleteComponentDto {
  /** 组件id */
  id: string
}

export interface ChangeListDto {
  /** 业务域code */
  domainCode: string
  /** 页码 */
  pageNum: number
  /** 分页大小 */
  pageSize: number
  /** 创建者 */
  createUser?: string
  /** 变更名称 */
  changeName?: string
}

export interface CreateChangeDto {
  changeName: string
  type: number
  status: number
  teamId: string
  createUser: string
  updateUser: string
  createTime: number
  updateTime: number
  domainCode: string
}

export interface UpdateStatusChangeDto {
  changeId: string
}

export interface ModuleListDto {
  /** 业务域code */
  businessDomainCode: string
  /** 页码 */
  pageNum: number
  /** 分页大小 */
  pageSize: number
}

export interface ModuleListWidthDto {
  /** 业务域code */
  changeId: string
  needContent?: boolean
}

export interface CreateModuleDto {
  /** 模块名称 */
  name: string
  /** 模块类型：页面级-0，模块级-1 */
  type: number
  /** 容器类型：web-0，H5-1，RN-2，Native-3 */
  containerType: number
  /** 业务域code */
  businessDomainCode: string
  /** 变更Id */
  changeId: string
  /** 模版Id */
  templateId?: number
}

export interface UpdateModuleDto {
  /** 模块id */
  id: string
  /** 模块版本 */
  version: string
  /** 模块内容 */
  content: string
  /** 工作量 */
  workload?: number
}

export interface ResponseType {
  result: number
  msg: string
}

export interface User {
  /** userCode */
  userCode: string
  /** 用户名 */
  userName: string
  /** 头像 */
  avatar: string
}

export interface BusinessDomain {
  /** 业务域code */
  domainCode: string
  /** 业务域名称 */
  domainName: string
  /** 业务域描述 */
  desc: string
  /** 业务域logo */
  logo: string
  /** 一级部门 */
  level1: string
  /** 二级部门 */
  level2: string
  /** 三级部门 */
  level3: string
  /** 创建者 */
  creator: string
  /** 修改者 */
  updater: string
  /** 创建时间 */
  createTime: number
  /** 修改时间 */
  updateTime: number
}

export interface BusinessDomainDetail {
  changeCount: number
  componentCount: number
  moduleCount: number
  id: string
  name: string
  desc: string
  greyConfig?: string
  radarProjects?: string
  schemaDomains?: string
  collaborativeModel?: CollaborativeModel
}

export interface ComponentDetail {
  /** 示例图url */
  coverUrl: string
  /** 关联物料中心组件id */
  associatedComponentId: string
  /** 关联物料中心组件版本 */
  associatedComponentVersion: string
  /** 资源url */
  resourceUrl: string
  /** 属性配置 */
  propsConfig: string
  /** 事件key */
  eventKey: string
  /** 组件名称 */
  name: string
  /** 组件类型：web-0，H5-1，RN-2，Native-3 */
  type: number
  /** 业务域code */
  businessDomainCode: string
  /** 组件描述 */
  descs: string
  /** 是否容器组件 */
  isContainer: boolean
  /** 已删除，软删除 */
  isDeleted: boolean
  /** 组件id */
  id: string
  /** 组件版本 */
  version: number
  /** 组件分类 */
  group?: string
}

export interface CreateOrUpdateComponentResponse {
  /** 组件id */
  id: string
}

export interface CreateOrUpdateChangeResponse {
  /** 模块id */
  changeId: string
}

export interface ChangeDto {
  changeId: string
  changeName: string
  type: number
  createUser: string
  publishTime: number
  createTime: number
  status: number
  isOnline: number
  teamId: string
  developer?: Array<string>
  tester?: string[]
}

export interface ModuleDetail {
  /** 模块名称 */
  name: string
  /** 模块类型：页面级-0，模块级-1 */
  type: number
  /** 容器类型：web-0，H5-1，RN-2，Native-3 */
  containerType: number
  /** 业务域code */
  businessDomainCode: string
  /** 模块id */
  id: string
  /** 模块版本 */
  version: number
  /** 模块内容 */
  content: string
  /** 创建者 */
  createUser: string
  /** 修改者 */
  updateUser: string
  /** 创建时间 */
  createTime: number
  /** 修改时间 */
  updateTime: number
  /** 是否能够编辑 */
  canEdit?: boolean
  /** 变更 ID */
  changeId?: string
}

export interface CreateOrUpdateModuleResponse {
  /** 模块id */
  id: string
}

export interface GetPermissionResDto {
  /** 正在编辑的房间 */
  editingRoom: Room
}

export interface Room {
  /** 用户 */
  user: UserInfo
  /** 房间 ID */
  roomId: string
}

export interface UserInfo {
  /** 邮箱前缀 */
  userCode: string
  /** 用户名 */
  userName: string
  /** 头像 */
  avatar: string
  /** 部门名称 */
  department: string
}

export interface GetPermissionBodyDto {
  /** 模块 ID */
  id: string
  /** 模块版本 */
  version: number
  /** 当前用户没有权限时，是否直接授予权限 */
  grant: boolean
  /** 房间 ID，用于区分设计器页面，由前端自动生成 */
  roomId: string
}

export interface CancelPermissionBodyDto {
  /** 模块 ID */
  id: string
  /** 模块版本 */
  version: number
  /** 房间 ID，用于区分设计器页面，由前端自动生成 */
  roomId: string
}

export interface ModuleDeployDto {
  moduleId: string
  version: number | string
  content: string
  stage: string
  frontLane?: string
  lane?: string
  globalModuleId?: string
}
export interface UpdateExpressionTemplateDto {
  /** 名称 */
  name: string
  /** 表达式 */
  expression: string
  /** 业务域编码 */
  domainCode: string
  /** 表达式类型，0: JSONata，1: JavaScript */
  expressionType: number
}

export interface ExpressionTemplate {
  /** 唯一标识 */
  id: string
  /** 名称 */
  name: string
  /** 表达式 */
  expression: string
  /** 业务域编码 */
  domainCode: string
  /** 表达式类型 */
  expressionType: number
  /** 创建时间戳 */
  createdAt: number
  /** 更新时间戳 */
  updatedAt: number
  /** 创建者 */
  creator: string
  /** 更新者 */
  updater: string
}

export interface CreateModuleTemplate {
  /** 业务域编码 */
  domainCode: string
  /** name */
  name: string
  /** desc */
  desc?: string
  /** 来源moduleId */
  moduleId: string
  /** 来源module version */
  version: number
  /** 示例图 */
  demoImage: string
}

export interface OverwriteModuleTemplate {
  /** 业务域编码 */
  domainCode: string
  /** 来源moduleId */
  moduleId: string
  /** 来源module version */
  version: number
  /** 模版id */
  templateId: string
}

export interface ModuleTemplate extends CreateModuleTemplate {
  /** content 模版内容 */
  content: string
  /** id */
  id: number
}

export interface ModuleTemplateListDto {
  /** 业务域code */
  domainCode: string
  /** 页码 */
  pageNum: number
  /** 分页大小 */
  pageSize: number
}

export interface ModuleTag {
  id?: number
  /** 业务域code */
  domainCode: string
  /** 一级标签 */
  l1Tag: string
  /** 二级标签 */
  l2Tag: string
  /** 预估时间 */
  estimateTime?: number
}

export interface DataSourceListDto {
  domainCode: string
  pageNum: number
  pageSize: number
}

export interface DataSourceConfigDto {
  id?: string
  domainCode: string
  name: string
  path: string
  type: string
  method: string
  stagingDomain?: string
  prtDomain?: string
  betaDomain?: string
  productionDomain?: string
  mockRes?: string
  createUser: string
  updateUser: string
  createTime: number
  updateTime: number
}

export interface Member {
  domainCode: string
  grantedBy: string
  grantedTime: string // 可以考虑使用 number 类型，表示时间戳
  id: string
  permissionLevel: string
  userId: string
}

export interface ComponentReferenceListDto {
  /** 业务域code */
  domainCode: string
  /** 组件id */
  id: string
  /** 组件版本 */
  version: string
  /** 页码 */
  pageNum: number
  /** 分页大小 */
  pageSize: number
}

export interface moduleDeployList {
  id: string
  moduleId: string
  stage: string
  createTime: number
  version: string
  createUser: string
  changeId: string
  changeName: string
}
export interface moduleDeployDetail extends moduleDeployList {
  content: string
}
export interface ModuleVersionLogFindBriefResDto {
  /** 日志 */
  logs: Array<ModuleVersionLogBriefDto>
  /** 签出模块版本 */
  checkoutModuleVersion: number
}

export interface ModuleVersionLogBriefDto {
  /** id */
  id: string
  /** 模块 id */
  moduleId: string
  /** 模块版本 */
  moduleVersion: number
  /** 创建时间戳 */
  createdAt: number
  /** 创建者 */
  creator: string
}

export interface ModuleVersionLog {
  /** id */
  id: string
  /** 模块 id */
  moduleId: string
  /** 模块版本 */
  moduleVersion: number
  /** 模块内容 */
  moduleContent: string
  /** 创建时间戳 */
  createdAt: number
  /** 创建者 */
  creator: string
}

export interface OnlineModuleVersion {
  id: string
  version: string
  status: number
  createUser: string
  updateUser: string
  createTime: number
  updateTime: number
  module: OnlineModule
}

export interface OnlineModule {
  id: string
  name: string
  type: number
  businessDomainCode: string
}

/**
 * AI 简单聊天请求 DTO
 */
export interface AiSimpleChatReqDto {
  /**
   * 提示词
   */
  prompt: string
}

/**
 * AI 简单聊天响应 DTO
 */
export interface AiSimpleChatResDto {
  /**
   * UUID
   */
  uuid: string
  /**
   * 输出
   */
  completion: string
}

/**
 * AI 更新日志请求 DTO
 */
export interface AiUpdateLogReqDto {
  /**
   * UUID
   */
  uuid: string
  /**
   * 输出
   */
  status: AiLogStatus
}
