import { request } from '@kmi/react'
import type * as model from './models'

/** business_domain  */
export function getAllBusinessDomain(
  payload: {
    type: string
    filterByUserPermission?: boolean
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: Array<model.BusinessDomain>
  }>(`/rest/canal/domain/list`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

/** business_domain  */
export function getDetailBusinessDomain(
  payload: {
    code: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: model.BusinessDomainDetail
  }>(`/rest/canal/domain/detail`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

/** business_domain  */
export function getMemberBusinessDomain(
  payload: {
    code: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: Array<model.Member> }>(
    `/rest/canal/domain/member`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}

/** business_domain  */
export function getSimpleDetailBusinessDomain(
  payload: {
    code: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: model.BusinessDomainDetail
  }>(`/rest/canal/domain/simple/detail`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

/** business_domain  */
export function checkDomainPermission(
  payload: {
    code: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: model.BusinessDomainDetail
  }>(`/rest/canal/domain/check/permission`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

export function editGreyConfig(
  payload: {
    code: string
    greyConfig: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/domain/edit/grey`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function addDomainMember(
  payload: {
    domainCode: string
    userId: string
    permissionLevel: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/domain/add/member`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function setSingle(
  payload: {
    domainCode: string
    value: unknown
    key: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/domain/set/single`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** business_domain  */
export function getDomainRouteConfig() {
  return request<{
    result: number
    msg: string
    data: Record<string, Record<string, boolean>>
  }>(`/rest/canal/domain/route/config`, {
    method: 'get',
  })
}
