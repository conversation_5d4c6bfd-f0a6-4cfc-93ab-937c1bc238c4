import { request } from '@kmi/react';
import type * as model from './models';

/** expression-template 查找表达式模板 */
export function findExpressionTemplates(
  payload: {
    domainCode: string;
    expressionType: number;
  },
  options?: { [key: string]: any }
) {
  const params = payload;

  return request<{ result: number; msg: string; data: Array<model.ExpressionTemplate> }>(
    `/rest/canal/expression-template`,
    {
      method: 'get',
      ...(options || {}),
      params,
    }
  );
}

/** expression-template 更新表达式模板 */
export function updateExpressionTemplateUsingPost(
  payload?: model.UpdateExpressionTemplateDto,
  options?: { [key: string]: any }
) {
  const data = payload;
  return request<void>(`/rest/canal/expression-template/update`, {
    method: 'post',
    ...(options || {}),
    data,
  });
}
