import { type IUpdateGreyDeploy } from '@/pages/grey/grey-const'
import { request } from '@kmi/react'
export function checkBeforePublishRequest(
  payload: {
    changeId: string
    ignoreComponentCheck?: boolean
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: {
      noHistory: boolean
      hasNoHistory: boolean
      moduleCount: number
      checkError?: {
        key: string
        content: {
          moduleId: string
          errorTips: {
            text: string
            content: string[]
          }[]
        }[]
      }[]
    }
  }>(`/rest/canal/change/publish/before/create/check`, {
    method: 'post',
    ...(options || {}),
    data: params,
  })
}

export function deployGreyRequest(
  payload: {
    id: number
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: { noHistory: boolean } }>(
    `/rest/canal/change/publish/deploy/grey`,
    {
      method: 'post',
      ...(options || {}),
      data: params,
    },
  )
}

export function updateGreyRequest(
  payload: IUpdateGreyDeploy,
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: { noHistory: boolean } }>(
    `/rest/canal/change/publish/edit/grey`,
    {
      method: 'post',
      ...(options || {}),
      data: params,
    },
  )
}

/** component  */
export function getGreyHistory(
  payload: {
    greyId: number
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: IUpdateGreyDeploy[] }>(
    `/rest/canal/change/publish/grey/history`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}
