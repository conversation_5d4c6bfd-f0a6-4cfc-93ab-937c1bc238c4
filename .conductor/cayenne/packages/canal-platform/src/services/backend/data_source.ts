import { request } from '@kmi/react'
import type * as model from './models'

/** 获取业务域下配置 */
export function getListByDomain(
  payload?: model.DataSourceListDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      total: number
      list: model.DataSourceConfigDto[]
    }
  }>(`/rest/canal/data-source-config/list`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** 获取业务域下配置所有 */
export function getAllListByDomain(
  payload?: {
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: model.DataSourceConfigDto[]
  }>(`/rest/canal/data-source-config/all`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}
/** 获取业务域下配置所有 */
export function getAllProdDomainsByCode(
  payload?: {
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: string[]
  }>(`/rest/canal/data-source-config/all/prod/domain`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

export function add(
  payload?: model.DataSourceConfigDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.DataSourceConfigDto
  }>(`/rest/canal/data-source-config/add`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function addStaging(
  payload?: model.DataSourceConfigDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.DataSourceConfigDto
  }>(`/rest/canal/data-source-config/staging/add`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function edit(
  payload?: {
    config: model.DataSourceConfigDto
    id: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.DataSourceConfigDto
  }>(`/rest/canal/data-source-config/edit`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function editStaging(
  payload?: {
    config: model.DataSourceConfigDto
    id: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.DataSourceConfigDto
  }>(`/rest/canal/data-source-config/staging/edit`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function getDataSourceDetail(
  payload?: {
    id: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.DataSourceConfigDto
  }>(`/rest/canal/data-source-config/one`, {
    method: 'get',
    ...(options || {}),
    params: data,
  })
}
