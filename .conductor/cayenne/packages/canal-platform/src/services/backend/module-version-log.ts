import { request } from '@kmi/react'
import type * as model from './models'

/** module-version-log 查找模块版本日志简介 */
export function findModuleVersionLogBrief(
  payload: {
    moduleId: string
    moduleVersion: number
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: model.ModuleVersionLogFindBriefResDto
  }>(`/rest/canal/module-version-log/brief`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

/** module-version-log 查找模块版本日志详情 */
export function findModuleVersionLogDetail(
  payload: {
    logId: string
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{ result: number; msg: string; data: model.ModuleVersionLog }>(
    `/rest/canal/module-version-log/detail`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}
