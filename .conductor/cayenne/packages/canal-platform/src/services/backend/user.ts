import { request } from '@kmi/react';
import type * as model from './models';

/** user  */
export function loginUsingGet(
  payload: {
    redirectUrl: string;
  },
  options?: { [key: string]: any }
) {
  const params = payload;

  return request<model.ResponseType>(`/rest/user/login`, {
    method: 'get',
    ...(options || {}),
    params,
  });
}

/** user  */
export function userInfoUsingGet(options?: { [key: string]: any }) {
  return request<{ result: number; msg: string; data: model.User }>(`/rest/user/info`, {
    method: 'get',
    ...(options || {}),
  });
}
