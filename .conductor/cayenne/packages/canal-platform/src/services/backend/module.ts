import { request } from '@kmi/react'
import type * as model from './models'

/** 检查模块配置合法性的响应类型 */
export interface CheckModulesResponse {
  result: number
  msg: string
  data: {
    checkError: Array<{
      key: string
      content: Array<any>
    }>
  }
}

export const ModuleStatus = {
  Online: 1,
  NotOnline: 0,
}

/** module 获取模块列表 */
export function getAllModulesUsingPost(
  payload?: model.ModuleListDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: Array<model.ModuleDetail>
  }>(`/rest/canal/module/list`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** module 创建模块 */
export function createModuleUsingPost(
  payload?: model.CreateModuleDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.CreateOrUpdateModuleResponse
  }>(`/rest/canal/module/create`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** module 更新模块 */
export function updateModuleUsingPost(
  payload?: model.UpdateModuleDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.CreateOrUpdateModuleResponse
  }>(`/rest/canal/module/update`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** module 更新模块元信息 */
export function updateModuleMetaUsingPost(
  payload?: model.UpdateModuleMetaDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.CreateOrUpdateModuleResponse
  }>(`/rest/canal/module/update-meta`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** module  */
export function getModuleDetail(
  payload: {
    id: string
    version?: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: model.ModuleDetail }>(
    `/rest/canal/module/detail`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}

export function getGlobalModuleDetail(
  payload: {
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: model.ModuleDetail }>(
    `/rest/canal/module/global-detail`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}

/** module 获取模块列表 */
export function getAllModulesUsingPostByChangeId(
  payload?: model.ModuleListWidthDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      list: Array<model.ModuleDetail>
    }
  }>(`/rest/canal/module/listByChangeId`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** module  获取业务域下在线模块*/
export function getAllOnlineModuleList(
  payload: {
    domainCode: string
    pageNum: number
    pageSize: number
    name?: string
    createUser?: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: {
      list: model.OnlineModuleVersion[]
      total: number
    }
  }>(`/rest/canal/module/onlineList`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}
export function getModuleList(
  payload: {
    domainCode: string
    pageNum: number
    pageSize: number
    name?: string
    createUser?: string
    componentId?: string
    dataSourceId?: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: {
      list: any[]
      total: number
    }
  }>(`/rest/canal/module/search/list`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

/** module  添加模块到当前变更*/
export function addModuleToChange(
  payload: {
    domainCode: string
    changeId: string
    moduleId: string
  },
  options?: { [key: string]: any },
) {
  const data = payload

  return request<{ result: number; msg: string; data: { id: string } }>(
    `/rest/canal/module/add/Change`,
    {
      method: 'post',
      ...(options || {}),
      data,
    },
  )
}

/** module 获取模块的权限 */
export function getPermission(
  payload?: model.GetPermissionBodyDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.GetPermissionResDto
  }>(`/rest/canal/module/getPermission`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** module 取消模块的权限 */
export function cancelPermission(
  payload?: model.CancelPermissionBodyDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{ result: number; msg: string; data: boolean }>(
    `/rest/canal/module/cancelPermission`,
    {
      method: 'post',
      ...(options || {}),
      data,
    },
  )
}

export function doStagingDeploy(
  payload?: {
    module: model.ModuleDeployDto
    isNewDeploy: boolean
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{ result: number; msg: string; data: boolean }>(
    `/rest/canal/deploy/staging`,
    {
      method: 'post',
      ...(options || {}),
      data,
    },
  )
}

export async function deployStaging(
  isNewDeploy: boolean,
  domainCode: string,
  changeId: string,
  stage: string,
  lane?: string,
  frontLane?: string,
) {
  // 需要调用单独的stage部署接口来同步数据，因为只能走前端方式，采用调用api方式，其他部署走后端
  // 获取该变更下所有的模块信息，需要获取到模块内容
  const [allModuleRes, globalModuleDetailRes] = await Promise.all([
    getAllModulesUsingPostByChangeId({
      changeId: changeId,
      needContent: true,
    }),
    getGlobalModuleDetail({ domainCode }),
  ])
  let isSuccess = false
  if (allModuleRes?.result === 1 && Array.isArray(allModuleRes.data?.list)) {
    await Promise.all(
      allModuleRes.data?.list.map(async (item) => {
        let globalModuleId: string | undefined = globalModuleDetailRes.data?.id // 可能不存在
        if (globalModuleId === item.id) {
          globalModuleId = undefined
        }
        return await doStagingDeploy({
          module: {
            moduleId: item.id,
            version: item.version,
            content: item.content,
            stage: stage,
            frontLane: frontLane,
            lane: lane,
            globalModuleId,
          },
          isNewDeploy,
        })
      }),
    ).then(async (deployValues) => {
      console.log(deployValues)
      if (deployValues?.every?.((currentValue) => currentValue.result === 1)) {
        isSuccess = true
      }
    })
  }
  return isSuccess
}
/**
 * 提供单模块的prt或beta环境的部署
 * 会读取最新的一条部署配置来部署，没有话新增一条主干部署
 * @param payload
 * @param options
 * @returns
 */
export function doAutoSingleModuleDeploy(
  payload?: {
    module: model.ModuleDeployDto
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{ result: number; msg: string; data: boolean }>(
    `/rest/canal/deploy/module/auto`,
    {
      method: 'post',
      ...(options || {}),
      data,
    },
  )
}

/**
 *
 */
export function checkBeforeMerge(
  payload?: {
    changeId: string
    moduleId: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      hasOnlineVersion: boolean
      onlineVersion: string | undefined
      checkoutVersion: string | undefined
    }
  }>(`/rest/canal/module/check/merge`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function deleteModuleFromChange(
  payload?: {
    moduleId: string
    changeId: string
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: number
  }>(`/rest/canal/module/delete/from/change`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/**
 * 获取一个模块的发布历史
 * @param payload
 * @param options
 * @returns
 */
export function getModuleDeployHistoryList(
  payload?: {
    moduleId: string
    domainCode: string
    pageNum: number
    pageSize: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      list: model.moduleDeployList[]
      total: number
    }
  }>(`/rest/canal/deploy/module/deploy/history`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/**
 * 获取确定id的模块部署内容详情
 */
export function getModuleDeployDetailById(
  payload?: {
    id: string
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.moduleDeployDetail
  }>(`/rest/canal/deploy/module/schema`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/**
 * 通过环境获取模块部署
 */
export function getModuleDeployByEnv(
  payload?: {
    moduleId: string
    env: string
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: model.moduleDeployDetail
  }>(`/rest/canal/deploy/module`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

export function getPreviousRecordById(
  payload?: {
    id: string
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: { id: string | null }
  }>(`/rest/canal/deploy/previous`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function search(
  payload: {
    searchText: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: (model.ModuleDetail & {
      changeId: string
      version: string
    })[]
  }>(`/rest/canal/module/search/global`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

export function uploadComponentVersionOfModule(
  payload: {
    moduleId: string
    moduleVersion: number
    componentId: string
    latestVersion: number
    currentVersion: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      editingRoom?: {
        user: {
          userName: string
          userCode: string
        }
      }
      schema: model.moduleDeployDetail
      old: model.ComponentDetail
      newest: model.ComponentDetail
    }
  }>(`/rest/canal/module/update/component/version/check`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** 检查模块配置合法性 */
export function checkModulesUsingPost(
  payload: {
    moduleVersions: Array<{ moduleId: string; version: string | number }>
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<CheckModulesResponse>(`/rest/canal/module/check`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}
