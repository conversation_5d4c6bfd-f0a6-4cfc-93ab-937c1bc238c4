import { request } from '@kmi/react'
import type * as model from './models'

/** AI 聊天 */
export function aiSimpleChat(
  payload: model.AiSimpleChatReqDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.AiSimpleChatResDto
  }>(`/rest/canal/ai/simple-chat`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** 更新 AI 日志 */
export function aiUpdateLog(
  payload: model.AiUpdateLogReqDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{ result: number; msg: string; data: void }>(
    `/rest/canal/ai/update-log`,
    {
      method: 'post',
      ...(options || {}),
      data,
    },
  )
}
