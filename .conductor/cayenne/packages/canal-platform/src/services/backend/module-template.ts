import { request } from '@kmi/react'
import type * as model from './models'

/** 创建模版 */
export function createModuleTemplateUsingPost(
  payload?: model.CreateModuleTemplate,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: { id: number }
  }>(`/rest/canal/template/add`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function overwriteTemplateUsingPost(
  payload?: model.OverwriteModuleTemplate,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: { id: number }
  }>(`/rest/canal/template/overwrite`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function getModuleTemplateList(
  payload?: model.ModuleTemplateListDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      list: model.ModuleTemplate[]
      pageInfo: {
        total: number
        pageSize: number
        pageNum: number
      }
    }
  }>(`/rest/canal/template/list`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}
