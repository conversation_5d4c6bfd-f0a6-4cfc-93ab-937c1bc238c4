import { request } from '@kmi/react'
import type * as model from './models'

/** 获取业务域下标签 */
export function getAllTagsByDomain(
  payload?: {
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: Record<
      string,
      {
        l2Tag: string
        estimateTime: number
        id: number
      }[]
    >
  }>(`/rest/canal/tags/list`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

export function addDomainTags(
  payload?: model.ModuleTag,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/tags/add`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function getInfoByIds(
  payload?: { ids: number[] },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: any
  }>(`/rest/canal/tags/getInfo`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function deleteDomainTags(
  payload?: model.ModuleTag,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/tags/delete`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function editDomainTags(
  payload?: model.ModuleTag,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.ModuleTag
  }>(`/rest/canal/tags/edit`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** 获取module下标签 */
export function getAllTagsByModuleId(
  payload?: {
    moduleId: string
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: any[]
  }>(`/rest/canal/moduleTags/list`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

export function deleteModuleTags(
  payload?: {
    id: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/moduleTags/delete`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export function addModuleTags(
  payload?: {
    moduleId: string
    tagId: number
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
  }>(`/rest/canal/moduleTags/add`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}
