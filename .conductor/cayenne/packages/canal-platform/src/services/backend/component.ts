import { request } from '@kmi/react'
import type * as model from './models'

/** component 获取组件列表 */
export function getAllComponentsUsingPost(
  payload?: model.ComponentListDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.GetAllComponentsResponseDto
  }>(`/rest/canal/component/list`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** component 创建组件 */
export function createComponentUsingPost(
  payload?: model.CreateComponentDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.CreateOrUpdateComponentResponse
  }>(`/rest/canal/component/create`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** component 更新组件 */
export function updateComponentUsingPost(
  payload?: model.UpdateComponentDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.CreateOrUpdateComponentResponse
  }>(`/rest/canal/component/update`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** component 删除组件 */
export function deleteComponentUsingPost(
  payload?: model.DeleteComponentDto,
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: model.CreateOrUpdateComponentResponse
  }>(`/rest/canal/component/delete`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

/** component  */
export function getComponentDetail(
  payload: {
    id: string
    version?: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{ result: number; msg: string; data: model.ComponentDetail }>(
    `/rest/canal/component/detail`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}

/** component  */
export function getStructureVersionInfo(
  payload: {
    domainCode: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: Record<
      string,
      {
        id: string
        associatedComponentVersion: string
        associatedComponentName: string
        version: string
        name: string
      }[]
    >
  }>(`/rest/canal/component/structure/version/info`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

/**
 * 获取结构化的当前业务域关联的
 */
export function copyComponent(
  payload: {
    targetDomainCode: string
    components: string[]
  },
  options?: { [key: string]: any },
) {
  const data = payload

  return request<{
    result: number
    msg: string
    data: { origin: string; target: string }[]
  }>(`/rest/canal/component/copy`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}
