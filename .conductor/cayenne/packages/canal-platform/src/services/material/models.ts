import type { CLMSGroup } from '@kael/material-schema'

export interface SearchMaterialPlatformComponentRes {
  list: MaterialPlatformComponentSummaryDto[]
  total: number
}

export interface MaterialPlatformComponentSummaryDto {
  id: number
  /** 名称 */
  name: string
  /** 展示名 */
  displayName: string
  /** 预览图 */
  imgs: any
  /** 版本 */
  version: string
  /** 描述 */
  description: string
  /** 创建于 */
  createAt: number
  /** 更新于 */
  updateAt: number
  /** 物料 ID */
  materialId: number
  /** 包名 */
  packageName: string
  /** 包类型 */
  packageType: string
}

export interface MaterialPlatformComponentDetailDto {
  id: number
  /** 物料 ID */
  materialId: number
  /** 创建于 */
  createAt: number
  /** 更新于 */
  updateAt: number
  /** 描述 */
  version: string
  /** 名称 */
  name: string
  /** 类型 */
  type: string
  /** 包描述 */
  packageDescription: string
  /** 展示名 */
  displayName: string
  /** 描述 */
  description: string
  /** 预览图 */
  imgs: any
  /** UMD 链接 */
  umdUrls: Array<string>
  /** 依赖 */
  dependencies: Record<string, any>
  /** 开发依赖 */
  devDependencies: Record<string, any>
  /** 同伴依赖 */
  peerDependencies: Record<string, any>
  /** 属性 */
  props: Array<Record<string, any>>
  /** 版本列表 */
  versionList: Array<string>
  /** 低代码物料 Schema */
  schema: Record<string, any>
  /** 低代码数据 */
  lowCodeData: MaterialPlatformComponentDetailLowCodeData
}

/**
 * 物料平台组件详情低代码数据
 */
export interface MaterialPlatformComponentDetailLowCodeData {
  /**
   * 多级分组
   */
  groups: CLMSGroup[]
}

export type GetMaterialPlatformPackageInfosRes = MaterialPlatformPackageInfo[]

export interface MaterialPlatformPackageInfo {
  id: number
  packageName: string
  packageType: 'single' | 'multiple' // 单包单组件、单包多组件
  createAt: number
  updateAt: number
  version: string
  codeType: 'pro-code' | 'low-code' | 'mixed' // 全代码、低代码、混合模式(全代码、低代码都支持)
}
