import { request } from '@kmi/react'
import type * as model from './models'

/**
 * 物料平台源
 */
const DROW_ORIGIN =
  process.env.KMI_ENV === 'online'
    ? 'https://drow.corp.kuaishou.com'
    : 'https://drow.staging.kuaishou.com'

/** material-platform 搜索物料平台组件 */
export function searchMaterialPlatformComponent(
  payload: {
    codeType: string
    search: string
    pageSize?: number
    currentPage?: number
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: model.SearchMaterialPlatformComponentRes
  }>(
    // `/rest/canal/material-platform/search`,

    // 物料平台暂时不发 openapi，临时替换为物料平台域名路径
    `${DROW_ORIGIN}/openapi/component/list`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}

/** material-platform 获取物料平台组件详情 */
export function getMaterialPlatformComponentDetail(
  payload: {
    materialId: number
    version: string
    componentName: string
  },
  options?: { [key: string]: any },
) {
  const params = payload

  return request<{
    result: number
    msg: string
    data: model.MaterialPlatformComponentDetailDto
  }>(
    // `/rest/canal/material-platform/get`,

    // 物料平台暂时不发 openapi，临时替换为物料平台域名路径
    `${DROW_ORIGIN}/openapi/component/detail`,
    {
      method: 'get',
      ...(options || {}),
      params,
    },
  )
}

export function getMaterialPlatformPackageInfos(
  payload: {
    ids: number[]
  },
  options?: { [key: string]: any },
) {
  const { ids } = payload

  return request<{
    result: number
    msg: string
    data: model.GetMaterialPlatformPackageInfosRes
  }>(`${DROW_ORIGIN}/openapi/material/list?ids=${ids.join(',')}`, {
    method: 'get',
    ...(options || {}),
  })
}

/**
 * 获取组件库最新版本
 * @param payload
 * @param options
 * @returns
 */
export function getMaterialLatestVersion(
  payload: {
    materialParams: {
      materialId: number
    }[]
  },
  options?: { [key: string]: any },
) {
  const data = payload
  return request<{
    result: number
    msg: string
    data: {
      materialId: number
      packageName: string
      version: string
    }[]
  }>(`${DROW_ORIGIN}/openapi/material/latestVersion`, {
    method: 'post',
    ...(options || {}),
    data,
  })
}

export interface TreeNode {
  label: string
  value: string
  children: TreeNode[] // 递归类型，表示子节点
}

/**
 * 查询组件库的所有版本
 * /openapi/material/versions
 */
export function getMaterialVersions(
  payload: {
    materialId: number
    dataType: 'array' | 'tree'
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: TreeNode[]
  }>(`${DROW_ORIGIN}/openapi/material/versions`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

export interface SearchOption {
  codeType: string
  createAt: number
  description: string
  displayName: string
  materialId: number
  packageName: string
  packageType: string
  version: string
}

/**
 * 搜索物料库
 * openapi/v2/material/list
 */
export function searchMaterial(
  payload: {
    keyword?: string
    currentPage?: number
    pageSize?: number
    codeType?: 'low-code' | 'pro-code' | 'mixed'
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: { list: SearchOption[] }
  }>(`${DROW_ORIGIN}/openapi/v2/material/list`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}

/**
 * 获取组件库详情
 * openapi/v2/material/detail
 */
export interface MaterialItem {
  /** 组件库Id */
  id: number
  /** 创建时间 */
  createAt: number
  /** 更新时间 */
  updateAt: number
  /** npm包名称 */
  packageName: string
  /** 组件库中文名 */
  displayName: string
  /** 组件库代码模式 */
  codeType: 'pro-code' | 'low-code' | 'mixed'
  /** 组件库描述 */
  description: string
  /** npm包类型 */
  packageType: string
  /** 最新版本 */
  version: string
  /** 物料资产列表 */
  components: Component[]
  /** gitUrl */
  gitUrl: string
  /** 物料的分组 */
  groups: Array<any>
  /** 低代码分组 */
  lowCodeData: {
    groups: LowCodeGroupsItem
  }
}

/** 低代码分组 */
interface LowCodeGroupsItem {
  name: string
  items: string[] | LowCodeGroupsItem[]
}

/** 组件 */
export interface Component {
  id: number
  materialId: number
  createAt: number
  updateAt: number
  version: string
  name: string
  type: 'component' | 'block' | 'template'
  displayName: string
  description: string
  /** 组件预览图 */
  thumbnailUrl: string
  /** 低代码组件Schema */
  schema: any
}
export function getMaterialDetail(
  payload: {
    materialId?: number | null
    version?: string
  },
  options?: { [key: string]: any },
) {
  const params = payload
  return request<{
    result: number
    msg: string
    data: MaterialItem
  }>(`${DROW_ORIGIN}/openapi/v2/material/detail`, {
    method: 'get',
    ...(options || {}),
    params,
  })
}
