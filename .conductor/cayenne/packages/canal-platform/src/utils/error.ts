import { ApiErrorCode } from '@ad/canal-shared'

/**
 * 接口响应
 */
export interface ApiRes {
  /**
   * 结果，编码
   */
  result: number
  /**
   * 信息
   */
  msg?: string
  /**
   * 信息
   */
  message?: string
}

/**
 * 断言接口成功返回
 * @param res 接口响应
 */
export function assertApiOk(res: ApiRes): void {
  if (res.result !== ApiErrorCode.OK) {
    throw new Error(`[${res.result}] ${res.msg || res.message}`)
  }
}
