import { history } from '@kmi/react'
import { message } from '@m-ui/react'
/**
 * 判断是否是lingzhu创建的变更
 * @param moduleId 模块 ID
 * @param moduleVersion 模块版本
 */
export function isCreateFromLingZhu(changeId: string): boolean {
  const regex = /^\d{13}$/
  return regex.test(changeId)
}

export function jumpToNewChangeDetail(
  changeId: string,
  domainCode: string,
): void {
  history.push(`/change/overview?changeId=${changeId}&domainCode=${domainCode}`)
}

export function checkRedirectToNewPlatform(
  changeId: string,
  domainCode: string,
): boolean {
  if (isCreateFromLingZhu(changeId)) {
    message.info('从新版界面创建的变更不能在老版页面进行此操作，为您自动跳转')
    jumpToNewChangeDetail(changeId, domainCode)
    return true
  } else {
    return false
  }
}
