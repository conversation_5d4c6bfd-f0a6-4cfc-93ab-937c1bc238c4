// @babel/standalone 非常大，这个文件需要通过 await import 异步使用

import { transform } from '@babel/standalone'
import { isString } from 'lodash'

/**
 * 后端 js 表达式包装
 * @param code 代码
 * @param entryFnKey 入口函数键值
 */
function backJSExprWrap(code: string, entryFnKey = 'default'): string {
  return `function main(mainArg0) {
  var exports = {}, module = {}
  module.exports = exports
  ;(function() {
    ${code}
  })()
  return module.exports.${entryFnKey}.apply(module.exports, mainArg0.args)
}`
}

/**
 * 后端模型包装
 * @param code 代码
 */
function backModelWrap(code: string): string {
  return `function main(mainArg0) {
  const exports = {}, module = {}
  module.exports = exports
  ;(function() {
    ${code}
  })()
  const M = module.exports.default
  return new M(...mainArg0.args)
}`
}

/**
 * 将前端 js 表达式包装
 * @param code 代码
 */
function frontJSExprWrap(code: string): string {
  return `(function(deps) {
  var exports = {}, module = {}
  module.exports = exports
  ;(function() {
    ${code}
  })()
  return module.exports.default
  function require(name) {
    return deps[name]
  }
})(ctx.runtime.container.dependencies)`
}

/**
 * 通过容器执行的前端 js 表达式包装
 * @param code 代码
 */
function frontJSExprEvalByContainerWrap(code: string): string {
  return `${code}(ctx.runtime.container)`
}

//#region js 表达式，编译到 ES5

/**
 * 将 js 代码转为 es5
 * @param code 代码
 */
export function jsToES5(code: string): string {
  const ret = transform(code, {
    filename: 'expr.js',
    presets: ['env'],
  })
  if (!isString(ret.code)) {
    console.log('jsToES5 ret', ret)
    throw new Error(`jsToES5 code is invalid: ${ret.code}`)
  }
  return ret.code
}

/**
 * 将后端 js 表达式转为 es5
 * @param code 代码
 * @param entryFnKey 入口函数键值
 */
export function backJSExprToES5(code: string, entryFnKey?: string): string {
  return backJSExprWrap(jsToES5(code), entryFnKey)
}

/**
 * 将后端 js 表达式转为 es5，通过内部 __main 函数
 * @param code 代码
 */
export function backJSExprToES5ByInternalMain(code: string): string {
  return backJSExprToES5(code, '__main')
}

/**
 * 将后端模型 js 转为 es5
 * @param code 代码
 */
export function backModelJSToES5(code: string): string {
  return backModelWrap(jsToES5(code))
}

/**
 * 将前端 js 表达式转为 es5
 * @param code 代码
 */
export function frontJSExprToES5(code: string): string {
  return frontJSExprWrap(jsToES5(code))
}

/**
 * 将通过容器执行的前端 js 表达式转为 es5
 * @param code 代码
 */
export function frontJSExprEvalByContainerToES5(code: string): string {
  return frontJSExprEvalByContainerWrap(frontJSExprToES5(code))
}

//#endregion

//#region ts 表达式，后端编译到最新的 CJS，前端编译到 ES5

/**
 * 将 ts 代码转为 js，CJS
 * @param code 代码
 * @param es5 编译到 ES5
 */
export function tsToJS(code: string, es5?: boolean): string {
  const ret = transform(
    code,
    es5
      ? {
          filename: 'expr.ts',
          presets: ['env'],
          plugins: ['transform-typescript'],
        }
      : {
          filename: 'expr.ts',
          presets: ['typescript', 'env'],
          targets: 'last 1 Chrome version',
        },
  )
  if (!isString(ret.code)) {
    console.log('tsToJS ret', ret)
    throw new Error(`tsToJS code is invalid: ${ret.code}`)
  }
  return ret.code
}

/**
 * 将后端 ts 表达式转为 js
 * @param code 代码
 * @param entryFnKey 入口函数键值
 */
export function backTSExprToJS(code: string, entryFnKey?: string): string {
  return backJSExprWrap(tsToJS(code), entryFnKey)
}

/**
 * 将后端 ts 表达式转为 js，通过内部 __main 函数
 * @param code 代码
 */
export function backTSExprToJSByInternalMain(code: string): string {
  return backTSExprToJS(code, '__main')
}

/**
 * 将后端模型 ts 转为 js
 * @param code 代码
 */
export function backModelTSToJS(code: string): string {
  return backModelWrap(tsToJS(code))
}

/**
 * 将前端 ts 表达式转为 js
 * @param code 代码
 */
export function frontTSExprToJS(code: string): string {
  return frontJSExprWrap(tsToJS(code, true))
}

/**
 * 将通过容器执行的前端 ts 表达式转为 js
 * @param code 代码
 */
export function frontTSExprEvalByContainerToJS(code: string): string {
  return frontJSExprEvalByContainerWrap(frontTSExprToJS(code))
}

//#endregion
