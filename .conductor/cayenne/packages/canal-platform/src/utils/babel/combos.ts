import type { E2EA<PERSON>, E2ESchemaExpression, Model } from '@ad/e2e-schema'
import { isApiExpression, isJsExpression } from '@ad/e2e-schema-utils'
import { isUndefined } from 'lodash'
import {
  asyncBackJSExprToES5,
  asyncBackModelJSToES5,
  asyncBackModelTSToJS,
  asyncBackTSExprToJS,
  asyncFrontJSExprEvalByContainerToES5,
  asyncFrontJSExprToES5,
  asyncFrontTSExprEvalByContainerToJS,
  asyncFrontTSExprToJS,
} from '.'

/**
 * 编译前端模型，原地修改
 * @param model 前端模型
 */
export async function compileFrontModel(model?: Model): Promise<void> {
  if (!model) return
  if (model.codeTS) {
    model.code = await asyncFrontTSExprToJS(model.codeTS)
  } else if (model.codeES) {
    model.code = await asyncFrontJSExprToES5(model.codeES)
  }
}

/**
 * 编译后端模型，原地修改
 * @param model 后端模型
 */
export async function compileBackModel(model?: Model): Promise<void> {
  if (!model) return
  if (model.codeTS) {
    model.code = await asyncBackModelTSToJS(model.codeTS)
  } else if (model.codeES) {
    model.code = await asyncBackModelJSToES5(model.codeES)
  }
}

/**
 * 编译端到端接口
 * @param api 端到端接口
 */
export async function compileApi(api?: E2EAPI): Promise<void> {
  if (!api) return
  for (const arg of api.args || []) {
    await compileBackJsExpression(arg)
  }
  await compileBackJsExpression(api.if)
}

/**
 * 编译后端 JS 表达式，原地修改
 * @param expr 后端 JS 表达式
 */
export async function compileBackJsExpression(
  expr?: E2ESchemaExpression,
): Promise<void> {
  if (!expr) return
  if (isJsExpression(expr)) {
    if (expr.codeTS) {
      expr.code = await asyncBackTSExprToJS(expr.codeTS)
    } else if (expr.codeES) {
      expr.code = await asyncBackJSExprToES5(expr.codeES)
    }
  }
}

/**
 * 编译前端 JS 表达式，原地修改
 * @param expr 前端 JS 表达式
 * @param evalByContainer 是否通过容器执行，默认根据 code 判断
 */
export async function compileFrontJsExpression(
  expr?: E2ESchemaExpression,
  evalByContainer?: boolean,
): Promise<void> {
  if (!expr) return
  if (isJsExpression(expr)) {
    if (isUndefined(evalByContainer)) {
      evalByContainer = expr.code.endsWith('(ctx.runtime.container)')
    }
    if (evalByContainer) {
      if (expr.codeTS) {
        expr.code = await asyncFrontTSExprEvalByContainerToJS(expr.codeTS)
      } else if (expr.codeES) {
        expr.code = await asyncFrontJSExprEvalByContainerToES5(expr.codeES)
      }
    } else {
      if (expr.codeTS) {
        expr.code = await asyncFrontTSExprToJS(expr.codeTS)
      } else if (expr.codeES) {
        expr.code = await asyncFrontJSExprToES5(expr.codeES)
      }
    }
  }
}

/**
 * 编译接口表达式，原地修改
 * @param expr 接口表达式
 */
export async function compileApiExpression(
  expr?: E2ESchemaExpression,
): Promise<void> {
  if (isApiExpression(expr)) {
    await compileBackJsExpression(expr.transform)
  }
}
