/**
 * 预加载 babel 相关代码
 */
export async function preloadBabel(): Promise<void> {
  await import('./babel')
}

//#region js 表达式，编译到 ES5

/**
 * 将 js 代码转为 es5，异步版本
 * @param code 代码
 */
export async function asyncJSToES5(code: string): Promise<string> {
  return (await import('./babel')).jsToES5(code)
}

/**
 * 将后端 js 表达式转为 es5，异步版本
 * @param code 代码
 * @param entryFnKey 入口函数键值
 */
export async function asyncBackJSExprToES5(
  code: string,
  entryFnKey?: string,
): Promise<string> {
  return (await import('./babel')).backJSExprToES5(code, entryFnKey)
}

/**
 * 将后端 js 表达式转为 es5，通过内部 __main 函数，异步版本
 * @param code 代码
 */
export async function asyncBackJSExprToES5ByInternalMain(
  code: string,
): Promise<string> {
  return (await import('./babel')).backJSExprToES5ByInternalMain(code)
}

/**
 * 将前端 js 表达式转为 es5，异步版本
 * @param code 代码
 */
export async function asyncBackModelJSToES5(code: string): Promise<string> {
  return (await import('./babel')).backModelJSToES5(code)
}

/**
 * 将前端 js 表达式转为 es5，异步版本
 * @param code 代码
 */
export async function asyncFrontJSExprToES5(code: string): Promise<string> {
  return (await import('./babel')).frontJSExprToES5(code)
}

/**
 * 将通过容器执行的前端 js 表达式转为 es5，异步版本
 * @param code 代码
 */
export async function asyncFrontJSExprEvalByContainerToES5(
  code: string,
): Promise<string> {
  return (await import('./babel')).frontJSExprEvalByContainerToES5(code)
}

//#endregion

//#region ts 表达式，后端编译到最新的 CJS，前端编译到 ES5

/**
 * 将 ts 代码转为最新的 js，CJS，异步版本
 * @param code 代码
 * @param es5 编译到 ES5
 */
export async function asyncTSToJS(
  code: string,
  es5?: boolean,
): Promise<string> {
  return (await import('./babel')).tsToJS(code, es5)
}

/**
 * 将后端 ts 表达式转为 js，异步版本
 * @param code 代码
 * @param entryFnKey 入口函数键值
 */
export async function asyncBackTSExprToJS(
  code: string,
  entryFnKey?: string,
): Promise<string> {
  return (await import('./babel')).backTSExprToJS(code, entryFnKey)
}

/**
 * 将后端 ts 表达式转为 js，通过内部 __main 函数，异步版本
 * @param code 代码
 */
export async function asyncBackTSExprToJSByInternalMain(
  code: string,
): Promise<string> {
  return (await import('./babel')).backTSExprToJSByInternalMain(code)
}

/**
 * 将后端模型 ts 转为 js，异步版本
 * @param code 代码
 */
export async function asyncBackModelTSToJS(code: string): Promise<string> {
  return (await import('./babel')).backModelTSToJS(code)
}

/**
 * 将前端 ts 表达式转为 js，异步版本
 * @param code 代码
 */
export async function asyncFrontTSExprToJS(code: string): Promise<string> {
  return (await import('./babel')).frontTSExprToJS(code)
}

/**
 * 将通过容器执行的前端 ts 表达式转为 js，异步版本
 * @param code 代码
 */
export async function asyncFrontTSExprEvalByContainerToJS(
  code: string,
): Promise<string> {
  return (await import('./babel')).frontTSExprEvalByContainerToJS(code)
}

//#endregion
