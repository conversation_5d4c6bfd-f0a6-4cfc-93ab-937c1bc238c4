import type { Rule } from '@m-ui/react/lib/form'
import { isArray, isObject, isString, noop } from 'lodash'
import { useMemo, useState } from 'react'
import { asyncJSToES5, asyncTSToJS } from './babel'
import { asyncAssertMoneyOperation } from './money-operation'

/**
 * json 对象规则
 */
export const jsonObjRule: Rule = {
  async validator(...[, value]) {
    if (!isString(value) || !value) {
      return
    }
    const ret = JSON.parse(value)
    if (!isObject(ret) || isArray(ret)) {
      throw new Error('必须为对象形式')
    }
  },
}

/**
 * js 规则，是否能够正常编译
 */
export const jsRule: Rule = {
  async validator(...[, value]) {
    await asyncJSToES5(value)
  },
}

/**
 * ts 规则，是否能够正常编译
 */
export const tsRule: Rule = {
  async validator(...[, value]) {
    await asyncTSToJS(value)
  },
}

/**
 * 创建金额规则，是否金额操作都合法
 * @param cb 校验后回调
 */
export function createMoneyRule(cb: (err?: Error) => void): Rule {
  return {
    async validator(...[, value]): Promise<void> {
      try {
        await asyncAssertMoneyOperation(value)
      } catch (err) {
        if (err instanceof Error) {
          cb(err)
        }
        throw err
      }
      cb()
    },
  }
}

/**
 * 使用金额规则，是否金额操作都合法
 * @returns 规则，是否曾经有校验报错
 */
export function useMoneyRule(): [Rule, boolean] {
  const [hasErrBefore, setHasErrBefore] = useState(false)
  const rule = useMemo(
    () =>
      createMoneyRule((err) => {
        if (err) setHasErrBefore(true)
      }),
    [],
  )
  return [rule, hasErrBefore]
}

/**
 * 手动校验，只支持 validator 返回 Promise 的
 * @param rules 规则
 * @param value 值
 * @returns 校验结果
 */
export async function manualValidate(
  rules: Rule[],
  value: unknown,
): Promise<null | Error> {
  try {
    for (const rule of rules) {
      if ('validator' in rule) {
        await rule.validator?.(rule, value, noop)
      }
    }
  } catch (err) {
    if (err instanceof Error) {
      return err
    }
  }
  return null
}
