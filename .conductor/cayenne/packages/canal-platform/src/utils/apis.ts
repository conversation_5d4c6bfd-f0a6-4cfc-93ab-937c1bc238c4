import {
  doAutoSingleModuleDeploy,
  doStagingDeploy,
  getGlobalModuleDetail,
  updateModuleUsingPost,
} from '@/services/backend/module'
import { assertApiOk } from './error'

/**
 * 保存并部署模块选项
 */
export interface SaveAndDeployModuleOptions {
  /**
   * 模块内容
   */
  content: string
  /**
   * （业务）域代码
   */
  domainCode: string
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 模块版本
   */
  moduleVersion: number
  /**
   * 工作量
   */
  workload: number
  /**
   * 自动部署标识
   */
  autoDeploy?: 'beta' | 'prt'
}

/**
 * 保存并部署模块
 * @param param0 选项
 */
export async function saveAndDeployModule({
  content,
  domainCode,
  moduleId,
  moduleVersion,
  workload,
  autoDeploy,
}: SaveAndDeployModuleOptions): ReturnType<typeof updateModuleUsingPost> {
  const [updateModuleUsingPostRes, globalModuleDetailRes] = await Promise.all([
    updateModuleUsingPost({
      id: moduleId,
      version: `${moduleVersion}`,
      content,
      workload,
    }),
    getGlobalModuleDetail({ domainCode }),
  ])
  let globalModuleId: string | undefined = globalModuleDetailRes.data?.id // 可能不存在
  if (globalModuleId === moduleId) {
    globalModuleId = undefined
  }
  assertApiOk(updateModuleUsingPostRes)
  // 做最新的一次staging部署覆盖
  doStagingDeploy({
    module: {
      moduleId,
      version: moduleVersion,
      content,
      stage: 'staging',
      globalModuleId,
    },
    isNewDeploy: false,
  })
  // 检测url参数中是否对prt和beta有自动部署的标识，如果有则自动部署
  if (autoDeploy && ['prt', 'beta'].includes(autoDeploy)) {
    await doAutoSingleModuleDeploy({
      module: {
        moduleId,
        version: moduleVersion,
        content: content,
        stage: autoDeploy,
      },
    })
  }
  return updateModuleUsingPostRes
}
