import { DOMAIN_CODE_AD_ESP_CREATE } from '@/constants/domain-codes'
import { DESIGNER_QUERY } from '@/pages/designer/constants'
import type { AutoMergedConfig } from '@ad/canal-react-runtime'

/**
 * 转换的配置
 */
export type TransformedConfig = NonNullable<AutoMergedConfig>

/**
 * 配置转换
 */
export interface ConfigTransform {
  /**
   * 配置转换
   * @param config 默认配置
   */
  (config: TransformedConfig): TransformedConfig
}

/**
 * 为设计器内的运行时加载配置转换
 */
export async function loadConfigTransformForDesignerRuntime(): Promise<ConfigTransform> {
  switch (DESIGNER_QUERY.domainCode) {
    case DOMAIN_CODE_AD_ESP_CREATE: {
      return (await import('./ad-esp-create-config-transform')).configTransform
    }
  }
  return (await import('./default-config-transform')).configTransform
}
