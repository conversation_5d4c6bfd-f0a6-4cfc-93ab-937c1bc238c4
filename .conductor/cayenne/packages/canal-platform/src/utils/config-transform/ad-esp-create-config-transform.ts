import type { ConfigTransform } from '.'
import { configTransform as defaultConfigTransform } from './default-config-transform'

/**
 * 默认配置转换
 */
export const configTransform: ConfigTransform = (config) => {
  config = defaultConfigTransform(config)
  config.params = {
    ...config.params,
    mode: 'add',
    whiteList: {},
    validators: require('@ad/create-biz/dist/esm/validators'),
    watchers: require('@ad/create-biz/dist/esm/watchers'),
  }
  return config
}
