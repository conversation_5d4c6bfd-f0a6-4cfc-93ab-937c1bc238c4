import BIG_JS_INDEX from '!!raw-loader!@types/big.js/index.d.ts'
import LODASH_ARRAY from '!!raw-loader!@types/lodash/common/array.d.ts'
import LODASH_COLLECTION from '!!raw-loader!@types/lodash/common/collection.d.ts'
import LODASH_COMMON from '!!raw-loader!@types/lodash/common/common.d.ts'
import LODASH_DATE from '!!raw-loader!@types/lodash/common/date.d.ts'
import LODASH_FUNCTION from '!!raw-loader!@types/lodash/common/function.d.ts'
import LODASH_LANG from '!!raw-loader!@types/lodash/common/lang.d.ts'
import LODASH_MATH from '!!raw-loader!@types/lodash/common/math.d.ts'
import LODASH_NUMBER from '!!raw-loader!@types/lodash/common/number.d.ts'
import LODASH_OBJECT from '!!raw-loader!@types/lodash/common/object.d.ts'
import LODASH_SEQ from '!!raw-loader!@types/lodash/common/seq.d.ts'
import LODASH_STRING from '!!raw-loader!@types/lodash/common/string.d.ts'
import LODASH_UTIL from '!!raw-loader!@types/lodash/common/util.d.ts'
import LODASH_INDEX from '!!raw-loader!@types/lodash/index.d.ts'

/**
 * 获取 lodash 所有类型定义文件
 */
export function getLodashDtsFiles(): [string, string][] {
  return [
    ['node_modules/@types/lodash/index.d.ts', LODASH_INDEX],
    ['node_modules/@types/lodash/common/common.d.ts', LODASH_COMMON],
    ['node_modules/@types/lodash/common/array.d.ts', LODASH_ARRAY],
    ['node_modules/@types/lodash/common/collection.d.ts', LODASH_COLLECTION],
    ['node_modules/@types/lodash/common/date.d.ts', LODASH_DATE],
    ['node_modules/@types/lodash/common/function.d.ts', LODASH_FUNCTION],
    ['node_modules/@types/lodash/common/lang.d.ts', LODASH_LANG],
    ['node_modules/@types/lodash/common/math.d.ts', LODASH_MATH],
    ['node_modules/@types/lodash/common/number.d.ts', LODASH_NUMBER],
    ['node_modules/@types/lodash/common/object.d.ts', LODASH_OBJECT],
    ['node_modules/@types/lodash/common/seq.d.ts', LODASH_SEQ],
    ['node_modules/@types/lodash/common/string.d.ts', LODASH_STRING],
    ['node_modules/@types/lodash/common/util.d.ts', LODASH_UTIL],
  ]
}

/**
 * 获取 big.js 所有类型定义文件
 */
export function getBigJsDtsFiles(): [string, string][] {
  return [['node_modules/@types/big.js/index.d.ts', BIG_JS_INDEX]]
}
