import type { Promisable } from '@ad/canal-shared'

/**
 * 数据加载器
 */
export interface DataLoader<T, LoadOptions = void, SaveOptions = void> {
  /**
   * 加载
   * @param options 选项
   */
  load(options: LoadOptions): Promisable<T>
  /**
   * 保存
   * @param data 数据
   * @param options 选项
   */
  save(data: T, options: SaveOptions): Promisable<void>
}

/**
 * localStorage 数据加载器
 */
export class LocalStorageDataLoader<T> implements DataLoader<T, void, void> {
  /**
   * localStorage 数据加载器
   * @param _key 键值
   * @param _defaultData 默认数据
   */
  public constructor(
    private _key: string,
    private _defaultData: T,
  ) {}

  /**
   * 加载
   */
  public load(): T {
    const key = this._key
    const str = localStorage.getItem(key)
    if (str) {
      try {
        return JSON.parse(str)[0]
      } catch (err) {
        console.error('LocalStorageDataLoader::load parse err', err, {
          key,
          str,
        })
      }
    }
    return this._defaultData
  }

  /**
   * 保存
   * @param data 数据
   */
  public save(data: T): void {
    localStorage.setItem(this._key, JSON.stringify([data]))
  }
}
