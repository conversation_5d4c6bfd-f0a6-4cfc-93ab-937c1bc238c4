import { DOMAIN_CODE_AD_ESP_CREATE } from '@/constants/domain-codes'
import { DESIGNER_QUERY } from '@/pages/designer/constants'
import type { PurePlugin } from '@kael/runtime'

/**
 * 为设计器内的运行时加载额外的插件
 */
export async function loadExtraPluginsForDesignerRuntime(): Promise<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  PurePlugin<any, any>[]
> {
  switch (DESIGNER_QUERY.domainCode) {
    case DOMAIN_CODE_AD_ESP_CREATE: {
      return (await import('./ad-esp-create-extra-plugins')).extraPlugins
    }
  }
  return (await import('./default-extra-plugins')).extraPlugins
}
