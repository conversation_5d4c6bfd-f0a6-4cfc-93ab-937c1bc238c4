import type { DesignerQuery } from '@/pages/designer/types'
import { ModuleType } from '@ad/canal-shared'
import { pick } from 'lodash'
import qs from 'query-string'

export interface CreateDesignerUrlOptions extends DesignerQuery {}

/**
 * 创建设计器 URL
 * @param moduleType 模块类型
 * @param options 选项
 */
export function createDesignerUrl(
  moduleType: ModuleType | number,
  options: CreateDesignerUrlOptions,
): string {
  if (moduleType === ModuleType.GLOBAL) {
    return `/designer-global?${qs.stringify(
      pick(options, ['domainCode', 'moduleId', 'moduleVersion']),
    )}`
  }
  return `/designer?${qs.stringify(options)}`
}
