/**
 * 依赖
 */
export const dependencies = {
  ['@ad/canal-react-component-context']: require('@ad/canal-react-component-context'),
  ['@m-ui/icons']: require('@m-ui/icons'),
  ['@m-ui/react']: require('@m-ui/react'),
  ['@mchart/pc-react']: require('@mchart/pc-react'),
  dayjs: require('dayjs'),
  moment: require('moment'),
  lodash: require('lodash'),
  mobx: require('mobx'),
  ['mobx-react-lite']: require('mobx-react-lite'),
  react: require('react'),
  ['react/jsx-runtime']: require('react/jsx-runtime'),
  ['react-dom']: require('react-dom'),
  ['react-use']: require('react-use'),
  ['styled-components']: require('styled-components'),
  immer: require('immer'),
  ['react-dnd']: require('react-dnd'),
  ['react-dnd-html5-backend']: require('react-dnd-html5-backend'),
}
