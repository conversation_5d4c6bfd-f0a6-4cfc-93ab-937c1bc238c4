import {
  DOMAIN_CODE_AD_ESP_CREATE,
  DOMAIN_CODE_AD_ESP_MOBILE_CREATE,
  DOMAIN_CODE_AD_INFRA_LUOPAN,
  DOMAIN_CODE_AD_INFRA_MARKETING,
  DOMAIN_CODE_AD_INFRA_TIANYAN,
} from '@/constants/domain-codes'
import { DESIGNER_QUERY } from '@/pages/designer/constants'

/**
 * 为设计器内的运行时加载依赖
 */
export async function loadDependenciesForDesignerRuntime(): Promise<
  Record<string, unknown>
> {
  switch (DESIGNER_QUERY.domainCode) {
    case DOMAIN_CODE_AD_ESP_CREATE: {
      return (await import('./ad-esp-create-dependencies')).dependencies
    }
    case DOMAIN_CODE_AD_ESP_MOBILE_CREATE: {
      return (await import('./ad-esp-mobile-create-dependencies')).dependencies
    }
    case DOMAIN_CODE_AD_INFRA_LUOPAN: {
      return (await import('./ad-infra-luopan-dependencies')).dependencies
    }
    case DOMAIN_CODE_AD_INFRA_TIANYAN: {
      return (await import('./ad-infra-tianyan-dependencies')).dependencies
    }
    case DOMAIN_CODE_AD_INFRA_MARKETING: {
      return (await import('./ad-infra-marketing-dependencies')).dependencies
    }
  }
  return (await import('./default-dependencies')).dependencies
}
