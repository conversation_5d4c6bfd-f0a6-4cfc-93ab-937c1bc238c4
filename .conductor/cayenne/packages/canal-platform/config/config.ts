/* eslint-disable @typescript-eslint/naming-convention */
// https://k-mi.corp.kuaishou.com/config/config.html
import { defineConfig } from '@kmi/react'
import routes from './routes'

const publicPathMap: Record<typeof process.env.KMI_ENV, string> = {
  online: '//cdnfile.corp.kuaishou.com/kc/files/a/adCanal/platform/prod/',
  beta: '//cdnfile.corp.kuaishou.com/kc/files/a/adCanal/platform/beta/',
  staging: '//cdnfile.corp.kuaishou.com/kc/files/a/adCanal/platform/staging/',
  local: '/',
}

export default defineConfig({
  rspack: {},
  /**
   * @name 包管理工具选择
   */
  npmClient: 'pnpm',
  /**
   * @name 路由的配置，不在路由中引入的文件不会编译
   * @description 详情可见pro-component的路由配置
   * @doc https://kmi-components.test.gifshow.com/components/layout#route
   */
  routes,
  /**
   * @name 网络请求配置
   * @description 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
   * @doc https://k-mi.corp.kuaishou.com/guide/advanced/request.html
   */
  request: {},
  swet: {
    //swagger 提供的接口文档地址
    sources: [
      'https://canal.staging.kuaishou.com/rest/swagger-json',
      'http://localhost:9527/rest/swagger-json',
    ],
    supportTs: true,
    outDir: 'src/services/backend',
  },
  model: {},
  initialState: {},
  /**
   * @name layout 插件
   * @doc https://k-mi.corp.kuaishou.com/guide/advanced/layout.html
   */
  layout: {
    // layout主题
    theme: 'dark',
    // layout布局方式
    layout: 'mix',
    // 菜单分离配置
    // splitMenus: true,
    // fixSiderbar: true,
    // 菜单鼠标放置预览
    menuPreview: true,
    // 左上角Logo
    logo: 'https://cdnfile.corp.kuaishou.com/kc/files/a/adCanal/images/canal-logo-light.d241e35897c57f50.png',
    // 左上角标题
    title: '大运河',
  },
  title: '大运河',
  favicons: [
    'https://cdnfile.corp.kuaishou.com/kc/files/a/adCanal/images/canal-logo-light.d241e35897c57f50.png',
  ],
  publicPath: process.env.KMI_ENV ? publicPathMap[process.env.KMI_ENV] : '/',
  proxy: {
    '/rest': {
      target: 'http://test.corp.kuaishou.com:9527/',
      changeOrigin: true,
    },
    '/gateway': {
      target: 'https://canal.staging.kuaishou.com/',
      changeOrigin: true,
    },
  },
  jsMinifier: 'swc',
  cssMinifier: 'lightningcss',
  compat: {
    disableCoreJSLock: true,
  },
}) as unknown
