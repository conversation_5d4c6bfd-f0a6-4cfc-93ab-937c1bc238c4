/**
 * @name kmi 的路由配置
 * @description 默认支持 path,component,exact,routes,redirect,wrappers,title 的配置，如使用layout，支持layout中路由配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @doc https://k-mi.corp.kuaishou.com/guide/essentials/route.html
 * @doc https://kmi-components.test.gifshow.com/components/layout#route
 *
 *
 */

export default [
  {
    path: '/designer',
    name: '设计器',
    component: './designer',
    layout: false,
    hideInMenu: true,
  },
  {
    path: '/designer-global',
    name: '全局设计器',
    component: './designer-global',
    layout: false,
    hideInMenu: true,
  },
  {
    path: '/simulator',
    name: '模拟器',
    component: './simulator',
    layout: false,
    hideInMenu: true,
  },
  {
    path: '/previewer',
    name: '预览器',
    component: './previewer',
    layout: false,
    hideInMenu: true,
  },
  {
    path: '/open-designer',
    name: '打开设计器',
    component: './open-designer',
    layout: false,
    hideInMenu: true,
  },
  {
    path: '/',
    name: '',
    component: './home',
    siderRender: false,
    hideInMenu: true,
  },
  {
    name: '业务域',
    icon: 'HomeOutlined',
    path: '/business-domain/detail',
    component: './business-domain',
    hideInBreadcrumb: true,
    hideConfigKey: 'hideOldOverview',
  },
  {
    name: '业务域概览',
    icon: 'HomeOutlined',
    path: '/business-domain/overview',
    component: './business-domain/overview',
    hideInBreadcrumb: true,
    showConfigKey: 'showNewOverview',
  },
  {
    path: '/change/create',
    name: '创建变更',
    hideInMenu: true,
    component: './change/create',
    hideInBreadcrumb: true,
  },
  {
    path: '/change/list',
    name: '变更',
    component: './change/list',
    hideInBreadcrumb: true,
  },
  {
    path: '/change/detail',
    name: '变更详情',
    hideInMenu: true,
    component: './change/detail',
    hideInBreadcrumb: true,
  },
  {
    path: '*',
    layout: false,
    component: './not-found',
    hideInMenu: true,
  },
  {
    path: '/business-domain/component/list',
    name: '组件',
    component: './component-list',
    icon: 'AppstoreOutlined',
    hideInBreadcrumb: true,
  },
  {
    path: '/business-domain/component/create',
    name: '创建组件',
    component: './create-component',
    hideInMenu: true,
    hideInBreadcrumb: true,
  },
  {
    path: '/business-domain/component/edit',
    name: '编辑组件',
    component: './create-component',
    hideInMenu: true,
    hideInBreadcrumb: true,
  },
  {
    path: '/module/list',
    name: '模块',
    component: './module/list',
    hideInMenu: false,
    hideInBreadcrumb: true,
  },
  {
    path: '/module/template',
    name: '模版',
    component: './template',
    hideInMenu: false,
    hideInBreadcrumb: true,
  },
  {
    path: '/change/publishManage',
    name: '发布',
    component: './publish',
    hideInMenu: false,
    hideInBreadcrumb: true,
  },
  {
    path: '/business-domain/config',
    name: '配置',
    component: './business-domain/config',
    hideInMenu: false,
    hideInBreadcrumb: true,
  },
  {
    path: '/search',
    name: '搜索',
    component: './search/index',
    hideInBreadcrumb: true,
    hideInMenu: true,
    siderRender: false,
  },
  {
    path: '/change/overview',
    name: '变更详情',
    component: './change-overview/index',
    hideInBreadcrumb: true,
    hideInMenu: true,
  },
]
