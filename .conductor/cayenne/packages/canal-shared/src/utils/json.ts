import { PropertyPath } from '@kael/schema-utils'
import {
  isArray,
  isBoolean,
  isNull,
  isNumber,
  isObject,
  isString,
} from 'lodash'
import safeStableStringify, { Replacer } from 'safe-stable-stringify'

/**
 * 安全解析 JSON 字符串
 * @param str JSON 字符串
 * @param defaultValue 解析失败时返回的默认值
 */
export function safeParseJson<T = unknown>(
  str: string,
  defaultValue?: T,
): T | undefined {
  try {
    return JSON.parse(str)
  } catch (err) {
    return defaultValue
  }
}

/**
 * 安全地将 JSON 值转换为字符串选项
 */
export interface SafeStringifyJsonOptions {
  /**
   * 间距
   */
  space?: string | number
  /**
   * 替换器
   */
  replacer?: Replacer
  /**
   * 字符串化函数
   */
  stringify?: typeof safeStableStringify
}

/**
 * 安全地将 JSON 值转换为字符串
 * @param json JSON 值
 * @param defaultStr 转换失败时返回的字符串
 * @param space 间隔
 */
export function safeStringifyJson(
  json: unknown,
  defaultStr?: string,
  {
    space,
    replacer = null,
    stringify = safeStableStringify,
  }: SafeStringifyJsonOptions = {},
): string {
  try {
    const ret = stringify(json, replacer, space)
    if (isString(ret)) {
      return ret
    }
    if (isString(defaultStr)) {
      return defaultStr
    }
    return ''
  } catch (err) {
    if (isString(defaultStr)) {
      return defaultStr
    }
    return (err as Error).message
  }
}

/**
 * JSON 值详情
 */
export interface JsonValueDetail<T = unknown> {
  /**
   * 路径
   */
  path: PropertyPath
  /**
   * 值
   */
  value: T
}

/**
 * 生成（所有） JSON 值选项
 */
export interface GenJsonValuesOptions<T = unknown> {
  /**
   * 过滤器
   * @returns false 表示舍弃当前节点以及子节点
   */
  filter?(detail: JsonValueDetail<T>): boolean
}

/**
 * 生成（所有） JSON 值
 * @param json JSON 值
 * @param path 路径
 * @param objSet 对象集合
 */
export function* genJsonValues(
  json: unknown,
  options?: GenJsonValuesOptions,
  path: PropertyPath = [],
  objSet = new Set<unknown>(),
): Generator<JsonValueDetail> {
  if (objSet.has(json)) {
    return
  }
  const { filter } = options || {}
  const detail = {
    path,
    value: json,
  }
  if (filter && !filter(detail)) {
    return
  }
  yield detail
  if (isObject(json)) {
    objSet.add(json)
    if (isArray(json)) {
      for (let i = 0; i < json.length; i++) {
        yield* genJsonValues(json[i], options, [...path, i], objSet)
      }
    } else {
      for (const key in json) {
        yield* genJsonValues(
          (json as Record<string, unknown>)[key],
          options,
          [...path, key],
          objSet,
        )
      }
    }
  }
}

/**
 * 是 JSON 原始值
 * @param v 值
 */
export function isJsonPrimitive(
  v?: unknown,
): v is null | boolean | number | string {
  return isNull(v) || isBoolean(v) || isNumber(v) || isString(v)
}
