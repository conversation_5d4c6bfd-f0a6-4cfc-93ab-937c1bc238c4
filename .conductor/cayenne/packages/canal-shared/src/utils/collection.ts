import { PropertyPath } from '@kael/schema-utils'
import { get, isArray, isEqualWith, isNil, isUndefined, last } from 'lodash'
import { Nil } from '../types'

export {
  Arrayable,
  Unarrayable,
  arrayToArrayable,
  arrayableToArray,
} from '@ad/e2e-schema-utils'

/**
 * 通过路径删除值，合 unset 不同的地方在于 unset 会在数组里留下 undefined
 * @param o 对象
 * @param path 路径
 */
export function deleteByPath<T>(o: T, path: PropertyPath): T | null {
  if (!path.length) {
    return null
  }
  const parent = get(o, path.slice(0, -1))
  const lastKey = last(path) as PropertyPath[number]
  if (isArray(parent)) {
    parent.splice(lastKey as number)
  } else {
    delete parent[lastKey]
  }
  return o
}

/**
 * 过滤出非空值
 * @param arr 数组
 */
export function filterNotNil<T>(arr: T[]): Exclude<T, Nil>[] {
  return arr.filter((v) => !isNil(v)) as Exclude<T, Nil>[]
}

/**
 * 浅比较两个对象（或数组）是否相等
 * @param a 对象 a
 * @param b 对象 b
 */
export function isShallowEqual(a: unknown, b: unknown): boolean {
  return isEqualWith(a, b, (aval, bval, indexOrKey) => {
    if (!isUndefined(indexOrKey)) return Object.is(aval, bval)
  })
}
