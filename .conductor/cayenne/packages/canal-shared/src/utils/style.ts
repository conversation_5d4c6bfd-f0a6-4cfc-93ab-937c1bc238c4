import { isNumber, isString } from 'lodash'
import { toFixed } from './number'

/**
 * 标准化样式值
 * @param v 样式值
 */
export function normalizeStyleValue(v: number | string): string {
  return isNumber(v) ? `${v}px` : v
}

/**
 * 最小像素值，小于等于该值后，不进行转换
 */
const MIN_PIXEL_VALUE = 1

/**
 * 单位精度
 */
const UNIT_PRECISION = 5

/**
 * 像素正则表达式
 */
const PX_REG_EXP = /\d*\.?\d+px/g

/**
 * 将 px 转换为 vw
 * @param v 值
 * @param designWidth 设计宽度
 */
export function px2vw(v: unknown, designWidth: number): unknown {
  if (isNumber(v)) {
    return px2vwUnit(v, designWidth)
  }
  if (isString(v)) {
    return v.replace(PX_REG_EXP, (m) => {
      return px2vwUnit(m, designWidth)
    })
  }
  return v
}

/**
 * 将 px 转换为 vw，单元版
 * @param v 值
 * @param designWidth 设计宽度
 */
function px2vwUnit(v: string, designWidth: number): string
/**
 * 将 px 转换为 vw，单元版
 * @param v 值
 * @param designWidth 设计宽度
 */
function px2vwUnit(v: number, designWidth: number): string | number
/**
 * 将 px 转换为 vw，单元版
 * @param v 值
 * @param designWidth 设计宽度
 */
function px2vwUnit(v: string | number, designWidth: number): string | number {
  const n = isNumber(v) ? v : parseFloat(v)
  if (isNaN(n) || n <= MIN_PIXEL_VALUE) {
    return v
  }
  return `${toFixed((n / designWidth) * 100, UNIT_PRECISION)}vw`
}

/**
 * 将 px 转换为 rem
 * @param v 值
 * @param rootElementFontSize 根元素字体大小
 */
export function px2rem(v: unknown, rootElementFontSize: number): unknown {
  if (isNumber(v)) {
    return px2remUnit(v, rootElementFontSize)
  }
  if (isString(v)) {
    return v.replace(PX_REG_EXP, (m) => {
      return px2remUnit(m, rootElementFontSize)
    })
  }
  return v
}

/**
 * 将 px 转换为 rem，单元版
 * @param v 值
 * @param rootElementFontSize 根元素字体大小
 */
function px2remUnit(v: string, rootElementFontSize: number): string
/**
 * 将 px 转换为 rem，单元版
 * @param v 值
 * @param rootElementFontSize 根元素字体大小
 */
function px2remUnit(v: number, rootElementFontSize: number): string | number
/**
 * 将 px 转换为 rem，单元版
 * @param v 值
 * @param rootElementFontSize 根元素字体大小
 */
function px2remUnit(
  v: string | number,
  rootElementFontSize: number,
): string | number {
  const n = isNumber(v) ? v : parseFloat(v)
  if (isNaN(n) || n <= MIN_PIXEL_VALUE) {
    return v
  }
  return `${toFixed(n / rootElementFontSize, UNIT_PRECISION)}rem`
}
