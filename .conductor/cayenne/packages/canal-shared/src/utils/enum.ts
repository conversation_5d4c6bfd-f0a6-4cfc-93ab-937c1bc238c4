/**
 * 获取枚举的所有 key
 * @param e 枚举
 * @returns 枚举所有的 key
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getEnumKeys<E extends Record<string, any>>(e: E): string[] {
  return Object.keys(e).filter((key) => typeof e[e[key]] !== 'number')
}

/**
 * 获取枚举的所有值
 * @param e 枚举
 * @returns 枚举所有的值
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getEnumValues<E extends Record<string, any>>(
  e: E,
): E[keyof E][] {
  return getEnumKeys(e).map((key) => e[key])
}

/**
 * 标准化枚举的值
 * @param value 需要标准化的值
 * @param e 枚举
 * @param defaultValue 默认值
 * @returns 标准化后的枚举值
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function normalizeEnumValue<E extends Record<string, any>>(
  value: unknown,
  e: E,
  defaultValue: E[keyof E],
): E[keyof E] {
  return getEnumValues(e).indexOf(value as E[keyof E]) !== -1
    ? (value as E[keyof E])
    : defaultValue
}
