import { Promisable } from 'type-fest'

/**
 * Promise 值
 */
export function promiseValue<T>(): [
  Promise<T>,
  (value: T | PromiseLike<T>) => void,
  (reason?: unknown) => void,
] {
  let resolve: (value: T | PromiseLike<T>) => void
  let reject: (reason?: unknown) => void
  const p = new Promise<T>(
    (pResolve, pReject) => ([resolve, reject] = [pResolve, pReject]),
  )
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  return [p, resolve!, reject!]
}

/**
 * 静默等待 Promise
 */
export async function waitPromiseSlient<T>(
  p: Promisable<T>,
): Promise<T | null> {
  try {
    return await p
  } catch (err) {
    void err
  }
  return null
}
