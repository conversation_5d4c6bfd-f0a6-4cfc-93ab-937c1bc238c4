import { CreateEndpointOptions, EncodingStrategy } from '@remote-ui/rpc'

/**
 * 创建快速编码器，不会有内置编码器的参数函数传输功能，但性能比较好
 */
export const createQuickEncoder: NonNullable<
  CreateEndpointOptions['createEncoder']
> = () => {
  const encoder: EncodingStrategy = {
    encode(value) {
      return [value]
    },
    decode(value) {
      return value
    },
    async call() {
      // noop
    },
    release() {
      // noop
    },
  }
  return encoder
}
