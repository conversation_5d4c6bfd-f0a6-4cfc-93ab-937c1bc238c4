/**
 * 睡 ms 毫秒
 * @param ms 毫秒
 */
export function sleep(ms?: number): Promise<void> {
  return new Promise((r) => setTimeout(r, ms))
}

/**
 * 睡一个微任务
 */
export function sleepMicrotask(): Promise<void> {
  return Promise.resolve()
}

/**
 * 睡一帧
 */
export function sleepFrame(): Promise<DOMHighResTimeStamp> {
  return new Promise((r) => requestAnimationFrame(r))
}

/**
 * 按帧等待
 * @param predicate 预测函数
 */
export async function waitByFrame(predicate: () => boolean): Promise<void> {
  while (!predicate()) {
    await sleepFrame()
  }
}
