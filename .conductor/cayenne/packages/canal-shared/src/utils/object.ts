import { isArray, isEqual, isPlainObject, isUndefined, transform } from 'lodash'
import { AnyObject } from '../types'

/**
 * 删除 undefined 的值
 * @param o 对象
 */
export function deleteUndefinedValues<T extends object>(o: T): T {
  for (const key in o) {
    if (isUndefined(o[key])) delete o[key]
  }
  return o
}

/**
 * 深度移除 undefined 的值，没有防嵌套，有需要的话，可以改一下
 * @param obj 对象
 */
export function removeUndefinedDeep<T>(obj: T): T {
  if (isArray(obj)) {
    return obj.map(removeUndefinedDeep) as T
  } else if (isPlainObject(obj)) {
    return transform(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      obj as any,
      (result, value, key) => {
        if (!isUndefined(value)) {
          ;(result as AnyObject)[key] = removeUndefinedDeep(value)
        }
      },
      {},
    ) as T
  }
  return obj
}

/**
 * 比较两个对象是否相等，忽略 undefined 的值
 * @param a 对象 a
 * @param b 对象 b
 */
export function isEqualIgnoreUndefined(a: unknown, b: unknown): boolean {
  return isEqual(removeUndefinedDeep(a), removeUndefinedDeep(b))
}
