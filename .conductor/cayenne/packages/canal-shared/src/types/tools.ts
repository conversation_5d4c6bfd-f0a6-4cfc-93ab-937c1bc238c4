import { Promisable } from 'type-fest'

/**
 * 假值
 */
export type Falsy = false | 0 | '' | null | undefined

/**
 * 去除假值
 */
export type NonFalsy<T> = T extends Falsy ? never : T

/**
 * 任意函数
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type AnyFn = (...args: any[]) => any

/**
 * 任意异步函数
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type AnyAsyncFn = (...args: any[]) => Promise<any>

/**
 * 去除任意函数
 */
export type ExcludeAnyFn<T> = Exclude<T, AnyFn>

export { Promisable }

/**
 * 取 Promisable 的值
 */
export type Unpromisable<T> = T extends Promisable<infer V> ? V : unknown

/**
 * 空值
 */
export type Nil = undefined | null

/**
 * 任意对象
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type AnyObject = Record<PropertyKey, any>

/**
 * 只读未知数组
 */
export type ReadonlyUnknownArray = readonly unknown[]

/**
 * 函数
 */
export type Fn<Args extends ReadonlyUnknownArray, R> = (...args: Args) => R

/**
 * 回调函数
 */
export type CbFn<Args extends ReadonlyUnknownArray, R = void> = Fn<Args, R>

/**
 * 返回某种值的函数
 */
export type ReturnValueFn<R, Args extends ReadonlyUnknownArray = []> = Fn<
  Args,
  R
>

/**
 * 可返回的值
 */
export type Returnable<T, Args extends ReadonlyUnknownArray = []> =
  | T
  | ReturnValueFn<T, Args>
