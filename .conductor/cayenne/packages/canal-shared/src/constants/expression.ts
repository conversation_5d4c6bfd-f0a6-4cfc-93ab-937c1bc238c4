import { getEnumValues } from '../utils'

/**
 * 表达式环境
 */
export enum ExpressionEnv {
  BACK,
  FRONT,
}

//#region 后端表达式

/**
 * 后端表达式类型
 */
export enum BackExpressionType {
  NO = -1,
  JSONATA,
  JAVASCRIPT,
  TYPESCRIPT,
}

/**
 * 后端表达式类型，有效的
 */
export type BackExpressionTypeValid = Exclude<
  BackExpressionType,
  BackExpressionType.NO
>

/**
 * 所有后端表达式类型，定制顺序
 */
export const BACK_EXPRESSION_TYPES = [
  BackExpressionType.NO,
  BackExpressionType.JAVASCRIPT,
  BackExpressionType.TYPESCRIPT,
  BackExpressionType.JSONATA,
]

/**
 * 后端表达式类型有效的值
 */
export const BACK_EXPRESSION_TYPE_VALID_VALUES = BACK_EXPRESSION_TYPES.filter(
  (t) => t !== BackExpressionType.NO,
) as BackExpressionTypeValid[]

/**
 * 后端表达式类型，js 和 ts
 */
export type BackExpressionTypeJSTS = (typeof BACK_EXPRESSION_TYPE_JS_TS)[number]

/**
 * 后端表达式类型，js 和 ts
 */
export const BACK_EXPRESSION_TYPE_JS_TS = [
  BackExpressionType.JAVASCRIPT,
  BackExpressionType.TYPESCRIPT,
] as const

/**
 * 后端表达式类型文本
 */
export const BACK_EXPRESSION_TYPE_TEXT: Record<BackExpressionType, string> = {
  [BackExpressionType.NO]: '无',
  [BackExpressionType.JSONATA]: '后端 JSONata',
  [BackExpressionType.JAVASCRIPT]: '后端 JavaScript',
  [BackExpressionType.TYPESCRIPT]: '后端 TypeScript',
}

/**
 * 后端表达式类型选项
 */
export const BACK_EXPRESSION_TYPE_OPTIONS = BACK_EXPRESSION_TYPES.map((t) => ({
  label: BACK_EXPRESSION_TYPE_TEXT[t],
  value: t,
}))

/**
 * 后端表达式类型有效的选项
 */
export const BACK_EXPRESSION_TYPE_VALID_OPTIONS =
  BACK_EXPRESSION_TYPE_VALID_VALUES.map((t) => ({
    label: BACK_EXPRESSION_TYPE_TEXT[t],
    value: t,
  }))

/**
 * 后端表达式类型，js 和 ts 选项
 */
export const BACK_EXPRESSION_TYPE_JS_TS_OPTIONS =
  BACK_EXPRESSION_TYPE_JS_TS.map((t) => ({
    label: BACK_EXPRESSION_TYPE_TEXT[t],
    value: t,
  }))

//#endregion

//#region 前端表达式

export enum FrontExpressionType {
  JAVASCRIPT = 100,
  TYPESCRIPT,
}

/**
 * 前端表达式类型文本
 */
export const FRONT_EXPRESSION_TYPE_TEXT: Record<FrontExpressionType, string> = {
  [FrontExpressionType.JAVASCRIPT]: '前端 JavaScript',
  [FrontExpressionType.TYPESCRIPT]: '前端 TypeScript',
}

/**
 * 前端表达式类型选项
 */
export const FRONT_EXPRESSION_TYPE_OPTIONS = getEnumValues(
  FrontExpressionType,
).map((t) => ({
  label: FRONT_EXPRESSION_TYPE_TEXT[t],
  value: t,
}))

//#endregion

/**
 * 统一的表达式类型文本
 */
export const UNI_EXPRESSION_TYPE_TEXT = {
  ...BACK_EXPRESSION_TYPE_TEXT,
  ...FRONT_EXPRESSION_TYPE_TEXT,
}
