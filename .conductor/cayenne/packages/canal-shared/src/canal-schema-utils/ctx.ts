import { E2ECMSFieldProp } from '@ad/e2e-material-schema'
import type { E2ESchema, Model } from '@ad/e2e-schema'
import { dfsGenComponentDetailBySchema } from '@kael/schema-utils'

/**
 * 上下文数据字段
 */
export interface CtxDataField {
  /**
   * 键值
   */
  key: string
  /**
   * 名称
   */
  name: string
  /**
   * TS 类型声明
   */
  declaration?: string
}

/**
 * 获取值属性
 */
export interface GetValueProp {
  /**
   * 获取值属性
   * @param componentType 组件类型
   */
  (componentType: string): E2ECMSFieldProp | null
}

/**
 * 通过 Schema 获取上下文数据字段
 * @param schema 端到端 Schema
 * @param getValueProp 获取值属性
 */
export function getCtxDataFieldsBySchema(
  schema: E2ESchema | null | undefined,
  getValueProp: GetValueProp,
): CtxDataField[] {
  const dataFields: CtxDataField[] = []
  if (schema) {
    for (const cd of dfsGenComponentDetailBySchema(schema)) {
      const declaration = getValueProp(cd.component.type)?.declaration
      dataFields.push({
        key: cd.component.id,
        name: cd.component.name,
        declaration,
      })
    }
  }
  return dataFields
}

/**
 * 获取后端上下文类型定义
 * @param dataFields 数据字段
 * @param model 模型
 * @param globalModel 全局模型
 */
export function getBackCtxDts(
  dataFields: CtxDataField[],
  model?: Model,
  globalModel?: Model,
): string {
  return `
declare module '*'

/**
 * 上下文
 */
declare interface Ctx {
  /**
   * 请求信息
   */
  request: CtxRequest;
  /**
   * 响应信息，选择多接口时，为第一个接口的响应
   */
  response: CtxResponse;
  /**
   * 响应信息，跟接口选择顺序对应
   */
  responses: CtxResponse[];
  /**
   * 循环信息
   */
  loop?: CtxLoop
  /**
   * 全局模型
   */
  globalModel?: ${getModelType(globalModel)}
  /**
   * 模型
   */
  model: ${getModelType(model)}
}

/**
 * 上下文请求
 */
declare interface CtxRequest {
  /**
   * Schema 文件 ID
   */
  schemaId: string | number;
  /**
   * 刷新类型
   */
  refreshType: RefreshType;
  /**
   * 参数
   */
  params: CtxRequestParams;
  /**
   * 环境
   */
  env: FetchSchemaEnv;
  /**
   * 版本，后端会设置为 E2EServerSchema['version']
   */
  version: string;
}

/**
 * 上下文请求参数
 */
declare interface CtxRequestParams {
  /**
   * 未知参数，可能由外部传入
   */
  [k: string]: any
  ${dataFields
    .map(
      (field) => `
  /**
   * ${field.name.replace(/\*\//g, '')}
   */
  ${JSON.stringify(field.key)}: ${field.declaration || 'any'};
`,
    )
    .join('')}
}

/**
 * 刷新类型
 * * default: 默认，比如第一次刷新
 * * submit: 表单提交
 * * auto: 自动刷新，由 linkage 定义什么时候刷新
 * * outside-params: 外部参数，由配置参数变化引起的刷新
 */
declare type RefreshType =
  | "default"
  | "submit"
  | "auto"
  | "outside-params"
  // eslint-disable-next-line @typescript-eslint/ban-types
  | (string & {});

/**
 * 获取 Schema 文件环境
 * * production: 生产环境
 * * staging: staging 环境
 * * prt: 生产回归测试环境
 */
declare type FetchSchemaEnv = "production" | "staging" | "prt";

/**
 * 上下文响应
 */
declare interface CtxResponse {
  /**
   * 结果（编码）
   */
  result: number;
  /**
   * （文本）信息
   */
  msg: string;
  /**
   * （接口）数据
   */
  data: any;
}

/**
 * 上下文循环
 */
declare interface CtxLoop {
  /**
   * 数据项
   */
  item: unknown
  /**
   * 数据项下标
   */
  index: number
  /**
   * 数据项键值，默认用下标生成
   */
  key: string
  /**
   * 上一层上下文循环
   */
  parentLoop?: CtxLoop
}

${getModelDeclaration(model)}

${getModelDeclaration(globalModel)}
`
}

/**
 * 获取前端上下文类型定义
 * @param dataFields 数据字段
 * @param model 模型
 * @param globalModel 全局模型
 */
export function getFrontCtxDts(
  dataFields: CtxDataField[],
  model?: Model,
  globalModel?: Model,
): string {
  return `
declare module '*'

/**
 * 容器上下文
 */
declare interface Container {
  /**
   * 数据，组件 ID -> 组件数据。
   * 部分业务数据、函数会挂在 data 上，提供给前端表达式使用。
   */
  data: ContainerData
  /**
   * 设置数据
   * @param partialData 部分数据
   */
  setData(partialData: Partial<ContainerData>): void
  /**
   * 初始化参数，指业务方传给运行时的 params 字段。
   * 部分业务数据、函数会挂在 initParams 上，提供给前端表达式使用。
   */
  initParams?: Record<string, unknown>
  /**
   * 触发埋点
   * @param trackId 埋点 ID
   * @param transformTrack 转换埋点
   */
  track(
    trackId: string,
    transformTrack?: Partial<Track> | ((track: Track) => Track | void),
  ): void
  /**
   * 全局模型
   */
  globalModel?: ${getModelType(globalModel)}
  /**
   * 模型
   */
  model: ${getModelType(model)}
  /**
   * 父模块上下文
   */
  parentModuleCtx?: Container
  /**
   * 子模块上下文，子模块组件 ID -> 上下文
   */
  childModuleCtxs: Record<string, Container>
  /**
   * 执行信息
   */
  evalInfo?: EvalInfo
}

/**
 * 容器上下文数据
 */
declare interface ContainerData {
  /**
   * 未知数据，可能由外部传入
   */
  [k: string]: any
  ${dataFields
    .map(
      (field) => `
  /**
   * ${field.name.replace(/\*\//g, '')}
   */
  ${JSON.stringify(field.key)}: ${field.declaration || 'any'};
`,
    )
    .join('')}
}

/**
 * 埋点
 * https://component.corp.kuaishou.com/docs/weblogger/views/docs/api.html#%E6%94%B6%E9%9B%86%E4%B8%8A%E6%8A%A5
 */
declare interface Track {
  /**
   * ID
   */
  id: string
  /**
   * 事件类型
   */
  eventType: string
  /**
   * 事件参数
   */
  eventOptions: Record<string, any>
  /**
   * 可重复上报，默认：true
   */
  canRepeat?: boolean
}

/**
 * 执行信息
 */
declare interface EvalInfo {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 组件 ID
   */
  componentId?: string
  /**
   * 相对路径
   */
  relativePath: (string | number)[]
  /**
   * 表达式类型
   */
  expressionType: string
}

${getModelDeclaration(model)}

${getModelDeclaration(globalModel)}
`
}

/**
 * 获取模型类型
 * @param model 模型
 */
function getModelType(model?: Model): string {
  return model?.codeTS?.match(/export\s+default\s+class\s+(\S+)/)?.[1] || 'any'
}

/**
 * 获取模型声明
 * @param model 模型
 */
function getModelDeclaration(model?: Model): string {
  return (
    model?.codeTS
      ?.replace(/^\s*export\s+default\s+/gm, 'declare ')
      .replace(/^\s*export\s+/gm, '') || ''
  )
}
