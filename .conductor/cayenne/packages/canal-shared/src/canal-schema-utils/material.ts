import { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { BizComp } from './types'

/**
 * 业务组件转组件物料 Schema
 * @param bizComp 业务组件
 */
export function bizComp2ComponentMaterialSchema(
  bizComp: BizComp,
): E2ERemoteComponentMaterialSchema {
  const jsonPart = JSON.parse(bizComp.propsConfig) as Pick<
    E2ERemoteComponentMaterialSchema,
    'type' | 'props' | 'implements'
  > &
    Partial<Pick<E2ERemoteComponentMaterialSchema, 'code'>>
  const ret: E2ERemoteComponentMaterialSchema = {
    schemaVersion: '0.0.1',
    ...jsonPart,
    type: bizComp.id,
    name: bizComp.name,
    icon: bizComp.coverUrl,
    description: bizComp.descs,
    code: {
      // exportIdentifier 和 code.js 字段名不能修改，ESP 的插件依赖了这个
      code: {
        js: bizComp.resourceUrl,
      },
      exportIdentifier:
        jsonPart?.code?.type === 'low-code'
          ? undefined
          : jsonPart.code?.exportIdentifier,
    },
    id: bizComp.id,
    version: `${bizComp.version}`,
  }
  ret.implements = {
    container: bizComp.isContainer,
    ...ret.implements,
  }
  return ret
}
