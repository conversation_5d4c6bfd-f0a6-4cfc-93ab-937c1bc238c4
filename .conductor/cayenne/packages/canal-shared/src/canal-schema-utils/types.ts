/**
 * 从 [models.ts](../../../canal-platform/src/services/backend/models.ts) 的 ComponentDetail 复制
 */
export interface BizComp {
  /** 示例图url */
  coverUrl: string
  /** 关联物料中心组件id */
  associatedComponentId: string
  /** 关联物料中心组件版本 */
  associatedComponentVersion: string
  /** 资源url */
  resourceUrl: string
  /** 属性配置 */
  propsConfig: string
  /** 事件key */
  eventKey: string
  /** 组件名称 */
  name: string
  /** 组件类型：web-0，H5-1，RN-2，Native-3 */
  type: number
  /** 业务域code */
  businessDomainCode: string
  /** 组件描述 */
  descs: string
  /** 是否容器组件 */
  isContainer: boolean
  /** 已删除，软删除 */
  isDeleted: boolean
  /** 组件id */
  id: string
  /** 组件版本 */
  version: number
  /** 组件分类 */
  group?: string
}

/**
 * 从 [models.ts](../../../canal-platform/src/services/backend/models.ts) 的 DataSourceConfigDto 复制
 */
export interface DataSrc {
  id?: string
  domainCode: string
  name: string
  path: string
  type: string
  method: string
  stagingDomain?: string
  prtDomain?: string
  betaDomain?: string
  productionDomain?: string
  mockRes?: string
  createUser: string
  updateUser: string
  createTime: number
  updateTime: number
}
