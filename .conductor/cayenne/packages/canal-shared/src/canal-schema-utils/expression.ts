import { E2ESchemaExpression } from '@ad/e2e-schema'
import { isJsExpression } from '@ad/e2e-schema-utils'
import { BackExpressionType, FrontExpressionType } from '../constants'

/**
 * 通过类型获取 JS 表达式代码
 * @param expressionType 表达式类型
 * @param expression 表达式
 */
export function getJsExpressionCodeWithType(
  expressionType:
    | BackExpressionType.JAVASCRIPT
    | BackExpressionType.TYPESCRIPT
    | FrontExpressionType.JAVASCRIPT
    | FrontExpressionType.TYPESCRIPT,
  expression?: E2ESchemaExpression,
): string | null {
  let code
  if (isJsExpression(expression)) {
    code =
      expressionType === BackExpressionType.JAVASCRIPT ||
      expressionType === FrontExpressionType.JAVASCRIPT
        ? expression.codeES
        : expression.codeTS
  }
  return code || null
}
