import { E2EAPI } from '@ad/e2e-schema'
import { DataSrc } from './types'

/**
 * 创建数据源标签
 * @param dataSource 数据源
 */
export function createDataSourceLabel(dataSource: DataSrc): string {
  return `【${dataSource.method}】${dataSource.name}-${dataSource.productionDomain}${dataSource.path}`
}

/**
 * 获取数据源
 */
export interface GetDataSrc {
  /**
   * 获取数据源
   * @param dataSourceId 数据源 ID
   */
  (dataSourceId: string): DataSrc | undefined | null
}

/**
 * 通过接口创建数据源标签
 * @param api 接口
 * @param getDataSrc 获取数据源
 */
export function createDataSourceLabelByApi(
  api: Pick<E2EAPI, 'dataSourceId' | 'service' | 'method'>,
  getDataSrc: GetDataSrc,
): string {
  if (api.dataSourceId) {
    const dataSource = getDataSrc(api.dataSourceId)
    if (dataSource) {
      return createDataSourceLabel(dataSource)
    }
  }
  if (api.service && api.method) {
    if (api.service?.startsWith('http')) {
      return `${api.service}${api.method}`
    } else {
      return `${api.service}::${api.method}`
    }
  }
  return ''
}

/**
 * 创建接口标签
 * @param api 接口
 * @param pos 位置
 * @param getDataSrc 获取数据源
 */
export function createApiLabel(
  api: E2EAPI,
  pos: 'module' | 'component' | null | undefined,
  getDataSrc: GetDataSrc,
): string {
  if (api.name) {
    return wrapByPos(api.name)
  } else if (api.dataSourceId) {
    const dataSource = getDataSrc(api.dataSourceId)
    if (dataSource) {
      return wrapByPos(
        `${dataSource.name} ${dataSource.productionDomain}${dataSource.path}`,
      )
    }
  } else {
    if (api.service?.startsWith('http')) {
      return wrapByPos(`${api.service}${api.method}`)
    } else {
      return wrapByPos(`${api.service}::${api.method}`)
    }
  }
  return ''

  function wrapByPos(ret: string): string {
    return pos ? `[${pos === 'module' ? '模块' : '组件'}] ${ret}` : ret
  }
}
