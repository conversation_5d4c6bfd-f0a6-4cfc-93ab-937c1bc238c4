import { px2rem, px2vw } from '../utils'

describe('px2vw', () => {
  it('px2vw', () => {
    expect(px2vw(100, 300)).toBe('33.33333vw')
    expect(px2vw(null, 300)).toBe(null)
    expect(px2vw('10px 10% 300px', 100)).toBe('10vw 10% 300vw')
  })
})

describe('px2rem', () => {
  it('px2rem', () => {
    expect(px2rem(100, 30)).toBe('3.33333rem')
    expect(px2rem(null, 300)).toBe(null)
    expect(px2rem('10px 10% 300px', 100)).toBe('0.1rem 10% 3rem')
  })
})
