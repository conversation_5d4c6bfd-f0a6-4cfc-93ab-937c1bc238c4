假如你是一个低代码应用的开发专家，请根据以下信息，产出用户正在编辑的后端表达式代码：

【名词解释】
模块：用户在低代码平台上开发的基础单位，类似于全代码开发中的文件。模块可能是一个页面，也可能是页面中的某一个独立的部分。
Schema：模块内容的一种 JSON 表示。用户不会感知到 Schema 的内容及其结构，用户是通过一个可视化的网页编辑器间接编辑 Schema 的。
表达式：基于 JSON 编写的数据计算逻辑，可以兼容各种类型的计算方式。
后端表达式：在后端运行时里运行的 JS 代码，以 JS 代码字符串的方式在 JSON 里描述数据计算逻辑。后端表达式需要在 default 字段导出一个函数，函数有一个上下文参数。
后端表达式场景：后端表达式常用于根据请求参数计算业务接口的参数，或者根据接口返回值计算组件属性等场景。接下来我们会用 JSON 来结构化后端表达式场景的描述方式。
后端运行时：在后端服务中读取并运行 Schema 的功能，会计算后端表达式，并将计算结果填入 Schema 后返回给前端。后端运行时目前只向后端表达式提供了一个 npm 包：big.js。

【后端表达式上下文参数类型】
TS 类型（提供给 monaco-editor 使用的定义文件）：

```ts
declare module '*'

/**
 * 上下文
 */
declare interface Ctx {
  /**
   * 请求信息
   */
  request: CtxRequest
  /**
   * 响应信息，选择多接口时，为第一个接口的响应
   */
  response: CtxResponse
  /**
   * 响应信息，跟接口选择顺序对应
   */
  responses: CtxResponse[]
  /**
   * 循环信息
   */
  loop?: CtxLoop
  /**
   * 全局模型
   */
  globalModel?: any
  /**
   * 模型
   */
  model: any
}

/**
 * 上下文请求
 */
declare interface CtxRequest {
  /**
   * Schema 文件 ID
   */
  schemaId: string | number
  /**
   * 刷新类型
   */
  refreshType: RefreshType
  /**
   * 参数
   */
  params: CtxRequestParams
  /**
   * 环境
   */
  env: FetchSchemaEnv
  /**
   * 版本，后端会设置为 E2EServerSchema['version']
   */
  version: string
}

/**
 * 上下文请求参数
 */
declare interface CtxRequestParams {
  /**
   * 未知参数，可能由外部传入
   */
  [k: string]: any
}

/**
 * 刷新类型
 * * default: 默认，比如第一次刷新
 * * submit: 表单提交
 * * auto: 自动刷新，由 linkage 定义什么时候刷新
 * * outside-params: 外部参数，由配置参数变化引起的刷新
 */
declare type RefreshType =
  | 'default'
  | 'submit'
  | 'auto'
  | 'outside-params'
  // eslint-disable-next-line @typescript-eslint/ban-types
  | (string & {})

/**
 * 获取 Schema 文件环境
 * * production: 生产环境
 * * staging: staging 环境
 * * prt: 生产回归测试环境
 */
declare type FetchSchemaEnv = 'production' | 'staging' | 'prt'

/**
 * 上下文响应
 */
declare interface CtxResponse {
  /**
   * 结果（编码）
   */
  result: number
  /**
   * （文本）信息
   */
  msg: string
  /**
   * （接口）数据
   */
  data: any
}

/**
 * 上下文循环
 */
declare interface CtxLoop {
  /**
   * 数据项
   */
  item: unknown
  /**
   * 数据项下标
   */
  index: number
  /**
   * 数据项键值，默认用下标生成
   */
  key: string
  /**
   * 上一层上下文循环
   */
  parentLoop?: CtxLoop
}
```

【后端表达式场景结构化】
TS 类型：

```ts
/**
 * 后端表达式场景
 */
type BackExpressionScene =
  | BackExpressionSceneProp
  | BackExpressionSceneApiArgs
  | BackExpressionSceneApiIf
  | BackExpressionSceneIife

/**
 * 后端表达式属性场景。
 * 该场景下，表达式的返回值会被用作组件的属性。
 * 但需要注意的是，value 属性是特殊的，前端也会维护一份组件的值，如果后端运行时返回一个非 null 的值，则会覆盖前端维护的组件值，返回 null 则会被忽略。
 */
interface BackExpressionSceneProp {
  /**
   * 类型
   */
  type: 'prop'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
  /**
   * 接口，这里给出的是展示给用户看的拼接字符串。
   * 存在一个接口时，表达式通常主要从 ctx.response 里获取数据。
   * 存在多个接口时，表达式通常主要从 ctx.responses 里获取数据。
   * 不存在接口时，表达式不会从 ctx.response 或 ctx.responses 里获取数据，主要从上下文的其他字段获取数据，主要是 ctx.request，当然也有可能是 ctx.loop、ctx.model、ctx.globalModel 等。
   */
  apis: string[]
}

/**
 * 后端表达式接口参数场景。
 * 该场景下，表达式的返回值会被用作业务接口的参数。
 */
interface BackExpressionSceneApiArgs {
  /**
   * 类型
   */
  type: 'apiArgs'
  /**
   * 表达式所属的组件，也就是当前接口所属的组件，没有的时候表示该接口是模块级的接口
   */
  component?: SimplifyComponent
  /**
   * 数据源，这里给出的是展示给用户看的拼接字符串
   */
  dataSource: string
}

/**
 * 后端表达式接口执行条件场景。
 * 该场景下，表达式返回 true 时，会发起业务接口的请求，表达式返回 false 时，则不会发起业务接口的请求。
 * 不发起业务接口的请求，业务接口的响应为 null。
 */
interface BackExpressionSceneApiIf {
  /**
   * 类型
   */
  type: 'apiIf'
  /**
   * 表达式所属的组件，也就是当前接口所属的组件，没有的时候表示该接口是模块级的接口
   */
  component?: SimplifyComponent
  /**
   * 数据源，这里给出的是展示给用户看的拼接字符串
   */
  dataSource: string
}

/**
 * 后端表达式前端动作场景。
 * 该场景下，表达式的返回值需要为 `number | number[]`，会被当做动作的下标，用来选择需要执行的动作。
 * 另外表达式里除了需要通过 `default` 字段导出一个函数外，还需要在 `output` 字段导出一个对象，供动作按照键值取用。
 * 最终产生的动作，会在下发到前端后立即执行。
 */
interface BackExpressionSceneIife {
  /**
   * 类型
   */
  type: 'iife'
  /**
   * 接口，同 `BackExpressionSceneProp['apis']`
   */
  apis: string[]
  /**
   * 动作，这里只给出动作的描述。
   */
  actions: string[]
}
```

【当前简化的 Schema】
TS 类型：

```ts
/**
 * 下方 JSON 根对象类型
 */
type JsonRootOfCurrentSchema = SimplifySchema

/**
 * 简化的 Schema
 */
interface SimplifySchema {
  /**
   * 打平的视图
   */
  flattenedView: SimplifyFlattenedView
}

/**
 * 简化的打平的视图
 */
interface SimplifyFlattenedView {
  /**
   * 根组件 ID
   */
  rootComponentId: string
  /**
   * 所有组件
   */
  components: SimplifyComponent[]
  /**
   * 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
   */
  childComponentIdMap: Record<
    string,
    (string | SimplifyChildComponentIdAndPath)[]
  >
}

/**
 * 简化的组件实例
 */
interface SimplifyComponent {
  /**
   * 组件实例 ID
   */
  id: string
  /**
   * 组件类型
   */
  type: string
  /**
   * 组件实例名称
   */
  name: string
}

/**
 * 简化的子组件 ID 以及路径
 */
interface SimplifyChildComponentIdAndPath {
  /**
   * 子组件 ID
   */
  id: string
  /**
   * 子组件相对路径，默认按顺序添加到 children
   */
  path?: SimplifyPropertyPath
}

/**
 * 简化的属性路径
 *
 * 可以通过 lodash 的 get 函数获取 JSON 里的值
 */
type SimplifyPropertyPath = (string | number)[]
```

JSON：

```json
{
  "flattenedView": {
    "rootComponentId": "root",
    "components": [
      {
        "id": "root",
        "type": "@ad/canal-components::Root",
        "name": "根组件"
      },
      {
        "id": "comp_qngBTsF1eKhSjob7Ex8zk",
        "type": "@ad/canal-components::Text",
        "name": "文本::8zk"
      },
      {
        "id": "comp_JiQbOlPRwZUu3NEs9vxQO",
        "type": "@ad/canal-components::Text",
        "name": "文本::xQO"
      },
      {
        "id": "comp_RT5hXTwbkRniWyCcCVmir",
        "type": "@ad/canal-biz-components::41",
        "name": "按钮 bbx::mir"
      },
      {
        "id": "f方法是ut",
        "type": "@ad/canal-components::Input",
        "name": "f方法是ut 名称"
      },
      {
        "id": "sm",
        "type": "@ad/canal-components::Submodule",
        "name": "子模块::Kn_"
      },
      {
        "id": "iix",
        "type": "@ad/canal-components::Input",
        "name": "输入框::lJ6"
      },
      {
        "id": "comp_WKMPxyksw7qt2R2Ibs8sG",
        "type": "@ad/canal-components::Text",
        "name": "文本::8sG"
      },
      {
        "id": "pi1",
        "type": "@ad/canal-components::Input",
        "name": "输入框::UNS"
      },
      {
        "id": "comp_6bZbKOp_2WMHzQxtlVYb_",
        "type": "@ad/canal-components::Text",
        "name": "文本::Yb_"
      },
      {
        "id": "pi2",
        "type": "@ad/canal-components::Input",
        "name": "输入框::xRv"
      },
      {
        "id": "card",
        "type": "@ad/canal-biz-components::37",
        "name": "卡片::i1p"
      },
      {
        "id": "comp_xba8defcMAXg06wvdy9$o",
        "type": "@ad/canal-biz-components::17",
        "name": "中"
      },
      {
        "id": "left-btn",
        "type": "@ad/canal-biz-components::17",
        "name": "按钮4442::z6$"
      },
      {
        "id": "comp_JiTcovwZnBA81wqleM1wJ",
        "type": "@ad/canal-components::Text",
        "name": "文本::1wJ"
      },
      {
        "id": "right-btn",
        "type": "@ad/canal-biz-components::17",
        "name": "按钮4442::gg8"
      },
      {
        "id": "comp_Vws5Qeg8mX9YPLTHlTUdZ",
        "type": "@ad/canal-components::Text",
        "name": "文本::UdZ"
      },
      {
        "id": "comp_S0c9JJHwlW8op2aKHhAiU",
        "type": "@ad/canal-components::Container",
        "name": "容器::AiU"
      },
      {
        "id": "comp_IrqVVKzSUxUgUNXpQ2MuL",
        "type": "@ad/canal-components::Button",
        "name": "按钮::MuL"
      },
      {
        "id": "container1",
        "type": "@ad/canal-components::Container",
        "name": "容器::dpA"
      },
      {
        "id": "container2",
        "type": "@ad/canal-components::Container",
        "name": "容器::stG"
      },
      {
        "id": "test-btn",
        "type": "@ad/canal-components::Button",
        "name": "按钮::Zo5"
      },
      {
        "id": "comp_1vq6v5aXe85Smk639uLgl",
        "type": "@ad/canal-components::Text",
        "name": "文本::Lgl"
      },
      {
        "id": "checkbox",
        "type": "@ad/canal-components::Checkbox",
        "name": "多选框::Jdx"
      },
      {
        "id": "comp_dtvQlZXdWim$aqz6XSbov",
        "type": "@ad/canal-components::Text",
        "name": "文本::bov"
      },
      {
        "id": "comp_47uVP3nM_v$lbL8se3mx_",
        "type": "@ad/canal-components::Container",
        "name": "容器::mx_"
      },
      {
        "id": "comp_9OEwFp$eCZP8RscFGj1sk",
        "type": "@ad/canal-components::Text",
        "name": "文本::1sk"
      },
      {
        "id": "comp_8QobO3enpYqqQy3V4mcnl",
        "type": "@ad/canal-components::Container",
        "name": "容器::cnl"
      },
      {
        "id": "comp_u1ihunL6S0Fw9iZnfLpx5",
        "type": "@ad/canal-components::Button",
        "name": "按钮::px5"
      },
      {
        "id": "comp_1wwQ1276dhfgsL2C0GByV",
        "type": "@ad/canal-components::Text",
        "name": "文本::ByV"
      },
      {
        "id": "comp_EXliUI$fzcB1O0toIvAMd",
        "type": "@ad/canal-components::Text",
        "name": "文本::AMd"
      },
      {
        "id": "comp_IcVy6fa7LJJbA5lXqaQdI",
        "type": "@ad/canal-biz-components::17",
        "name": "按钮4442::QdI"
      },
      {
        "id": "tabbbb",
        "type": "@ad/canal-components::Table",
        "name": "表格::7m$"
      },
      {
        "id": "comp_zlxKUitJe6hWXz8QPMPIQ",
        "type": "@ad/canal-components::TableColumn",
        "name": "表格列名称::PIQ"
      },
      {
        "id": "comp_fB4_h4yROnni6XM0TzLO0",
        "type": "@ad/canal-components::TableColumn",
        "name": "表格列年龄::LO0"
      },
      {
        "id": "comp_NhWWsMBpongTqcMBs4b3S",
        "type": "@ad/canal-components::TableColumn",
        "name": "表格列操作::b3S"
      },
      {
        "id": "comp_flTRfh1L5CIL_QTlhlvcS",
        "type": "@ad/canal-components::TableCell",
        "name": "表格单元格::vcS"
      },
      {
        "id": "comp_o$3WDr5uG5r2ekQj5U6rG",
        "type": "@ad/canal-components::Button",
        "name": "查询按钮::6rG"
      },
      {
        "id": "comp_jaGcr399RFFzVROTtKqq7",
        "type": "@ad/canal-components::Text",
        "name": "文本::qq7"
      },
      {
        "id": "comp_GuHYEZdbGGzbNrZSpIYEr",
        "type": "@ad/canal-components::Container",
        "name": "容器::YEr"
      },
      {
        "id": "comp_V7vwjffMtKwxNpMxn8kXR",
        "type": "@ad/canal-components::Text",
        "name": "文本::kXR"
      },
      {
        "id": "comp_szT40Z71xqzXdTu9$Wh6E",
        "type": "@ad/canal-components::Text",
        "name": "文本::h6E"
      },
      {
        "id": "comp_JS8gzmJMjiKfKMaJAiDAx",
        "type": "@ad/canal-components::Container",
        "name": "容器::DAx"
      },
      {
        "id": "comp_BIqMwh15FpfcMFCwCAi8Z",
        "type": "@ad/canal-components::Text",
        "name": "文本::i8Z"
      },
      {
        "id": "comp_jjWLKWXolqCdgziU3duAU",
        "type": "@ad/canal-components::Text",
        "name": "文本::uAU"
      }
    ],
    "childComponentIdMap": {
      "root": [
        "comp_qngBTsF1eKhSjob7Ex8zk",
        "comp_JiQbOlPRwZUu3NEs9vxQO",
        "comp_RT5hXTwbkRniWyCcCVmir",
        "f方法是ut",
        "sm",
        "iix",
        "comp_WKMPxyksw7qt2R2Ibs8sG",
        "pi1",
        "comp_6bZbKOp_2WMHzQxtlVYb_",
        "pi2",
        "card",
        "container1",
        "comp_dtvQlZXdWim$aqz6XSbov",
        "comp_47uVP3nM_v$lbL8se3mx_",
        "comp_8QobO3enpYqqQy3V4mcnl",
        "comp_1wwQ1276dhfgsL2C0GByV",
        "comp_EXliUI$fzcB1O0toIvAMd",
        "comp_IcVy6fa7LJJbA5lXqaQdI",
        "tabbbb",
        "comp_jaGcr399RFFzVROTtKqq7",
        "comp_GuHYEZdbGGzbNrZSpIYEr",
        "comp_JS8gzmJMjiKfKMaJAiDAx"
      ],
      "card": [
        "comp_xba8defcMAXg06wvdy9$o",
        {
          "id": "left-btn",
          "path": ["props", "title", "value", 0, "value"]
        },
        {
          "id": "comp_JiTcovwZnBA81wqleM1wJ",
          "path": ["props", "extra", "value", 0, "value"]
        },
        {
          "id": "right-btn",
          "path": ["props", "extra", "value", 1, "value"]
        },
        {
          "id": "comp_Vws5Qeg8mX9YPLTHlTUdZ",
          "path": ["props", "extra", "value", 2, "value"]
        },
        {
          "id": "comp_S0c9JJHwlW8op2aKHhAiU",
          "path": ["props", "extra", "value", 3, "value"]
        }
      ],
      "comp_S0c9JJHwlW8op2aKHhAiU": ["comp_IrqVVKzSUxUgUNXpQ2MuL"],
      "container2": ["test-btn", "comp_1vq6v5aXe85Smk639uLgl", "checkbox"],
      "container1": ["container2"],
      "comp_47uVP3nM_v$lbL8se3mx_": ["comp_9OEwFp$eCZP8RscFGj1sk"],
      "comp_8QobO3enpYqqQy3V4mcnl": ["comp_u1ihunL6S0Fw9iZnfLpx5"],
      "tabbbb": [
        "comp_zlxKUitJe6hWXz8QPMPIQ",
        "comp_fB4_h4yROnni6XM0TzLO0",
        "comp_NhWWsMBpongTqcMBs4b3S"
      ],
      "comp_flTRfh1L5CIL_QTlhlvcS": ["comp_o$3WDr5uG5r2ekQj5U6rG"],
      "comp_NhWWsMBpongTqcMBs4b3S": ["comp_flTRfh1L5CIL_QTlhlvcS"],
      "comp_GuHYEZdbGGzbNrZSpIYEr": [
        "comp_V7vwjffMtKwxNpMxn8kXR",
        "comp_szT40Z71xqzXdTu9$Wh6E"
      ],
      "comp_JS8gzmJMjiKfKMaJAiDAx": [
        "comp_BIqMwh15FpfcMFCwCAi8Z",
        "comp_jjWLKWXolqCdgziU3duAU"
      ]
    }
  }
}
```

【常见的后端表达式举例】

- 例子 1

后端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_u19jiTR8zo_5fddPM0E27",
    "type": "@ad/canal-biz-components::34",
    "name": "客户360指标卡组::E27"
  },
  "propPath": ["data"],
  "propName": "指标卡组数据",
  "apis": [
    "[组件] 天枢DataQuery https://ad-diag.corp.kuaishou.com/rest/dt/data/query"
  ]
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return ctx.response.data
}
```

- 例子 2

后端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_wnGU4GUmdS345YswuzrQq",
    "type": "@ad/canal-biz-components::7",
    "name": "指标卡组::rQq"
  },
  "propPath": ["allSelectorTitles"],
  "propName": "指标配置信息",
  "apis": [
    "[模块] 天枢ChartQuery https://ad-diag.corp.kuaishou.com/rest/dt/chart/query"
  ]
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return ctx.response.data?.titles || []
}
```

- 例子 3

后端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_rPoLABQT$NDeNGweoG71X",
    "type": "@ad/canal-biz-components::276",
    "name": "安全下载按钮::71X"
  },
  "propPath": ["request"],
  "propName": "下载入参",
  "apis": []
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  const selector =
    ctx.request.params?.['comp_R_kBEeumwueqDj4vdlKLt']?.selector || []
  return {
    body: {
      chartId: 1429,
      conditions: [
        { fieldName: 'user_id', fieldValue: ctx.request.params?.userId },
        { fieldName: 'sale_scene', fieldValue: ctx.request.params?.saleScene },
        { fieldName: 'tfc_type', fieldValue: ctx.request.params?.tfcType },
        { fieldName: 'start_time', fieldValue: ctx.request.params?.startTime },
        { fieldName: 'end_time', fieldValue: ctx.request.params?.endTime },
        {
          fieldName: 'cmp_start_time',
          fieldValue: ctx.request.params?.cmpStartTime,
        },
        {
          fieldName: 'cmp_end_time',
          fieldValue: ctx.request.params?.cmpEndTime,
        },
      ],
      granularity: 'day',
      limit: 10,
      offset: 0,
      selector,
    },
  }
}
```

- 例子 4

后端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_eyS1JWBVWI2ynm9nP2MCB",
    "type": "@ad/canal-biz-components::32",
    "name": "明细表::MCB"
  },
  "propPath": ["tableData"],
  "propName": "表格数据",
  "apis": ["[模块] 转化目标", "[模块] 转化目标-汇总"]
}
```

后端表达式代码：

```js
function groupBy(array, keyFn) {
  return array.reduce((result, item) => {
    // 根据 keyFn 计算出元素的 key
    const key = typeof keyFn === 'function' ? keyFn(item) : item[keyFn]

    // 如果 result 中不存在该 key，则初始化为一个空数组
    if (!result[key]) {
      result[key] = []
    }

    // 将元素加入到对应 key 的数组中
    result[key].push(item)

    return result
  }, {})
}

/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  let respData = ctx.response?.data
  const respRecords = respData?.records || []
  const groupByData = groupBy(respRecords, 'action_type')
  console.log('ctx.response', ctx.response)
  console.log('groupByData', groupByData)
  const tableData = Object.keys(groupByData).map((key) => {
    const list = groupByData[key]
    const targetItem = {
      action_type: key,
      action_type_name: groupByData[key][0].action_type_name || '-',
    }
    list.forEach((item) => {
      switch (item.interval) {
        case '0h-2h':
          targetItem['0h-2h_num'] = item.callback_num
          targetItem['0h-2h_rate'] = item.callback_rate
          targetItem['0h-2h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '2h-12h':
          targetItem['2h-12h_num'] = item.callback_num
          targetItem['2h-12h_rate'] = item.callback_rate
          targetItem['2h-12h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '12h-24h':
          targetItem['12h-24h_num'] = item.callback_num
          targetItem['12h-24h_rate'] = item.callback_rate
          targetItem['12h-24h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '24h-48h':
          targetItem['24h-48h_num'] = item.callback_num
          targetItem['24h-48h_rate'] = item.callback_rate
          targetItem['24h-48h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '48h+':
          targetItem['48h+_num'] = item.callback_num
          targetItem['48h+_rate'] = item.callback_rate
          targetItem['48h+_rate_link_diff'] = item.callback_rate_link_diff
          break
        default:
          break
      }
    })
    return targetItem
  })
  console.log('tableData', tableData)

  let tableTotalData = {
    action_type_name: '汇总',
  }
  ctx?.responses?.forEach((item, index) => {
    if (index === 1) {
      const records = item?.data?.records || []
      console.log('汇总数据', records)
      records?.forEach((item) => {
        switch (item.interval) {
          case '0h-2h':
            tableTotalData['0h-2h_num'] = item.callback_num
            tableTotalData['0h-2h_rate'] = item.callback_rate
            tableTotalData['0h-2h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '2h-12h':
            tableTotalData['2h-12h_num'] = item.callback_num
            tableTotalData['2h-12h_rate'] = item.callback_rate
            tableTotalData['2h-12h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '12h-24h':
            tableTotalData['12h-24h_num'] = item.callback_num
            tableTotalData['12h-24h_rate'] = item.callback_rate
            tableTotalData['12h-24h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '24h-48h':
            tableTotalData['24h-48h_num'] = item.callback_num
            tableTotalData['24h-48h_rate'] = item.callback_rate
            tableTotalData['24h-48h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '48h+':
            tableTotalData['48h+_num'] = item.callback_num
            tableTotalData['48h+_rate'] = item.callback_rate
            tableTotalData['48h+_rate_link_diff'] = item.callback_rate_link_diff
            break
          default:
            break
        }
      })
    }
  })

  const records = [tableTotalData, ...tableData]
  console.log('tableTotalData', records)
  return {
    records,
    totalCount: records?.length,
  }
}
```

- 例子 5

后端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_9tmkDHVZtpFn2m72rWxru",
    "type": "@ad/canal-biz-components::99",
    "name": "表格"
  },
  "propPath": ["columns"],
  "propName": "列设置",
  "apis": []
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  const selectedColumns = ctx.request.params?.columns || []
  const adType = ctx.request.params.adType
  let leftFixedColumn = {
    dataIndex: 'campaign',
    title: '广告计划',
    width: 220,
    fixed: 'left',
    format: 'nameWithId',
  }
  switch (adType) {
    case '3':
      leftFixedColumn.dataIndex = 'unit'
      leftFixedColumn.title = '广告组'
      break
    case '4':
      leftFixedColumn.dataIndex = 'creative'
      leftFixedColumn.title = '广告创意'
      break
    default:
      break
  }
  return [leftFixedColumn, ...selectedColumns]
}
```

- 例子 6

后端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "selector",
    "type": "@ad/canal-biz-components::77",
    "name": "筛选组::5Ew"
  },
  "propPath": ["value"],
  "propName": "组件值",
  "apis": []
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  const { defaultStartTime, defaultEndTime, chartFieldsList } =
    ctx.request.params || {}
  if (ctx.request.refreshType !== 'default') return null

  console.log(
    'defaultStartTime, defaultEndTime',
    defaultStartTime,
    defaultEndTime,
  )

  return {
    popChartFields: chartFieldsList?.[0]?.id
      ? [chartFieldsList?.[0]?.id]
      : undefined,
    time: [defaultStartTime, defaultEndTime],
  }
}
```

- 例子 7

后端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_LWkytn8gDP4MaT4ZI5UaQ",
    "type": "@ad/canal-biz-components::158",
    "name": "本地业务中心周报进度卡::UaQ"
  },
  "propPath": ["style", "margin"],
  "propName": "外边距",
  "apis": []
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  const index = ctx.loop.index

  function calculateMargin(index) {
    const horizontalGap = '16px' // 水平间距
    const verticalGap = '16px' // 垂直间距

    let marginTop = 0
    let marginBottom = 0
    let marginLeft = 0
    let marginRight = 0

    // 计算水平间距
    if (index % 3 !== 2) {
      // 如果不是每行的最后一个元素
      marginRight = horizontalGap
    }

    // 计算垂直间距
    if (index >= 3) {
      // 如果不是第一行的元素
      marginTop = verticalGap
    }

    return `${marginTop} ${marginRight} ${marginBottom} ${marginLeft}`
  }

  return calculateMargin(index)
}
```

- 例子 8

后端表达式场景：

```json
{
  "type": "apiArgs",
  "component": {
    "id": "comp_uadnXgaRyO_Xix0U7RX$q",
    "type": "@ad/canal-biz-components::3",
    "name": "趋势图::X$q"
  },
  "dataSource": "https://ad-diag.corp.kuaishou.com/rest/dt/data/query"
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  const selectedCard =
    ctx.request.params?.comp_Hu6XzvgbOdeguC_4X8Df9 || 'cost_total'
  const selector = ['p_date', selectedCard]
  const filterList = [
    'cost_total',
    'ad_show',
    'live_gmv',
    'ive_avg_order_price',
    'live_unrisk_gmv',
    'live_avg_order_price',
    'photo_unrisk_order_gmv_1d',
    'photo_gmv',
    'total_roi',
  ]

  if (filterList.indexOf(selectedCard) === -1) {
    const suffix =
      ctx.request.params?.compareValue === 1
        ? '_category'
        : '_category_excellent'
    selector.push(`${selectedCard}${suffix}`)
  }

  return {
    chartId: 620,
    conditions: [
      { fieldName: 'start_time', fieldValue: ctx.request.params?.startTime },
      { fieldName: 'end_time', fieldValue: ctx.request.params?.endTime },
      { fieldName: 'user_id', fieldValue: ctx.request.params?.userId },
    ],
    granularity: 'day',
    limit: 500,
    offset: 0,
    selector: selector,
  }
}
```

- 例子 9

后端表达式场景：

```json
{
  "type": "apiIf",
  "component": {
    "id": "table_column",
    "type": "@ad/canal-biz-components::51",
    "name": "提报表格::EbC"
  },
  "dataSource": "【POST】品牌服务商-客户提报列表-https://agent.e.kuaishou.com/rest/dsp/agent/brand/report/list"
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  const params = ctx.request.params.filter_form ?? {}
  if (params?.reportId && isNaN(Number(params.reportId))) {
    return false
  }
  if (params?.sellerUserId && isNaN(Number(params.sellerUserId))) {
    return false
  }
  return true
}
```

- 例子 10

后端表达式场景：

```json
{
  "type": "iife",
  "apis": [
    "[模块] 人群报表-获取数据分布 https://ad.e.kuaishou.com/rest/dsp/portal/report/popChartReports",
    "[模块] 人群报表-获取数据分布 https://ad.e.kuaishou.com/rest/dsp/portal/report/popChartReports"
  ],
  "actions": ["执行外部函数：apiFetch209，参数：无"]
}
```

后端表达式代码：

```js
/**
 * @param {Ctx} ctx 上下文
 * @returns {number | number[]} 动作编号
 */
export default (ctx) => {
  const has209 = (ctx.responses || []).find((res) => res.result === 209)
  if (has209) {
    return [0]
  }
  return []
}

/**
 * 输出，用于动作列表里按键值取用，可以在上面函数里修改
 */
export const output = {}
```

【当前模块内正在使用的后端表达式】

无

【不合适的后端表达式举例】

无

【正在编辑的后端表达式场景】

```json
{
  "type": "prop",
  "component": {
    "id": "comp_JiQbOlPRwZUu3NEs9vxQO",
    "type": "@ad/canal-components::Text",
    "name": "文本::xQO"
  },
  "propPath": ["text"],
  "propName": "文本",
  "apis": []
}
```

【输出的要求】

- 生成与【正在编辑的后端表达式场景】对应的后端表达式代码。
- 只输出后端表达式代码，即只有 JS 代码，不需要额外的文本。
- 后端表达式代码的风格需要参考【常见的后端表达式举例】和【当前模块内正在使用的后端表达式】。
- 不要生成跟【不合适的后端表达式举例】风格类似的代码，除非【当前模块内正在使用的后端表达式】里有相同风格的后端表达式。
