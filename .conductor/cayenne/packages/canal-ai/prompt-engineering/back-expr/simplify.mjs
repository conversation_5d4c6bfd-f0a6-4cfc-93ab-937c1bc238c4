import fullSchema from "./full-schema.json" with { type: "json" };
import _ from "lodash";
import fs from "fs";
import path from "path";

const simpleSchema = {
  flattenedView: {
    ...fullSchema.flattenedView,
    components: fullSchema.flattenedView.components.map((comp) =>
      _.pick(comp, "id", "type", "name")
    ),
  },
};

await fs.promises.writeFile(
  path.join(import.meta.dirname, "simple-schema.json"),
  JSON.stringify(simpleSchema, null, 2)
);
