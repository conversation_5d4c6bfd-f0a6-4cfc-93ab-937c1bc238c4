{"schemaVersion": "0.0.1", "componentCodes": {"@ad/canal-biz-components::41": {"code": {"js": "https://p1-ad.adkwai.com/kcdn/cdn-kcdn111910/material/staging/@ad/drow-brook-test/0.1.0/dist/button/index.min.js"}, "exportIdentifier": "<PERSON><PERSON>", "version": "10"}, "@ad/canal-biz-components::37": {"code": {"js": "https://p1.adkwai.com/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.a8cc82ac.js"}, "version": "2"}, "@ad/canal-biz-components::17": {"code": {"js": "https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.6/dist/button/index.069f5f93.js"}, "version": "26"}}, "tracks": [{"id": "wfyXqt0lsdhFM4X6LYn5J", "eventType": "CLICK", "eventOptions": {"action": "CLICK_ACTION", "params": {"paramA": 333311}}, "canRepeat": true}], "iife": {"transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.__main = __main;\nexports.output = exports.default = void 0;\nfunction __main(...args) {\n  const actions = JSON.parse(\"[{\\\"type\\\":\\\"exec-effect\\\",\\\"expr\\\":{\\\"type\\\":\\\"js\\\",\\\"code\\\":\\\"(function(deps) {\\\\n  var exports = {}, module = {}\\\\n  module.exports = exports\\\\n  ;(function() {\\\\n    \\\\\\\"use strict\\\\\\\";\\\\n\\\\nObject.defineProperty(exports, \\\\\\\"__esModule\\\\\\\", {\\\\n  value: true\\\\n});\\\\nexports[\\\\\\\"default\\\\\\\"] = void 0;\\\\nvar _lodash = _interopRequireDefault(require(\\\\\\\"lodash\\\\\\\"));\\\\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \\\\\\\"default\\\\\\\": obj }; }\\\\n/**\\\\n* @param {Container} ctx 上下文\\\\n* @param {any} arg 参数\\\\n*/\\\\nvar _default = exports[\\\\\\\"default\\\\\\\"] = function _default(ctx, arg) {\\\\n  console.log('模块级前端动作副作用44ttff', ctx, arg, _lodash[\\\\\\\"default\\\\\\\"].range(11));\\\\n};\\\\n  })()\\\\n  return module.exports.default\\\\n  function require(name) {\\\\n    return deps[name]\\\\n  }\\\\n})(ctx.runtime.container.dependencies)\\\",\\\"codeES\\\":\\\"import _ from 'lodash'\\\\n\\\\n/**\\\\n* @param {Container} ctx 上下文\\\\n* @param {any} arg 参数\\\\n*/\\\\nexport default (ctx, arg) => {\\\\n  console.log('模块级前端动作副作用44ttff', ctx, arg, _.range(11))\\\\n}\\\\n\\\"},\\\"arg0\\\":\\\"副作用44ttffaa23\\\",\\\"arg0IsPath\\\":false}]\");\n  let actionIdxes = this.default.apply(this, args);\n  if (typeof actionIdxes === 'number') {\n    actionIdxes = [actionIdxes];\n  }\n  const getByOutput = (v, isPath) => isPath ? this.output[v] : v;\n  const actionsExpr = {\n    type: 'actions',\n    fns: actions.map(action => {\n      switch (action.type) {\n        case 'open-url':\n          {\n            return {\n              type: 'open-url',\n              url: getByOutput(action.url, action.urlIsPath),\n              inPlace: action.inPlace\n            };\n          }\n        case 'exec-external-fn':\n          {\n            return {\n              type: 'bind',\n              fn: {\n                type: 'get-data',\n                path: action.fnPath\n              },\n              args: [{\n                type: 'static',\n                value: getByOutput(action.arg0, action.arg0IsPath)\n              }]\n            };\n          }\n        case 'exec-effect':\n          {\n            return {\n              type: 'bind',\n              fn: {\n                type: 'js',\n                code: action.expr.code\n              },\n              args: [{\n                type: 'js',\n                code: 'ctx'\n              }, {\n                type: 'static',\n                value: getByOutput(action.arg0, action.arg0IsPath)\n              }]\n            };\n          }\n      }\n    })\n  };\n  actionsExpr.fns = actionIdxes.map(idx => actionsExpr.fns[idx]);\n  return actionsExpr;\n}\n/**\n* @param ctx 上下文\n* @returns 动作编号\n*/\nvar _default = ctx => {\n  if (ctx.request.refreshType === 'default') {\n    return [0];\n  }\n  return 0;\n};\n/**\n * 输出，用于动作列表里按键值取用，可以在上面函数里修改\n */\nexports.default = _default;\nconst output = exports.output = {};\n  })()\n  return module.exports.__main.apply(module.exports, mainArg0.args)\n}", "codeTS": "/**\n* @param ctx 上下文\n* @returns 动作编号\n*/\nexport default (ctx: Ctx): number | number[] => {\n  if (ctx.request.refreshType === 'default') {\n    return [0]\n  }\n  return 0\n}\n\n/**\n * 输出，用于动作列表里按键值取用，可以在上面函数里修改\n */\nexport const output: Record<string, any> = {}\n"}, "type": "apis", "apiIds": []}, "model": {"code": "(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : String(i); }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 模型\n */\nvar Model = exports[\"default\"] = /*#__PURE__*/function () {\n  function Model(ctx) {\n    _classCallCheck(this, Model);\n    _defineProperty(this, \"ab\", 'model-ab');\n    this.ctx = ctx;\n    console.log('Model init', ctx);\n    console.log('model eval info', ctx.evalInfo);\n    console.log('model sss', 123);\n  }\n  _createClass(Model, [{\n    key: \"ddf\",\n    value: function ddf(v) {\n      alert('ddf' + v);\n    }\n  }]);\n  return Model;\n}();\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)", "codeES": "/**\n * 模型\n */\nexport default class Model {\n\n  ab = 'model-ab'\n\n  constructor(ctx) {\n    this.ctx = ctx\n    console.log('Model init', ctx)\n    console.log('model eval info', ctx.evalInfo)\n    console.log('model sss', 123)\n  }\n\n  ddf(v) {\n    alert('ddf' + v)\n  }\n}\n"}, "backModel": {"code": "function main(mainArg0) {\n  const exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n/**\n * 后端模型\n */\nclass BackModel {\n  jjbb2 = 1234;\n  f() {\n    return 'f' + JSON.stringify(Object.keys(this.ctx.request.params));\n  }\n  constructor(ctx) {\n    this.ctx = ctx;\n    console.log('BackModel init', ctx);\n    console.log('model sss', 123);\n  }\n}\nexports.default = BackModel;\n  })()\n  const M = module.exports.default\n  return new M(...mainArg0.args)\n}", "codeTS": "/**\n * 后端模型\n */\nexport default class BackModel {\n\n  jjbb2 = 1234\n\n  f() {\n    return 'f' + JSON.stringify(Object.keys(this.ctx.request.params))\n  }\n\n  public constructor(private ctx: Ctx) {\n    console.log('BackModel init', ctx)\n    console.log('model sss', 123)\n  }\n}\n"}, "px2vw": {"designWidth": 414, "propLogicPaths": {"@ad/canal-components::Root": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-components::Text": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-biz-components::41": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-components::Input": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-components::Submodule": [], "@ad/canal-biz-components::37": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-biz-components::17": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-components::Container": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-components::Button": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-components::Checkbox": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-components::Table": [["style", "width"], ["style", "height"], ["style", "padding"], ["style", "margin"], ["style", "fontSize"], ["style", "borderWidth"], ["style", "borderRadius"], ["style", "top"], ["style", "bottom"], ["style", "left"], ["style", "right"]], "@ad/canal-components::TableColumn": [], "@ad/canal-components::TableCell": []}}, "linkage": {"autoRefreshByComponent": ["checkbox", "iix", "f方法是ut"], "componentDataParams": {"common": ["checkbox", "iix"], "byRefreshType": {"auto": ["checkbox", "iix"], "submit": ["checkbox"], "outside-params": ["checkbox", "iix"]}}, "commonParams": {"a": 33}}, "flattenedView": {"rootComponentId": "root", "components": [{"type": "@ad/canal-components::Root", "id": "root", "name": "根组件", "props": {"__$backData": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return {\n    a: 3,\n    xjdf: 6773,\n    b: ctx.response.data[0].contractName\n  };\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return {\n    a: 3,\n    xjdf: 6773,\n    b: ctx.response.data[0].contractName,\n  }\n}\n"}}, "style": {"type": "static", "value": {}}}}, {"id": "comp_qngBTsF1eKhSjob7Ex8zk", "type": "@ad/canal-components::Text", "name": "文本::8zk", "props": {"text": {"type": "js", "code": "(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  var _ctx$globalModel;\n  console.log('dd', ctx.evalInfo);\n  return \"xxxa212|||\".concat((_ctx$globalModel = ctx.globalModel) === null || _ctx$globalModel === void 0 ? void 0 : _ctx$globalModel.abc);\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)(ctx.runtime.container)", "defaultValue": "文本11123", "codeTS": "import _ from 'lodash'\n\n/**\n* @param ctx 上下文\n*/\nexport default (ctx: Container) => {\n  console.log('dd', ctx.evalInfo)\n  return `xxxa212|||${ctx.globalModel?.abc}`\n}\n"}}}, {"id": "comp_JiQbOlPRwZUu3NEs9vxQO", "type": "@ad/canal-components::Text", "name": "文本::xQO", "props": {"text": {"type": "js", "code": "(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _lodash = _interopRequireDefault(require(\"lodash\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n/**\n* @param ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  var _ctx$globalModel;\n  console.log('dd22', ctx.evalInfo);\n  return \"\".concat(_lodash[\"default\"].range(3).join('-'), \"|||\").concat(ctx.model.ab, \"|||\").concat((_ctx$globalModel = ctx.globalModel) === null || _ctx$globalModel === void 0 ? void 0 : _ctx$globalModel.abc);\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)(ctx.runtime.container)", "defaultValue": "文本11", "codeTS": "import _ from 'lodash'\n\n/**\n* @param ctx 上下文\n*/\nexport default (ctx: Container) => {\n  console.log('dd22', ctx.evalInfo)\n  return `${_.range(3).join('-')}|||${ctx.model.ab}|||${ctx.globalModel?.abc}`\n}\n"}}}, {"id": "comp_RT5hXTwbkRniWyCcCVmir", "type": "@ad/canal-biz-components::41", "name": "按钮 bbx::mir", "props": {"text": "文本", "style": {"type": "static", "value": {"width": "2px"}}}}, {"id": "f方法是ut", "type": "@ad/canal-components::Input", "name": "f方法是ut 名称", "props": {"value": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n/**\n* @param ctx 上下文\n*/\nvar _default = ctx => {\n  return ctx.response.data[0].contractName + '方法是33';\n};\nexports.default = _default;\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeTS": "/**\n* @param ctx 上下文\n*/\nexport default (ctx: Ctx) => {\n  return ctx.response.data[0].contractName + '方法是33'\n}\n"}}}, "effect": {"type": "js", "code": "(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param value 当前值\n* @param prevValue 上一次值\n* @param ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(value, prevValue, ctx) {\n  console.log('组件副作用 333想', value, prevValue, ctx);\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)", "codeTS": "/**\n* @param value 当前值\n* @param prevValue 上一次值\n* @param ctx 上下文\n*/\nexport default (value: any, prevValue: any, ctx: Container) => {\n  console.log('组件副作用 333想', value, prevValue, ctx)\n}\n"}}, {"id": "sm", "type": "@ad/canal-components::Submodule", "name": "子模块::Kn_", "props": {"schemaId": "a653164c-fbf1-4be1-bb28-da78f35a806c", "params": {"defaultValue": {"type": "static"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n/**\n* @param ctx 上下文\n*/\nvar _default = ctx => {\n  const {\n    jj\n  } = {\n    ...{\n      jj: 5_34\n    }\n  };\n  return {\n    abc: ctx.request.params.iix + 'ts33',\n    jj\n  };\n};\nexports.default = _default;\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeTS": "/**\n* @param ctx 上下文\n*/\nexport default (ctx: Ctx) => {\n  const { jj } = { ...{ jj: 5_34 } }\n  return {\n    abc: ctx.request.params.iix + 'ts33',\n    jj,\n  }\n}\n"}, "type": "apis", "apiIds": []}}}, {"id": "iix", "type": "@ad/canal-components::Input", "name": "输入框::lJ6", "effect": {"type": "js", "code": "(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {any} value 当前值\n* @param {any} prevValue 上一次值\n* @param {Container} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(value, prevValue, ctx) {\n  ctx.track('wfyXqt0lsdhFM4X6LYn5J');\n  console.log('组件副作用asdfa', value, prevValue, ctx);\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)", "codeES": "/**\n* @param {any} value 当前值\n* @param {any} prevValue 上一次值\n* @param {Container} ctx 上下文\n*/\nexport default (value, prevValue, ctx) => {\n  ctx.track('wfyXqt0lsdhFM4X6LYn5J')\n  console.log('组件副作用asdfa', value, prevValue, ctx)\n}\n"}}, {"id": "comp_WKMPxyksw7qt2R2Ibs8sG", "type": "@ad/canal-components::Text", "name": "文本::8sG", "props": {"text": "pi1"}}, {"id": "pi1", "type": "@ad/canal-components::Input", "name": "输入框::UNS", "effect": {"type": "js", "code": "(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _lodash = _interopRequireDefault(require(\"lodash\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n/**\n* @param {any} value 当前值\n* @param {any} prevValue 上一次值\n* @param {Container} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(value, prevValue, ctx) {\n  var _ctx$childModuleCtxs$;\n  console.log('组件副作用', value, prevValue, ctx, _lodash[\"default\"].range(5));\n  (_ctx$childModuleCtxs$ = ctx.childModuleCtxs.sm) === null || _ctx$childModuleCtxs$ === void 0 || _ctx$childModuleCtxs$.setData({\n    cc: value\n  });\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)", "codeES": "import _ from 'lodash'\n\n/**\n* @param {any} value 当前值\n* @param {any} prevValue 上一次值\n* @param {Container} ctx 上下文\n*/\nexport default (value, prevValue, ctx) => {\n  console.log('组件副作用', value, prevValue, ctx, _.range(5))\n  ctx.childModuleCtxs.sm?.setData({\n    cc: value,\n  })\n}\n"}}, {"id": "comp_6bZbKOp_2WMHzQxtlVYb_", "type": "@ad/canal-components::Text", "name": "文本::Yb_", "props": {"text": "pi2"}}, {"id": "pi2", "type": "@ad/canal-components::Input", "name": "输入框::xRv"}, {"id": "card", "type": "@ad/canal-biz-components::37", "name": "卡片::i1p", "props": {"size": "default", "title": {"type": "array", "value": [{"type": "component"}]}, "extra": {"type": "array", "value": [{"type": "component"}, {"type": "component"}, {"type": "component"}, {"type": "component"}]}}, "backFor": {"items": [1, 2]}}, {"id": "comp_xba8defcMAXg06wvdy9$o", "type": "@ad/canal-biz-components::17", "name": "中", "props": {"text": "中", "style": {"type": "static", "value": {}}}}, {"id": "left-btn", "type": "@ad/canal-biz-components::17", "name": "按钮4442::z6$", "props": {"text": "左", "style": {"type": "static", "value": {}}}, "backFor": {"items": [1, 2, 3]}}, {"id": "comp_JiTcovwZnBA81wqleM1wJ", "type": "@ad/canal-components::Text", "name": "文本::1wJ", "props": {"text": "文本"}}, {"id": "right-btn", "type": "@ad/canal-biz-components::17", "name": "按钮4442::gg8", "props": {"text": {"defaultValue": {"type": "static", "value": "右"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return \"\".concat(ctx.loop.parentLoop.item, \"r\").concat(ctx.loop.item);\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return `${ctx.loop.parentLoop.item}r${ctx.loop.item}`\n}\n"}, "type": "apis", "apiIds": []}, "style": {"type": "static", "value": {}}}, "backFor": {"items": [2, 3, 4]}}, {"id": "comp_Vws5Qeg8mX9YPLTHlTUdZ", "type": "@ad/canal-components::Text", "name": "文本::UdZ", "props": {"text": "文本2"}, "backFor": {"items": []}}, {"id": "comp_S0c9JJHwlW8op2aKHhAiU", "type": "@ad/canal-components::Container", "name": "容器::AiU"}, {"id": "comp_IrqVVKzSUxUgUNXpQ2MuL", "type": "@ad/canal-components::Button", "name": "按钮::MuL", "props": {"text": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static", "value": "查询"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return ctx.loop.item + ctx.response.data[0].contractName;\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return ctx.loop.item + ctx.response.data[0].contractName\n}\n"}}, "type": "primary"}, "backFor": {"items": ["a", "b"]}}, {"id": "container1", "type": "@ad/canal-components::Container", "name": "容器::dpA"}, {"id": "container2", "type": "@ad/canal-components::Container", "name": "容器::stG", "props": {}, "backFor": {"items": {"defaultValue": {"type": "static", "value": [{"label": "a", "value": "va"}, {"label": "bb", "value": "vb"}, {"label": "cccc", "value": "vc"}]}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return ctx.request.refreshType === 'default' ? [{\n    \"label\": \"a\",\n    \"value\": \"va\"\n  }, {\n    \"label\": \"bb\",\n    \"value\": \"vb\"\n  }, {\n    \"label\": \"cccc\",\n    \"value\": \"vc\"\n  }] : [{\n    \"label\": \"bb22\",\n    \"value\": \"vb\"\n  }, {\n    \"label\": \"cccc222\",\n    \"value\": \"vc\"\n  }];\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return ctx.request.refreshType === 'default' ? [\n    {\n      \"label\": \"a\",\n      \"value\": \"va\"\n    },\n    {\n      \"label\": \"bb\",\n      \"value\": \"vb\"\n    },\n    {\n      \"label\": \"cccc\",\n      \"value\": \"vc\"\n    }\n  ] : [\n    {\n      \"label\": \"bb22\",\n      \"value\": \"vb\"\n    },\n    {\n      \"label\": \"cccc222\",\n      \"value\": \"vc\"\n    }\n  ]\n}\n"}, "type": "apis", "apiIds": []}, "key": {"defaultValue": {"type": "static", "value": "label"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return 'v' + 'alue';\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return 'v' + 'alue'\n}\n"}, "type": "apis", "apiIds": []}}}, {"id": "test-btn", "type": "@ad/canal-components::Button", "name": "按钮::Zo5", "props": {"text": {"defaultValue": {"type": "static", "value": "查询"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return \"\".concat(ctx.loop.parentLoop.parentLoop, \" \").concat(ctx.loop.parentLoop.item.label, \" \").concat(ctx.loop.item);\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return `${ctx.loop.parentLoop.parentLoop} ${ctx.loop.parentLoop.item.label} ${ctx.loop.item}`\n}\n"}, "type": "apis", "apiIds": []}}, "backFor": {"items": [1, 2, 3]}}, {"id": "comp_1vq6v5aXe85Smk639uLgl", "type": "@ad/canal-components::Text", "name": "文本::Lgl", "props": {"text": {"defaultValue": {"type": "static", "value": "文本"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return JSON.stringify(ctx.loop);\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return JSON.stringify(ctx.loop)\n}\n"}, "type": "apis", "apiIds": []}}}, {"id": "checkbox", "type": "@ad/canal-components::Checkbox", "name": "多选框::Jdx", "props": {"text": "显示"}}, {"id": "comp_dtvQlZXdWim$aqz6XSbov", "type": "@ad/canal-components::Text", "name": "文本::bov", "props": {"text": {"defaultValue": "文本a33", "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return \"\".concat(ctx.responses[0].data[0].contractName, \" | \").concat(ctx.responses[1].data[0].contractName, \"\\n  \");\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return `${\n    ctx.responses[0].data[0].contractName\n    } | ${\n    ctx.responses[1].data[0].contractName\n    }\n  `\n}\n"}, "type": "apis", "apiIds": ["k6NUGE90RBkNzKt1wFSb2", "QR1IyEVFls46TfLWkTNCm"]}}, "apis": {"QR1IyEVFls46TfLWkTNCm": {"service": "https://luopan-internal.staging.kuaishou.com", "method": "/luopan/contract/v2/contracts", "mockRes": {"status": 200, "result": 1, "message": "成功", "data": [{"contractId": 116462, "contractNo": "DLS-20241115-0003", "contractName": "【补充协议】mock3xx22222222222", "contractStatus": "PRE_DRAFT", "operator": "guowei<PERSON><PERSON>", "creator": "guowei<PERSON><PERSON>", "signType": "SUPPLEMENTARY", "contractType": "CT_AGENT_SETTLE_IN", "startTime": 0, "endTime": 0, "createSource": "代理商平台", "associatedByOthers": false, "userContractPowers": ["GRANT", "VIEW"], "ourSideContractCorporation": {}, "otherSideContractCorporation": {"corpId": 15694, "corpCode": "", "corpName": "北京快手广告有限公司", "usci": "91110108MA008E1A71", "contactName": "", "contactPersonMailPrefix": "", "contactPhone": "", "contactEmail": "", "contactAddress": "", "corpShortName": "", "agentType": "INDUSTRY_JINNIU"}, "marginCorpInfos": [], "relateCorpInfos": [], "bizManagement": {"frameTaskList": [], "historyCorpList": [], "marginPayerList": [], "ksInfo": [], "cooperationMargin": 0, "riskMargin": 0, "otherMargin": 0, "scopeOfAuth": ""}, "marginPayedAmount": 0, "marginHistoryTotalAmount": null, "marginPayerList": []}], "pageNo": 1, "pageSize": 10, "total": 15506, "totalPage": null, "host": "public-xm-c-kce-node-staging43.idchb1az1.hb1.kwaidc.com", "port": 22150, "timestamp": 1731641810661}, "id": "QR1IyEVFls46TfLWkTNCm", "args": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return {\n    \"pageNo\": 1,\n    \"pageSize\": 10\n  };\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return {\n    \"pageNo\": 1,\n    \"pageSize\": 10\n  }\n}\n"}}}}, {"id": "comp_47uVP3nM_v$lbL8se3mx_", "type": "@ad/canal-components::Container", "name": "容器::mx_"}, {"id": "comp_9OEwFp$eCZP8RscFGj1sk", "type": "@ad/canal-components::Text", "name": "文本::1sk", "props": {"text": {"type": "api", "apiId": "dpeDxkFq67Zxb1NsrmVec", "defaultValue": {"type": "static", "value": "文本1111"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return JSON.stringify({\n    model: _typeof(ctx.model),\n    tglobalModel: _typeof(ctx.globalModel),\n    sglobalModel: \"\".concat(ctx.globalModel),\n    ks: Object.keys(ctx),\n    globalks: ctx.globalModel && Object.keys(ctx.globalModel)\n  });\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return JSON.stringify({\n    model: typeof ctx.model,\n    tglobalModel: typeof ctx.globalModel,\n    sglobalModel: `${ctx.globalModel}`,\n    ks: Object.keys(ctx),\n    globalks:  ctx.globalModel && Object.keys(ctx.globalModel),\n  })\n}\n"}}}, "apis": {"dpeDxkFq67Zxb1NsrmVec": {"service": "https://luopan-internal.staging.kuaishou.com", "method": "/luopan/contract/v2/contracts", "id": "dpeDxkFq67Zxb1NsrmVec", "args": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return {\n    \"pageNo\": 1,\n    \"pageSize\": 10\n  };\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return {\n    \"pageNo\": 1,\n    \"pageSize\": 10\n  }\n}\n"}, "if": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return ctx.request.refreshType === 'submit';\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return ctx.request.refreshType === 'submit'\n}\n"}}}}, {"id": "comp_8QobO3enpYqqQy3V4mcnl", "type": "@ad/canal-components::Container", "name": "容器::cnl"}, {"id": "comp_u1ihunL6S0Fw9iZnfLpx5", "type": "@ad/canal-components::Button", "name": "按钮::px5", "props": {"text": {"defaultValue": "查询23", "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  console.log('ctx', ctx);\n  return 'asdf' + ctx.model.jjbb2 + ctx.model.f();\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  console.log('ctx', ctx)\n  return 'asdf' + ctx.model.jjbb2 + ctx.model.f()\n}\n"}, "type": "apis", "apiIds": ["k6NUGE90RBkNzKt1wFSb2", "PrZdG3MiPJF1cmsbEoXPD"]}, "danger": {"type": "api", "apiId": "PrZdG3MiPJF1cmsbEoXPD", "defaultValue": {"type": "static"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return ctx.response.data[0].contractName;\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return ctx.response.data[0].contractName\n}\n"}}, "onClick": {"type": "actions", "fns": [{"type": "track", "trackId": "wfyXqt0lsdhFM4X6LYn5J"}, {"type": "refresh", "refreshType": "submit"}]}, "type": "primary"}, "apis": {"PrZdG3MiPJF1cmsbEoXPD": {"id": "PrZdG3MiPJF1cmsbEoXPD", "service": "https://luopan-internal.staging.kuaishou.com", "method": "/luopan/contract/v2/contracts", "args": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return {\n    \"pageNo\": 1,\n    \"pageSize\": 10\n  };\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return {\n    \"pageNo\": 1,\n    \"pageSize\": 10\n  }\n}\n"}}}}, {"id": "comp_1wwQ1276dhfgsL2C0GByV", "type": "@ad/canal-components::Text", "name": "文本::ByV", "props": {"text": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static", "value": "文本"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _big = _interopRequireDefault(require(\"big.js\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n/**\n * @param ctx 上下文\n */\nvar _default = ctx => {\n  return ctx.model.f() + `bigts: ${new _big.default(Number.MAX_SAFE_INTEGER).times(Number.MAX_SAFE_INTEGER)}`;\n};\nexports.default = _default;\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeTS": "import Big from 'big.js'\n\n/**\n * @param ctx 上下文\n */\nexport default (ctx: Ctx) => {\n  return ctx.model.f() + `bigts: ${new Big(Number.MAX_SAFE_INTEGER).times(Number.MAX_SAFE_INTEGER)}`\n\n}\n"}}, "style": {"type": "object", "value": {"color": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return ctx.response.data;\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return ctx.response.data\n}\n"}}, "background": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  var arr = [];\n  for (var i = 0; i < ctx.response.data.length; i++) {\n    arr.push(ctx.response.data[i]);\n  }\n  return arr;\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  const arr = []\n  for (let i = 0; i < ctx.response.data.length; i++) {\n    arr.push(ctx.response.data[i])\n  }\n  return arr\n}\n"}}}}}}, {"id": "comp_EXliUI$fzcB1O0toIvAMd", "type": "@ad/canal-components::Text", "name": "文本::AMd", "props": {"text": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static", "value": "文本"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _big = _interopRequireDefault(require(\"big.js\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n/**\n * @param ctx 上下文\n */\nvar _default = ctx => {\n  return ctx.model.f() + `bigts: ${new _big.default(Number.MAX_SAFE_INTEGER).times(Number.MAX_SAFE_INTEGER)}`;\n};\nexports.default = _default;\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeTS": "import Big from 'big.js'\n\n/**\n * @param ctx 上下文\n */\nexport default (ctx: Ctx) => {\n  return ctx.model.f() + `bigts: ${new Big(Number.MAX_SAFE_INTEGER).times(Number.MAX_SAFE_INTEGER)}`\n\n}\n"}}, "style": {"type": "object", "value": {"color": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return ctx.response.data;\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return ctx.response.data\n}\n"}}, "background": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  var arr = [];\n  for (var i = 0; i < ctx.response.data.length; i++) {\n    arr.push(ctx.response.data[i]);\n  }\n  return arr;\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  const arr = []\n  for (let i = 0; i < ctx.response.data.length; i++) {\n    arr.push(ctx.response.data[i])\n  }\n  return arr\n}\n"}}}}}}, {"id": "comp_IcVy6fa7LJJbA5lXqaQdI", "type": "@ad/canal-biz-components::17", "name": "按钮4442::QdI", "props": {"text": "文本", "style": {"type": "static", "value": {"backgroundColor": "#7ed321"}}}}, {"id": "tabbbb", "type": "@ad/canal-components::Table", "name": "表格::7m$", "props": {"dataSource": [{"key": "1", "name": "<PERSON>", "age": 32, "address": "New York No. 1 Lake Park", "bb": true, "bb2": false, "nn": null}, {"key": "2", "name": "<PERSON>", "age": 42, "address": "London No. 1 Lake Park"}, {"key": "3", "name": "<PERSON>", "age": 32, "address": "Sidney No. 1 Lake Park"}, {"key": "4", "name": "<PERSON>", "age": 321, "address": "Sidney No. 1 Lake Park11"}], "emptyText": "暂无数据", "pagination": {"type": "static", "value": {"pageSizeOptions": ["10", "20", "50", "100"]}}}}, {"type": "@ad/canal-components::TableColumn", "name": "表格列名称::PIQ", "props": {"title": "名称", "dataIndex": "name"}, "id": "comp_zlxKUitJe6hWXz8QPMPIQ"}, {"type": "@ad/canal-components::TableColumn", "name": "表格列年龄::LO0", "props": {"title": "年龄", "dataIndex": "age"}, "id": "comp_fB4_h4yROnni6XM0TzLO0"}, {"type": "@ad/canal-components::TableColumn", "name": "表格列操作::b3S", "props": {"title": "操作", "dataIndex": "op"}, "id": "comp_NhWWsMBpongTqcMBs4b3S"}, {"type": "@ad/canal-components::TableCell", "name": "表格单元格::vcS", "props": {"recordKey": {"defaultValue": {"type": "static", "value": "1"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return \"\".concat(ctx.loop.item);\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return `${ctx.loop.item}`\n}\n"}, "type": "apis", "apiIds": []}}, "id": "comp_flTRfh1L5CIL_QTlhlvcS", "backFor": {"items": [1, 2, 3]}}, {"type": "@ad/canal-components::Button", "name": "查询按钮::6rG", "props": {"text": {"defaultValue": {"type": "static", "value": "查询"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return \"\\u64CD\\u4F5C \".concat(ctx.loop.item, \" \").concat(ctx.loop.index);\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return `操作 ${ctx.loop.item} ${ctx.loop.index}`\n}\n"}, "type": "apis", "apiIds": []}}, "id": "comp_o$3WDr5uG5r2ekQj5U6rG"}, {"id": "comp_jaGcr399RFFzVROTtKqq7", "type": "@ad/canal-components::Text", "name": "文本::qq7", "props": {"text": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static", "value": "文本222"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return \"find\\u3001findIndex: \".concat([3, 4, 5].find(function (v) {\n    return v === 4;\n  }), \"\\u3001\").concat([3, 4, 5].findIndex(function (v) {\n    return v === 5;\n  }), \"\\n    Symbol: \").concat(String(Symbol('asdf')), \"\\n    toString: \").concat(21478123 .toString(), \"\\n    toLocaleString: \").concat(21478123 .toLocaleString(), \"\\n    \");\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return `find、findIndex: ${[3, 4, 5].find(v => v === 4)\n    }、${[3, 4, 5].findIndex(v => v === 5)\n    }\n    Symbol: ${String(Symbol('asdf'))}\n    toString: ${(21478123).toString()}\n    toLocaleString: ${(21478123).toLocaleString()}\n    `\n}\n"}}}}, {"id": "comp_GuHYEZdbGGzbNrZSpIYEr", "type": "@ad/canal-components::Container", "name": "容器::YEr"}, {"id": "comp_V7vwjffMtKwxNpMxn8kXR", "type": "@ad/canal-components::Text", "name": "文本::kXR", "props": {"text": "java 对象测试："}}, {"id": "comp_szT40Z71xqzXdTu9$Wh6E", "type": "@ad/canal-components::Text", "name": "文本::h6E", "props": {"text": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static", "value": "文本"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  console.log('原始类型', true, 23, 'adsf', Symbol('symbol3'), null, undefined, 23n);\n  console.log('对象和数组', {\n    a: 3,\n    b: {\n      x: 4,\n      y: [3]\n    },\n    c: [1, 2]\n  }, [1, 2, null, {\n    a: 3\n  }, [1, 2, [3], {\n    x: 44\n  }]]);\n  console.log('Java 传入的对象', ctx, ctx.request, ctx.response);\n  return \"JSON.stringify(ctx.response).length: \".concat(JSON.stringify(ctx.response).length, \"\\n    Object.keys(ctx.response.data): \").concat(Object.keys(ctx.response.data), \"\\n    ctx.response.data.map: \").concat(ctx.response.data.map(function (v) {\n    return v.creator;\n  }), \"\\n    find: \").concat(ctx.response.data[0].userContractPowers.find(function (v) {\n    return v !== 'GRANT';\n  }), \"\\n    findIndex: \").concat(ctx.response.data[0].userContractPowers.findIndex(function (v) {\n    return v !== 'GRANT';\n  }), \"\\n    join: \").concat(ctx.response.data[0].userContractPowers.join(' | '), \"\\n    splice: \").concat(ctx.response.data[0].userContractPowers.splice(1, 1, 'vv'), \"\\n    slice: \").concat(ctx.response.data[0].userContractPowers.slice(1), \"\\n    \");\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  console.log('原始类型',\n    true, 23, 'adsf', Symbol('symbol3'), null, undefined, 23n\n  )\n  console.log('对象和数组',\n    { a: 3, b: { x: 4, y: [3] }, c: [1, 2] },\n    [1, 2, null, { a: 3 }, [1, 2, [3], { x: 44 }]]\n  )\n  console.log('Java 传入的对象', ctx, ctx.request, ctx.response)\n  return `JSON.stringify(ctx.response).length: ${JSON.stringify(ctx.response).length\n    }\n    Object.keys(ctx.response.data): ${Object.keys(ctx.response.data)\n    }\n    ctx.response.data.map: ${ctx.response.data.map(v => v.creator)}\n    find: ${ctx.response.data[0].userContractPowers.find(v => v !== 'GRANT')}\n    findIndex: ${ctx.response.data[0].userContractPowers.findIndex(v => v !== 'GRANT')}\n    join: ${ctx.response.data[0].userContractPowers.join(' | ')}\n    splice: ${ctx.response.data[0].userContractPowers.splice(1, 1, 'vv')}\n    slice: ${ctx.response.data[0].userContractPowers.slice(1)}\n    `\n}\n"}}}}, {"id": "comp_JS8gzmJMjiKfKMaJAiDAx", "type": "@ad/canal-components::Container", "name": "容器::DAx"}, {"id": "comp_BIqMwh15FpfcMFCwCAi8Z", "type": "@ad/canal-components::Text", "name": "文本::i8Z", "props": {"text": "java 对象测试："}}, {"id": "comp_jjWLKWXolqCdgziU3duAU", "type": "@ad/canal-components::Text", "name": "文本::uAU", "props": {"text": {"type": "api", "apiId": "k6NUGE90RBkNzKt1wFSb2", "defaultValue": {"type": "static", "value": "文本"}, "transform": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return JSON.stringify(ctx.response);\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return JSON.stringify(ctx.response)\n}\n"}}}}], "childComponentIdMap": {"root": ["comp_qngBTsF1eKhSjob7Ex8zk", "comp_JiQbOlPRwZUu3NEs9vxQO", "comp_RT5hXTwbkRniWyCcCVmir", "f方法是ut", "sm", "iix", "comp_WKMPxyksw7qt2R2Ibs8sG", "pi1", "comp_6bZbKOp_2WMHzQxtlVYb_", "pi2", "card", "container1", "comp_dtvQlZXdWim$aqz6XSbov", "comp_47uVP3nM_v$lbL8se3mx_", "comp_8QobO3enpYqqQy3V4mcnl", "comp_1wwQ1276dhfgsL2C0GByV", "comp_EXliUI$fzcB1O0toIvAMd", "comp_IcVy6fa7LJJbA5lXqaQdI", "tabbbb", "comp_jaGcr399RFFzVROTtKqq7", "comp_GuHYEZdbGGzbNrZSpIYEr", "comp_JS8gzmJMjiKfKMaJAiDAx"], "card": ["comp_xba8defcMAXg06wvdy9$o", {"id": "left-btn", "path": ["props", "title", "value", 0, "value"]}, {"id": "comp_JiTcovwZnBA81wqleM1wJ", "path": ["props", "extra", "value", 0, "value"]}, {"id": "right-btn", "path": ["props", "extra", "value", 1, "value"]}, {"id": "comp_Vws5Qeg8mX9YPLTHlTUdZ", "path": ["props", "extra", "value", 2, "value"]}, {"id": "comp_S0c9JJHwlW8op2aKHhAiU", "path": ["props", "extra", "value", 3, "value"]}], "comp_S0c9JJHwlW8op2aKHhAiU": ["comp_IrqVVKzSUxUgUNXpQ2MuL"], "container2": ["test-btn", "comp_1vq6v5aXe85Smk639uLgl", "checkbox"], "container1": ["container2"], "comp_47uVP3nM_v$lbL8se3mx_": ["comp_9OEwFp$eCZP8RscFGj1sk"], "comp_8QobO3enpYqqQy3V4mcnl": ["comp_u1ihunL6S0Fw9iZnfLpx5"], "tabbbb": ["comp_zlxKUitJe6hWXz8QPMPIQ", "comp_fB4_h4yROnni6XM0TzLO0", "comp_NhWWsMBpongTqcMBs4b3S"], "comp_flTRfh1L5CIL_QTlhlvcS": ["comp_o$3WDr5uG5r2ekQj5U6rG"], "comp_NhWWsMBpongTqcMBs4b3S": ["comp_flTRfh1L5CIL_QTlhlvcS"], "comp_GuHYEZdbGGzbNrZSpIYEr": ["comp_V7vwjffMtKwxNpMxn8kXR", "comp_szT40Z71xqzXdTu9$Wh6E"], "comp_JS8gzmJMjiKfKMaJAiDAx": ["comp_BIqMwh15FpfcMFCwCAi8Z", "comp_jjWLKWXolqCdgziU3duAU"]}}, "apis": {"k6NUGE90RBkNzKt1wFSb2": {"service": "https://luopan-internal.staging.kuaishou.com", "method": "/luopan/contract/v2/contracts", "name": "阿斯蒂芬323238880", "mockRes": {"status": 200, "result": 1, "message": "成功", "data": [{"contractId": 116462, "contractNo": "DLS-20241115-0003", "contractName": "【补充协议】mock3xx", "contractStatus": "PRE_DRAFT", "operator": "guowei<PERSON><PERSON>", "creator": "guowei<PERSON><PERSON>", "signType": "SUPPLEMENTARY", "contractType": "CT_AGENT_SETTLE_IN", "startTime": 0, "endTime": 0, "createSource": "代理商平台", "associatedByOthers": false, "userContractPowers": ["GRANT", "VIEW"], "ourSideContractCorporation": {}, "otherSideContractCorporation": {"corpId": 15694, "corpCode": "", "corpName": "北京快手广告有限公司", "usci": "91110108MA008E1A71", "contactName": "", "contactPersonMailPrefix": "", "contactPhone": "", "contactEmail": "", "contactAddress": "", "corpShortName": "", "agentType": "INDUSTRY_JINNIU"}, "marginCorpInfos": [], "relateCorpInfos": [], "bizManagement": {"frameTaskList": [], "historyCorpList": [], "marginPayerList": [], "ksInfo": [], "cooperationMargin": 0, "riskMargin": 0, "otherMargin": 0, "scopeOfAuth": ""}, "marginPayedAmount": 0, "marginHistoryTotalAmount": null, "marginPayerList": []}], "pageNo": 1, "pageSize": 10, "total": 15506, "totalPage": null, "host": "public-xm-c-kce-node-staging43.idchb1az1.hb1.kwaidc.com", "port": 22150, "timestamp": 1731641810661}, "id": "k6NUGE90RBkNzKt1wFSb2", "args": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n/**\n* @param ctx 上下文\n*/\nvar _default = ctx => {\n  console.log('根组件参数', ctx.model.jjbb2, ctx.model.f());\n  console.log('根组件参数', ctx);\n  return {\n    \"pageNo\": 3,\n    \"pageSize\": 10\n  };\n};\nexports.default = _default;\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeTS": "/**\n* @param ctx 上下文\n*/\nexport default (ctx: Ctx) => {\n  console.log('根组件参数', ctx.model.jjbb2, ctx.model.f())\n  console.log('根组件参数',ctx)\n  return {\n    \"pageNo\": 3,\n    \"pageSize\": 10\n  }\n}\n"}, "if": {"type": "js", "code": "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n* @param {Ctx} ctx 上下文\n*/\nvar _default = exports[\"default\"] = function _default(ctx) {\n  return ctx.request.refreshType === 'default';\n};\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}", "codeES": "/**\n* @param {Ctx} ctx 上下文\n*/\nexport default (ctx) => {\n  return ctx.request.refreshType === 'default'\n}\n"}}}}