export const a = 3

/**
 * 简化的组件实例
 */
interface SimplifyComponent {
  /**
   * 组件实例 ID
   */
  id: string
  /**
   * 组件类型
   */
  type: string
  /**
   * 组件实例名称
   */
  name: string
}

export const b = 3

/**
 * 前端表达式场景
 */
type FrontExpressionScene = FrontExpressionSceneProp

/**
 * 前端表达式属性场景。
 * 该场景下，表达式的返回值会被用作组件的属性。
 * 需要注意的是，该场景下，表达式是作为 mobx 的 reaction 运行的。
 * 表达式计算的时候，如果访问了 observable 的值（比如组件的值：ctx.data.组件ID），那对应的值发生变化时，当前的表达式也会自动重新计算。
 */
interface FrontExpressionSceneProp {
  /**
   * 类型
   */
  type: 'prop'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
}

export const c = 3
export { FrontExpressionScene }
