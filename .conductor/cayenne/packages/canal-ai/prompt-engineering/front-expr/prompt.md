假如你是一个低代码应用的开发专家，请根据以下信息，产出用户正在编辑的前端表达式代码：

【名词解释】
模块：用户在低代码平台上开发的基础单位，类似于全代码开发中的文件。模块可能是一个页面，也可能是页面中的某一个独立的部分。
Schema：模块内容的一种 JSON 表示。用户不会感知到 Schema 的内容及其结构，用户是通过一个可视化的网页编辑器间接编辑 Schema 的。
表达式：基于 JSON 编写的数据计算逻辑，可以兼容各种类型的计算方式。
前端表达式：在前端运行时里运行的 JS 代码，以 JS 代码字符串的方式在 JSON 里描述数据计算逻辑。前端表达式需要在 default 字段导出一个函数，函数有一个上下文参数，在有些场景里也会有一些其他参数。
前端表达式场景：前端表达式常用于即时计算组件属性的值，或者在组件事件发生后执行一些动作。这些事件，可能是按钮点击，或者组件值发生变化等等。接下来我们会用 JSON 来结构化前端表达式场景的描述方式。
前端运行时：从后端运行时获取 Schema 后，运行并渲染为 React 虚拟 DOM 树，然后通过 React 渲染到页面上。在这过程中，前端运行时会计算前端表达式，获取一些动态的数据。宿主会向前端运行时提供一系列的依赖，这些依赖都会通过前端运行时提供给前端表达式。前端表达式可以通过 import 语法去引入、使用。

【前端表达式上下文参数类型】
TS 类型（提供给 monaco-editor 使用的定义文件）：

```ts
declare module '*'

/**
 * 容器上下文
 */
declare interface Container {
  /**
   * 数据，组件 ID -> 组件数据。
   * 部分业务数据、函数会挂在 data 上，提供给前端表达式使用。
   */
  data: ContainerData
  /**
   * 设置数据
   * @param partialData 部分数据
   */
  setData(partialData: Partial<ContainerData>): void
  /**
   * 初始化参数，指业务方传给运行时的 params 字段。
   * 部分业务数据、函数会挂在 initParams 上，提供给前端表达式使用。
   */
  initParams?: Record<string, unknown>
  /**
   * 触发埋点
   * @param trackId 埋点 ID
   * @param transformTrack 转换埋点
   */
  track(
    trackId: string,
    transformTrack?: Partial<Track> | ((track: Track) => Track | void),
  ): void
  /**
   * 全局模型
   */
  globalModel?: any
  /**
   * 模型
   */
  model: any
  /**
   * 父模块上下文
   */
  parentModuleCtx?: Container
  /**
   * 子模块上下文，子模块组件 ID -> 上下文
   */
  childModuleCtxs: Record<string, Container>
  /**
   * 执行信息
   */
  evalInfo?: EvalInfo
}

/**
 * 容器上下文数据
 */
declare interface ContainerData {
  /**
   * 未知数据，可能由外部传入
   */
  [k: string]: any
}

/**
 * 埋点
 * https://component.corp.kuaishou.com/docs/weblogger/views/docs/api.html#%E6%94%B6%E9%9B%86%E4%B8%8A%E6%8A%A5
 */
declare interface Track {
  /**
   * ID
   */
  id: string
  /**
   * 事件类型
   */
  eventType: string
  /**
   * 事件参数
   */
  eventOptions: Record<string, any>
  /**
   * 可重复上报，默认：true
   */
  canRepeat?: boolean
}

/**
 * 执行信息
 */
declare interface EvalInfo {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 组件 ID
   */
  componentId?: string
  /**
   * 相对路径
   */
  relativePath: (string | number)[]
  /**
   * 表达式类型
   */
  expressionType: string
}
```

【前端表达式场景结构化】
TS 类型：

```ts
/**
 * 前端表达式场景
 */
type FrontExpressionScene = FrontExpressionSceneProp

/**
 * 前端表达式属性场景。
 * 该场景下，表达式的返回值会被用作组件的属性。
 * 需要注意的是，该场景下，表达式是作为 mobx 的 reaction 运行的。
 * 表达式计算的时候，如果访问了 observable 的值（比如组件的值：ctx.data.组件ID），那对应的值发生变化时，当前的表达式也会自动重新计算。
 */
interface FrontExpressionSceneProp {
  /**
   * 类型
   */
  type: 'prop'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
}
```

【当前简化的 Schema】
TS 类型：

```ts
/**
 * 下方 JSON 根对象类型
 */
type JsonRootOfCurrentSchema = SimplifySchema

/**
 * 简化的 Schema
 */
interface SimplifySchema {
  /**
   * 打平的视图
   */
  flattenedView: SimplifyFlattenedView
}

/**
 * 简化的打平的视图
 */
interface SimplifyFlattenedView {
  /**
   * 根组件 ID
   */
  rootComponentId: string
  /**
   * 所有组件
   */
  components: SimplifyComponent[]
  /**
   * 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
   */
  childComponentIdMap: Record<
    string,
    (string | SimplifyChildComponentIdAndPath)[]
  >
}

/**
 * 简化的组件实例
 */
interface SimplifyComponent {
  /**
   * 组件实例 ID
   */
  id: string
  /**
   * 组件类型
   */
  type: string
  /**
   * 组件实例名称
   */
  name: string
}

/**
 * 简化的子组件 ID 以及路径
 */
interface SimplifyChildComponentIdAndPath {
  /**
   * 子组件 ID
   */
  id: string
  /**
   * 子组件相对路径，默认按顺序添加到 children
   */
  path?: SimplifyPropertyPath
}

/**
 * 简化的属性路径
 *
 * 可以通过 lodash 的 get 函数获取 JSON 里的值
 */
type SimplifyPropertyPath = (string | number)[]
```

JSON：

```json
{
  "flattenedView": {
    "rootComponentId": "root",
    "components": [
      {
        "id": "root",
        "type": "@ad/canal-components::Root",
        "name": "根组件"
      },
      {
        "id": "comp_qngBTsF1eKhSjob7Ex8zk",
        "type": "@ad/canal-components::Text",
        "name": "文本::8zk"
      },
      {
        "id": "comp_JiQbOlPRwZUu3NEs9vxQO",
        "type": "@ad/canal-components::Text",
        "name": "文本::xQO"
      },
      {
        "id": "comp_RT5hXTwbkRniWyCcCVmir",
        "type": "@ad/canal-biz-components::41",
        "name": "按钮 bbx::mir"
      },
      {
        "id": "f方法是ut",
        "type": "@ad/canal-components::Input",
        "name": "f方法是ut 名称"
      },
      {
        "id": "sm",
        "type": "@ad/canal-components::Submodule",
        "name": "子模块::Kn_"
      },
      {
        "id": "iix",
        "type": "@ad/canal-components::Input",
        "name": "输入框::lJ6"
      },
      {
        "id": "comp_WKMPxyksw7qt2R2Ibs8sG",
        "type": "@ad/canal-components::Text",
        "name": "文本::8sG"
      },
      {
        "id": "pi1",
        "type": "@ad/canal-components::Input",
        "name": "输入框::UNS"
      },
      {
        "id": "comp_6bZbKOp_2WMHzQxtlVYb_",
        "type": "@ad/canal-components::Text",
        "name": "文本::Yb_"
      },
      {
        "id": "pi2",
        "type": "@ad/canal-components::Input",
        "name": "输入框::xRv"
      },
      {
        "id": "card",
        "type": "@ad/canal-biz-components::37",
        "name": "卡片::i1p"
      },
      {
        "id": "comp_xba8defcMAXg06wvdy9$o",
        "type": "@ad/canal-biz-components::17",
        "name": "中"
      },
      {
        "id": "left-btn",
        "type": "@ad/canal-biz-components::17",
        "name": "按钮4442::z6$"
      },
      {
        "id": "comp_JiTcovwZnBA81wqleM1wJ",
        "type": "@ad/canal-components::Text",
        "name": "文本::1wJ"
      },
      {
        "id": "right-btn",
        "type": "@ad/canal-biz-components::17",
        "name": "按钮4442::gg8"
      },
      {
        "id": "comp_Vws5Qeg8mX9YPLTHlTUdZ",
        "type": "@ad/canal-components::Text",
        "name": "文本::UdZ"
      },
      {
        "id": "comp_S0c9JJHwlW8op2aKHhAiU",
        "type": "@ad/canal-components::Container",
        "name": "容器::AiU"
      },
      {
        "id": "comp_IrqVVKzSUxUgUNXpQ2MuL",
        "type": "@ad/canal-components::Button",
        "name": "按钮::MuL"
      },
      {
        "id": "container1",
        "type": "@ad/canal-components::Container",
        "name": "容器::dpA"
      },
      {
        "id": "container2",
        "type": "@ad/canal-components::Container",
        "name": "容器::stG"
      },
      {
        "id": "test-btn",
        "type": "@ad/canal-components::Button",
        "name": "按钮::Zo5"
      },
      {
        "id": "comp_1vq6v5aXe85Smk639uLgl",
        "type": "@ad/canal-components::Text",
        "name": "文本::Lgl"
      },
      {
        "id": "checkbox",
        "type": "@ad/canal-components::Checkbox",
        "name": "多选框::Jdx"
      },
      {
        "id": "comp_dtvQlZXdWim$aqz6XSbov",
        "type": "@ad/canal-components::Text",
        "name": "文本::bov"
      },
      {
        "id": "comp_47uVP3nM_v$lbL8se3mx_",
        "type": "@ad/canal-components::Container",
        "name": "容器::mx_"
      },
      {
        "id": "comp_9OEwFp$eCZP8RscFGj1sk",
        "type": "@ad/canal-components::Text",
        "name": "文本::1sk"
      },
      {
        "id": "comp_8QobO3enpYqqQy3V4mcnl",
        "type": "@ad/canal-components::Container",
        "name": "容器::cnl"
      },
      {
        "id": "comp_u1ihunL6S0Fw9iZnfLpx5",
        "type": "@ad/canal-components::Button",
        "name": "按钮::px5"
      },
      {
        "id": "comp_1wwQ1276dhfgsL2C0GByV",
        "type": "@ad/canal-components::Text",
        "name": "文本::ByV"
      },
      {
        "id": "comp_EXliUI$fzcB1O0toIvAMd",
        "type": "@ad/canal-components::Text",
        "name": "文本::AMd"
      },
      {
        "id": "comp_IcVy6fa7LJJbA5lXqaQdI",
        "type": "@ad/canal-biz-components::17",
        "name": "按钮4442::QdI"
      },
      {
        "id": "tabbbb",
        "type": "@ad/canal-components::Table",
        "name": "表格::7m$"
      },
      {
        "id": "comp_zlxKUitJe6hWXz8QPMPIQ",
        "type": "@ad/canal-components::TableColumn",
        "name": "表格列名称::PIQ"
      },
      {
        "id": "comp_fB4_h4yROnni6XM0TzLO0",
        "type": "@ad/canal-components::TableColumn",
        "name": "表格列年龄::LO0"
      },
      {
        "id": "comp_NhWWsMBpongTqcMBs4b3S",
        "type": "@ad/canal-components::TableColumn",
        "name": "表格列操作::b3S"
      },
      {
        "id": "comp_flTRfh1L5CIL_QTlhlvcS",
        "type": "@ad/canal-components::TableCell",
        "name": "表格单元格::vcS"
      },
      {
        "id": "comp_o$3WDr5uG5r2ekQj5U6rG",
        "type": "@ad/canal-components::Button",
        "name": "查询按钮::6rG"
      },
      {
        "id": "comp_jaGcr399RFFzVROTtKqq7",
        "type": "@ad/canal-components::Text",
        "name": "文本::qq7"
      },
      {
        "id": "comp_GuHYEZdbGGzbNrZSpIYEr",
        "type": "@ad/canal-components::Container",
        "name": "容器::YEr"
      },
      {
        "id": "comp_V7vwjffMtKwxNpMxn8kXR",
        "type": "@ad/canal-components::Text",
        "name": "文本::kXR"
      },
      {
        "id": "comp_szT40Z71xqzXdTu9$Wh6E",
        "type": "@ad/canal-components::Text",
        "name": "文本::h6E"
      },
      {
        "id": "comp_JS8gzmJMjiKfKMaJAiDAx",
        "type": "@ad/canal-components::Container",
        "name": "容器::DAx"
      },
      {
        "id": "comp_BIqMwh15FpfcMFCwCAi8Z",
        "type": "@ad/canal-components::Text",
        "name": "文本::i8Z"
      },
      {
        "id": "comp_jjWLKWXolqCdgziU3duAU",
        "type": "@ad/canal-components::Text",
        "name": "文本::uAU"
      }
    ],
    "childComponentIdMap": {
      "root": [
        "comp_qngBTsF1eKhSjob7Ex8zk",
        "comp_JiQbOlPRwZUu3NEs9vxQO",
        "comp_RT5hXTwbkRniWyCcCVmir",
        "f方法是ut",
        "sm",
        "iix",
        "comp_WKMPxyksw7qt2R2Ibs8sG",
        "pi1",
        "comp_6bZbKOp_2WMHzQxtlVYb_",
        "pi2",
        "card",
        "container1",
        "comp_dtvQlZXdWim$aqz6XSbov",
        "comp_47uVP3nM_v$lbL8se3mx_",
        "comp_8QobO3enpYqqQy3V4mcnl",
        "comp_1wwQ1276dhfgsL2C0GByV",
        "comp_EXliUI$fzcB1O0toIvAMd",
        "comp_IcVy6fa7LJJbA5lXqaQdI",
        "tabbbb",
        "comp_jaGcr399RFFzVROTtKqq7",
        "comp_GuHYEZdbGGzbNrZSpIYEr",
        "comp_JS8gzmJMjiKfKMaJAiDAx"
      ],
      "card": [
        "comp_xba8defcMAXg06wvdy9$o",
        {
          "id": "left-btn",
          "path": ["props", "title", "value", 0, "value"]
        },
        {
          "id": "comp_JiTcovwZnBA81wqleM1wJ",
          "path": ["props", "extra", "value", 0, "value"]
        },
        {
          "id": "right-btn",
          "path": ["props", "extra", "value", 1, "value"]
        },
        {
          "id": "comp_Vws5Qeg8mX9YPLTHlTUdZ",
          "path": ["props", "extra", "value", 2, "value"]
        },
        {
          "id": "comp_S0c9JJHwlW8op2aKHhAiU",
          "path": ["props", "extra", "value", 3, "value"]
        }
      ],
      "comp_S0c9JJHwlW8op2aKHhAiU": ["comp_IrqVVKzSUxUgUNXpQ2MuL"],
      "container2": ["test-btn", "comp_1vq6v5aXe85Smk639uLgl", "checkbox"],
      "container1": ["container2"],
      "comp_47uVP3nM_v$lbL8se3mx_": ["comp_9OEwFp$eCZP8RscFGj1sk"],
      "comp_8QobO3enpYqqQy3V4mcnl": ["comp_u1ihunL6S0Fw9iZnfLpx5"],
      "tabbbb": [
        "comp_zlxKUitJe6hWXz8QPMPIQ",
        "comp_fB4_h4yROnni6XM0TzLO0",
        "comp_NhWWsMBpongTqcMBs4b3S"
      ],
      "comp_flTRfh1L5CIL_QTlhlvcS": ["comp_o$3WDr5uG5r2ekQj5U6rG"],
      "comp_NhWWsMBpongTqcMBs4b3S": ["comp_flTRfh1L5CIL_QTlhlvcS"],
      "comp_GuHYEZdbGGzbNrZSpIYEr": [
        "comp_V7vwjffMtKwxNpMxn8kXR",
        "comp_szT40Z71xqzXdTu9$Wh6E"
      ],
      "comp_JS8gzmJMjiKfKMaJAiDAx": [
        "comp_BIqMwh15FpfcMFCwCAi8Z",
        "comp_jjWLKWXolqCdgziU3duAU"
      ]
    }
  }
}
```

【常见的前端表达式举例】

- 例子 1

前端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "lineChart",
    "type": "@ad/canal-biz-components::109",
    "name": "折线图::6hl"
  },
  "propPath": ["showLine"],
  "propName": "显示折线"
}
```

前端表达式代码：

```js
/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  const currentSelector = ctx.data.chartSelectorCard
  return currentSelector
}
```

- 例子 2

前端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_z_a2gcvor_zGHIOATCPY9",
    "type": "@ad/canal-components::Text",
    "name": "文本::PY9"
  },
  "propPath": ["style", "fontSize"],
  "propName": "大小"
}
```

前端表达式代码：

```js
/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return '10px'
}
```

- 例子 3

前端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_nZMdxJ$a5fL8ClN73e6UL",
    "type": "@ad/canal-biz-components::195",
    "name": "有埋点的输入框::6UL"
  },
  "propPath": ["placeholder"],
  "propName": "占位文本"
}
```

前端表达式代码：

```js
import NP from '@ad/number-precision'

/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return `独立成本保障，当前追投任务总预算限额 ${NP.divide(
    ctx.initParams.dayBudgetRange.maxBudget,
    1000,
  )}`
}
```

- 例子 4

前端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_L1is6fCSyMz49bCdfGBtP",
    "type": "@ad/canal-biz-components::351",
    "name": "推广平台::BtP"
  },
  "propPath": ["options"],
  "propName": "options"
}
```

前端表达式代码：

```js
import { EUnitMaterialType } from '@ad/create-biz'

/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  const map = {
    [EUnitMaterialType.CID_TB]: '淘宝',
    [EUnitMaterialType.CID_TM]: '天猫',
    [EUnitMaterialType.CID_PDD]: '拼多多',
    [EUnitMaterialType.CID_JD]: '京东',
    [EUnitMaterialType.CID_WX_SHOP]: '微信商城',
    [EUnitMaterialType.CID_JD_WX_APP]: '京东+京东微小',
    [EUnitMaterialType.CID_PDD_WX_APP]: '拼多多+拼多多微小',
    [EUnitMaterialType.CID_OTHER]: '其他',
  }

  /** 单元素材类型选项 */
  const options = Object.entries(map).map(([value, label]) => ({
    value: +value,
    label,
  }))
  return options
}
```

- 例子 5

前端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "comp_D$o$8GwUKJy8KG6ckbAsw",
    "type": "@ad/canal-biz-components::197",
    "name": "指标对比折线图::Asw"
  },
  "propPath": ["showKeys"],
  "propName": "指标"
}
```

前端表达式代码：

```js
/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return ctx.data.comp_Rs6sMKDpnYtCT8zH0YlmS?.showKeys || []
}
```

解释：

comp_Rs6sMKDpnYtCT8zH0YlmS 是其他组件的 ID，`ctx.data.comp_Rs6sMKDpnYtCT8zH0YlmS` 表示那个组件的值，所以这个表达式就是去那个组件的值里拿 showKeys 字段作为当前组件的 showKeys 属性。

- 例子 6

前端表达式场景：

```json
{
  "type": "componentValueEffect",
  "component": {
    "id": "chartSelectorCard2",
    "type": "@ad/canal-biz-components::107",
    "name": "素材数量-概览卡列表"
  }
}
```

前端表达式代码：

```js
const dataMap = {
  inPutPhotoNum: '6cam_cBROz9V_KqkN1c3G',
  validPhotoRatio: 'wpeKg2IhRVAyS8Pg5yL2D',
  activePhotoNum: 'UnVYwFDjGDZ1VfNCoF-Mc',
  uploadPhotoNum: '6KzRbyICUhiFHhQPw-2JA',
  campaignRelatePhotoNum: 'RPfTDCVAo9s3OXwQZYlN9',
  unitRelatePhotoNum: 'mFkKaIvKIEZwGv1bWwDQz',
}

/**
 * @param {any} value 当前值
 * @param {any} prevValue 上一次值
 * @param {Container} ctx 上下文
 */
export default (value, prevValue, ctx) => {
  ctx.data.chartSelectorCard2.map((item) => {
    ctx.track(dataMap?.[item] || '6cam_cBROz9V_KqkN1c3G')
  })
}
```

- 例子 7

前端表达式场景：

```json
{
  "type": "eventActionEffect",
  "component": {
    "id": "comp_Ebxfrin2lvGskZWG_xYHU",
    "type": "@ad/canal-components::Button",
    "name": "按钮::YHU"
  },
  "propPath": ["onClick"],
  "propName": "点击",
  "index": 1
}
```

前端表达式代码：

```js
/**
 * @param {Container} ctx 上下文
 * @param {any[]} args 组件调用事件函数时传递的参数
 */
export default (ctx, ...args) => {
  ctx.data.search({ reset: true })
}
```

- 例子 8

前端表达式场景：

```json
{
  "type": "iifeActionEffect",
  "index": 2
}
```

前端表达式代码：

```js
/**
 * @param {Container} ctx 上下文
 * @param {any} arg 参数
 */
export default (ctx, arg) => {
  console.log('初始化立即执行', ctx, arg)
  ctx.setData({
    initialized: true,
  })
}
```

【当前模块内正在使用的前端表达式】

无

【不合适的前端表达式举例】

- 例子 1

前端表达式场景：

```json
{
  "type": "prop",
  "component": {
    "id": "ttx",
    "type": "@ad/canal-components::Text",
    "name": "文本::ttx"
  },
  "propPath": ["text"],
  "propName": "文本"
}
```

前端表达式代码：

```js
/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return ctx.data.ttx || '默认文本'
}
```

解释：

`ctx.data.ttx` 里的 `ttx` 和场景里的 id 一致，表示取用当前组件的值来生成组件的文本属性。但一般组件属性不会依赖自身的值，所以这种类型的代码是不合适的。

【正在编辑的前端表达式场景】

```json
{
  "type": "prop",
  "component": {
    "id": "comp_JiQbOlPRwZUu3NEs9vxQO",
    "type": "@ad/canal-components::Text",
    "name": "文本::xQO"
  },
  "propPath": ["text"],
  "propName": "文本"
}
```

【输出的要求】

- 生成与【正在编辑的前端表达式场景】对应的前端表达式代码。
- 只输出前端表达式代码，即只有 JS 代码，不需要额外的文本。
- 前端表达式代码的风格需要参考【常见的前端表达式举例】和【当前模块内正在使用的前端表达式】。
- 不要生成跟【不合适的前端表达式举例】风格类似的代码，除非【当前模块内正在使用的前端表达式】里有相同风格的前端表达式。
