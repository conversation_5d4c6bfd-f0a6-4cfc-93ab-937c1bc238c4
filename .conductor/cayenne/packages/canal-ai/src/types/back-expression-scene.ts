import { SimplifyComponent } from '../utils'

/**
 * 后端表达式场景
 */
export type BackExpressionScene =
  | BackExpressionSceneProp
  | BackExpressionSceneApiArgs
  | BackExpressionSceneApiIf
  | BackExpressionSceneIife

/**
 * 后端表达式属性场景。
 * 该场景下，表达式的返回值会被用作组件的属性。
 * 但需要注意的是，value 属性是特殊的，前端也会维护一份组件的值，如果后端运行时返回一个非 null 的值，则会覆盖前端维护的组件值，返回 null 则会被忽略。
 */
export interface BackExpressionSceneProp {
  /**
   * 类型
   */
  type: 'prop'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
  /**
   * 接口，这里给出的是展示给用户看的拼接字符串。
   * 存在一个接口时，表达式通常主要从 ctx.response 里获取数据。
   * 存在多个接口时，表达式通常主要从 ctx.responses 里获取数据。
   * 不存在接口时，表达式不会从 ctx.response 或 ctx.responses 里获取数据，主要从上下文的其他字段获取数据，主要是 ctx.request，当然也有可能是 ctx.loop、ctx.model、ctx.globalModel 等。
   */
  apis: string[]
}

/**
 * 后端表达式接口参数场景。
 * 该场景下，表达式的返回值会被用作业务接口的参数。
 */
export interface BackExpressionSceneApiArgs {
  /**
   * 类型
   */
  type: 'apiArgs'
  /**
   * 表达式所属的组件，也就是当前接口所属的组件，没有的时候表示该接口是模块级的接口
   */
  component?: SimplifyComponent
  /**
   * 数据源，这里给出的是展示给用户看的拼接字符串
   */
  dataSource: string
}

/**
 * 后端表达式接口执行条件场景。
 * 该场景下，表达式返回 true 时，会发起业务接口的请求，表达式返回 false 时，则不会发起业务接口的请求。
 * 不发起业务接口的请求，业务接口的响应为 null。
 */
export interface BackExpressionSceneApiIf {
  /**
   * 类型
   */
  type: 'apiIf'
  /**
   * 表达式所属的组件，也就是当前接口所属的组件，没有的时候表示该接口是模块级的接口
   */
  component?: SimplifyComponent
  /**
   * 数据源，这里给出的是展示给用户看的拼接字符串
   */
  dataSource: string
}

/**
 * 后端表达式前端动作场景。
 * 该场景下，表达式的返回值需要为 `number | number[]`，会被当做动作的下标，用来选择需要执行的动作。
 * 另外表达式里除了需要通过 `default` 字段导出一个函数外，还需要在 `output` 字段导出一个对象，供动作按照键值取用。
 * 最终产生的动作，会在下发到前端后立即执行。
 */
export interface BackExpressionSceneIife {
  /**
   * 类型
   */
  type: 'iife'
  /**
   * 接口，同 `BackExpressionSceneProp['apis']`
   */
  apis: string[]
  /**
   * 动作，这里只给出动作的描述。
   */
  actions: string[]
}
