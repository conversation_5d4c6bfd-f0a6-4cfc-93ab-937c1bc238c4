import { SimplifyComponent } from '../utils'

/**
 * 前端表达式场景
 */
export type FrontExpressionScene =
  | FrontExpressionSceneProp
  | FrontExpressionSceneComponentValueEffect
  | FrontExpressionSceneEventActionEffect
  | FrontExpressionSceneIifeActionEffect

/**
 * 前端表达式属性场景。
 * 该场景下，表达式只有一个参数：上下文 `ctx`，表达式的返回值会被用作组件的属性。
 * 需要注意的是，该场景下，表达式是作为 mobx 的 reaction 运行的。
 * 表达式计算的时候，如果访问了 observable 的值（比如组件的值：ctx.data.组件ID），那对应的值发生变化时，当前的表达式也会自动重新计算。
 */
export interface FrontExpressionSceneProp {
  /**
   * 类型
   */
  type: 'prop'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
}

/**
 * 前端表达式组件值副作用场景。
 * 该场景下，表达式有三个参数：当前值 `value`、上一次值 `prevValue` 和上下文 `ctx`，表达式不需要有返回值。
 * 该函数在每次组件值变化时，会自动执行。
 */
export interface FrontExpressionSceneComponentValueEffect {
  /**
   * 类型
   */
  type: 'componentValueEffect'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
}

/**
 * 前端表达式事件动作副作用场景。
 * 该场景下，表达式至少有一个参数：上下文 `ctx`，剩下的参数为组件事件参数的透传，大部分事件也不需要表达式的返回值。
 * 该函数只在事件发生时调用。
 */
export interface FrontExpressionSceneEventActionEffect {
  /**
   * 类型
   */
  type: 'eventActionEffect'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
  /**
   * 下标，一个事件可以触发多个动作，下标表示这是第几个动作
   */
  index: number
}

/**
 * 前端表达式（模块级）前端动作副作用场景。
 * 该场景下，表达式有两个参数：上下文 `ctx` 和参数 `arg`，表达式不需要有返回值。
 * 该函数会在每次 Schema 下发到前端后立即执行。
 */
export interface FrontExpressionSceneIifeActionEffect {
  /**
   * 类型
   */
  type: 'iifeActionEffect'
  /**
   * 下标，即第几个动作
   */
  index: number
}
