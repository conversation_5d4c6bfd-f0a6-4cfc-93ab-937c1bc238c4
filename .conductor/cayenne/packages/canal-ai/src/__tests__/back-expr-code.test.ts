/* eslint-disable @typescript-eslint/naming-convention */
import { BackExpressionType } from '@ad/canal-shared'
import { E2ESchema } from '@ad/e2e-schema'
import { createBackExprCodePrompt } from '../prompts'
import { BackExpressionScene } from '../types'

const schema = {
  schemaVersion: '0.0.1',
  componentCodes: {
    '@ad/canal-biz-components::236': {
      code: {
        js: 'https://p2-ad.adkwai.com/kcdn/cdn-kcdn111910/material/prod/@ad/report-material/1.0.43-20241106025451/dist/base/TextCollapse/index.min.js',
      },
      version: '2',
    },
    '@ad/canal-biz-components::235': {
      code: {
        js: 'https://p2-ad.adkwai.com/kcdn/cdn-kcdn111910/material/prod/@ad/report-material/1.0.43-20241106021438/dist/base/RefreshButtonSetter/index.min.js',
      },
      exportIdentifier: 'default',
      version: '1',
    },
    '@ad/canal-biz-components::234': {
      code: {
        js: 'https://p2-ad.adkwai.com/kcdn/cdn-kcdn111910/material/prod/@ad/report-material/1.0.43-20241101072152/dist/base/ChartSelector/index.min.js',
      },
      version: '1',
    },
  },
  view: {
    type: '@ad/canal-components::Root',
    id: 'root',
    name: '根组件',
    props: {
      style: {
        type: 'static',
        value: {
          width: '100%',
          margin: '0 0 32px 0',
          height: '',
        },
      },
      __$backData: {
        type: 'api',
        apiId: 'xu3de9LwS3PsfCilc6Ljk',
        defaultValue: {
          type: 'static',
        },
        transform: {
          type: 'js',
          code: 'function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports.default = void 0;\nconst COLUMN_KEYS = [\'totalCharge\', \'activeCampaignNum\', \'click\', \'actionbarClick\', \'conversionNum\', \'conversionNumCost\', \'impression\', "actionNewRatio", "actionRatio", "photoClickRatio", "click1kCost", "impression1kCost", "actionCost", "photoClickCost", "dupPhotoTotalCost"];\nconst custmoColumnsList = [{\n  dataIndex: "activeCampaignNum",\n  title: "活跃计划数",\n  desc: "统计时间内有花费的计划总数",\n  format: "precise2"\n}];\n\n/**\n * @param ctx 上下文\n */\nvar _default = ctx => {\n  const {\n    sum = {},\n    ...rest\n  } = ctx.response || {};\n  const data = {\n    ...sum,\n    ...rest\n  };\n  let {\n    columnsList = []\n  } = ctx.request.params;\n  columnsList = [...columnsList, ...custmoColumnsList];\n  const columnConfig = COLUMN_KEYS.map(key => {\n    const config = columnsList.find(item => item.dataIndex === key);\n    if (!config) return;\n    return {\n      ...config,\n      value: data[config.dataIndex]\n    };\n  }).filter(Boolean);\n  return {\n    columnKeys: COLUMN_KEYS,\n    columnConfig\n  };\n};\nexports.default = _default;\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}',
          codeTS:
            'const COLUMN_KEYS = [\n  \'totalCharge\',\n  \'activeCampaignNum\',\n  \'click\',\n  \'actionbarClick\',\n  \'conversionNum\',\n  \'conversionNumCost\',\n  \'impression\',\n  "actionNewRatio",\n  "actionRatio",\n  \n  "photoClickRatio",\n  "click1kCost",\n  "impression1kCost",\n  "actionCost",\n  "photoClickCost",\n  "dupPhotoTotalCost",\n];\n\nconst custmoColumnsList = [{\n  dataIndex: "activeCampaignNum",\n  title: "活跃计划数",\n  desc: "统计时间内有花费的计划总数",\n  format: "precise2"\n}]\n\n/**\n * @param ctx 上下文\n */\nexport default (ctx: Ctx) => {\n  const { sum = {}, ...rest } = ctx.response as any || {}\n  const data = {...sum,...rest}\n  let { columnsList = [] } = ctx.request.params;\n  columnsList = [...columnsList, ...custmoColumnsList];\n  const columnConfig = COLUMN_KEYS.map(key => {\n    const config = columnsList.find(item => item.dataIndex === key)\n    if(!config) return;\n    return {\n      ...config,\n      value: data[config.dataIndex]\n    }\n  }).filter(Boolean)\n\n  return {\n    columnKeys: COLUMN_KEYS,\n    columnConfig\n  }\n}\n',
        },
      },
    },
    apis: [
      {
        dataSourceId: 'd76d1e0e-f8a4-4de0-8c47-d17482a71158',
        id: 'xu3de9LwS3PsfCilc6Ljk',
        args: [
          {
            type: 'js',
            code: "function main(mainArg0) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nconst COLUMN_KEYS = ['totalCharge', 'activeCampaignNum', 'click', 'actionbarClick', 'conversionNum', 'conversionNumCost', 'impression', 'actionNewRatio', 'actionRatio', \"photoClickRatio\", \"click1kCost\", \"impression1kCost\", \"actionCost\", \"photoClickCost\", \"dupPhotoTotalCost\"];\n\n/**\n * @param ctx 上下文\n */\nvar _default = ctx => {\n  return {\n    searchParam: {\n      reportStartDay: ctx.request.params.defaultStartTime,\n      reportEndDay: ctx.request.params.defaultEndTime,\n      selectedColumns: COLUMN_KEYS\n    }\n  };\n};\nexports.default = _default;\n  })()\n  return module.exports.default.apply(module.exports, mainArg0.args)\n}",
            codeTS:
              "const COLUMN_KEYS = [\n  'totalCharge',\n  'activeCampaignNum',\n  'click',\n  'actionbarClick',\n  'conversionNum',\n  'conversionNumCost',\n  'impression',\n  'actionNewRatio',\n  'actionRatio',\n\n  \"photoClickRatio\",\n  \"click1kCost\",\n  \"impression1kCost\",\n  \"actionCost\",\n  \"photoClickCost\",\n  \"dupPhotoTotalCost\",\n];\n\n/**\n * @param ctx 上下文\n */\nexport default (ctx: Ctx) => {\n  return {\n    searchParam: {\n      reportStartDay: ctx.request.params.defaultStartTime,\n      reportEndDay: ctx.request.params.defaultEndTime,\n      selectedColumns: COLUMN_KEYS\n    }\n  }\n}\n",
          },
        ],
      },
    ],
    children: [
      {
        id: 'comp_EE12U_yXYLpMYNkiTMtkB',
        type: '@ad/canal-components::Container',
        name: 'header',
        props: {
          style: {
            type: 'static',
            value: {
              display: 'flex',
              justifyContent: 'space-between',
              margin: '0 0 24px 0',
            },
          },
        },
        children: [
          {
            id: 'comp_FRpSu6ypodel8rgI$PWKD',
            type: '@ad/canal-components::Container',
            name: 'left',
            props: {
              style: {
                type: 'static',
                value: {
                  display: 'flex',
                  alignItems: 'center',
                },
              },
            },
            children: [
              {
                id: 'comp_bmmuHtIUFFjLdrcIFR6wz',
                type: '@ad/canal-components::Text',
                name: '标题',
                props: {
                  text: '数据总览',
                  style: {
                    type: 'static',
                    value: {
                      fontSize: '16px',
                      fontWeight: 600,
                    },
                  },
                },
              },
              {
                id: 'comp_0GIjJQTS1rhsaxd0YDMCr',
                type: '@ad/canal-components::Container',
                name: '容器::MCr',
                props: {
                  style: {
                    type: 'static',
                    value: {
                      width: '1px',
                      height: '14px',
                      color: '#000000',
                      background: '#D7DBE0',
                      margin: '0px 16px 0px 16px',
                    },
                  },
                },
              },
              {
                id: 'comp_lFqYX649W7tItLGT4fPuB',
                type: '@ad/canal-components::Text',
                name: '文本::PuB',
                props: {
                  text: {
                    type: 'js',
                    code: '(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = exports.DATE_FORMATTER = void 0;\nvar _dayjs = _interopRequireDefault(require("dayjs"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }\nvar DATE_FORMATTER = exports.DATE_FORMATTER = \'YYYY/MM/DD\';\n\n/**\n * @param {Container} ctx 上下文\n */\nvar _default = exports["default"] = function _default(ctx) {\n  var _ctx$initParams = ctx.initParams,\n    defaultStartTime = _ctx$initParams.defaultStartTime,\n    defaultEndTime = _ctx$initParams.defaultEndTime;\n  return "\\u6570\\u636E\\u7EDF\\u8BA1\\u65F6\\u95F4\\uFF1A".concat((0, _dayjs["default"])(defaultStartTime).format(DATE_FORMATTER), "-").concat((0, _dayjs["default"])(defaultEndTime).format(DATE_FORMATTER));\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)(ctx.runtime.container)',
                    defaultValue: {
                      type: 'static',
                      value: '数据统计时间：2024/10/28-2024/11/02',
                    },
                    codeES:
                      "import dayjs from 'dayjs';\n\nexport const DATE_FORMATTER = 'YYYY/MM/DD';\n\n/**\n * @param {Container} ctx 上下文\n */\nexport default (ctx) => {\n   const { defaultStartTime, defaultEndTime } = ctx.initParams;\n   \n   return `数据统计时间：${dayjs(defaultStartTime).format(DATE_FORMATTER)}-${dayjs(defaultEndTime).format(DATE_FORMATTER)}`\n}\n",
                  },
                  style: {
                    type: 'static',
                    value: {
                      color: '#B9BEC4',
                      fontWeight: 400,
                    },
                  },
                },
              },
            ],
          },
          {
            id: 'comp_SVw4Q4prUyE6vII4t2sSi',
            type: '@ad/canal-components::Container',
            name: 'right',
            props: {
              style: {
                type: 'static',
                value: {
                  display: 'flex',
                  alignItems: 'center',
                  margin: '0 8px 0 0',
                },
              },
            },
            children: [
              {
                id: 'collapse',
                type: '@ad/canal-biz-components::236',
                name: '展开折叠::snV',
                props: {
                  showText: '展开指标卡',
                  hideText: '收起指标卡',
                  defaultStatus: false,
                  style: {
                    type: 'static',
                    value: {
                      padding: '0px',
                      color: '#333840',
                    },
                  },
                  iconPosition: 'left',
                },
              },
              {
                id: 'comp_eaoPbu3_jhLEdDnDenwn7',
                type: '@ad/canal-components::Container',
                name: '容器::wn7',
                props: {
                  style: {
                    type: 'static',
                    value: {
                      width: '1px',
                      height: '20px',
                      color: '#000000',
                      background: '#D7DBE0',
                      margin: '0px 16px 0px 16px',
                    },
                  },
                },
              },
              {
                id: 'comp_7L2uz2MfQXimgRR$IsxQ6',
                type: '@ad/canal-biz-components::235',
                name: '刷新按钮::xQ6',
                props: {
                  title: '刷新',
                  onClick: {
                    type: 'actions',
                    fns: [
                      {
                        type: 'refresh',
                        refreshType: 'submit',
                      },
                    ],
                  },
                },
              },
            ],
          },
        ],
      },
      {
        id: 'comp_xh95jKKfvnUNqVFV5vsQm',
        type: '@ad/canal-components::Container',
        name: '容器::sQm',
        props: {
          enableLoading: true,
        },
        children: [
          {
            id: 'comp_UqlReNBdOSpTsaiTbnlvi',
            type: '@ad/canal-biz-components::234',
            name: '概览卡列表::lvi',
            props: {
              dataSource: {
                type: 'js',
                code: '(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = void 0;\n/**\n * @param ctx 上下文\n */\nvar _default = exports["default"] = function _default(ctx) {\n  var _ctx$data;\n  return ((_ctx$data = ctx.data) === null || _ctx$data === void 0 ? void 0 : _ctx$data.columnConfig) || [];\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)(ctx.runtime.container)',
                defaultValue: {
                  type: 'static',
                  value: [
                    {
                      value: 2000,
                      dataIndex: 'costTotal',
                      title: '花费',
                      format: 'formatToMoney',
                      desc: '广告在投放期间的花费总额',
                    },
                    {
                      value: 200,
                      dataIndex: 't0OrderPaymentAmt',
                      title: 'GMV',
                      format: 'formatToMoney',
                      desc: '当日累计GMV',
                    },
                    {
                      value: 200,
                      dataIndex: 't0OrderCnt',
                      title: '成交订单数',
                      format: 'formatToNumber',
                      desc: '通过推广直接带来的+关注/互动用户在投放当天的总成交订单数，不剔除售后订单',
                    },
                    {
                      value: 200,
                      dataIndex: 'adShow',
                      title: '广告曝光数',
                      format: 'formatToNumber',
                      desc: '直播或作品首次曝光的次数',
                    },
                    {
                      value: 200,
                      dataIndex: 't0Roi',
                      title: 'ROI',
                      format: 'formatToNumberFixed2',
                      desc: '当日累计ROI = 当日累计GMV/花费',
                    },
                  ],
                },
                codeTS:
                  '/**\n * @param ctx 上下文\n */\nexport default (ctx: Container) => {\n  return ctx.data?.columnConfig || []\n}\n',
              },
              defaultValue: [],
              autoSelect: false,
              maxSelectCountOverReplace: false,
              girdGap: 16,
              colors: [
                '#376DF7',
                '#53B997',
                '#6750AA',
                '#F8C541',
                '#294680',
                '#68B2FF',
                '#7A4388',
                '#E883B9',
                '#00827F',
                '#FF9B47',
              ],
              colorType: 'single',
              autoSort: false,
              autoAdaption: true,
              collapseStatus: {
                type: 'js',
                code: '(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    "use strict";\n\nObject.defineProperty(exports, "__esModule", {\n  value: true\n});\nexports["default"] = void 0;\n/**\n * @param {Container} ctx 上下文\n */\nvar _default = exports["default"] = function _default(ctx) {\n  return ctx.data.collapse || false;\n};\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)(ctx.runtime.container)',
                defaultValue: {
                  type: 'static',
                  value: true,
                },
                codeES:
                  '/**\n * @param {Container} ctx 上下文\n */\nexport default (ctx) => {\n  return ctx.data.collapse || false\n}\n',
              },
              collapseMaxHeight: 92,
              cardWidth: 184,
              smallLetter: true,
              needCompare: false,
              needCompareFormat: false,
              compareType: 'abs',
              disabled: true,
              customStyle: {
                type: 'static',
                value: {
                  background: '#F8F9FA',
                },
              },
              valueCustomStyle: {
                type: 'static',
                value: {
                  position: 'relative',
                  top: '5px',
                },
              },
            },
          },
        ],
      },
    ],
  },
} as unknown as E2ESchema

describe('createBackExprCodePrompt', () => {
  it('createBackExprCodePrompt', () => {
    const scene: BackExpressionScene = {
      type: 'apiArgs',
      dataSource: '[getDataSourceLabelByApi][xu3de9LwS3PsfCilc6Ljk]',
    }
    expect(
      createBackExprCodePrompt({
        expressionType: BackExpressionType.TYPESCRIPT,
        scene,
        ctxDts: '',
        schema,
        getComponentMaterialSchema() {
          return null
        },
        getApiLabelsInComponent() {
          return []
        },
        getDataSourceLabelByApi(api) {
          return `[getDataSourceLabelByApi][${api.id}]`
        },
        keepCurrentScene: false,
      }).match(/\[getDataSourceLabelByApi\]\[xu3de9LwS3PsfCilc6Ljk\]/g)?.length,
    ).toBe(1)
  })
})
