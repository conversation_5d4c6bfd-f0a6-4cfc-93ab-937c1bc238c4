//#region 正面例子

export const example1Js = `/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  const currentSelector = ctx.data.chartSelectorCard
  return currentSelector
}`

export const example1Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Container) => {
  const currentSelector = ctx.data.chartSelectorCard
  return currentSelector
}`

export const example2Js = `/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return '10px'
}`

export const example2Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Container) => {
  return '10px'
}`

export const example3Js = `import NP from '@ad/number-precision'

/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return \`独立成本保障，当前追投任务总预算限额 \${NP.divide(
    ctx.initParams.dayBudgetRange.maxBudget,
    1000,
  )}\`
}`

export const example3Ts = `import NP from '@ad/number-precision'

/**
 * @param ctx 上下文
 */
export default (ctx: Container) => {
  return \`独立成本保障，当前追投任务总预算限额 \${NP.divide(
    ctx.initParams.dayBudgetRange.maxBudget,
    1000,
  )}\`
}`

export const example4Js = `import { EUnitMaterialType } from '@ad/create-biz'

/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  const map = {
    [EUnitMaterialType.CID_TB]: '淘宝',
    [EUnitMaterialType.CID_TM]: '天猫',
    [EUnitMaterialType.CID_PDD]: '拼多多',
    [EUnitMaterialType.CID_JD]: '京东',
    [EUnitMaterialType.CID_WX_SHOP]: '微信商城',
    [EUnitMaterialType.CID_JD_WX_APP]: '京东+京东微小',
    [EUnitMaterialType.CID_PDD_WX_APP]: '拼多多+拼多多微小',
    [EUnitMaterialType.CID_OTHER]: '其他',
  }

  /** 单元素材类型选项 */
  const options = Object.entries(map).map(([value, label]) => ({
    value: +value,
    label,
  }))
  return options
}`

export const example4Ts = `import { EUnitMaterialType } from '@ad/create-biz'

/**
 * @param ctx 上下文
 */
export default (ctx: Container) => {
  const map = {
    [EUnitMaterialType.CID_TB]: '淘宝',
    [EUnitMaterialType.CID_TM]: '天猫',
    [EUnitMaterialType.CID_PDD]: '拼多多',
    [EUnitMaterialType.CID_JD]: '京东',
    [EUnitMaterialType.CID_WX_SHOP]: '微信商城',
    [EUnitMaterialType.CID_JD_WX_APP]: '京东+京东微小',
    [EUnitMaterialType.CID_PDD_WX_APP]: '拼多多+拼多多微小',
    [EUnitMaterialType.CID_OTHER]: '其他',
  }

  /** 单元素材类型选项 */
  const options = Object.entries(map).map(([value, label]) => ({
    value: +value,
    label,
  }))
  return options
}`

export const example5Js = `/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return ctx.data.comp_Rs6sMKDpnYtCT8zH0YlmS?.showKeys || []
}`

export const example5Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Container) => {
  return ctx.data.comp_Rs6sMKDpnYtCT8zH0YlmS?.showKeys || []
}`

export const example6Js = `const dataMap = {
  inPutPhotoNum: '6cam_cBROz9V_KqkN1c3G',
  validPhotoRatio: 'wpeKg2IhRVAyS8Pg5yL2D',
  activePhotoNum: 'UnVYwFDjGDZ1VfNCoF-Mc',
  uploadPhotoNum: '6KzRbyICUhiFHhQPw-2JA',
  campaignRelatePhotoNum: 'RPfTDCVAo9s3OXwQZYlN9',
  unitRelatePhotoNum: 'mFkKaIvKIEZwGv1bWwDQz',
}

/**
 * @param {any} value 当前值
 * @param {any} prevValue 上一次值
 * @param {Container} ctx 上下文
 */
export default (value, prevValue, ctx) => {
  ctx.data.chartSelectorCard2.map((item) => {
    ctx.track(dataMap?.[item] || '6cam_cBROz9V_KqkN1c3G')
  })
}`

export const example6Ts = `const dataMap = {
  inPutPhotoNum: '6cam_cBROz9V_KqkN1c3G',
  validPhotoRatio: 'wpeKg2IhRVAyS8Pg5yL2D',
  activePhotoNum: 'UnVYwFDjGDZ1VfNCoF-Mc',
  uploadPhotoNum: '6KzRbyICUhiFHhQPw-2JA',
  campaignRelatePhotoNum: 'RPfTDCVAo9s3OXwQZYlN9',
  unitRelatePhotoNum: 'mFkKaIvKIEZwGv1bWwDQz',
}

/**
 * @param value 当前值
 * @param prevValue 上一次值
 * @param ctx 上下文
 */
export default (value: any, prevValue: any, ctx: Container) => {
  ctx.data.chartSelectorCard2.map((item) => {
    ctx.track(dataMap?.[item] || '6cam_cBROz9V_KqkN1c3G')
  })
}`

export const example7Js = `/**
 * @param {Container} ctx 上下文
 * @param {any[]} args 组件调用事件函数时传递的参数
 */
export default (ctx, ...args) => {
  ctx.data.search({ reset: true })
}`

export const example7Ts = `/**
 * @param ctx 上下文
 * @param args 组件调用事件函数时传递的参数
 */
export default (ctx: Container, ...args: any[]) => {
  ctx.data.search({ reset: true })
}`

export const example8Js = `/**
 * @param {Container} ctx 上下文
 * @param {any} arg 参数
 */
export default (ctx, arg) => {
  console.log('初始化立即执行', ctx, arg)
  ctx.setData({
    initialized: true,
  })
}`

export const example8Ts = `/**
 * @param ctx 上下文
 * @param arg 参数
 */
export default (ctx: Container, arg: any) => {
  console.log('初始化立即执行', ctx, arg)
  ctx.setData({
    initialized: true,
  })
}`

//#endregion 正面例子

//#region 反面例子

export const negativeExample1Js = `/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  return ctx.data.ttx || '默认文本'
}`

export const negativeExample1Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Container) => {
  return ctx.data.ttx || '默认文本'
}`

//#endregion 反面例子
