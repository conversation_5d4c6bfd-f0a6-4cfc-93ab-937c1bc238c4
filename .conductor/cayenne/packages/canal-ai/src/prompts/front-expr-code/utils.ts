import {
  FrontExpressionType,
  getJsExpressionCodeWithType,
} from '@ad/canal-shared'
import { E2ECMSProp } from '@ad/e2e-material-schema'
import { E2ESchema, E2ESchemaExpression } from '@ad/e2e-schema'
import {
  getBindIIFEActionsFromCode,
  getComponentPropDetail,
  isActionsExpression,
  isApiExpression,
  isBindExpression,
  isJsExpression,
} from '@ad/e2e-schema-utils'
import {
  dfsGenComponentDetailBySchema,
  type ComponentDetailOfSchema,
} from '@kael/schema-utils'
import { isEqual, pick } from 'lodash'
import { FrontExpressionScene, FrontExpressionSceneProp } from '../../types'
import { SimplifyComponent } from '../../utils'
import { CreateFrontExprCodePromptOptions } from './prompt'

/**
 * 根据 Schema 生成前端表达式代码
 * @param expressionType 表达式类型
 * @param schema 端到端 Schema
 * @param getComponentMaterialSchema 获取组件物料 Schema
 */
export function* genFrontExprCodesBySchema(
  expressionType:
    | FrontExpressionType.JAVASCRIPT
    | FrontExpressionType.TYPESCRIPT,
  schema: E2ESchema,
  getComponentMaterialSchema: CreateFrontExprCodePromptOptions['getComponentMaterialSchema'],
): Generator<[FrontExpressionScene, string]> {
  for (const cd of dfsGenComponentDetailBySchema(schema)) {
    yield* genFrontExprCodesBySchemaComponent(
      expressionType,
      schema,
      cd,
      getComponentMaterialSchema,
    )
  }
  if (isApiExpression(schema.iife) && isJsExpression(schema.iife.transform)) {
    const actions = getBindIIFEActionsFromCode(schema.iife.transform.code)
    for (let i = 0; i < actions.length; i++) {
      const action = actions[i]
      if (action.type === 'exec-effect') {
        const code = getJsExpressionCodeWithType(expressionType, action.expr)
        if (code) {
          yield [
            {
              type: 'iifeActionEffect',
              index: i,
            },
            code,
          ]
        }
      }
    }
  }
}

/**
 * 根据组件 Schema 生成前端表达式代码
 * @param expressionType 表达式类型
 * @param schema 端到端 Schema
 * @param cd 组件详情
 * @param getComponentMaterialSchema 获取组件物料 Schema
 */
export function* genFrontExprCodesBySchemaComponent(
  expressionType:
    | FrontExpressionType.JAVASCRIPT
    | FrontExpressionType.TYPESCRIPT,
  schema: E2ESchema,
  cd: ComponentDetailOfSchema<E2ESchema>,
  getComponentMaterialSchema: CreateFrontExprCodePromptOptions['getComponentMaterialSchema'],
): Generator<[FrontExpressionScene, string]> {
  const { component } = cd
  const simplifyComponent: SimplifyComponent = pick(
    component,
    'id',
    'type',
    'name',
  )
  const materialSchema = getComponentMaterialSchema(
    component.type,
    schema.componentCodes?.[component.type]?.version,
  )
  if (!materialSchema) return
  for (const prop of materialSchema.props || []) {
    yield* searchProp(prop, [])
  }
  if (isJsExpression(component.effect)) {
    const code = getJsExpressionCodeWithType(expressionType, component.effect)
    if (code) {
      yield [
        {
          type: 'componentValueEffect',
          component: simplifyComponent,
        },
        code,
      ]
    }
  }

  function* searchProp(
    prop: E2ECMSProp,
    parentPropPath: string[],
  ): Generator<[FrontExpressionScene, string]> {
    const propPath = [...parentPropPath, ...(prop.path || [])]
    if (prop.type === 'group') {
      for (const item of prop.items) {
        yield* searchProp(item, propPath)
      }
    } else {
      if (isEqual(propPath, ['value']) || isEqual(propPath, ['onChange'])) {
        return
      }
      const propDetail = getComponentPropDetail(component, propPath)
      yield* searchPropExpression(
        {
          type: 'prop',
          component: simplifyComponent,
          propPath,
          propName: `${prop.name}`,
        },
        propDetail.expression,
      )
    }
  }

  function* searchPropExpression(
    scene: FrontExpressionSceneProp,
    expression?: E2ESchemaExpression,
  ): Generator<[FrontExpressionScene, string]> {
    if (isJsExpression(expression)) {
      const code = getJsExpressionCodeWithType(expressionType, expression)
      if (code) {
        yield [scene, code]
      }
    } else if (isActionsExpression(expression)) {
      for (let i = 0; i < expression.fns.length; i++) {
        const fn = expression.fns[i]
        if (isBindExpression(fn) && isJsExpression(fn.fn)) {
          const code = getJsExpressionCodeWithType(expressionType, fn.fn)
          if (code) {
            yield [
              {
                type: 'eventActionEffect',
                component: scene.component,
                propPath: scene.propPath,
                propName: scene.propName,
                index: i,
              },
              code,
            ]
          }
        }
      }
    }
  }
}
