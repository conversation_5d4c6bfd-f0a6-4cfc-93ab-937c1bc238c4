import { FrontExpressionType, isEqualIgnoreUndefined } from '@ad/canal-shared'
import { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { E2ESchema } from '@ad/e2e-schema'
import { isEqual } from 'lodash'
import { FrontExpressionScene } from '../../types'
import { simplifySchema } from '../../utils'
import {
  example1Js,
  example1Ts,
  example2Js,
  example2Ts,
  example3Js,
  example3Ts,
  example4Js,
  example4Ts,
  example5Js,
  example5Ts,
  example6Js,
  example6Ts,
  example7Js,
  example7Ts,
  example8Js,
  example8Ts,
  negativeExample1Js,
  negativeExample1Ts,
} from './examples'
import { genFrontExprCodesBySchema } from './utils'

/**
 * 创建前端表达式代码提示
 */
export interface CreateFrontExprCodePromptOptions {
  /**
   * 表达式类型
   */
  expressionType:
    | FrontExpressionType.JAVASCRIPT
    | FrontExpressionType.TYPESCRIPT
  /**
   * 场景
   */
  scene: FrontExpressionScene
  /**
   * 上下文类型定义
   */
  ctxDts: string
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
  /**
   * 获取组件物料 Schema
   * @param type 组件类型
   * @param version 版本
   */
  getComponentMaterialSchema(
    type: string,
    version?: string,
  ): E2ERemoteComponentMaterialSchema | null
  /**
   * 保留当前场景，默认为：`true`
   */
  keepCurrentScene?: boolean
}

/**
 * 创建前端表达式代码提示
 * @param options 选项
 */
export function createFrontExprCodePrompt({
  expressionType,
  scene,
  ctxDts,
  schema,
  getComponentMaterialSchema,
  keepCurrentScene = true,
}: CreateFrontExprCodePromptOptions): string {
  const isJs = expressionType === FrontExpressionType.JAVASCRIPT
  const upperTextJsOrTs = isJs ? 'JS' : 'TS'
  const lowerTextJsOrTs = isJs ? 'js' : 'ts'
  let sceneCodes = [
    ...genFrontExprCodesBySchema(
      expressionType,
      schema,
      getComponentMaterialSchema,
    ),
  ]
  if (!keepCurrentScene) {
    sceneCodes = sceneCodes.filter(([s]) => !isEqualIgnoreUndefined(scene, s))
  }
  return `你是一个低代码应用的开发专家，请根据以下信息，输出用户正在编辑的前端表达式代码：

【名词解释】
模块：用户在低代码平台上开发的基础单位，类似于全代码开发中的文件。模块可能是一个页面，也可能是页面中的某一个独立的部分。
Schema：模块内容的一种 JSON 表示。用户不会感知到 Schema 的内容及其结构，用户是通过一个可视化的网页编辑器间接编辑 Schema 的。
表达式：基于 JSON 编写的数据计算逻辑，可以兼容各种类型的计算方式。
前端表达式：在前端运行时里运行的 ${upperTextJsOrTs} 代码，以 ${upperTextJsOrTs} 代码字符串的方式在 JSON 里描述数据计算逻辑。前端表达式需要在 default 字段导出一个函数，函数有一个上下文参数，在有些场景里也会有一些其他参数。
前端表达式场景：前端表达式常用于即时计算组件属性的值，或者在组件事件发生后执行一些动作。这些事件，可能是按钮点击，或者组件值发生变化等等。接下来我们会用 JSON 来结构化前端表达式场景的描述方式。
前端运行时：从后端运行时获取 Schema 后，运行并渲染为 React 虚拟 DOM 树，然后通过 React 渲染到页面上。在这过程中，前端运行时会计算前端表达式，获取一些动态的数据。宿主会向前端运行时提供一系列的依赖，这些依赖都会通过前端运行时提供给前端表达式。前端表达式可以通过 import 语法去引入、使用。

【前端表达式上下文参数类型】
TS 类型（提供给 monaco-editor 使用的定义文件）：

\`\`\`ts
${ctxDts}
\`\`\`

【前端表达式场景结构化】
TS 类型：

\`\`\`ts
/**
 * 前端表达式场景
 */
type FrontExpressionScene =
  | FrontExpressionSceneProp
  | FrontExpressionSceneComponentValueEffect
  | FrontExpressionSceneEventActionEffect
  | FrontExpressionSceneIifeActionEffect

/**
 * 前端表达式属性场景。
 * 该场景下，表达式只有一个参数：上下文 \`ctx\`，表达式的返回值会被用作组件的属性。
 * 需要注意的是，该场景下，表达式是作为 mobx 的 reaction 运行的。
 * 表达式计算的时候，如果访问了 observable 的值（比如组件的值：ctx.data.组件ID），那对应的值发生变化时，当前的表达式也会自动重新计算。
 */
interface FrontExpressionSceneProp {
  /**
   * 类型
   */
  type: 'prop'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
}

/**
 * 前端表达式组件值副作用场景。
 * 该场景下，表达式有三个参数：当前值 \`value\`、上一次值 \`prevValue\` 和上下文 \`ctx\`，表达式不需要有返回值。
 * 该函数在每次组件值变化时，会自动执行。
 */
interface FrontExpressionSceneComponentValueEffect {
  /**
   * 类型
   */
  type: 'componentValueEffect'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
}

/**
 * 前端表达式事件动作副作用场景。
 * 该场景下，表达式至少有一个参数：上下文 \`ctx\`，剩下的参数为组件事件参数的透传，大部分事件也不需要表达式的返回值。
 * 该函数只在事件发生时调用。
 */
interface FrontExpressionSceneEventActionEffect {
  /**
   * 类型
   */
  type: 'eventActionEffect'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
  /**
   * 下标，一个事件可以触发多个动作，下标表示这是第几个动作
   */
  index: number
}

/**
 * 前端表达式（模块级）前端动作副作用场景。
 * 该场景下，表达式有两个参数：上下文 \`ctx\` 和参数 \`arg\`，表达式不需要有返回值。
 * 该函数会在每次 Schema 下发到前端后立即执行。
 */
interface FrontExpressionSceneIifeActionEffect {
  /**
   * 类型
   */
  type: 'iifeActionEffect'
  /**
   * 下标，即第几个动作
   */
  index: number
}
\`\`\`

【当前简化的 Schema】
TS 类型：

\`\`\`ts
/**
 * 下方 JSON 根对象类型
 */
type JsonRootOfCurrentSchema = SimplifySchema

/**
 * 简化的 Schema
 */
interface SimplifySchema {
  /**
   * 打平的视图
   */
  flattenedView: SimplifyFlattenedView
}

/**
 * 简化的打平的视图
 */
interface SimplifyFlattenedView {
  /**
   * 根组件 ID
   */
  rootComponentId: string
  /**
   * 所有组件
   */
  components: SimplifyComponent[]
  /**
   * 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
   */
  childComponentIdMap: Record<
    string,
    (string | SimplifyChildComponentIdAndPath)[]
  >
}

/**
 * 简化的组件实例
 */
interface SimplifyComponent {
  /**
   * 组件实例 ID
   */
  id: string
  /**
   * 组件类型
   */
  type: string
  /**
   * 组件实例名称
   */
  name: string
}

/**
 * 简化的子组件 ID 以及路径
 */
interface SimplifyChildComponentIdAndPath {
  /**
   * 子组件 ID
   */
  id: string
  /**
   * 子组件相对路径，默认按顺序添加到 children
   */
  path?: SimplifyPropertyPath
}

/**
 * 简化的属性路径
 *
 * 可以通过 lodash 的 get 函数获取 JSON 里的值
 */
type SimplifyPropertyPath = (string | number)[]
\`\`\`

JSON：

\`\`\`json
${JSON.stringify(simplifySchema(schema), null, 2)}
\`\`\`

【常见的前端表达式举例】

- 例子 1

前端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "lineChart",
    "type": "@ad/canal-biz-components::109",
    "name": "折线图::6hl"
  },
  "propPath": ["showLine"],
  "propName": "显示折线"
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example1Js : example1Ts}
\`\`\`

- 例子 2

前端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_z_a2gcvor_zGHIOATCPY9",
    "type": "@ad/canal-components::Text",
    "name": "文本::PY9"
  },
  "propPath": ["style", "fontSize"],
  "propName": "大小"
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example2Js : example2Ts}
\`\`\`

- 例子 3

前端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_nZMdxJ$a5fL8ClN73e6UL",
    "type": "@ad/canal-biz-components::195",
    "name": "有埋点的输入框::6UL"
  },
  "propPath": ["placeholder"],
  "propName": "占位文本"
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example3Js : example3Ts}
\`\`\`

- 例子 4

前端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_L1is6fCSyMz49bCdfGBtP",
    "type": "@ad/canal-biz-components::351",
    "name": "推广平台::BtP"
  },
  "propPath": ["options"],
  "propName": "options"
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example4Js : example4Ts}
\`\`\`

- 例子 5

前端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_D$o$8GwUKJy8KG6ckbAsw",
    "type": "@ad/canal-biz-components::197",
    "name": "指标对比折线图::Asw"
  },
  "propPath": ["showKeys"],
  "propName": "指标"
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example5Js : example5Ts}
\`\`\`

解释：

comp_Rs6sMKDpnYtCT8zH0YlmS 是其他组件的 ID，\`ctx.data.comp_Rs6sMKDpnYtCT8zH0YlmS\` 表示那个组件的值，所以这个表达式就是去那个组件的值里拿 showKeys 字段作为当前组件的 showKeys 属性。

- 例子 6

前端表达式场景：

\`\`\`json
{
  "type": "componentValueEffect",
  "component": {
    "id": "chartSelectorCard2",
    "type": "@ad/canal-biz-components::107",
    "name": "素材数量-概览卡列表"
  }
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example6Js : example6Ts}
\`\`\`

- 例子 7

前端表达式场景：

\`\`\`json
{
  "type": "eventActionEffect",
  "component": {
    "id": "comp_Ebxfrin2lvGskZWG_xYHU",
    "type": "@ad/canal-components::Button",
    "name": "按钮::YHU"
  },
  "propPath": ["onClick"],
  "propName": "点击",
  "index": 1
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example7Js : example7Ts}
\`\`\`

- 例子 8

前端表达式场景：

\`\`\`json
{
  "type": "iifeActionEffect",
  "index": 2
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example8Js : example8Ts}
\`\`\`

【当前模块内正在使用的前端表达式】

${
  sceneCodes.length
    ? sceneCodes
        .map(
          ([s, code], index) => `- 例子 ${index + 1}

前端表达式场景：

\`\`\`json
${JSON.stringify(s, null, 2)}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${code}
\`\`\`
${
  isEqual(scene, s)
    ? `
解释：

这就是当前正在编辑的前端表达式代码，请勿原样输出，根据实际情况，酌情修改这份代码后输出。也请不要敷衍地只修改一些空格、换行，至少修改一些注释，最好能直接针对代码的逻辑进行修改。
`
    : ''
}`,
        )
        .join('\n')
    : '无\n'
}
【不合适的前端表达式举例】

- 例子 1

前端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "ttx",
    "type": "@ad/canal-components::Text",
    "name": "文本::ttx"
  },
  "propPath": [
    "text"
  ],
  "propName": "文本"
}
\`\`\`

前端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? negativeExample1Js : negativeExample1Ts}
\`\`\`

解释：

\`ctx.data.ttx\` 里的 \`ttx\` 和场景里的 id 一致，表示取用当前组件的值来生成组件的文本属性。但一般组件属性不会依赖自身的值，所以这种类型的代码是不合适的。

【正在编辑的前端表达式场景】

\`\`\`json
${JSON.stringify(scene, null, 2)}
\`\`\`

【输出的要求】

- 生成与【正在编辑的前端表达式场景】对应的前端表达式代码。
- 只输出前端表达式代码，即只有 ${upperTextJsOrTs} 代码，不需要额外的文本。
- 前端表达式代码的风格需要参考【常见的前端表达式举例】和【当前模块内正在使用的前端表达式】。
- 不要生成跟【不合适的前端表达式举例】风格类似的代码，除非【当前模块内正在使用的前端表达式】里有相同风格的前端表达式。
`
}
