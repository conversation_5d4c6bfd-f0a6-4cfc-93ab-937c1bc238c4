import { BackExpressionType, isEqualIgnoreUndefined } from '@ad/canal-shared'
import { E2ERemoteComponentMaterialSchema } from '@ad/e2e-material-schema'
import { E2EAPI, E2ESchema } from '@ad/e2e-schema'
import { isEqual } from 'lodash'
import { BackExpressionScene } from '../../types'
import { simplifySchema } from '../../utils'
import {
  example10Js,
  example10Ts,
  example1Js,
  example1Ts,
  example2Js,
  example2Ts,
  example3Js,
  example3Ts,
  example4Js,
  example4Ts,
  example5Js,
  example5Ts,
  example6Js,
  example6Ts,
  example7Js,
  example7Ts,
  example8Js,
  example8Ts,
  example9Js,
  example9Ts,
} from './examples'
import { genBackExprCodesBySchema } from './utils'

/**
 * 创建后端表达式代码提示
 */
export interface CreateBackExprCodePromptOptions {
  /**
   * 表达式类型
   */
  expressionType: BackExpressionType.JAVASCRIPT | BackExpressionType.TYPESCRIPT
  /**
   * 场景
   */
  scene: BackExpressionScene
  /**
   * 上下文类型定义
   */
  ctxDts: string
  /**
   * 端到端 Schema
   */
  schema: E2ESchema
  /**
   * 获取组件物料 Schema
   * @param type 组件类型
   * @param version 版本
   */
  getComponentMaterialSchema(
    type: string,
    version?: string,
  ): E2ERemoteComponentMaterialSchema | null
  /**
   * 在组件里获取多个接口标签
   * @param componentId 组件 ID
   * @param apiIds 接口 ID
   */
  getApiLabelsInComponent(
    componentId: string | undefined,
    apiIds: string[],
  ): string[]
  /**
   * 通过接口获取数据源标签
   * @param api 接口
   */
  getDataSourceLabelByApi(api: E2EAPI): string
  /**
   * 保留当前场景，默认为：`true`
   */
  keepCurrentScene?: boolean
}

/**
 * 创建后端表达式代码提示
 * @param options 选项
 */
export function createBackExprCodePrompt({
  expressionType,
  scene,
  ctxDts,
  schema,
  getComponentMaterialSchema,
  getApiLabelsInComponent,
  getDataSourceLabelByApi,
  keepCurrentScene = true,
}: CreateBackExprCodePromptOptions): string {
  const isJs = expressionType === BackExpressionType.JAVASCRIPT
  const upperTextJsOrTs = isJs ? 'JS' : 'TS'
  const lowerTextJsOrTs = isJs ? 'js' : 'ts'
  let sceneCodes = [
    ...genBackExprCodesBySchema(
      expressionType,
      schema,
      getComponentMaterialSchema,
      getApiLabelsInComponent,
      getDataSourceLabelByApi,
    ),
  ]
  if (!keepCurrentScene) {
    sceneCodes = sceneCodes.filter(([s]) => !isEqualIgnoreUndefined(scene, s))
  }
  return `你是一个低代码应用的开发专家，请根据以下信息，输出用户正在编辑的后端表达式代码：

【名词解释】
模块：用户在低代码平台上开发的基础单位，类似于全代码开发中的文件。模块可能是一个页面，也可能是页面中的某一个独立的部分。
Schema：模块内容的一种 JSON 表示。用户不会感知到 Schema 的内容及其结构，用户是通过一个可视化的网页编辑器间接编辑 Schema 的。
表达式：基于 JSON 编写的数据计算逻辑，可以兼容各种类型的计算方式。
后端表达式：在后端运行时里运行的 ${upperTextJsOrTs} 代码，以 ${upperTextJsOrTs} 代码字符串的方式在 JSON 里描述数据计算逻辑。后端表达式需要在 default 字段导出一个函数，函数有一个上下文参数。
后端表达式场景：后端表达式常用于根据请求参数计算业务接口的参数，或者根据接口返回值计算组件属性等场景。接下来我们会用 JSON 来结构化后端表达式场景的描述方式。
后端运行时：在后端服务中读取并运行 Schema 的功能，会计算后端表达式，并将计算结果填入 Schema 后返回给前端。后端运行时目前只向后端表达式提供了一个 npm 包：big.js。

【后端表达式上下文参数类型】
TS 类型（提供给 monaco-editor 使用的定义文件）：

\`\`\`ts
${ctxDts}
\`\`\`

【后端表达式场景结构化】
TS 类型：

\`\`\`ts
/**
 * 后端表达式场景
 */
type BackExpressionScene =
  | BackExpressionSceneProp
  | BackExpressionSceneApiArgs
  | BackExpressionSceneApiIf
  | BackExpressionSceneIife

/**
 * 后端表达式属性场景。
 * 该场景下，表达式的返回值会被用作组件的属性。
 * 但需要注意的是，value 属性是特殊的，前端也会维护一份组件的值，如果后端运行时返回一个非 null 的值，则会覆盖前端维护的组件值，返回 null 则会被忽略。
 */
interface BackExpressionSceneProp {
  /**
   * 类型
   */
  type: 'prop'
  /**
   * 表达式所属的组件
   */
  component: SimplifyComponent
  /**
   * 属性路径
   */
  propPath: string[]
  /**
   * 属性名称
   */
  propName: string
  /**
   * 接口，这里给出的是展示给用户看的拼接字符串。
   * 存在一个接口时，表达式通常主要从 ctx.response 里获取数据。
   * 存在多个接口时，表达式通常主要从 ctx.responses 里获取数据。
   * 不存在接口时，表达式不会从 ctx.response 或 ctx.responses 里获取数据，主要从上下文的其他字段获取数据，主要是 ctx.request，当然也有可能是 ctx.loop、ctx.model、ctx.globalModel 等。
   */
  apis: string[]
}

/**
 * 后端表达式接口参数场景。
 * 该场景下，表达式的返回值会被用作业务接口的参数。
 */
interface BackExpressionSceneApiArgs {
  /**
   * 类型
   */
  type: 'apiArgs'
  /**
   * 表达式所属的组件，也就是当前接口所属的组件，没有的时候表示该接口是模块级的接口
   */
  component?: SimplifyComponent
  /**
   * 数据源，这里给出的是展示给用户看的拼接字符串
   */
  dataSource: string
}

/**
 * 后端表达式接口执行条件场景。
 * 该场景下，表达式返回 true 时，会发起业务接口的请求，表达式返回 false 时，则不会发起业务接口的请求。
 * 不发起业务接口的请求，业务接口的响应为 null。
 */
interface BackExpressionSceneApiIf {
  /**
   * 类型
   */
  type: 'apiIf'
  /**
   * 表达式所属的组件，也就是当前接口所属的组件，没有的时候表示该接口是模块级的接口
   */
  component?: SimplifyComponent
  /**
   * 数据源，这里给出的是展示给用户看的拼接字符串
   */
  dataSource: string
}

/**
 * 后端表达式前端动作场景。
 * 该场景下，表达式的返回值需要为 \`number | number[]\`，会被当做动作的下标，用来选择需要执行的动作。
 * 另外表达式里除了需要通过 \`default\` 字段导出一个函数外，还需要在 \`output\` 字段导出一个对象，供动作按照键值取用。
 * 最终产生的动作，会在下发到前端后立即执行。
 */
interface BackExpressionSceneIife {
  /**
   * 类型
   */
  type: 'iife'
  /**
   * 接口，同 \`BackExpressionSceneProp['apis']\`
   */
  apis: string[]
  /**
   * 动作，这里只给出动作的描述。
   */
  actions: string[]
}
\`\`\`

【当前简化的 Schema】
TS 类型：

\`\`\`ts
/**
 * 下方 JSON 根对象类型
 */
type JsonRootOfCurrentSchema = SimplifySchema

/**
 * 简化的 Schema
 */
interface SimplifySchema {
  /**
   * 打平的视图
   */
  flattenedView: SimplifyFlattenedView
}

/**
 * 简化的打平的视图
 */
interface SimplifyFlattenedView {
  /**
   * 根组件 ID
   */
  rootComponentId: string
  /**
   * 所有组件
   */
  components: SimplifyComponent[]
  /**
   * 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
   */
  childComponentIdMap: Record<
    string,
    (string | SimplifyChildComponentIdAndPath)[]
  >
}

/**
 * 简化的组件实例
 */
interface SimplifyComponent {
  /**
   * 组件实例 ID
   */
  id: string
  /**
   * 组件类型
   */
  type: string
  /**
   * 组件实例名称
   */
  name: string
}

/**
 * 简化的子组件 ID 以及路径
 */
interface SimplifyChildComponentIdAndPath {
  /**
   * 子组件 ID
   */
  id: string
  /**
   * 子组件相对路径，默认按顺序添加到 children
   */
  path?: SimplifyPropertyPath
}

/**
 * 简化的属性路径
 *
 * 可以通过 lodash 的 get 函数获取 JSON 里的值
 */
type SimplifyPropertyPath = (string | number)[]
\`\`\`

JSON：

\`\`\`json
${JSON.stringify(simplifySchema(schema), null, 2)}
\`\`\`

【常见的后端表达式举例】

- 例子 1

后端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_u19jiTR8zo_5fddPM0E27",
    "type": "@ad/canal-biz-components::34",
    "name": "客户360指标卡组::E27"
  },
  "propPath": ["data"],
  "propName": "指标卡组数据",
  "apis": [
    "[组件] 天枢DataQuery https://ad-diag.corp.kuaishou.com/rest/dt/data/query"
  ]
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example1Js : example1Ts}
\`\`\`

- 例子 2

后端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_wnGU4GUmdS345YswuzrQq",
    "type": "@ad/canal-biz-components::7",
    "name": "指标卡组::rQq"
  },
  "propPath": ["allSelectorTitles"],
  "propName": "指标配置信息",
  "apis": [
    "[模块] 天枢ChartQuery https://ad-diag.corp.kuaishou.com/rest/dt/chart/query"
  ]
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example2Js : example2Ts}
\`\`\`

- 例子 3

后端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_rPoLABQT$NDeNGweoG71X",
    "type": "@ad/canal-biz-components::276",
    "name": "安全下载按钮::71X"
  },
  "propPath": ["request"],
  "propName": "下载入参",
  "apis": []
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example3Js : example3Ts}
\`\`\`

- 例子 4

后端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_eyS1JWBVWI2ynm9nP2MCB",
    "type": "@ad/canal-biz-components::32",
    "name": "明细表::MCB"
  },
  "propPath": ["tableData"],
  "propName": "表格数据",
  "apis": ["[模块] 转化目标", "[模块] 转化目标-汇总"]
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example4Js : example4Ts}
\`\`\`

- 例子 5

后端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_9tmkDHVZtpFn2m72rWxru",
    "type": "@ad/canal-biz-components::99",
    "name": "表格"
  },
  "propPath": ["columns"],
  "propName": "列设置",
  "apis": []
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example5Js : example5Ts}
\`\`\`

- 例子 6

后端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "selector",
    "type": "@ad/canal-biz-components::77",
    "name": "筛选组::5Ew"
  },
  "propPath": ["value"],
  "propName": "组件值",
  "apis": []
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example6Js : example6Ts}
\`\`\`

- 例子 7

后端表达式场景：

\`\`\`json
{
  "type": "prop",
  "component": {
    "id": "comp_LWkytn8gDP4MaT4ZI5UaQ",
    "type": "@ad/canal-biz-components::158",
    "name": "本地业务中心周报进度卡::UaQ"
  },
  "propPath": ["style", "margin"],
  "propName": "外边距",
  "apis": []
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example7Js : example7Ts}
\`\`\`

- 例子 8

后端表达式场景：

\`\`\`json
{
  "type": "apiArgs",
  "component": {
    "id": "comp_uadnXgaRyO_Xix0U7RX$q",
    "type": "@ad/canal-biz-components::3",
    "name": "趋势图::X$q"
  },
  "dataSource": "https://ad-diag.corp.kuaishou.com/rest/dt/data/query"
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example8Js : example8Ts}
\`\`\`

- 例子 9

后端表达式场景：

\`\`\`json
{
  "type": "apiIf",
  "component": {
    "id": "table_column",
    "type": "@ad/canal-biz-components::51",
    "name": "提报表格::EbC"
  },
  "dataSource": "【POST】品牌服务商-客户提报列表-https://agent.e.kuaishou.com/rest/dsp/agent/brand/report/list"
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example9Js : example9Ts}
\`\`\`

- 例子 10

后端表达式场景：

\`\`\`json
{
  "type": "iife",
  "apis": [
    "[模块] 人群报表-获取数据分布 https://ad.e.kuaishou.com/rest/dsp/portal/report/popChartReports",
    "[模块] 人群报表-获取数据分布 https://ad.e.kuaishou.com/rest/dsp/portal/report/popChartReports"
  ],
  "actions": ["执行外部函数：apiFetch209，参数：无"]
}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${isJs ? example10Js : example10Ts}
\`\`\`

【当前模块内正在使用的后端表达式】

${
  sceneCodes.length
    ? sceneCodes
        .map(
          ([s, code], index) => `- 例子 ${index + 1}

后端表达式场景：

\`\`\`json
${JSON.stringify(s, null, 2)}
\`\`\`

后端表达式代码：

\`\`\`${lowerTextJsOrTs}
${code}
\`\`\`
${
  isEqual(scene, s)
    ? `
解释：

这就是当前正在编辑的后端表达式代码，请勿原样输出，根据实际情况，酌情修改这份代码后输出。也请不要敷衍地只修改一些空格、换行，至少修改一些注释，最好能直接针对代码的逻辑进行修改。
`
    : ''
}`,
        )
        .join('\n')
    : '无\n'
}
【不合适的后端表达式举例】

无

【正在编辑的后端表达式场景】

\`\`\`json
${JSON.stringify(scene, null, 2)}
\`\`\`

【输出的要求】

- 生成与【正在编辑的后端表达式场景】对应的后端表达式代码。
- 只输出后端表达式代码，即只有 ${upperTextJsOrTs} 代码，不需要额外的文本。
- 后端表达式代码的风格需要参考【常见的后端表达式举例】和【当前模块内正在使用的后端表达式】。
- 不要生成跟【不合适的后端表达式举例】风格类似的代码，除非【当前模块内正在使用的后端表达式】里有相同风格的后端表达式。
`
}
