export const example1Js = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return ctx.response.data
}`

export const example1Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  return ctx.response.data
}`

export const example2Js = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return ctx.response.data?.titles || []
}`

export const example2Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  return ctx.response.data?.titles || []
}`

export const example3Js = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  export const selector =
    ctx.request.params?.['comp_R_kBEeumwueqDj4vdlKLt']?.selector || []
  return {
    body: {
      chartId: 1429,
      conditions: [
        { fieldName: 'user_id', fieldValue: ctx.request.params?.userId },
        { fieldName: 'sale_scene', fieldValue: ctx.request.params?.saleScene },
        { fieldName: 'tfc_type', fieldValue: ctx.request.params?.tfcType },
        { fieldName: 'start_time', fieldValue: ctx.request.params?.startTime },
        { fieldName: 'end_time', fieldValue: ctx.request.params?.endTime },
        {
          fieldName: 'cmp_start_time',
          fieldValue: ctx.request.params?.cmpStartTime,
        },
        {
          fieldName: 'cmp_end_time',
          fieldValue: ctx.request.params?.cmpEndTime,
        },
      ],
      granularity: 'day',
      limit: 10,
      offset: 0,
      selector,
    },
  }
}`

export const example3Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  export const selector =
    ctx.request.params?.['comp_R_kBEeumwueqDj4vdlKLt']?.selector || []
  return {
    body: {
      chartId: 1429,
      conditions: [
        { fieldName: 'user_id', fieldValue: ctx.request.params?.userId },
        { fieldName: 'sale_scene', fieldValue: ctx.request.params?.saleScene },
        { fieldName: 'tfc_type', fieldValue: ctx.request.params?.tfcType },
        { fieldName: 'start_time', fieldValue: ctx.request.params?.startTime },
        { fieldName: 'end_time', fieldValue: ctx.request.params?.endTime },
        {
          fieldName: 'cmp_start_time',
          fieldValue: ctx.request.params?.cmpStartTime,
        },
        {
          fieldName: 'cmp_end_time',
          fieldValue: ctx.request.params?.cmpEndTime,
        },
      ],
      granularity: 'day',
      limit: 10,
      offset: 0,
      selector,
    },
  }
}`

export const example4Js = `function groupBy(array, keyFn) {
  return array.reduce((result, item) => {
    // 根据 keyFn 计算出元素的 key
    export const key = typeof keyFn === 'function' ? keyFn(item) : item[keyFn]

    // 如果 result 中不存在该 key，则初始化为一个空数组
    if (!result[key]) {
      result[key] = []
    }

    // 将元素加入到对应 key 的数组中
    result[key].push(item)

    return result
  }, {})
}

/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  let respData = ctx.response?.data
  export const respRecords = respData?.records || []
  export const groupByData = groupBy(respRecords, 'action_type')
  console.log('ctx.response', ctx.response)
  console.log('groupByData', groupByData)
  export const tableData = Object.keys(groupByData).map((key) => {
    export const list = groupByData[key]
    export const targetItem = {
      action_type: key,
      action_type_name: groupByData[key][0].action_type_name || '-',
    }
    list.forEach((item) => {
      switch (item.interval) {
        case '0h-2h':
          targetItem['0h-2h_num'] = item.callback_num
          targetItem['0h-2h_rate'] = item.callback_rate
          targetItem['0h-2h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '2h-12h':
          targetItem['2h-12h_num'] = item.callback_num
          targetItem['2h-12h_rate'] = item.callback_rate
          targetItem['2h-12h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '12h-24h':
          targetItem['12h-24h_num'] = item.callback_num
          targetItem['12h-24h_rate'] = item.callback_rate
          targetItem['12h-24h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '24h-48h':
          targetItem['24h-48h_num'] = item.callback_num
          targetItem['24h-48h_rate'] = item.callback_rate
          targetItem['24h-48h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '48h+':
          targetItem['48h+_num'] = item.callback_num
          targetItem['48h+_rate'] = item.callback_rate
          targetItem['48h+_rate_link_diff'] = item.callback_rate_link_diff
          break
        default:
          break
      }
    })
    return targetItem
  })
  console.log('tableData', tableData)

  let tableTotalData = {
    action_type_name: '汇总',
  }
  ctx?.responses?.forEach((item, index) => {
    if (index === 1) {
      export const records = item?.data?.records || []
      console.log('汇总数据', records)
      records?.forEach((item) => {
        switch (item.interval) {
          case '0h-2h':
            tableTotalData['0h-2h_num'] = item.callback_num
            tableTotalData['0h-2h_rate'] = item.callback_rate
            tableTotalData['0h-2h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '2h-12h':
            tableTotalData['2h-12h_num'] = item.callback_num
            tableTotalData['2h-12h_rate'] = item.callback_rate
            tableTotalData['2h-12h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '12h-24h':
            tableTotalData['12h-24h_num'] = item.callback_num
            tableTotalData['12h-24h_rate'] = item.callback_rate
            tableTotalData['12h-24h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '24h-48h':
            tableTotalData['24h-48h_num'] = item.callback_num
            tableTotalData['24h-48h_rate'] = item.callback_rate
            tableTotalData['24h-48h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '48h+':
            tableTotalData['48h+_num'] = item.callback_num
            tableTotalData['48h+_rate'] = item.callback_rate
            tableTotalData['48h+_rate_link_diff'] = item.callback_rate_link_diff
            break
          default:
            break
        }
      })
    }
  })

  export const records = [tableTotalData, ...tableData]
  console.log('tableTotalData', records)
  return {
    records,
    totalCount: records?.length,
  }
}`

export const example4Ts = `function groupBy(array, keyFn) {
  return array.reduce((result, item) => {
    // 根据 keyFn 计算出元素的 key
    export const key = typeof keyFn === 'function' ? keyFn(item) : item[keyFn]

    // 如果 result 中不存在该 key，则初始化为一个空数组
    if (!result[key]) {
      result[key] = []
    }

    // 将元素加入到对应 key 的数组中
    result[key].push(item)

    return result
  }, {})
}

/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  let respData = ctx.response?.data
  export const respRecords = respData?.records || []
  export const groupByData = groupBy(respRecords, 'action_type')
  console.log('ctx.response', ctx.response)
  console.log('groupByData', groupByData)
  export const tableData = Object.keys(groupByData).map((key) => {
    export const list = groupByData[key]
    export const targetItem = {
      action_type: key,
      action_type_name: groupByData[key][0].action_type_name || '-',
    }
    list.forEach((item) => {
      switch (item.interval) {
        case '0h-2h':
          targetItem['0h-2h_num'] = item.callback_num
          targetItem['0h-2h_rate'] = item.callback_rate
          targetItem['0h-2h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '2h-12h':
          targetItem['2h-12h_num'] = item.callback_num
          targetItem['2h-12h_rate'] = item.callback_rate
          targetItem['2h-12h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '12h-24h':
          targetItem['12h-24h_num'] = item.callback_num
          targetItem['12h-24h_rate'] = item.callback_rate
          targetItem['12h-24h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '24h-48h':
          targetItem['24h-48h_num'] = item.callback_num
          targetItem['24h-48h_rate'] = item.callback_rate
          targetItem['24h-48h_rate_link_diff'] = item.callback_rate_link_diff
          break
        case '48h+':
          targetItem['48h+_num'] = item.callback_num
          targetItem['48h+_rate'] = item.callback_rate
          targetItem['48h+_rate_link_diff'] = item.callback_rate_link_diff
          break
        default:
          break
      }
    })
    return targetItem
  })
  console.log('tableData', tableData)

  let tableTotalData = {
    action_type_name: '汇总',
  }
  ctx?.responses?.forEach((item, index) => {
    if (index === 1) {
      export const records = item?.data?.records || []
      console.log('汇总数据', records)
      records?.forEach((item) => {
        switch (item.interval) {
          case '0h-2h':
            tableTotalData['0h-2h_num'] = item.callback_num
            tableTotalData['0h-2h_rate'] = item.callback_rate
            tableTotalData['0h-2h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '2h-12h':
            tableTotalData['2h-12h_num'] = item.callback_num
            tableTotalData['2h-12h_rate'] = item.callback_rate
            tableTotalData['2h-12h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '12h-24h':
            tableTotalData['12h-24h_num'] = item.callback_num
            tableTotalData['12h-24h_rate'] = item.callback_rate
            tableTotalData['12h-24h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '24h-48h':
            tableTotalData['24h-48h_num'] = item.callback_num
            tableTotalData['24h-48h_rate'] = item.callback_rate
            tableTotalData['24h-48h_rate_link_diff'] =
              item.callback_rate_link_diff
            break
          case '48h+':
            tableTotalData['48h+_num'] = item.callback_num
            tableTotalData['48h+_rate'] = item.callback_rate
            tableTotalData['48h+_rate_link_diff'] = item.callback_rate_link_diff
            break
          default:
            break
        }
      })
    }
  })

  export const records = [tableTotalData, ...tableData]
  console.log('tableTotalData', records)
  return {
    records,
    totalCount: records?.length,
  }
}`

export const example5Js = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  export const selectedColumns = ctx.request.params?.columns || []
  export const adType = ctx.request.params.adType
  let leftFixedColumn = {
    dataIndex: 'campaign',
    title: '广告计划',
    width: 220,
    fixed: 'left',
    format: 'nameWithId',
  }
  switch (adType) {
    case '3':
      leftFixedColumn.dataIndex = 'unit'
      leftFixedColumn.title = '广告组'
      break
    case '4':
      leftFixedColumn.dataIndex = 'creative'
      leftFixedColumn.title = '广告创意'
      break
    default:
      break
  }
  return [leftFixedColumn, ...selectedColumns]
}`

export const example5Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  export const selectedColumns = ctx.request.params?.columns || []
  export const adType = ctx.request.params.adType
  let leftFixedColumn = {
    dataIndex: 'campaign',
    title: '广告计划',
    width: 220,
    fixed: 'left',
    format: 'nameWithId',
  }
  switch (adType) {
    case '3':
      leftFixedColumn.dataIndex = 'unit'
      leftFixedColumn.title = '广告组'
      break
    case '4':
      leftFixedColumn.dataIndex = 'creative'
      leftFixedColumn.title = '广告创意'
      break
    default:
      break
  }
  return [leftFixedColumn, ...selectedColumns]
}`

export const example6Js = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  export const { defaultStartTime, defaultEndTime, chartFieldsList } =
    ctx.request.params || {}
  if (ctx.request.refreshType !== 'default') return null

  console.log(
    'defaultStartTime, defaultEndTime',
    defaultStartTime,
    defaultEndTime,
  )

  return {
    popChartFields: chartFieldsList?.[0]?.id
      ? [chartFieldsList?.[0]?.id]
      : undefined,
    time: [defaultStartTime, defaultEndTime],
  }
}`

export const example6Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  export const { defaultStartTime, defaultEndTime, chartFieldsList } =
    ctx.request.params || {}
  if (ctx.request.refreshType !== 'default') return null

  console.log(
    'defaultStartTime, defaultEndTime',
    defaultStartTime,
    defaultEndTime,
  )

  return {
    popChartFields: chartFieldsList?.[0]?.id
      ? [chartFieldsList?.[0]?.id]
      : undefined,
    time: [defaultStartTime, defaultEndTime],
  }
}`

export const example7Js = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  export const index = ctx.loop.index

  function calculateMargin(index) {
    export const horizontalGap = '16px' // 水平间距
    export const verticalGap = '16px' // 垂直间距

    let marginTop = 0
    let marginBottom = 0
    let marginLeft = 0
    let marginRight = 0

    // 计算水平间距
    if (index % 3 !== 2) {
      // 如果不是每行的最后一个元素
      marginRight = horizontalGap
    }

    // 计算垂直间距
    if (index >= 3) {
      // 如果不是第一行的元素
      marginTop = verticalGap
    }

    return \`\${marginTop} \${marginRight} \${marginBottom} \${marginLeft}\`
  }

  return calculateMargin(index)
}`

export const example7Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  export const index = ctx.loop.index

  function calculateMargin(index) {
    export const horizontalGap = '16px' // 水平间距
    export const verticalGap = '16px' // 垂直间距

    let marginTop = 0
    let marginBottom = 0
    let marginLeft = 0
    let marginRight = 0

    // 计算水平间距
    if (index % 3 !== 2) {
      // 如果不是每行的最后一个元素
      marginRight = horizontalGap
    }

    // 计算垂直间距
    if (index >= 3) {
      // 如果不是第一行的元素
      marginTop = verticalGap
    }

    return \`\${marginTop} \${marginRight} \${marginBottom} \${marginLeft}\`
  }

  return calculateMargin(index)
}`

export const example8Js = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  export const selectedCard =
    ctx.request.params?.comp_Hu6XzvgbOdeguC_4X8Df9 || 'cost_total'
  export const selector = ['p_date', selectedCard]
  export const filterList = [
    'cost_total',
    'ad_show',
    'live_gmv',
    'ive_avg_order_price',
    'live_unrisk_gmv',
    'live_avg_order_price',
    'photo_unrisk_order_gmv_1d',
    'photo_gmv',
    'total_roi',
  ]

  if (filterList.indexOf(selectedCard) === -1) {
    export const suffix =
      ctx.request.params?.compareValue === 1
        ? '_category'
        : '_category_excellent'
    selector.push(\`\${selectedCard}\${suffix}\`)
  }

  return {
    chartId: 620,
    conditions: [
      { fieldName: 'start_time', fieldValue: ctx.request.params?.startTime },
      { fieldName: 'end_time', fieldValue: ctx.request.params?.endTime },
      { fieldName: 'user_id', fieldValue: ctx.request.params?.userId },
    ],
    granularity: 'day',
    limit: 500,
    offset: 0,
    selector: selector,
  }
}`

export const example8Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  export const selectedCard =
    ctx.request.params?.comp_Hu6XzvgbOdeguC_4X8Df9 || 'cost_total'
  export const selector = ['p_date', selectedCard]
  export const filterList = [
    'cost_total',
    'ad_show',
    'live_gmv',
    'ive_avg_order_price',
    'live_unrisk_gmv',
    'live_avg_order_price',
    'photo_unrisk_order_gmv_1d',
    'photo_gmv',
    'total_roi',
  ]

  if (filterList.indexOf(selectedCard) === -1) {
    export const suffix =
      ctx.request.params?.compareValue === 1
        ? '_category'
        : '_category_excellent'
    selector.push(\`\${selectedCard}\${suffix}\`)
  }

  return {
    chartId: 620,
    conditions: [
      { fieldName: 'start_time', fieldValue: ctx.request.params?.startTime },
      { fieldName: 'end_time', fieldValue: ctx.request.params?.endTime },
      { fieldName: 'user_id', fieldValue: ctx.request.params?.userId },
    ],
    granularity: 'day',
    limit: 500,
    offset: 0,
    selector: selector,
  }
}`

export const example9Js = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  export const params = ctx.request.params.filter_form ?? {}
  if (params?.reportId && isNaN(Number(params.reportId))) {
    return false
  }
  if (params?.sellerUserId && isNaN(Number(params.sellerUserId))) {
    return false
  }
  return true
}`

export const example9Ts = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  export const params = ctx.request.params.filter_form ?? {}
  if (params?.reportId && isNaN(Number(params.reportId))) {
    return false
  }
  if (params?.sellerUserId && isNaN(Number(params.sellerUserId))) {
    return false
  }
  return true
}`

export const example10Js = `/**
 * @param {Ctx} ctx 上下文
 * @returns {number | number[]} 动作编号
 */
export default (ctx) => {
  const has209 = (ctx.responses || []).find((res) => res.result === 209)
  if (has209) {
    return [0]
  }
  return []
}

/**
 * 输出，用于动作列表里按键值取用，可以在上面函数里修改
 */
export const output = {}`

export const example10Ts = `/**
 * @param ctx 上下文
 * @returns 动作编号
 */
export default (ctx: Ctx): number | number[] => {
  const has209 = (ctx.responses || []).find((res) => res.result === 209)
  if (has209) {
    return [0]
  }
  return []
}

/**
 * 输出，用于动作列表里按键值取用，可以在上面函数里修改
 */
export const output: Record<string, any> = {}`
