import {
  arrayableToArray,
  BackExpressionType,
  getJsExpressionCodeWithType,
} from '@ad/canal-shared'
import { E2ECMSProp } from '@ad/e2e-material-schema'
import { E2EAPI, E2ESchema, E2ESchemaExpression } from '@ad/e2e-schema'
import {
  CanalRootComponentField,
  getBindIIFEActionLabel,
  getBindIIFEActionsFromCode,
  getComponentPropDetail,
  isApiExpression,
  isJsExpression,
} from '@ad/e2e-schema-utils'
import {
  dfsGenComponentDetailBySchema,
  type ComponentDetailOfSchema,
} from '@kael/schema-utils'
import { isEqual, pick } from 'lodash'
import { BackExpressionScene, BackExpressionSceneProp } from '../../types'
import { SimplifyComponent } from '../../utils'
import { CreateBackExprCodePromptOptions } from './prompt'

/**
 * 根据 Schema 生成后端表达式代码
 * @param expressionType 表达式类型
 * @param schema 端到端 Schema
 * @param getComponentMaterialSchema 获取组件物料 Schema
 * @param getApiLabelsInComponent 在组件里获取接口标签
 * @param getDataSourceLabelByApi 通过接口获取数据源标签
 */
export function* genBackExprCodesBySchema(
  expressionType: BackExpressionType.JAVASCRIPT | BackExpressionType.TYPESCRIPT,
  schema: E2ESchema,
  getComponentMaterialSchema: CreateBackExprCodePromptOptions['getComponentMaterialSchema'],
  getApiLabelsInComponent: CreateBackExprCodePromptOptions['getApiLabelsInComponent'],
  getDataSourceLabelByApi: CreateBackExprCodePromptOptions['getDataSourceLabelByApi'],
): Generator<[BackExpressionScene, string]> {
  for (const api of schema.apis || []) {
    yield* genBackExprCodesByApi(
      expressionType,
      api,
      undefined,
      getDataSourceLabelByApi,
    )
  }
  if (isApiExpression(schema.iife) && isJsExpression(schema.iife.transform)) {
    const code = getJsExpressionCodeWithType(
      expressionType,
      schema.iife.transform,
    )
    if (code) {
      const actions = getBindIIFEActionsFromCode(schema.iife.transform.code)
      yield [
        {
          type: 'iife',
          apis: getApiLabelsInComponent(
            undefined,
            arrayableToArray(schema.iife.apiId),
          ),
          actions: actions.map(getBindIIFEActionLabel),
        },
        code,
      ]
    }
  }

  for (const cd of dfsGenComponentDetailBySchema(schema)) {
    yield* genBackExprCodesBySchemaComponent(
      expressionType,
      schema,
      cd,
      getComponentMaterialSchema,
      getApiLabelsInComponent,
      getDataSourceLabelByApi,
    )
  }
}

/**
 * 根据组件 Schema 生成后端表达式代码
 * @param expressionType 表达式类型
 * @param schema 端到端 Schema
 * @param cd 组件详情
 * @param getComponentMaterialSchema 获取组件物料 Schema
 * @param getApiLabelsInComponent 在组件里获取接口标签
 * @param getDataSourceLabelByApi 通过接口获取数据源标签
 */
export function* genBackExprCodesBySchemaComponent(
  expressionType: BackExpressionType.JAVASCRIPT | BackExpressionType.TYPESCRIPT,
  schema: E2ESchema,
  cd: ComponentDetailOfSchema<E2ESchema>,
  getComponentMaterialSchema: CreateBackExprCodePromptOptions['getComponentMaterialSchema'],
  getApiLabelsInComponent: CreateBackExprCodePromptOptions['getApiLabelsInComponent'],
  getDataSourceLabelByApi: CreateBackExprCodePromptOptions['getDataSourceLabelByApi'],
): Generator<[BackExpressionScene, string]> {
  const { component } = cd
  const isRoot = component.id === schema.view.id
  const simplifyComponent: SimplifyComponent = pick(
    component,
    'id',
    'type',
    'name',
  )
  for (const api of component.apis || []) {
    yield* genBackExprCodesByApi(
      expressionType,
      api,
      isRoot ? undefined : simplifyComponent,
      getDataSourceLabelByApi,
    )
  }
  const materialSchema = getComponentMaterialSchema(
    component.type,
    schema.componentCodes?.[component.type]?.version,
  )
  if (!materialSchema) return
  for (const prop of materialSchema.props || []) {
    yield* searchProp(prop, [])
  }
  if (isRoot) {
    const propPath = [CanalRootComponentField.BACK_DATA]
    const backDataExpressionDetail = getComponentPropDetail(component, propPath)
    yield* searchPropExpression(
      {
        type: 'prop',
        component: simplifyComponent,
        propPath,
        propName: '数据',
      },
      backDataExpressionDetail.expression,
    )
  } else {
    const propPath = ['value']
    const valueExpressionDetail = getComponentPropDetail(component, propPath)
    yield* searchPropExpression(
      {
        type: 'prop',
        component: simplifyComponent,
        propPath,
        propName: '值',
      },
      valueExpressionDetail.expression,
    )
  }

  function* searchProp(
    prop: E2ECMSProp,
    parentPropPath: string[],
  ): Generator<[BackExpressionScene, string]> {
    const propPath = [...parentPropPath, ...(prop.path || [])]
    if (prop.type === 'group') {
      for (const item of prop.items) {
        yield* searchProp(item, propPath)
      }
    } else {
      if (isEqual(propPath, ['value']) || isEqual(propPath, ['onChange'])) {
        return
      }
      const propDetail = getComponentPropDetail(component, propPath)
      yield* searchPropExpression(
        {
          type: 'prop',
          component: simplifyComponent,
          propPath,
          propName: `${prop.name}`,
        },
        propDetail.expression,
      )
    }
  }

  function* searchPropExpression(
    scene: Omit<BackExpressionSceneProp, 'apis'>,
    expression?: E2ESchemaExpression,
  ): Generator<[BackExpressionScene, string]> {
    if (!isApiExpression(expression)) {
      return
    }
    const code = getJsExpressionCodeWithType(
      expressionType,
      expression.transform,
    )
    if (!code) return
    yield [
      {
        ...scene,
        apis: getApiLabelsInComponent(
          component.id,
          arrayableToArray(expression.apiId),
        ),
      },
      code,
    ]
  }
}

/**
 * 根据接口生成后端表达式代码
 * @param expressionType 表达式类型
 * @param api 接口
 * @param component 简化组件
 * @param getDataSourceLabelByApi 通过接口获取数据源标签
 */
export function* genBackExprCodesByApi(
  expressionType: BackExpressionType.JAVASCRIPT | BackExpressionType.TYPESCRIPT,
  api: E2EAPI,
  component: SimplifyComponent | undefined,
  getDataSourceLabelByApi: CreateBackExprCodePromptOptions['getDataSourceLabelByApi'],
): Generator<[BackExpressionScene, string]> {
  for (const argExpression of api.args || []) {
    const code = getJsExpressionCodeWithType(expressionType, argExpression)
    if (code) {
      yield [
        {
          type: 'apiArgs',
          component,
          dataSource: getDataSourceLabelByApi(api),
        },
        code,
      ]
    }
  }
  if (api.if) {
    const code = getJsExpressionCodeWithType(expressionType, api.if)
    if (code) {
      yield [
        {
          type: 'apiIf',
          component,
          dataSource: getDataSourceLabelByApi(api),
        },
        code,
      ]
    }
  }
}
