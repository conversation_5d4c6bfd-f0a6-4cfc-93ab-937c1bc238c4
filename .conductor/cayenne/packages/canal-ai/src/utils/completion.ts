/**
 * JS 输出开始
 */
const JS_COMPLETION_START = '```js'

/**
 * TS 输出开始
 */
const TS_COMPLETION_START = '```ts'

/**
 * javascript 输出开始
 */
const JAVASCRIPT_COMPLETION_START = '```javascript'

/**
 * typescript 输出开始
 */
const TYPESCRIPT_COMPLETION_START = '```typescript'

/**
 * JS/TS 输出结束
 */
const JS_TS_COMPLETION_END = '```'

/**
 * 从输出里获取代码
 * @param completion 输出
 */
export function getCodeFromCompletion(completion: string): string {
  const str = completion.trim()
  let startIndex = 0
  if (
    str.startsWith(JS_COMPLETION_START) ||
    str.startsWith(TS_COMPLETION_START)
  ) {
    startIndex = JS_COMPLETION_START.length
  } else if (
    str.startsWith(JAVASCRIPT_COMPLETION_START) ||
    str.startsWith(TYPESCRIPT_COMPLETION_START)
  ) {
    startIndex = JAVASCRIPT_COMPLETION_START.length
  }
  if (startIndex && str.endsWith(JS_TS_COMPLETION_END)) {
    return str.slice(startIndex, -JS_TS_COMPLETION_END.length).trim()
  }
  return completion
}
