import { E2ESchema, E2EServerSchema } from '@ad/e2e-schema'
import { f2bE2ESchema } from '@ad/e2e-schema-utils'
import { pick } from 'lodash'

/**
 * 简化 Schema
 * @param schema 端到端 Schema
 */
export function simplifySchema(schema: E2ESchema): SimplifySchema {
  const fullSchema: E2EServerSchema = f2bE2ESchema(schema)
  return {
    flattenedView: {
      ...fullSchema.flattenedView,
      components: fullSchema.flattenedView.components.map((comp) =>
        pick(comp, 'id', 'type', 'name'),
      ),
    },
  }
}

/**
 * 简化的 Schema
 */
export interface SimplifySchema {
  /**
   * 打平的视图
   */
  flattenedView: SimplifyFlattenedView
}

/**
 * 简化的打平的视图
 */
export interface SimplifyFlattenedView {
  /**
   * 根组件 ID
   */
  rootComponentId: string
  /**
   * 所有组件
   */
  components: SimplifyComponent[]
  /**
   * 子组件 ID 对照表，父组件 ID -> 子组件 ID 列表
   */
  childComponentIdMap: Record<
    string,
    (string | SimplifyChildComponentIdAndPath)[]
  >
}

/**
 * 简化的组件实例
 */
export interface SimplifyComponent {
  /**
   * 组件实例 ID
   */
  id: string
  /**
   * 组件类型
   */
  type: string
  /**
   * 组件实例名称
   */
  name: string
}

/**
 * 简化的子组件 ID 以及路径
 */
export interface SimplifyChildComponentIdAndPath {
  /**
   * 子组件 ID
   */
  id: string
  /**
   * 子组件相对路径，默认按顺序添加到 children
   */
  path?: SimplifyPropertyPath
}

/**
 * 简化的属性路径
 *
 * 可以通过 lodash 的 get 函数获取 JSON 里的值
 */
export type SimplifyPropertyPath = (string | number)[]
