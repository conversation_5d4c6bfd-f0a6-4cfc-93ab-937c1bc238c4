import {
  example1AJs,
  example1ATs,
  example1BJs,
  example1BTs,
  example2AJs,
  example2ATs,
  example2BJs,
  example2BTs,
  example3AJs,
  example3ATs,
  example3BJs,
  example3BTs,
  example4AJs,
  example4ATs,
  example4BJs,
  example4BTs,
} from './examples'

/**
 * 创建相似度提示
 * @param expressionType 表达式类型
 * @param targetCode 目标代码
 * @param predictCode 预测代码
 */
export function createSimilarityPrompt(
  expressionType: 'js' | 'ts',
  targetCode: string,
  predictCode: string,
): string {
  const isJs = expressionType === 'js'
  return `假如你是前端开发专家，请根据以下信息，判断两份代码在逻辑上的相似度：

【代码对举例】

- 例子 1

代码 A：

\`\`\`${expressionType}
${isJs ? example1AJs : example1ATs}
\`\`\`

代码 B：

\`\`\`${expressionType}
${isJs ? example1BJs : example1BTs}
\`\`\`

相似度分数：

1

解释：

\`ctx.response.data['entity'].title\` 和 \`ctx.response.data.entity['title']\` 在逻辑上是完全相同的，都是先获取 \`entity\` 对象，然后从中获取 \`title\` 属性，因此相似度分数为 1。

- 例子 2

代码 A：

\`\`\`${expressionType}
${isJs ? example2AJs : example2ATs}
\`\`\`

代码 B：

\`\`\`${expressionType}
${isJs ? example2BJs : example2BTs}
\`\`\`

相似度分数：

0.01

解释：

首先，函数里面的内容是完全不一样的。唯一一样的是，两个函数都只有一个参数 \`ctx\`，但需要注意的是，代码 A 里的 \`ctx\` 的类型是 \`Ctx\`，而代码 B 里的 \`ctx\` 的类型是 \`Container\`，意味着两者不是同一个东西，所以参数的相似性也很小。总的来说相似度分数为 0.01。

- 例子 3

代码 A：

\`\`\`${expressionType}
${isJs ? example3AJs : example3ATs}
\`\`\`

代码 B：

\`\`\`${expressionType}
${isJs ? example3BJs : example3BTs}
\`\`\`

相似度分数：

0.8

解释：

仅从文本上看的话，代码 A 和代码 B 的相似度是非常高的，只有注释里的一两个字不一样和代码里的调用的一个函数不一样：\`shift()\` 和 \`pop()\`。但实际上，\`shift()\` 和 \`pop()\` 的语义是完全相反的，前者是移除数组的第一个元素，而后者是移除数组的最后一个元素，注释里的信息也印证了这个区别。因此在这一点上需要多扣一点分数，这个“多扣”是指文本上因为不相似而大概扣 0.1 分左右的话，逻辑上完全相反要比这多扣一点，比如 0.2 分。因此相似度分数为 0.8。

- 例子 4

代码 A：

\`\`\`${expressionType}
${isJs ? example4AJs : example4ATs}
\`\`\`

代码 B：

\`\`\`${expressionType}
${isJs ? example4BJs : example4BTs}
\`\`\`

相似度分数：

0.2

解释：

代码 A 和 代码 B 的参数是完全一样的，但函数体里只有第一行日志输出代码是一样的，函数体的核心部分不一样。因此，我们认为这两份代码的主要部分是不一样的，但次要部分是一样的，所以相似度分数为 0.2。

【目标代码】
\`\`\`${expressionType}
${targetCode}
\`\`\`

【预测代码】
\`\`\`${expressionType}
${predictCode}
\`\`\`

【输出的要求】

- 对比【目标代码】和【预测代码】，请输出一个介于 0 到 1 之间的相似度分数，分数越高表示两份代码越相似。
- 代码相似，是指逻辑上的相似，而不是代码文本上的相似。文本相似的时候，逻辑肯定相似。文本不相似的时候，逻辑也可能相似。比如快速排序和归并排序的代码文本完全不同，但它们的逻辑是几乎完全相同的。快速排序和冒泡排序则相似度要稍微低一点，因为它们虽然功能一样，但性能不一样。
- 只输出一个数字，不要输出任何解释或描述，不需要任何其他的文本。
- 相似度分数的计算方式，可以参考【代码对举例】中的代码对。
`
}
