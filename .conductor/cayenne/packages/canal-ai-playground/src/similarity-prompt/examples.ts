export const example1AJs = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return ctx.response.data['entity'].title
}`

export const example1ATs = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  return ctx.response.data['entity'].title
}`

export const example1BJs = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return ctx.response.data.entity['title']
}`

export const example1BTs = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  return ctx.response.data.entity['title']
}`

export const example2AJs = `/**
 * @param {Ctx} ctx 上下文
 */
export default (ctx) => {
  return {
    // 模拟基础数据
    chartType: 'card',
    titles: [
      {
        fieldName: 'cash',
        displayName: '现金/前后返钱包',
        label: '现金/前后返钱包',
        columnTips: '现金/前后返钱包',
        propertyType: 'measure',
        columnType: 'measure',
        valueType: 'double'
      },
      {
        fieldName: 'credit',
        displayName: '信用钱包',
        label: '信用钱包',
        columnTips: '信用钱包',
        propertyType: 'measure',
        columnType: 'measure',
        valueType: 'int'
      },
      {
        fieldName: 'frame',
        displayName: '框返钱包',
        label: '框返钱包',
        columnTips: '框返钱包',
        propertyType: 'measure',
        columnType: 'measure',
        valueType: 'double'
      },
      {
        fieldName: 'incentive',
        displayName: '激励钱包',
        label: '激励钱包',
        columnTips: '激励钱包',
        propertyType: 'measure',
        columnType: 'measure',
        valueType: 'double'
      },
    ],
    records: [
      {
        // 主指标数据
        'frame': String(ctx.request.params?.frame) || '0',
        'cash': String(ctx.request.params?.cash) || '0',
        'credit': String(ctx.request.params?.credit) || '0',
        'incentive': String(ctx.request.params?.incentive) || '0'
      }
    ]
  }
}`

export const example2ATs = `/**
 * @param ctx 上下文
 */
export default (ctx: Ctx) => {
  return {
    // 模拟基础数据
    chartType: 'card',
    titles: [
      {
        fieldName: 'cash',
        displayName: '现金/前后返钱包',
        label: '现金/前后返钱包',
        columnTips: '现金/前后返钱包',
        propertyType: 'measure',
        columnType: 'measure',
        valueType: 'double'
      },
      {
        fieldName: 'credit',
        displayName: '信用钱包',
        label: '信用钱包',
        columnTips: '信用钱包',
        propertyType: 'measure',
        columnType: 'measure',
        valueType: 'int'
      },
      {
        fieldName: 'frame',
        displayName: '框返钱包',
        label: '框返钱包',
        columnTips: '框返钱包',
        propertyType: 'measure',
        columnType: 'measure',
        valueType: 'double'
      },
      {
        fieldName: 'incentive',
        displayName: '激励钱包',
        label: '激励钱包',
        columnTips: '激励钱包',
        propertyType: 'measure',
        columnType: 'measure',
        valueType: 'double'
      },
    ],
    records: [
      {
        // 主指标数据
        'frame': String(ctx.request.params?.frame) || '0',
        'cash': String(ctx.request.params?.cash) || '0',
        'credit': String(ctx.request.params?.credit) || '0',
        'incentive': String(ctx.request.params?.incentive) || '0'
      }
    ]
  }
}`

export const example2BJs = `/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  const currentSelector = ctx.data.chartSelectorCardAll;
  console.log(123123, currentSelector);
  return currentSelector;
}`

export const example2BTs = `/**
 * @param ctx 上下文
 */
export default (ctx: Container) => {
  const currentSelector = ctx.data.chartSelectorCardAll;
  console.log(123123, currentSelector);
  return currentSelector;
}`

export const example3AJs = `// 去除数组第一项数据
const processArrayData = (data) => {
  const clonedData = JSON.parse(JSON.stringify(data));
  if (clonedData && clonedData.length) {
    clonedData.shift();
  }
  return clonedData;
}

/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  const categories = processArrayData(ctx.data.qualityData.chartReports.categories)
  return categories;
}`

export const example3ATs = `// 去除数组第一项数据
const processArrayData = (data) => {
  const clonedData = JSON.parse(JSON.stringify(data));
  if (clonedData && clonedData.length) {
    clonedData.shift();
  }
  return clonedData;
}

/**
 * @param {Container} ctx 上下文
 */
export default (ctx: Container) => {
  const categories = processArrayData(ctx.data.qualityData.chartReports.categories)
  return categories;
}`

export const example3BJs = `// 去除数组最后一项数据
const processArrayData = (data) => {
  const clonedData = JSON.parse(JSON.stringify(data));
  if (clonedData && clonedData.length) {
    clonedData.pop();
  }
  return clonedData;
}

/**
 * @param {Container} ctx 上下文
 */
export default (ctx) => {
  const categories = processArrayData(ctx.data.qualityData.chartReports.categories)
  return categories;
}`

export const example3BTs = `// 去除数组最后一项数据
const processArrayData = (data) => {
  const clonedData = JSON.parse(JSON.stringify(data));
  if (clonedData && clonedData.length) {
    clonedData.pop();
  }
  return clonedData;
}

/**
 * @param {Container} ctx 上下文
 */
export default (ctx: Container) => {
  const categories = processArrayData(ctx.data.qualityData.chartReports.categories)
  return categories;
}`

export const example4AJs = `/**
 * @param {Container} ctx 上下文
 * @param {any[]} args 组件调用事件函数时传递的参数
 */
export default (ctx, ...args) => {
  console.log('事件级前端动作副作用', ctx, args)
  return ctx.data.columnsRender?.ocpc_action_type_name;
}`

export const example4ATs = `/**
 * @param ctx 上下文
 * @param args 组件调用事件函数时传递的参数
 */
export default (ctx: Container, ...args: any[]) => {
  console.log('事件级前端动作副作用', ctx, args)
  return ctx.data.columnsRender?.ocpc_action_type_name;
}`

export const example4BJs = `/**
 * @param {Container} ctx 上下文
 * @param {any[]} args 组件调用事件函数时传递的参数
 */
export default (ctx, ...args) => {
  console.log('事件级前端动作副作用', ctx, args)
  ctx.track('FePQu0w1_MquqeUZQjMfx', (track) => {
    track.eventOptions.params.diag_value = ctx.data.accountId;
    track.eventOptions.params.account_type = ctx.data.accountType;
  })
}`

export const example4BTs = `/**
 * @param ctx 上下文
 * @param args 组件调用事件函数时传递的参数
 */
export default (ctx: Container, ...args: any[]) => {
  console.log('事件级前端动作副作用', ctx, args)
  ctx.track('FePQu0w1_MquqeUZQjMfx', (track) => {
    track.eventOptions.params.diag_value = ctx.data.accountId;
    track.eventOptions.params.account_type = ctx.data.accountType;
  })
}`
