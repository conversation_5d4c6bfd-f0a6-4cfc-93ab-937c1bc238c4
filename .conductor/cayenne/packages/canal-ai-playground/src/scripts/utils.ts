import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import fs from 'node:fs'
import { SceneDetail, SimilarityResult } from './types'

/**
 * 判断文件是否存在
 * @param filePath 文件路径
 */
export async function isFile(filePath: string): Promise<boolean> {
  try {
    const stat = await fs.promises.stat(filePath)
    return stat.isFile()
  } catch (err) {
    return false
  }
}

/**
 * 判断文件夹是否存在
 * @param directoryPath 文件夹路径
 */
export async function isDirectory(directoryPath: string): Promise<boolean> {
  try {
    const stat = await fs.promises.stat(directoryPath)
    return stat.isDirectory()
  } catch (err) {
    return false
  }
}

/**
 * 相似度结果转 MD 字符串
 * @param sr 相似度结果
 * @param sceneDetails 场景详情
 */
export function similarityResult2md(
  sr: SimilarityResult,
  sceneDetails: SceneDetail[],
): string {
  return `总得分: ${sr.averageScore}

${sr.items
  .map((item) => {
    const lowerTextJsOrTs = [
      FrontExpressionType.JAVASCRIPT,
      BackExpressionType.JAVASCRIPT,
    ].includes(sceneDetails[item.index].type)
      ? 'js'
      : 'ts'
    return `# ${item.index}

得分：${item.score}

模块 ID：${item.moduleId}

目标代码：

\`\`\`${lowerTextJsOrTs}
${item.targetCode}
\`\`\`

预测代码：

\`\`\`${lowerTextJsOrTs}
${item.predictCode}
\`\`\`
`
  })
  .join('\n')}
`
}
