import './global-init'

import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import _ from 'lodash'
import fs from 'node:fs'
import path from 'node:path'
import { SceneDetail } from './types'
import { isFile } from './utils'

// 对抽样的场景和挑选过的抽样的场景，进行计数，在抽样的基础上，尽量保证各种类型的场景都有

await countScenesByType(
  path.join(import.meta.dirname, '../data/sample-scene-details.json'),
)
// 一般就是复制 sample-scene-details.json 到 picked-sample-scene-details.json，保留一个稳定版本
await countScenesByType(
  path.join(import.meta.dirname, '../data/picked-sample-scene-details.json'),
)

async function countScenesByType(filePath: string): Promise<void> {
  console.log('filePath', filePath)
  if (await isFile(filePath)) {
    const sceneDetails: SceneDetail[] = JSON.parse(
      await fs.promises.readFile(filePath, 'utf-8'),
    )
    const scenesByType = _.groupBy(sceneDetails, 'type')
    for (const [text, type] of [
      ['front-js', FrontExpressionType.JAVASCRIPT],
      ['front-ts', FrontExpressionType.TYPESCRIPT],
      ['back-js', BackExpressionType.JAVASCRIPT],
      ['back-ts', BackExpressionType.TYPESCRIPT],
    ]) {
      console.log(
        `count ${text}: ${scenesByType[type]?.length}`,
        _.countBy(scenesByType[type], 'scene.type'),
      )
    }
  } else {
    console.log('file not exists')
  }
  console.log()
}
