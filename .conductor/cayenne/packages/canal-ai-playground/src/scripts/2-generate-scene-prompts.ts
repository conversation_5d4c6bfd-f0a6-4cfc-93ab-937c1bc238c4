import './global-init'

import {
  createBackExprCodePrompt,
  createFrontExprCodePrompt,
} from '@ad/canal-ai'
import {
  BackExpressionType,
  FrontExpressionType,
  getBackCtxDts,
  getCtxDataFieldsBySchema,
  getFrontCtxDts,
} from '@ad/canal-shared'
import fs from 'node:fs'
import path from 'node:path'
import {
  loadGetApiLabelsInComponent,
  loadGetComponentMaterialSchema,
  loadGetDataSourceLabelByApi,
  loadGetValueProp,
} from './prompt-tools'
import { SceneDetail } from './types'

// 生成场景对应的提示

const sceneDetails: SceneDetail[] = JSON.parse(
  await fs.promises.readFile(
    path.join(import.meta.dirname, '../data/picked-sample-scene-details.json'),
    'utf-8',
  ),
)
const scenePrompts: string[] = []
for (const sceneDetail of sceneDetails) {
  const getComponentMaterialSchema = await loadGetComponentMaterialSchema(
    sceneDetail.schemaDetail,
  )
  const getApiLabelsInComponent = loadGetApiLabelsInComponent(
    sceneDetail.schemaDetail,
  )
  const getDataSourceLabelByApi = loadGetDataSourceLabelByApi()
  const getValueProp = loadGetValueProp(getComponentMaterialSchema)
  switch (sceneDetail.type) {
    case FrontExpressionType.JAVASCRIPT:
    case FrontExpressionType.TYPESCRIPT: {
      scenePrompts.push(
        createFrontExprCodePrompt({
          expressionType: sceneDetail.type,
          scene: sceneDetail.scene,
          ctxDts: getFrontCtxDts(
            getCtxDataFieldsBySchema(
              sceneDetail.schemaDetail.schema,
              getValueProp,
            ),
            sceneDetail.schemaDetail.schema?.model,
            // TODO: 加载全局模型
          ),
          schema: sceneDetail.schemaDetail.schema,
          getComponentMaterialSchema,
          keepCurrentScene: false,
        }),
      )
      break
    }
    case BackExpressionType.JAVASCRIPT:
    case BackExpressionType.TYPESCRIPT: {
      scenePrompts.push(
        createBackExprCodePrompt({
          expressionType: sceneDetail.type,
          scene: sceneDetail.scene,
          ctxDts: getBackCtxDts(
            getCtxDataFieldsBySchema(
              sceneDetail.schemaDetail.schema,
              getValueProp,
            ),
            sceneDetail.schemaDetail.schema?.model,
            // TODO: 加载全局模型
          ),
          schema: sceneDetail.schemaDetail.schema,
          getComponentMaterialSchema,
          getApiLabelsInComponent,
          getDataSourceLabelByApi,
          keepCurrentScene: false,
        }),
      )
      break
    }
    default: {
      throw new Error(`unknown type: ${sceneDetail.type}`)
    }
  }
}
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/scene-prompts.json'),
  JSON.stringify(scenePrompts, null, 2),
)
console.log('scenePrompts.length', scenePrompts.length)
