import './global-init'

import { getCodeFromCompletion } from '@ad/canal-ai'
import fs from 'node:fs'
import path from 'node:path'
import pLimit from 'p-limit'
import { serverApis } from '../server-apis'

// 生成场景对应的预测代码

/**
 * 模型
 */
const MODEL = 'claude-3.7-sonnet'
const limit = pLimit(20)

const scenePrompts: string[] = JSON.parse(
  await fs.promises.readFile(
    path.join(import.meta.dirname, '../data/scene-prompts.json'),
    'utf-8',
  ),
)
const predictCodes: string[] = await Promise.all(
  scenePrompts.map((scenePrompt, i) =>
    limit(async () => {
      const ret = await serverApis.internalLangBridge({
        model: MODEL,
        messages: [
          {
            role: 'user',
            content: scenePrompt,
          },
        ],
        bizKey: 'adCanal',
        operator: 'system',
      })
      console.log(`finish ${++i}/${scenePrompts.length}`)
      return getCodeFromCompletion(ret.data)
    }),
  ),
)
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/predict-codes.json'),
  JSON.stringify(predictCodes, null, 2),
)
