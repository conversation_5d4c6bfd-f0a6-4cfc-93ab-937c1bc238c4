import { BackExpressionScene, FrontExpressionScene } from '@ad/canal-ai'
import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import { E2ESchema } from '@ad/e2e-schema'

/**
 * Schema 详情
 */
export interface SchemaDetail {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 业务域
   */
  domainCode: string
  /**
   * Schema
   */
  schema: E2ESchema
}

/**
 * 场景详情
 */
export type SceneDetail = {
  /**
   * Schema 详情
   */
  schemaDetail: SchemaDetail
  /**
   * 代码
   */
  code: string
} & (
  | {
      /**
       * 类型
       */
      type: FrontExpressionType
      /**
       * 场景
       */
      scene: FrontExpressionScene
    }
  | {
      /**
       * 类型
       */
      type: BackExpressionType
      /**
       * 场景
       */
      scene: BackExpressionScene
    }
)

/**
 * 相似度结果
 */
export interface SimilarityResult {
  /**
   * 平均分
   */
  averageScore: number
  /**
   * 条目
   */
  items: SimilarityResultItem[]
}

/**
 * 相似度结果条目
 */
export interface SimilarityResultItem {
  /**
   * 得分
   */
  score: number
  /**
   * 下标，即 picked-sample-scene-details.json 里的下标
   */
  index: number
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 目标代码
   */
  targetCode: string
  /**
   * 预测代码
   */
  predictCode: string
}
