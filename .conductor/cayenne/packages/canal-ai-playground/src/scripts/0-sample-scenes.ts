import './global-init'

import {
  genBackExprCodesBySchema,
  genFrontExprCodesBySchema,
} from '@ad/canal-ai'
import {
  BackExpressionType,
  FrontExpressionType,
  ModuleType,
} from '@ad/canal-shared'
import { E2EServerSchema } from '@ad/e2e-schema'
import { b2fE2ESchema } from '@ad/e2e-schema-utils'
import _ from 'lodash'
import fs from 'node:fs'
import path from 'node:path'
import {
  loadGetApiLabelsInComponent,
  loadGetComponentMaterialSchema,
  loadGetDataSourceLabelByApi,
} from './prompt-tools'
import { SceneDetail, SchemaDetail } from './types'

// 从线上 DB 记录里抽样出一批场景

/**
 * 抽样数
 */
const SAMPLE_SIZE = 100

//#region Schema 读取

const dbRecordsOnlineSchemas = JSON.parse(
  await fs.promises.readFile(
    path.join(import.meta.dirname, '../data/db-records-online-schemas.json'),
    'utf-8',
  ),
)
const moduleIdIndex = dbRecordsOnlineSchemas.data.columns.findIndex(
  (column: { name: string }) => column.name === 'module_id',
)
const domainCodeIndex = dbRecordsOnlineSchemas.data.columns.findIndex(
  (column: { name: string }) => column.name === 'domain_code',
)
const pageTypeIndex = dbRecordsOnlineSchemas.data.columns.findIndex(
  (column: { name: string }) => column.name === 'page_type',
)
const schemaIndex = dbRecordsOnlineSchemas.data.columns.findIndex(
  (column: { name: string }) => column.name === 'version_context',
)
const onlineSchemaDetails: SchemaDetail[] = []
for (const item of dbRecordsOnlineSchemas.data.dataList as string[][]) {
  if (!item[schemaIndex] || item[pageTypeIndex] === `${ModuleType.GLOBAL}`) {
    continue
  }
  const serverSchema: E2EServerSchema = JSON.parse(item[schemaIndex])
  // console.log('serverSchema', serverSchema, [item[pageTypeIndex]])
  const schema = b2fE2ESchema(serverSchema, {
    keepBackDataProp: true,
    keepValueProp: true,
    keepIfProp: true,
  })
  onlineSchemaDetails.push({
    moduleId: item[moduleIdIndex],
    domainCode: item[domainCodeIndex],
    schema,
  })
}
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/online-schema-details.json'),
  JSON.stringify(onlineSchemaDetails, null, 2),
)
console.log('onlineSchemaDetails.length', onlineSchemaDetails.length)

//#endregion

//#region 场景抽样

const sampleSceneDetails: SceneDetail[] = []
for (const schemaDetail of _.shuffle(onlineSchemaDetails)) {
  // if (schemaDetail.moduleId !== '47330c47-0709-4c55-9553-0dedfff4b433') {
  //   continue
  // }
  const getComponentMaterialSchema =
    await loadGetComponentMaterialSchema(schemaDetail)
  const getApiLabelsInComponent = loadGetApiLabelsInComponent(schemaDetail)
  const getDataSourceLabelByApi = loadGetDataSourceLabelByApi()
  const sceneDetails: SceneDetail[] = [
    ...Array.from(
      genFrontExprCodesBySchema(
        FrontExpressionType.JAVASCRIPT,
        schemaDetail.schema,
        getComponentMaterialSchema,
      ),
      ([scene, code]) => ({
        schemaDetail,
        type: FrontExpressionType.JAVASCRIPT,
        scene,
        code,
      }),
    ),
    ...Array.from(
      genFrontExprCodesBySchema(
        FrontExpressionType.TYPESCRIPT,
        schemaDetail.schema,
        getComponentMaterialSchema,
      ),
      ([scene, code]) => ({
        schemaDetail,
        type: FrontExpressionType.TYPESCRIPT,
        scene,
        code,
      }),
    ),
    ...Array.from(
      genBackExprCodesBySchema(
        BackExpressionType.JAVASCRIPT,
        schemaDetail.schema,
        getComponentMaterialSchema,
        getApiLabelsInComponent,
        getDataSourceLabelByApi,
      ),
      ([scene, code]) => ({
        schemaDetail,
        type: BackExpressionType.JAVASCRIPT,
        scene,
        code,
      }),
    ),
    ...Array.from(
      genBackExprCodesBySchema(
        BackExpressionType.TYPESCRIPT,
        schemaDetail.schema,
        getComponentMaterialSchema,
        getApiLabelsInComponent,
        getDataSourceLabelByApi,
      ),
      ([scene, code]) => ({
        schemaDetail,
        type: BackExpressionType.TYPESCRIPT,
        scene,
        code,
      }),
    ),
  ]
  // console.log('sceneDetails', sceneDetails)
  if (!sceneDetails.length) continue
  sampleSceneDetails.push(_.sample(sceneDetails) as SceneDetail)
  if (sampleSceneDetails.length >= SAMPLE_SIZE) break
}
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/sample-scene-details.json'),
  JSON.stringify(sampleSceneDetails, null, 2),
)
console.log('sampleSceneDetails.length', sampleSceneDetails.length)

//#endregion
