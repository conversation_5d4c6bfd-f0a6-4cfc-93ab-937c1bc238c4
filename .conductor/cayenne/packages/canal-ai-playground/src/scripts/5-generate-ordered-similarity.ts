import './global-init'

import fs from 'node:fs'
import path from 'node:path'
import { SceneDetail, SimilarityResult } from './types'
import { isDirectory, similarityResult2md } from './utils'

// 生成排序后的相似度

const sceneDetails: SceneDetail[] = JSON.parse(
  await fs.promises.readFile(
    path.join(import.meta.dirname, '../data/picked-sample-scene-details.json'),
    'utf-8',
  ),
)
const scenePrompts: string[] = JSON.parse(
  await fs.promises.readFile(
    path.join(import.meta.dirname, '../data/scene-prompts.json'),
    'utf-8',
  ),
)
const similarityResult: SimilarityResult = JSON.parse(
  await fs.promises.readFile(
    path.join(import.meta.dirname, '../data/similarity-result.json'),
    'utf-8',
  ),
)
similarityResult.items.sort((item1, item2) => item1.score - item2.score)
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/ordered-similarity-result.json'),
  JSON.stringify(similarityResult, null, 2),
)
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/ordered-similarity-result.md'),
  similarityResult2md(similarityResult, sceneDetails),
)
const PATH_ORDERED_SCENE_PROMPTS = path.join(
  import.meta.dirname,
  '../data/ordered-scene-prompts',
)
if (!(await isDirectory(PATH_ORDERED_SCENE_PROMPTS))) {
  await fs.promises.mkdir(PATH_ORDERED_SCENE_PROMPTS)
}
for (const item of similarityResult.items) {
  await fs.promises.writeFile(
    path.join(PATH_ORDERED_SCENE_PROMPTS, `${item.index}-${item.moduleId}.md`),
    scenePrompts[item.index],
  )
}
