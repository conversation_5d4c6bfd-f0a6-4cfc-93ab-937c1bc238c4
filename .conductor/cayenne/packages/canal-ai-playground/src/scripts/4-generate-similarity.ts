import './global-init'

import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import _ from 'lodash'
import fs from 'node:fs'
import path from 'node:path'
import pLimit from 'p-limit'
import { serverApis } from '../server-apis'
import { createSimilarityPrompt } from '../similarity-prompt'
import { SceneDetail, SimilarityResult, SimilarityResultItem } from './types'
import { similarityResult2md } from './utils'

// 生成相似度

/**
 * 模型
 */
const MODEL = 'claude-3.7-sonnet'
const limit = pLimit(20)

const sceneDetails: SceneDetail[] = JSON.parse(
  await fs.promises.readFile(
    path.join(import.meta.dirname, '../data/picked-sample-scene-details.json'),
    'utf-8',
  ),
)
const predictCodes: string[] = JSON.parse(
  await fs.promises.readFile(
    path.join(import.meta.dirname, '../data/predict-codes.json'),
    'utf-8',
  ),
)
const similarityPrompts: string[] = []
const items: SimilarityResultItem[] = await Promise.all(
  sceneDetails.map((sceneDetail, i) => {
    const targetCode = sceneDetail.code
    const predictCode = predictCodes[i]
    return limit(async () => {
      const similarityPrompt = createSimilarityPrompt(
        [
          FrontExpressionType.JAVASCRIPT,
          BackExpressionType.JAVASCRIPT,
        ].includes(sceneDetail.type)
          ? 'js'
          : 'ts',
        targetCode,
        predictCode,
      )
      similarityPrompts[i] = similarityPrompt
      const ret = await serverApis.internalLangBridge({
        model: MODEL,
        messages: [
          {
            role: 'user',
            content: similarityPrompt,
          },
        ],
        bizKey: 'adCanal',
        operator: 'system',
      })
      const score = +ret.data
      if (!_.isNumber(score)) {
        throw new Error(
          `score is not number: ${score}, moduleId: ${sceneDetail.schemaDetail.moduleId}`,
        )
      }
      console.log(`finish ${i + 1}/${sceneDetails.length}`)
      return {
        score,
        index: i,
        moduleId: sceneDetail.schemaDetail.moduleId,
        targetCode,
        predictCode,
      }
    })
  }),
)
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/similarity-prompts.json'),
  JSON.stringify(similarityPrompts, null, 2),
)
const averageScore = _.sum(items.map((r) => r.score)) / items.length
console.log('averageScore', averageScore)
const similarityResult: SimilarityResult = {
  averageScore,
  items,
}
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/similarity-result.json'),
  JSON.stringify(similarityResult, null, 2),
)
await fs.promises.writeFile(
  path.join(import.meta.dirname, '../data/similarity-result.md'),
  similarityResult2md(similarityResult, sceneDetails),
)
