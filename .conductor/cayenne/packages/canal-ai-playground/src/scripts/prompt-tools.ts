import {
  CreateBackExprCodePromptOptions,
  CreateFrontExprCodePromptOptions,
} from '@ad/canal-ai'
import {
  GetDataSrc,
  GetValueProp,
  bizComp2ComponentMaterialSchema,
  createApiLabel,
  createDataSourceLabelByApi,
} from '@ad/canal-shared'
import {
  E2ECMSFieldProp,
  E2ERemoteComponentLibraryMaterialSchema,
  E2ERemoteComponentMaterialSchema,
} from '@ad/e2e-material-schema'
import {
  INTERNAL_REACT_COMPONENT_LIB_NAME,
  genAllFieldPropDetails,
  getLatestMaterialLibSchemaUrl,
  mergeComponentWithImplementProps,
} from '@ad/e2e-material-schema-utils'
import {
  decodeComponentType,
  dfsGenComponentDetailBySchema,
} from '@kael/schema-utils'
import _ from 'lodash'
import { serverApis } from '../server-apis'
import { SchemaDetail } from './types'

const internalReactComponentLibSchema: E2ERemoteComponentLibraryMaterialSchema =
  await (
    await fetch(
      await getLatestMaterialLibSchemaUrl(
        INTERNAL_REACT_COMPONENT_LIB_NAME,
        'production',
      ),
    )
  ).json()
const internalComponentMaterialMap = new Map(
  internalReactComponentLibSchema.components.map((comp) => [comp.type, comp]),
)

const memoizeGetComponentDetail = _.memoize(
  serverApis.getComponentDetail,
  (args) => JSON.stringify(args),
)

/**
 * 加载：获取组件物料 Schema
 * @param schemaDetail Schema 详情
 */
export async function loadGetComponentMaterialSchema(
  schemaDetail: SchemaDetail,
): Promise<CreateFrontExprCodePromptOptions['getComponentMaterialSchema']> {
  const {
    schema: { componentCodes },
  } = schemaDetail
  const m = new Map<string, E2ERemoteComponentMaterialSchema>()
  for (const [componentType, { version }] of _.entries(componentCodes)) {
    const [, idAndType] = decodeComponentType(componentType)
    const { data: bizComp } = await memoizeGetComponentDetail({
      id: idAndType,
      version,
    })
    const materialSchema = bizComp2ComponentMaterialSchema(bizComp)
    m.set(idAndType, mergeComponentWithImplementProps(materialSchema))
  }
  return (componentType) => {
    const [, idAndType] = decodeComponentType(componentType)
    const ret =
      m.get(idAndType) || internalComponentMaterialMap.get(idAndType) || null
    // console.log('getComponentMaterialSchema', { idAndType, ret })
    return ret
  }
}

const { data: dataSources } = await serverApis.getAllDataSources({})
const dataSourceMap = new Map(
  dataSources.map((dataSource) => [dataSource.id, dataSource]),
)
const getDataSrc: GetDataSrc = (dataSourceId) => dataSourceMap.get(dataSourceId)

/**
 * 加载：在组件里获取接口标签
 * @param schemaDetail Schema 详情
 */
export function loadGetApiLabelsInComponent(
  schemaDetail: SchemaDetail,
): CreateBackExprCodePromptOptions['getApiLabelsInComponent'] {
  const componentMap = new Map(
    Array.from(dfsGenComponentDetailBySchema(schemaDetail.schema), (ed) => [
      ed.component.id,
      ed.component,
    ]),
  )
  return (componentId, apiIds) => {
    const apiIdSet = new Set(apiIds)
    const comp = componentId ? componentMap.get(componentId) : null
    const apis = (comp?.apis || []).filter((api) => apiIdSet.has(api.id))
    const rootApis = (schemaDetail.schema.view.apis || []).filter((api) =>
      apiIdSet.has(api.id),
    )
    return [
      ...apis.map((api) => createApiLabel(api, 'component', getDataSrc)),
      ...rootApis.map((api) => createApiLabel(api, 'module', getDataSrc)),
    ]
  }
}

/**
 * 加载：通过接口获取数据源标签
 */
export function loadGetDataSourceLabelByApi(): CreateBackExprCodePromptOptions['getDataSourceLabelByApi'] {
  return (api) => createDataSourceLabelByApi(api, getDataSrc)
}

/**
 * 获取值属性
 * @param getComponentMaterialSchema 获取组件物料 Schema
 */
export function loadGetValueProp(
  getComponentMaterialSchema: CreateFrontExprCodePromptOptions['getComponentMaterialSchema'],
): GetValueProp {
  return (componentType) => {
    const component = getComponentMaterialSchema(componentType)
    let prop: E2ECMSFieldProp | null = null
    if (component) {
      for (const detail of genAllFieldPropDetails(component)) {
        if (_.isEqual(detail.path, ['value'])) {
          prop = detail.filedProp
        }
      }
    }
    return prop
  }
}
