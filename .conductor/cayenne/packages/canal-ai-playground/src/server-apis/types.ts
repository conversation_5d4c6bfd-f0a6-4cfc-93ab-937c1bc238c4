import { FetchSchemaEnv } from '@ad/canal-react-runtime'
import { BizComp, CommonRes } from '@ad/canal-shared'

/**
 * 获取组件详情请求
 */
export interface GetComponentDetailReq {
  /**
   * ID
   */
  id: string
  /**
   * 版本
   */
  version?: string
}

/**
 * 获取组件详情响应
 */
export type GetComponentDetailRes = CommonRes<BizComp>

/**
 * 获取模块部署记录请求
 */
export interface GetModuleDeployReq {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 环境
   */
  env: FetchSchemaEnv
}

/**
 * 获取模块部署记录响应
 */
export type GetModuleDeployRes = CommonRes<{
  id: string
  moduleId: string
  version: string
  content: string
  stage: string
  createUser: string
  lane?: string
  frontLane?: string
  updateUser: string
  createTime: number
  updateTime: number
  greyType: 'white' | 'lane' | 'percentage' | 'host'
  greyValue: string
  greyKey: string
  greyStatus: number
}>

/**
 * 获取所有数据源请求
 */
export interface GetAllDataSourcesReq {
  /**
   * （业务）域代码
   */
  domainCode?: string
}

/**
 * 数据源
 */
export interface DataSource {
  id?: string
  domainCode: string
  name: string
  path: string
  type: string
  method: string
  stagingDomain?: string
  prtDomain?: string
  betaDomain?: string
  productionDomain?: string
  mockRes?: string
  createUser: string
  updateUser: string
  createTime: number
  updateTime: number
}

/**
 * 获取所有数据源响应
 */
export type GetAllDataSourcesRes = CommonRes<DataSource[]>
