import {
  CommonRes,
  PLATFORM_ORIGIN,
  PLATFORM_ORIGIN_STAGING,
} from '@ad/canal-shared'
import { Kof } from '@ks/kof'
import { kofFetch } from '@ks/kof-fetch'
import {
  GetAllDataSourcesReq,
  GetAllDataSourcesRes,
  GetComponentDetailReq,
  GetComponentDetailRes,
} from './types'

/**
 * Kof 应用
 */
const app = new Kof().use(kofFetch)

/**
 * 服务器接口，免登录
 */
export const serverApis = app.apis(
  {
    /**
     * 获取组件详情
     */
    getComponentDetail: app.options<
      GetComponentDetailReq,
      GetComponentDetailRes
    >({
      urlPath: '/rest/canal/component/detail',
    }),
    /**
     * 获取所有数据源
     */
    getAllDataSources: app.options<GetAllDataSourcesReq, GetAllDataSourcesRes>({
      urlPath: '/rest/canal/data-source-config/all',
    }),
    /**
     * 获取所有数据源
     */
    internalLangBridge: app.options<unknown, CommonRes<string>>({
      method: 'POST',
      urlBase: PLATFORM_ORIGIN_STAGING,
      urlPath: '/rest/canal/ai/__internal-lang-bridge',
    }),
  },
  {
    urlBase: PLATFORM_ORIGIN,
  },
)
