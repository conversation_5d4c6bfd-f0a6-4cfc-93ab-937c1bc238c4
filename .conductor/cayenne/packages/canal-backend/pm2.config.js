module.exports = {
  apps: [
    {
      name: 'canal-backend-server',
      script: process.env.ENTRY_PATH || './dist/src/main.js',
      // exec_mode: 'cluster',
      instances: process.env.KCS_CPU_CORE_NUM || 1,
      max_memory_restart: `${process.env.MAX_MEMORY_RESTART || 1}G`,
      env: {
        NODE_ENV: process.env.NODE_ENV || 'development',
        MY_POD_NAME: process.env.MY_POD_NAME || 'MY_POD_NAME',
      },
    },
  ],
}
