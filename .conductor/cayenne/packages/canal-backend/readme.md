# canal-backend

> 大运河后端

## 开发

```bash
# 本地开发
npm start
```

接口开发参考：packages/canal-backend/src/user  
记得在packages/canal-backend/src/app.module.ts中注册新开发的接口

## 访问

- 使用[SwitchHosts](https://github.com/oldj/SwitchHosts)绑定：127.0.0.1 test.corp.kuaishou.com
- 访问接口swagger：http://test.corp.kuaishou.com:9527/rest/swagger
- 登录访问：http://test.corp.kuaishou.com:9527/rest/user/login?redirectUrl=http%3A%2F%2Ftest.corp.kuaishou.com%3A9527%2Frest%2Fuser%2Finfo
- 验证登录成功访问：http://test.corp.kuaishou.com:9527/rest/user/info 返回用户信息即是登录成功

## 部署

[流水线](https://halo.corp.kuaishou.com/devcloud/pipeline/history/715109?differ=all&back=mine-pipeline&node_id=56224)

## 重新登录

staging: https://canal.staging.kuaishou.com/rest/user/login?redirectUrl=http%3A%2F%2Fcanal.staging.kuaishou.com%3A%2Frest%2Fuser%2Finfo
