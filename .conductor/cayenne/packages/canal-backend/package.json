{"name": "@ad/canal-backend", "version": "2.6.3", "private": true, "scripts": {"build": "nest build", "start": "NODE_ENV=local nest start --watch", "start:prod": "node dist/main", "start:staging": "NODE_ENV=staging nest start --watch", "test": "jest", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:watch": "jest --watch"}, "dependencies": {"@ad/canal-ai": "workspace:^", "@ad/canal-shared": "workspace:^", "@ad/e2e-schema": "workspace:^", "@infra-node/kconf": "^1.1.16", "@infra-node/logger": "^1.1.12", "@infra-node/redis": "^1.3.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/mapped-types": "^2.0.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.13", "@nestjs/typeorm": "^10.0.0", "axios": "^1.5.1", "body-parser": "^1.20.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "dayjs": "^1.11.10", "express-mysql-session": "^3.0.0", "express-session": "^1.17.3", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "openai": "^4.96.0", "raven": "^2.6.4", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/body-parser": "^1.19.5", "@types/compression": "^1.7.4", "@types/express": "^4.17.17", "@types/express-mysql-session": "^3.0.2", "@types/express-session": "^1.17.9", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.199", "@types/node": "^18.17.14", "@types/raven": "^2.5.5", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.5", "jest": "^29.7.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}}