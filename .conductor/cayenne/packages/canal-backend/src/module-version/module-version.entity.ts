import { bigNumberStringToIntTransformer } from 'src/utils/transform'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'module_version' })
export class ModuleVersion {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  id: string

  @Column({
    name: 'module_id',
    type: 'char',
    length: 36,
    comment: '模块id',
  })
  moduleId: string

  @Column({
    name: 'module_version',
    type: 'bigint',
    comment: '版本',
  })
  version: number

  @Column({
    type: 'int',
    comment: '状态',
  })
  status: number

  @Column({
    name: 'version_context',
    type: 'text',
    comment: '内容',
  })
  content: string

  @Column({
    name: 'workload',
    type: 'bigint',
    comment: '工作量',
    transformer: bigNumberStringToIntTransformer,
  })
  workload: number

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
    select: false,
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number
}
