import type { ComponentCode, E2EServerSchema } from '@ad/e2e-schema'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import _ from 'lodash'
import { deepSearch } from 'src/utils/json'
import { Repository } from 'typeorm'
import { DataSourceConfigService } from '../data-source-config/data-source-config.service'
import { MixModuleDto, UpdateModuleDto } from '../module/module.dto'
import { ModuleVersion } from './module-version.entity'

@Injectable()
export class ModuleVersionService {
  public constructor(
    @InjectRepository(ModuleVersion)
    public readonly moduleVersionRepo: Repository<ModuleVersion>,
    private readonly dataSourceConfigService: DataSourceConfigService,
  ) {}

  async getLatestVersionByModuleId(moduleId: string) {
    return this.moduleVersionRepo
      .createQueryBuilder('moduleVersion')
      .where('moduleVersion.module_id = :moduleId', { moduleId })
      .orderBy('moduleVersion.create_time', 'DESC')
      .getOne()
  }

  async getOnlineVersionByModuleId(moduleId: string) {
    return this.moduleVersionRepo
      .createQueryBuilder('moduleVersion')
      .where('moduleVersion.module_id = :moduleId', { moduleId })
      .andWhere('moduleVersion.status = :status', { status: '1' })
      .orderBy('moduleVersion.create_time', 'DESC')
      .getOne()
  }

  async getDetailByModule(moduleId: string, moduleVersion: number) {
    return this.moduleVersionRepo
      .createQueryBuilder('moduleVersion')
      .where('moduleVersion.module_id = :moduleId', { moduleId })
      .andWhere('moduleVersion.version = :moduleVersion', {
        moduleVersion,
      })
      .getOne()
  }

  /**
   * 获取工作量
   * @param moduleId 模块 ID
   * @param moduleVersion 模块版本
   */
  async getWorkload(moduleId: string, moduleVersion: number) {
    const mv = await this.moduleVersionRepo.findOne({
      where: {
        moduleId,
        version: moduleVersion,
      },
      select: ['workload'],
    })
    return mv?.workload || 0
  }

  async create(updateModuleDto: UpdateModuleDto, userCode: string) {
    const moduleVersion = new ModuleVersion()
    const now = new Date().getTime()
    let version = 1

    const latestVersion = await this.getLatestVersionByModuleId(
      updateModuleDto.id,
    )

    if (latestVersion) {
      version = +latestVersion.version + 1
    }

    version = 1

    moduleVersion.moduleId = updateModuleDto.id
    moduleVersion.content = updateModuleDto.content
    moduleVersion.createUser = userCode
    moduleVersion.updateUser = userCode
    moduleVersion.createTime = now
    moduleVersion.updateTime = now
    moduleVersion.version = version

    try {
      await this.moduleVersionRepo.save(moduleVersion)
    } catch (err: unknown) {
      if ((err as { code: string }).code === 'ER_DUP_ENTRY') {
        // 版本重复
        const newLatestVersion = await this.getLatestVersionByModuleId(
          updateModuleDto.id,
        )

        if (newLatestVersion) {
          version = +newLatestVersion.version + 1
        }
        moduleVersion.version = version
        await this.moduleVersionRepo.save(moduleVersion)
      } else {
        throw err
      }
    }
  }

  /**
   * 通过模块删除
   * @param moduleId 模块 ID
   */
  async deleteByModule(moduleId: string) {
    return await this.moduleVersionRepo.delete({
      moduleId,
    })
  }

  /**
   * 根据模块ID和版本号数组获取模块内容并进行检查
   * @param moduleVersions 模块ID和版本号的组合数组
   * @returns 检查结果，如果没有错误则返回空的checkError
   */
  async checkModulesByIdAndVersion(
    moduleVersions: { moduleId: string; version: string | number }[],
  ) {
    try {
      // 直接从 module_version 表中获取模块内容
      const moduleList: Partial<MixModuleDto>[] = []

      // 获取所有模块的详细信息
      await Promise.all(
        moduleVersions.map(async ({ moduleId, version }) => {
          // 直接查询模块内容
          const moduleVersionDetail = await this.moduleVersionRepo
            .createQueryBuilder('moduleVersion')
            .where('moduleVersion.module_id = :moduleId', { moduleId })
            .andWhere('moduleVersion.version = :version', {
              version: Number(version),
            })
            .getOne()

          if (moduleVersionDetail) {
            const _m: Partial<MixModuleDto> = {
              moduleId,
              id: moduleId,
              version: Number(version),
              content: moduleVersionDetail.content,
            }

            moduleList.push(_m)
          }
        }),
      )

      // 执行检查
      if (moduleList.length > 0) {
        const results = await Promise.allSettled([
          await this.checkDataSourceConfig(
            moduleList as MixModuleDto[],
            'dataSource',
          ),
          await this.checkComponentsConfig(
            moduleList as MixModuleDto[],
            'component',
          ),
        ])

        const mergedResults = results.map((result) => {
          if (
            result.status === 'fulfilled' &&
            Array.isArray(result.value) &&
            result.value.length
          ) {
            const rejectInfoArray = result.value.filter(
              (item): item is PromiseRejectedResult =>
                item.status === 'rejected',
            )
            if (rejectInfoArray.length) {
              return {
                key: rejectInfoArray[0].reason?.key,
                content: rejectInfoArray.map((item) => item.reason),
              }
            }
          }
        })

        const filterResult = mergedResults.filter((item) => !!item)
        if (filterResult.length) {
          return {
            checkError: filterResult,
          }
        }
      }

      // 没有错误，返回空的checkError
      return {
        checkError: [],
      }
    } catch (error) {
      console.error('检查模块配置出错:', error)
      throw error
    }
  }

  /**
   * 检查数据源的配置
   * 1、数据源定义后无人使用
   * 2、数据源本身的定义不完全
   */
  async checkDataSourceConfig(modules: MixModuleDto[], key: string) {
    return await Promise.allSettled(
      modules.map(async (module) => {
        if (module.content) {
          try {
            const errorTips: Record<string, string[] | string>[] = []
            let schema: E2EServerSchema

            try {
              schema = JSON.parse(module.content)
            } catch (parseError) {
              return Promise.reject({
                moduleId: module.moduleId || module.id,
                key,
                errorTips: [
                  {
                    text: 'JSON解析错误',
                    content: `模块内容解析失败: ${
                      (parseError as Error).message
                    }`,
                  },
                ],
              })
            }

            // 模块级接口
            const apis = schema.apis
            let dataSourceIds: string[] = []
            const componentDataSourceNotUsedResult: string[] = []
            const globalDataSourceNotUsedResult: string[] = []
            let globalApiKeys: string[] = []
            // 保存原始的data source的Id，用来查询是否配置是ok的
            if (apis && Object.values(apis).length) {
              Object.values(apis).forEach((api) => {
                api?.dataSourceId && dataSourceIds.push(api?.dataSourceId)
              })
              globalApiKeys = Object.keys(apis)
            }
            const components = schema.flattenedView?.components || []
            components?.forEach?.((component) => {
              // 组件级别的数据源
              const localApis = component.apis || {}
              if (Object.keys(localApis).length) {
                // 加入到data source的合集里
                dataSourceIds = dataSourceIds.concat(
                  [
                    ...new Set(
                      Object.values(localApis).map((api) => api.dataSourceId),
                    ),
                  ].filter((item): item is string => item !== undefined),
                )
              }
              // 查找组件中引用的API配置
              const apiConfigs = deepSearch(component, [
                {
                  type: 'api',
                },
                {
                  type: 'apis',
                },
              ])

              // 引用的数据源id - 不依赖于apiConfigs是否存在
              const apiIds: string[] =
                apiConfigs?.flatMap((config) => {
                  if (Array.isArray(config.apiIds)) {
                    // 如果 apiIds 是一个数组，返回它
                    return config.apiIds
                  } else if (typeof config.apiId === 'string') {
                    // 如果 apiId 是一个字符串，返回它
                    return [config.apiId]
                  } else {
                    // 如果都不是，返回一个空数组
                    return []
                  }
                }) || []

              // 检查未引用的数据源 - 不依赖于apiConfigs.length
              const notInReference = _.difference(
                Object.keys(localApis),
                apiIds,
              )

              // 处理模块级API引用
              const usedGlobalApi: string[] = _.intersection(
                globalApiKeys,
                apiIds,
              )
              // 去掉已经引用过的模块级数据源
              globalApiKeys = _.difference(globalApiKeys, usedGlobalApi)

              // 组件级数据源定义了但并未使用
              if (notInReference.length) {
                notInReference.forEach((item) => {
                  componentDataSourceNotUsedResult.push(
                    `组件【${component.name}】内定义的接口【${
                      localApis?.[item as string]?.name || 'ID'
                    }${
                      localApis?.[item as string]?.dataSourceId || ''
                    }】并未被使用`,
                  )
                })
              }
            })
            if (globalApiKeys.length) {
              globalApiKeys.forEach((apiId) => {
                globalDataSourceNotUsedResult.push(
                  `模块级接口【${apis?.[apiId as string]?.name || 'ID'}-${
                    apis?.[apiId as string]?.dataSourceId || ''
                  }】并未被使用`,
                )
              })
            }
            console.log('dataSourceIds', dataSourceIds)
            if (dataSourceIds.length) {
              const checkResult =
                await this.dataSourceConfigService.checkDataSourceBeforePublish(
                  dataSourceIds,
                )
              const reasons = checkResult
                .filter(
                  (item): item is PromiseRejectedResult =>
                    item.status === 'rejected',
                )
                .map((item) => item.reason)
              if (reasons.length) {
                errorTips.push({
                  text: '数据源配置缺失（有可能是path、method、domain)',
                  content: reasons,
                })
              }
            }
            if (componentDataSourceNotUsedResult.length) {
              errorTips.push({
                text: '组件级数据源配置但未使用，请删除',
                content: componentDataSourceNotUsedResult,
              })
            }
            if (globalDataSourceNotUsedResult.length) {
              errorTips.push({
                text: '模块级数据源配置但未使用，请删除',
                content: globalDataSourceNotUsedResult,
              })
            }
            if (errorTips.length) {
              return Promise.reject({
                moduleId: module.moduleId || module.id,
                key,
                errorTips,
              })
            }
            return errorTips
          } catch (error) {
            console.error('数据源配置检查出错:', error)
            return Promise.reject({
              moduleId: module.moduleId || module.id,
              key,
              errorTips: [
                {
                  text: '数据源配置检查出错',
                  content: `检查过程中发生错误: ${(error as Error).message}`,
                },
              ],
            })
          }
        }
      }),
    )
  }

  /**
   * 组件cdn域名检查，是否是标准域名
   * 组件版本不允许有beta版本
   * @param modules
   */
  async checkComponentsConfig(modules: MixModuleDto[], key: string) {
    return await Promise.allSettled(
      modules.map(async (module) => {
        if (module.content) {
          try {
            const errorTips: Record<string, string[] | string>[] = []
            let schema: E2EServerSchema

            try {
              schema = JSON.parse(module.content)
            } catch (parseError) {
              return Promise.reject({
                moduleId: module.moduleId || module.id,
                key,
                errorTips: [
                  {
                    text: 'JSON解析错误',
                    content: `模块内容解析失败: ${
                      (parseError as Error).message
                    }`,
                  },
                ],
              })
            }

            const componentCodes: ComponentCode[] = schema.componentCodes
              ? Object.values(schema.componentCodes)
              : []
            const domainCheckResult: string[] = []
            const resourceVersionCheck: string[] = []
            const regex = /^https:\/\/p[1-5]-ad\.ad(kwai|bkwai)\.com\/?/
            Array.isArray(componentCodes) &&
              componentCodes.forEach((item) => {
                if (
                  item &&
                  'code' in item &&
                  item.code &&
                  'js' in item.code &&
                  typeof item.code.js === 'string'
                ) {
                  console.log(item?.code?.js)
                  if (!regex.test(item.code.js)) {
                    domainCheckResult.push(
                      `引用的组件资源【${item.code.js}】域名不符合商业化标准域名`,
                    )
                  }
                  const urlObj = new URL(item.code.js)
                  let path = urlObj.pathname
                  const distIndex = path.indexOf('/dist')
                  if (distIndex !== -1) {
                    // 截取从开始到 'dist' 的部分
                    path = path.substring(0, distIndex)
                    console.log(path)
                    if (path.includes('beta') || path.includes('alpha')) {
                      resourceVersionCheck.push(
                        `引用的组件资源【${item.code.js}】不是正式版本`,
                      )
                    }
                  }
                }
              })
            if (domainCheckResult.length) {
              errorTips.push({
                text: '域名校验不通过（需要是p*-ad.adkwai.com或p*-ad.adbkwai.com）',
                content: domainCheckResult,
              })
            }
            if (resourceVersionCheck.length) {
              errorTips.push({
                text: '组件版本校验不通过',
                content: resourceVersionCheck,
              })
            }
            if (errorTips.length) {
              return Promise.reject({
                moduleId: module.moduleId || module.id,
                key,
                errorTips,
              })
            }
            return errorTips
          } catch (error) {
            console.error('组件配置检查出错:', error)
            return Promise.reject({
              moduleId: module.moduleId || module.id,
              key,
              errorTips: [
                {
                  text: '组件配置检查出错',
                  content: `检查过程中发生错误: ${(error as Error).message}`,
                },
              ],
            })
          }
        }
      }),
    )
  }
}
