import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { InjectRepository } from '@nestjs/typeorm'
import axios from 'axios'
import _ from 'lodash'
import OpenAI, { ClientOptions } from 'openai'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { langBridgeStreamStr2Content } from 'src/utils/stream'
import { Repository } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import { AiLog } from './ai-log.entity'
import {
  AiSimpleChatReqDto,
  AiSimpleChatResDto,
  AiUpdateLogReqDto,
} from './ai.dto'

/**
 * openai sdk 选项
 */
interface OpenaiSdkOptions {
  /**
   * 初始化选项
   */
  initOptions: ClientOptions
  /**
   * 模型
   */
  model: string
}

/**
 * 获取 openai 客户端
 */
const getOpenai = _.once(
  (configService: ConfigService) =>
    new OpenAI(configService.get('ai.openaiSdkOptions.initOptions')),
)

/**
 * LangBridge 选项
 */
interface LangBridgeOptions {
  /**
   * 源
   */
  origin: string
  /**
   * 模型
   */
  model: string
  /**
   * 业务键值
   */
  bizKey: string
  /**
   * 操作人
   */
  operator: string
}

@Injectable()
export class AiService {
  /**
   * openai sdk 选项
   */
  private get _openaiSdkOptions(): OpenaiSdkOptions {
    return this.configService.get('ai.openaiSdkOptions') as OpenaiSdkOptions
  }

  /**
   * openai 客户端
   */
  private get _openai(): OpenAI {
    return getOpenai(this.configService)
  }

  /**
   * LangBridge 选项
   */
  private get _langBridgeOptions(): LangBridgeOptions {
    return this.configService.get('ai.langBridgeOptions') as LangBridgeOptions
  }

  public constructor(
    private readonly configService: ConfigService,
    @InjectRepository(AiLog)
    public readonly aiLogRepo: Repository<AiLog>,
  ) {}

  /**
   * openai sdk 简单聊天
   * @param aiSimpleChatReqDto AI 简单聊天请求 DTO
   * @param userCode 用户邮箱前缀
   */
  public async openaiSdkSimpleChat(
    { prompt }: AiSimpleChatReqDto,
    userCode: string,
  ): Promise<AiSimpleChatResDto> {
    const { model } = this._openaiSdkOptions
    const completion = await this._openai.chat.completions.create({
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      model,
    })
    const content = completion.choices[0].message.content
    if (!content) {
      throw new CommonException(ExceptionCodes.AI_INVALID_OUTPUT)
    }
    // 并行保存
    const uuid = this.asyncSaveLog(prompt, content, userCode)
    return { uuid, completion: content }
  }

  /**
   * LangBridge 内部调用
   * @param body 请求体
   */
  public async internalLangBridge(body: unknown): Promise<string> {
    const { origin } = this._langBridgeOptions
    const response = await axios.post(
      `${origin}/index/api/model/chat/completions/stream`,
      body,
    )
    const content = langBridgeStreamStr2Content(response.data)
    return content
  }

  /**
   * LangBridge 简单聊天
   * @param aiSimpleChatReqDto AI 简单聊天请求 DTO
   * @param userCode 用户邮箱前缀
   */
  public async langBridgeSimpleChat(
    { prompt }: AiSimpleChatReqDto,
    userCode: string,
  ): Promise<AiSimpleChatResDto> {
    const { origin, model, bizKey, operator } = this._langBridgeOptions
    const response = await axios.post(
      `${origin}/index/api/model/chat/completions/stream`,
      {
        model,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        bizKey,
        operator,
      },
    )
    const content = langBridgeStreamStr2Content(response.data)
    if (!content) {
      throw new CommonException(ExceptionCodes.AI_INVALID_OUTPUT)
    }
    // 并行保存
    const uuid = this.asyncSaveLog(prompt, content, userCode)
    return { uuid, completion: content }
  }

  /**
   * 异步保存日志
   * @param input 输入
   * @param output 输出
   * @param creator 创建者
   */
  public asyncSaveLog(input: string, output: string, creator: string): string {
    const uuid = uuidv4()
    this.saveLog(uuid, input, output, creator)
    return uuid
  }

  /**
   * 保存日志
   * @param uuid UUID
   * @param input 输入
   * @param output 输出
   * @param creator 创建者
   */
  public async saveLog(
    uuid: string,
    input: string,
    output: string,
    creator: string,
  ): Promise<AiLog> {
    const log = this.aiLogRepo.create({
      uuid,
      input,
      output,
      creator,
    })
    const savedLog = await this.aiLogRepo.save(log)
    return savedLog
  }

  /**
   * 更新日志
   * @param aiUpdateLogReqDto AI 更新日志请求 DTO
   */
  public async updateLog(aiUpdateLogReqDto: AiUpdateLogReqDto): Promise<void> {
    const { uuid, status } = aiUpdateLogReqDto
    const log = await this.aiLogRepo.findOne({ where: { uuid } })
    if (log) {
      log.status = status
      await this.aiLogRepo.save(log)
    }
  }
}
