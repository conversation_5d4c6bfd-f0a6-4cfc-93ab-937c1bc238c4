import { AiLogStatus } from '@ad/canal-ai'
import { ApiProperty } from '@nestjs/swagger'
import { bigNumberStringToIntTransformer } from 'src/utils/transform'
import { BeforeInsert, Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

/**
 * AI 日志
 */
@Entity({ name: 'ai_log' })
export class AiLog {
  @ApiProperty({
    description: 'id',
  })
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  public id: string

  @ApiProperty({
    description: 'UUID',
  })
  @Column({
    name: 'uuid',
    type: 'varchar',
    length: 255,
    comment: 'UUID',
  })
  public uuid: string

  @ApiProperty({
    description: '输入',
  })
  @Column({
    name: 'input',
    type: 'longtext',
    comment: '输入',
  })
  public input: string

  @ApiProperty({
    description: '输出',
  })
  @Column({
    name: 'output',
    type: 'longtext',
    comment: '输出',
  })
  public output: string

  @ApiProperty({
    description: '状态',
  })
  @Column({
    type: 'int',
    comment: '状态',
    enum: AiLogStatus,
  })
  public status: number

  @ApiProperty({
    description: '创建时间戳',
  })
  @Column({
    type: 'bigint',
    transformer: bigNumberStringToIntTransformer,
    name: 'created_at',
  })
  public createdAt: number

  @ApiProperty({
    description: '创建者',
  })
  @Column({
    type: 'varchar',
    length: 128,
  })
  public creator: string

  /**
   * 初始化
   */
  @BeforeInsert()
  public init(): void {
    if (!this.status) {
      this.status = AiLogStatus.DEFAULT
    }
    if (!this.createdAt) {
      this.createdAt = Date.now()
    }
  }
}
