import { Body, Controller, Post, Session, UseGuards } from '@nestjs/common'
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { UserSession } from 'src/types'
import {
  AiSimpleChatReqDto,
  AiSimpleChatResDto,
  AiUpdateLogReqDto,
} from './ai.dto'
import { AiService } from './ai.service'

@Controller('/rest/canal/ai')
@ApiTags('ai')
@Controller()
export class AiController {
  public constructor(private readonly aiService: AiService) {}

  @Post('__internal-lang-bridge')
  public async internalLangBridge(@Body() body: unknown): Promise<string> {
    return await this.aiService.internalLangBridge(body)
  }

  @Post('simple-chat')
  @ApiOperation({
    summary: 'AI 聊天',
    operationId: 'aiSimpleChat',
  })
  @ApiBody({
    type: AiSimpleChatReqDto,
  })
  @CommonApiResponse(AiSimpleChatResDto)
  @UseGuards(AuthGuard)
  public async simpleChat(
    @Session() userSession: UserSession,
    @Body() aiSimpleChatReqDto: AiSimpleChatReqDto,
  ): Promise<AiSimpleChatResDto> {
    return await this.aiService.langBridgeSimpleChat(
      aiSimpleChatReqDto,
      userSession.userInfo.userCode,
    )
  }

  @Post('update-log')
  @ApiOperation({
    summary: '更新 AI 日志',
    operationId: 'aiUpdateLog',
  })
  @ApiBody({
    type: AiUpdateLogReqDto,
  })
  @UseGuards(AuthGuard)
  public async updateLog(
    @Body() aiUpdateLogReqDto: AiUpdateLogReqDto,
  ): Promise<void> {
    await this.aiService.updateLog(aiUpdateLogReqDto)
  }
}
