import { AiLogStatus } from '@ad/canal-ai'
import { ApiProperty } from '@nestjs/swagger'
import { IsInt, IsNotEmpty, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'

/**
 * AI 简单聊天请求 DTO
 */
export class AiSimpleChatReqDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '提示词' })
  prompt: string
}

/**
 * AI 简单聊天响应 DTO
 */
export class AiSimpleChatResDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: 'UUID' })
  uuid: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '输出' })
  completion: string
}

/**
 * AI 更新日志请求 DTO
 */
export class AiUpdateLogReqDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: 'UUID' })
  uuid: string

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '状态' })
  status: AiLogStatus
}
