import { KCONF_KEY_FOR_CONFIG } from 'src/common/constants'
import kconf from 'src/common/kconf'

type DBConfig = {
  host: string
  port: number
  user: string
  password: string
}
type IConfig = {
  db: DBConfig
  openApi: {
    secretKey: string
    appKey: string
  }
}

export default async () => {
  try {
    const ret = (await kconf.getJSONValue<IConfig>(
      KCONF_KEY_FOR_CONFIG,
    )) as IConfig
    return ret
  } catch (e) {}
}
