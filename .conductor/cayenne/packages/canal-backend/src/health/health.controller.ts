import { <PERSON>, Get, Head, HttpStatus, Res } from '@nestjs/common'
import { ApiExcludeEndpoint } from '@nestjs/swagger'
import { Response } from 'express'

// 容器云心跳检测
@Controller('health')
export class HealthController {
  @Get()
  @ApiExcludeEndpoint()
  health(@Res() res: Response) {
    res.status(HttpStatus.OK).send('ok')
  }

  @Head()
  @ApiExcludeEndpoint()
  nginxHealth(@Res() res: Response) {
    res.status(HttpStatus.OK).send('ok')
  }
}
