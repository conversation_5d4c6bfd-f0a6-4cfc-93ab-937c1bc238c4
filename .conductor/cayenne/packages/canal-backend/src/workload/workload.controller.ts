import { Controller, Get } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { WorkloadService } from './workload.service'

@Controller('/rest/workload')
@ApiTags('workload')
export class WorkloadController {
  public constructor(protected readonly workloadService: WorkloadService) {}

  @ApiOperation({
    description: '获取所有绑定开发任务',
    operationId: 'getBindingDevTasks',
  })
  @Get('/binding-dev-task')
  public async getBindingDevTasks() {
    return await this.workloadService.getBindingDevTasks()
  }
}
