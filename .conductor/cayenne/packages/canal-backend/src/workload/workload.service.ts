import { Injectable } from '@nestjs/common'
import { ChangeModuleVersionService } from 'src/change-module-version/change-module-version.service'
import { ChangeService } from 'src/change/change.service'
import { ModuleVersionService } from 'src/module-version/module-version.service'

@Injectable()
export class WorkloadService {
  public constructor(
    private readonly changeService: ChangeService,
    private readonly changeModuleVersionService: ChangeModuleVersionService,
    private readonly moduleVersionService: ModuleVersionService,
  ) {}

  /**
   * 获取所有绑定开发任务
   */
  public async getBindingDevTasks() {
    const changes = await this.changeService.getAllPublished()
    const changeId2TeamIdMap = new Map(
      changes.map((c) => [c.changeId, c.teamId]),
    )
    const changeModuleVersions =
      await this.changeModuleVersionService.getListByChangeIds(
        changes.map((c) => c.changeId),
      )
    const moduleIdVersion2WorkloadMap = new Map<string, number>()
    await Promise.all(
      changeModuleVersions.map(async (cmv) => {
        moduleIdVersion2WorkloadMap.set(
          JSON.stringify([cmv.moduleId, cmv.moduleVersion]),
          await this.moduleVersionService.getWorkload(
            cmv.moduleId,
            cmv.moduleVersion,
          ),
        )
      }),
    )
    const ret: {
      lowCodeId: string
      teamTaskId: string
      workload: number
    }[] = []
    for (const cmv of changeModuleVersions) {
      const teamTaskId = changeId2TeamIdMap.get(cmv.changeId)
      const workload = moduleIdVersion2WorkloadMap.get(
        JSON.stringify([cmv.moduleId, cmv.moduleVersion]),
      )
      if (!teamTaskId || !workload) {
        continue
      }
      ret.push({
        lowCodeId: cmv.moduleId,
        teamTaskId,
        workload,
      })
    }
    return ret
  }
}
