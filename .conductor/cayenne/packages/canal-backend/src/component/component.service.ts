import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { isNil } from 'lodash'
import { BusinessDomainService } from 'src/business-domain/business-domain.service'
import { logger } from 'src/common/logger'
import { ComponentVersion } from 'src/component-version/component-version.entity'
import { ComponentVersionService } from 'src/component-version/component-version.service'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { DataSource, Repository } from 'typeorm'
import {
  ComponentDetail,
  ComponentListDto,
  CopyComponentDto,
  CreateComponentDto,
  DeleteComponentDto,
  UpdateComponentDto,
} from './component.dto'
import { ComponentEntity } from './component.entity'
@Injectable()
export class ComponentService {
  public constructor(
    private readonly dataSource: DataSource,
    private readonly componentVersionService: ComponentVersionService,
    private readonly businessDomainService: BusinessDomainService,
    @InjectRepository(ComponentEntity)
    public readonly componentRepo: Repository<ComponentEntity>,
  ) {}

  async getAll({
    businessDomainCode,
    pageNum,
    pageSize,
    keepDeleted = true,
    name,
  }: ComponentListDto) {
    let qb = this.componentRepo
      .createQueryBuilder('component')
      .where('component.domain_code = :businessDomainCode', {
        businessDomainCode,
      })
    if (!keepDeleted) {
      qb = qb.andWhere('component.is_deleted = 0')
    }
    if (name) {
      qb = qb.andWhere('component.name LIKE :name', {
        name: `%${name}%`,
      })
    }
    const [list, total] = await qb
      .orderBy('component.create_time', 'DESC')
      .skip((pageNum - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount()

    const componentList: ComponentDetail[] = []
    for (let i = 0; i < list.length; i++) {
      const component = list[i]
      const componentVersion =
        await this.componentVersionService.getLatestVersionByComponentId(
          component.id,
        )

      if (componentVersion) {
        componentList.push({
          ...component,
          isContainer: component.isContainer === 1,
          coverUrl: componentVersion.coverUrl as string,
          associatedComponentId: componentVersion.associatedComponentId,
          associatedComponentVersion:
            componentVersion.associatedComponentVersion,
          propsConfig: componentVersion.propsConfig,
          resourceUrl: componentVersion.resourceUrl,
          eventKey: componentVersion.eventKey,
          version: componentVersion.version,
          group: componentVersion.group,
        })
      }
    }

    return {
      list: componentList,
      total,
    }
  }

  async create(createComponentDto: CreateComponentDto, userCode: string) {
    const hasPermission = await this.businessDomainService.hasPermission(
      createComponentDto.businessDomainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    const component = new ComponentEntity()
    const now = new Date().getTime()

    component.businessDomainCode = createComponentDto.businessDomainCode
    component.name = createComponentDto.name
    component.type = createComponentDto.type
    component.descs = createComponentDto.descs
    component.isContainer = createComponentDto.isContainer ? 1 : 0
    component.createUser = userCode
    component.updateUser = userCode
    component.createTime = now
    component.updateTime = now

    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction()

    let newComponent: ComponentEntity | null = null

    try {
      newComponent = await queryRunner.manager.save(component)

      const componentVersion = new ComponentVersion()

      componentVersion.componentId = newComponent.id
      componentVersion.coverUrl = createComponentDto.coverUrl
      componentVersion.associatedComponentId =
        createComponentDto.associatedComponentId
      componentVersion.associatedComponentVersion =
        createComponentDto.associatedComponentVersion
      componentVersion.resourceUrl = createComponentDto.resourceUrl
      componentVersion.propsConfig = createComponentDto.propsConfig
      componentVersion.eventKey = createComponentDto.eventKey
      componentVersion.resourceUrl = createComponentDto.resourceUrl
      componentVersion.createUser = userCode
      componentVersion.updateUser = userCode
      componentVersion.createTime = now
      componentVersion.updateTime = now
      componentVersion.version = 1
      componentVersion.group = createComponentDto.group

      await queryRunner.manager.save(componentVersion)

      await queryRunner.commitTransaction()
    } catch (e) {
      logger.error('[Component Service] create error', e as unknown as Error)
      await queryRunner.rollbackTransaction()
      throw new CommonException(ExceptionCodes.Component_Create_Error)
    } finally {
      await queryRunner.release()
    }

    return newComponent.id
  }

  async update(updateComponentDto: UpdateComponentDto, userCode: string) {
    const component = await this.getDetailById(updateComponentDto.id)

    if (!component) {
      throw new CommonException(ExceptionCodes.Component_Not_Found)
    }

    const hasPermission = await this.businessDomainService.hasPermission(
      component.businessDomainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    if (!isNil(updateComponentDto.name)) {
      component.name = updateComponentDto.name
    }
    if (!isNil(updateComponentDto.type)) {
      component.type = updateComponentDto.type
    }
    if (!isNil(updateComponentDto.descs)) {
      component.descs = updateComponentDto.descs
    }
    if (!isNil(updateComponentDto.isContainer)) {
      component.isContainer = +!!updateComponentDto.isContainer
    }
    component.updateTime = Date.now()
    component.updateUser = userCode
    await this.componentRepo.save(component)

    await this.componentVersionService.create(updateComponentDto, userCode)
  }

  async delete(deleteComponentDto: DeleteComponentDto, userCode: string) {
    const component = await this.getDetailById(deleteComponentDto.id)
    if (!component) {
      throw new CommonException(ExceptionCodes.Component_Not_Found)
    }
    const hasPermission = await this.businessDomainService.hasPermission(
      component.businessDomainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    component.isDeleted = true
    component.updateUser = userCode
    await this.componentRepo.save(component)
  }

  async getDetailById(id: string) {
    return await this.componentRepo.findOneBy({ id })
  }

  async getDetailByIdWithVersion(
    id: string,
    version?: string,
  ): Promise<Partial<ComponentDetail>> {
    const component = await this.getDetailById(id)
    let componentVersion: ComponentVersion | null

    if (!component) {
      throw new CommonException(ExceptionCodes.Component_Not_Found)
    }

    if (version) {
      componentVersion =
        await this.componentVersionService.getDetailByComponent(id, version)
    } else {
      componentVersion =
        await this.componentVersionService.getLatestVersionByComponentId(id)
    }

    if (!componentVersion) {
      throw new CommonException(ExceptionCodes.Component_Not_Found)
    }

    return {
      ...component,
      isContainer: component.isContainer === 1,
      coverUrl: componentVersion.coverUrl,
      associatedComponentId: componentVersion.associatedComponentId,
      associatedComponentVersion: componentVersion.associatedComponentVersion,
      propsConfig: componentVersion.propsConfig,
      resourceUrl: componentVersion.resourceUrl,
      eventKey: componentVersion.eventKey,
      version: componentVersion.version,
      group: componentVersion.group,
    }
  }

  async getComponentCountByDomain(domainCode: string) {
    return await this.componentRepo.count({
      where: {
        businessDomainCode: domainCode,
      },
    })
  }
  /**
   * 判断是否有相同的组件
   * @param targetDomainCode
   * @param componentVersionArray
   * @returns
   */
  async findSameComponent(
    targetDomainCode: string,
    componentVersionArray: ComponentVersion[],
  ) {
    /**
     * 找出目标业务域的所有组件
     */
    const components = await this.componentRepo
      .createQueryBuilder('component')
      .where('component.domain_code = :targetDomainCode', {
        targetDomainCode,
      })
      .andWhere('component.is_deleted = 0')
      .getMany()

    const resourceUrls: { resource: string; id: string }[] = []
    const existenceMap: Record<string, boolean | string> = {}
    /**
     * 先假设都不存在
     */
    componentVersionArray.forEach((item) => {
      item.resourceUrl &&
        resourceUrls.push({ resource: item.resourceUrl, id: item.componentId })
      existenceMap[item.componentId] = false
    })

    for (let i = 0; i < components.length; i++) {
      // 最新版
      const latestComponentVersion =
        await this.componentVersionService.getLatestVersionByComponentId(
          components[i].id,
        )
      if (latestComponentVersion) {
        // 存在相同的
        const exist = resourceUrls.find(
          (item) => item.resource === latestComponentVersion?.resourceUrl,
        )
        // 找到的话先存一下
        if (exist) {
          existenceMap[exist.id] = latestComponentVersion?.componentId
        }
      }
    }
    return existenceMap
  }
  async copyComponent(copyComponentDto: CopyComponentDto, userCode: string) {
    /**
     * 需要有目标业务域的权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      copyComponentDto.targetDomainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    const fromComponentVersions: ComponentVersion[] = []
    const fromComponents: Record<
      string,
      {
        component: ComponentEntity
        version: ComponentVersion
      }
    > = {}
    for (let i = 0; i < copyComponentDto.components.length; i++) {
      const id = copyComponentDto.components[i]
      const component = await this.getDetailById(id)
      if (component) {
        /**找到要复制的组件的最新的版本 */
        const componentVersion =
          await this.componentVersionService.getLatestVersionByComponentId(
            component.id,
          )
        if (componentVersion) {
          fromComponentVersions.push(componentVersion)
          // 先保存, from的信息
          fromComponents[component.id] = {
            component,
            version: componentVersion,
          }
        }
      }
    }
    // 判断是否有一样的组件存在
    const checkResult = await this.findSameComponent(
      copyComponentDto.targetDomainCode,
      fromComponentVersions,
    )
    for (const key in checkResult) {
      // 没找到的就新建
      if (checkResult[key] === false) {
        const fromInfo = fromComponents[key]
        const { name, type, descs, isContainer } = fromInfo.component
        const {
          coverUrl,
          associatedComponentId,
          associatedComponentVersion,
          resourceUrl,
          propsConfig,
          group,
          eventKey,
        } = fromInfo.version
        const createdComponentId = await this.create(
          {
            name,
            type,
            descs,
            isContainer: isContainer === 1,
            coverUrl: coverUrl || '',
            associatedComponentId,
            associatedComponentVersion,
            resourceUrl,
            group,
            eventKey,
            propsConfig,
            businessDomainCode: copyComponentDto.targetDomainCode,
          },
          userCode,
        )
        if (createdComponentId) {
          checkResult[key] = createdComponentId
        }
      }
    }
    return checkResult
  }

  /**
   * 获取当前业务域对应的物料的组件库版本信息【最新的版本信息】，以及对应的单个组件的版本信息
   * @param domainCode
   */
  async getStructureVersionInfo(domainCode: string, libraryId: string) {
    const qb = this.componentRepo
      .createQueryBuilder('component')
      .where('component.domain_code = :domainCode', {
        domainCode,
      })
      .andWhere('component.is_deleted = 0')

    const componentList = await qb
      .orderBy('component.create_time', 'DESC')
      .getMany()
    const materialLibraryIds = new Map<
      string,
      {
        id: string
        associatedComponentVersion: string | undefined
        version: number
        name: string
      }[]
    >()
    if (Array.isArray(componentList) && componentList.length > 0) {
      for (let i = 0; i < componentList.length; i++) {
        const component = componentList[i]
        //非删除的
        const componentVersion =
          await this.componentVersionService.getLatestVersionByComponentId(
            component.id,
          )
        if (componentVersion && componentVersion.associatedComponentId) {
          const materialInfo = JSON.parse(
            componentVersion.associatedComponentId,
          )
          if (Array.isArray(materialInfo) && materialInfo.length == 2) {
            const associatedComponentLibraryId = materialInfo[0]
            const associatedComponentName = materialInfo[1]

            // 如果只是查询特定组件库的，其他就不必加入返回体
            if (
              (libraryId &&
                Number(associatedComponentLibraryId) === Number(libraryId)) ||
              !libraryId
            ) {
              const mapItemValue = {
                id: componentVersion.componentId,
                associatedComponentVersion:
                  componentVersion.associatedComponentVersion,
                associatedComponentName,
                version: componentVersion.version,
                name: component.name,
              }
              if (!materialLibraryIds.has(associatedComponentLibraryId)) {
                materialLibraryIds.set(associatedComponentLibraryId, [
                  mapItemValue,
                ])
              } else {
                materialLibraryIds
                  .get(associatedComponentLibraryId)
                  ?.push(mapItemValue)
              }
            }
          }
        }
      }
      return Object.fromEntries(materialLibraryIds)
    }
  }
}
