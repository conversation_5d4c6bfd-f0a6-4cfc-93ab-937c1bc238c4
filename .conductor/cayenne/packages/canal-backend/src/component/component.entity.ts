import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'component' })
export class ComponentEntity {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: '组件id',
    unsigned: true,
  })
  id: string

  @Column({
    type: 'varchar',
    length: 100,
    comment: '组件名称',
  })
  name: string

  @Column({
    type: 'tinyint',
    comment: '组件类型',
  })
  type: number

  @Column({
    type: 'varchar',
    length: 255,
    comment: '组件描述',
  })
  descs?: string

  @Column({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    comment: '业务域code',
  })
  businessDomainCode: string

  @Column({
    name: 'is_container',
    type: 'tinyint',
    comment: '是否是容器组件',
  })
  isContainer: number

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    select: false,
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number

  @Column({
    name: 'is_deleted',
    type: 'boolean',
    comment: '已删除，软删除',
  })
  isDeleted: boolean
}
