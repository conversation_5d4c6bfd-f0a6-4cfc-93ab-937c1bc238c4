import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import {
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
import { DBTablePageInfo } from 'src/common/dto'

class ComponentVersionDetail {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '示例图url' })
  readonly coverUrl: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiPropertyOptional({ description: '关联物料中心组件id' })
  readonly associatedComponentId?: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiPropertyOptional({ description: '关联物料中心组件版本' })
  readonly associatedComponentVersion?: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiPropertyOptional({ description: '资源url' })
  readonly resourceUrl?: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiPropertyOptional({ description: '组件分类，即物料 Schema 里的 group' })
  readonly group?: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiPropertyOptional({ description: '属性配置' })
  readonly propsConfig?: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiPropertyOptional({ description: '事件key' })
  readonly eventKey?: string
}

export class CreateComponentDto extends ComponentVersionDetail {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '组件名称' })
  readonly name: string

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '组件类型：web-0，H5-1，RN-2，Native-3' })
  readonly type: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域code' })
  readonly businessDomainCode: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiPropertyOptional({ description: '组件描述' })
  readonly descs?: string

  @IsBoolean({ message: ErrorMessage.IS_BOOLEAN })
  @ApiPropertyOptional({ description: '是否容器组件' })
  readonly isContainer?: boolean
}

export class UpdateComponentDto extends ComponentVersionDetail {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '组件id' })
  readonly id: string
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '组件名称' })
  readonly name: string

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '组件类型：web-0，H5-1，RN-2，Native-3' })
  readonly type: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiPropertyOptional({ description: '组件描述' })
  readonly descs?: string

  @IsBoolean({ message: ErrorMessage.IS_BOOLEAN })
  @ApiPropertyOptional({ description: '是否容器组件' })
  readonly isContainer?: boolean
}

export class CreateOrUpdateComponentResponse {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '组件id' })
  readonly id: string
}

export class ComponentListDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域code' })
  readonly businessDomainCode: string

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '页码' })
  readonly pageNum: number

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '分页大小' })
  readonly pageSize: number

  @IsBoolean({ message: ErrorMessage.IS_BOOLEAN })
  @ApiProperty({ description: '保留已删除的，默认为 true' })
  readonly keepDeleted?: boolean

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '组件名称模糊查询' })
  readonly name?: string
}

export class ComponentDetail extends CreateComponentDto {
  @ApiProperty({ description: '组件id' })
  readonly id: string

  @ApiProperty({ description: '组件版本' })
  readonly version: number
}

/**
 * 获取所有组件响应 DTO
 */
export class GetAllComponentsResponseDto {
  @ApiProperty({ description: '组件列表', type: [ComponentDetail] })
  readonly list: ComponentDetail[]

  @ApiProperty({ description: '分页信息', type: DBTablePageInfo })
  readonly pageInfo: DBTablePageInfo
}

/**
 * 删除组件 DTO
 */
export class DeleteComponentDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '组件 id' })
  readonly id: string
}

export class CopyComponentDto {
  @ApiProperty({ description: '目标业务域code' })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  readonly targetDomainCode: string
  @ApiProperty({ description: '组件id列表' })
  readonly components: string[]
}

export class ReferenceListDto {
  /** 业务域code */
  readonly domainCode: string
  /** 组件id */
  readonly id: string
  /** 组件版本 */
  readonly version: string
  /** 页码 */
  readonly pageNum: number
  /** 分页大小 */
  readonly pageSize: number
}
