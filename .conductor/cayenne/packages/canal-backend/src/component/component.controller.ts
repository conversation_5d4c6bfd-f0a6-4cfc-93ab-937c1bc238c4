import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Session,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { UserSession } from 'src/types'
import {
  ComponentDetail,
  ComponentListDto,
  CopyComponentDto,
  CreateComponentDto,
  CreateOrUpdateComponentResponse,
  DeleteComponentDto,
  GetAllComponentsResponseDto,
  UpdateComponentDto,
} from './component.dto'
import { ComponentService } from './component.service'

@Controller('/rest/canal/component')
@ApiTags('component')
@Controller()
export class ComponentController {
  public constructor(private componentService: ComponentService) {}

  @Post('list')
  @ApiBody({
    type: ComponentListDto,
  })
  @ApiOperation({
    summary: '获取组件列表',
    operationId: 'getAllComponentsUsingPost',
  })
  @CommonApiResponse(GetAllComponentsResponseDto)
  @UseGuards(AuthGuard)
  public async getAllComponentsUsingPost(
    @Body() componentListDto: ComponentListDto,
  ) {
    const { list, total } = await this.componentService.getAll(componentListDto)

    return {
      list,
      pageInfo: {
        total,
        pageSize: componentListDto.pageSize,
        pageNum: componentListDto.pageNum,
      },
    }
  }

  @Post('create')
  @ApiBody({
    type: CreateComponentDto,
  })
  @ApiOperation({
    summary: '创建组件',
    operationId: 'createComponentUsingPost',
  })
  @CommonApiResponse(CreateOrUpdateComponentResponse)
  @UseGuards(AuthGuard)
  public async createComponentUsingPost(
    @Session() userSession: UserSession,
    @Body() createComponentDto: CreateComponentDto,
  ) {
    const componentId = await this.componentService.create(
      createComponentDto,
      userSession.userInfo.userCode,
    )

    return {
      id: componentId,
    }
  }

  @Post('update')
  @ApiBody({
    type: UpdateComponentDto,
  })
  @ApiOperation({
    summary: '更新组件',
    operationId: 'updateComponentUsingPost',
  })
  @CommonApiResponse(CreateOrUpdateComponentResponse)
  @UseGuards(AuthGuard)
  public async updateComponentUsingPost(
    @Session() userSession: UserSession,
    @Body() updateComponentDto: UpdateComponentDto,
  ) {
    await this.componentService.update(
      updateComponentDto,
      userSession.userInfo.userCode,
    )
  }

  @Post('delete')
  @ApiBody({
    type: DeleteComponentDto,
  })
  @ApiOperation({
    summary: '删除组件',
    operationId: 'updateComponentUsingPost',
  })
  @CommonApiResponse(CreateOrUpdateComponentResponse)
  @UseGuards(AuthGuard)
  public async deleteComponentUsingPost(
    @Session() userSession: UserSession,
    @Body() deleteComponentDto: DeleteComponentDto,
  ) {
    await this.componentService.delete(
      deleteComponentDto,
      userSession.userInfo.userCode,
    )
    return deleteComponentDto
  }

  @Get('detail')
  @ApiOperation({
    description: '获取组件详情',
    operationId: 'getComponentDetail',
  })
  @ApiQuery({
    name: 'id',
    required: true,
    description: '组件id',
    type: String,
  })
  @ApiQuery({
    name: 'version',
    required: false,
    description: '组件版本，不传返回最新版本',
    type: String,
  })
  @CommonApiResponse(ComponentDetail)
  async getComponentDetail(
    @Query('id') id: string,
    @Query('version') version: string,
  ) {
    return await this.componentService.getDetailByIdWithVersion(id, version)
  }

  @Post('copy')
  @ApiOperation({
    summary: '复制组件',
    operationId: 'copyComponent',
  })
  @ApiBody({
    type: CopyComponentDto,
  })
  @UseGuards(AuthGuard)
  public async copyComponentUsingPost(
    @Session() userSession: UserSession,
    @Body() copyComponentDto: CopyComponentDto,
  ) {
    return await this.componentService.copyComponent(
      copyComponentDto,
      userSession.userInfo.userCode,
    )
  }

  @Get('structure/version/info')
  @ApiOperation({
    summary: '获取结构化的组件库版本信息',
    operationId: 'structureVersionInfo',
  })
  @ApiQuery({
    name: 'domainCode',
    required: true,
    description: '业务域code',
    type: String,
  })
  @ApiQuery({
    name: 'libraryId',
    required: false,
    description: '特定的物料库的id',
    type: String,
  })
  @UseGuards(AuthGuard)
  async getStructureVersionInfo(
    @Query('domainCode') domainCode: string,
    @Query('libraryId') libraryId: string,
  ) {
    return await this.componentService.getStructureVersionInfo(
      domainCode,
      libraryId,
    )
  }
}
