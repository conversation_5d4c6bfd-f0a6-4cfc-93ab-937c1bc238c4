import { ApiProperty } from '@nestjs/swagger'
import { IsInt, IsNotEmpty, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
import { DBTablePageInfo } from 'src/common/dto'

export class ModuleTemplateDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模版名称' })
  readonly name: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '模版描述' })
  readonly desc: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: 'schema内容' })
  readonly content: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '来源模块id' })
  readonly moduleId: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '来源模块版本' })
  readonly version: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '示例图' })
  readonly demoImage: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域' })
  readonly domainCode: string

  @ApiProperty()
  createUser?: string
  @ApiProperty()
  updateUser?: string
  @ApiProperty()
  createTime?: number
  @ApiProperty()
  updateTime?: number
}

export class SaveResponseDto {
  @ApiProperty({ description: 'id' })
  readonly id: string
}

export class ModuleTemplateListDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域code' })
  readonly domainCode: string

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '页码' })
  readonly pageNum: number

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '分页大小' })
  readonly pageSize: number
}

export class TemplateDetail extends ModuleTemplateDto {
  @ApiProperty({ description: '模版id' })
  readonly id: string
}

export class GetAllComponentsResponseDto {
  @ApiProperty({ description: '模版列表', type: [TemplateDetail] })
  readonly list: TemplateDetail[]

  @ApiProperty({ description: '分页信息', type: DBTablePageInfo })
  readonly pageInfo: DBTablePageInfo
}

export class AddToTemplateDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模版名称' })
  readonly name: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '模版描述' })
  readonly desc: string
  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '来源模块id' })
  readonly moduleId: string
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '来源模块版本' })
  readonly version: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '示例图' })
  readonly demoImage: string
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域' })
  readonly domainCode: string
}

export class AddResponseDto {
  @ApiProperty({ description: '模版id' })
  readonly id: string
}

export class OverwriteTemplateDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '来源模块id' })
  readonly moduleId: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '来源模块版本' })
  readonly version: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域' })
  readonly domainCode: string

  @ApiProperty({ description: '现有模版id' })
  readonly templateId: string
}
