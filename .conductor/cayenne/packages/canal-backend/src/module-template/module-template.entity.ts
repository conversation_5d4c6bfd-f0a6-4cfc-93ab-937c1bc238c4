import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'module_template' })
export class ModuleTemplateEntity {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  id: string

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    comment: '名称',
  })
  name: string

  @Column({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    comment: '业务域code',
  })
  domainCode: string

  @Column({
    name: 'desc',
    type: 'varchar',
    length: 255,
    comment: '描述',
  })
  desc: string

  @Column({
    name: 'version_context',
    type: 'text',
    comment: 'schema内容',
  })
  content: string

  @Column({
    name: 'module_id',
    type: 'char',
    length: 36,
    comment: '来源模块id',
  })
  moduleId: string

  @Column({
    name: 'module_version',
    type: 'bigint',
    comment: '来源版本',
  })
  version: number

  @Column({
    name: 'demo_image_url',
    type: 'varchar',
    length: 255,
    comment: '示例图',
  })
  demoImage: string

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    select: false,
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number
}
