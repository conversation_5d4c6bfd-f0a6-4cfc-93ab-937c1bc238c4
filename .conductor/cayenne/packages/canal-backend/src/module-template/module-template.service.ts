import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { UserSession } from 'src/types'
import { Repository } from 'typeorm'
import { ModuleVersionService } from '../module-version/module-version.service'
import {
  AddToTemplateDto,
  ModuleTemplateListDto,
  OverwriteTemplateDto,
} from './module-template.dto'
import { ModuleTemplateEntity } from './module-template.entity'

@Injectable()
export class ModuleTemplateService {
  public constructor(
    private readonly moduleVersionService: ModuleVersionService,
    @InjectRepository(ModuleTemplateEntity)
    public readonly moduleTemplateRepo: Repository<ModuleTemplateEntity>,
  ) {}

  async create(addToTemplateDto: AddToTemplateDto, userSession: UserSession) {
    // 查找是否有重名的模版存在
    const same = await this.moduleTemplateRepo.find({
      where: {
        name: addToTemplateDto.name,
        domainCode: addToTemplateDto.domainCode,
      },
    })
    if (same.length) {
      throw new CommonException(ExceptionCodes.Module_Template_Duplicate_Name)
    }
    // 获取模块内容
    const moduleVersion = await this.moduleVersionService.getDetailByModule(
      addToTemplateDto.moduleId,
      addToTemplateDto.version,
    )
    if (!moduleVersion) {
      throw new CommonException(ExceptionCodes.Module_Template_Create_Fail)
    }
    const createTime = new Date().getTime()
    const moduleTemplateDto = new ModuleTemplateEntity()

    moduleTemplateDto.content = moduleVersion.content
    moduleTemplateDto.moduleId = moduleVersion.moduleId
    moduleTemplateDto.version = moduleVersion.version
    moduleTemplateDto.domainCode = addToTemplateDto.domainCode
    moduleTemplateDto.name = addToTemplateDto.name
    moduleTemplateDto.desc = addToTemplateDto.desc
    moduleTemplateDto.demoImage = addToTemplateDto.demoImage
    moduleTemplateDto.createTime = createTime
    moduleTemplateDto.updateTime = createTime
    moduleTemplateDto.createUser = userSession.userInfo?.userCode
    moduleTemplateDto.updateUser = userSession.userInfo?.userCode
    try {
      return await this.moduleTemplateRepo.save(moduleTemplateDto)
    } catch (err) {
      throw new CommonException(ExceptionCodes.Module_Template_Create_Fail)
    }
  }
  async overwrite(
    overwriteDto: OverwriteTemplateDto,
    userSession: UserSession,
  ) {
    // 获取模块内容
    const moduleVersion = await this.moduleVersionService.getDetailByModule(
      overwriteDto.moduleId,
      overwriteDto.version,
    )
    if (!moduleVersion) {
      throw new CommonException(ExceptionCodes.Module_Template_Create_Fail)
    }
    const updateTime = new Date().getTime()

    // update
    return await this.moduleTemplateRepo.update(
      {
        id: overwriteDto.templateId,
      },
      {
        content: moduleVersion.content,
        version: moduleVersion.version,
        moduleId: moduleVersion.moduleId,
        updateTime,
        updateUser: userSession.userInfo?.userCode,
        domainCode: overwriteDto.domainCode,
      },
    )
  }

  async getAll({ domainCode, pageNum, pageSize }: ModuleTemplateListDto) {
    const [list, total] = await this.moduleTemplateRepo
      .createQueryBuilder('moduleTemplate')
      .select([
        'moduleTemplate.id',
        'moduleTemplate.name',
        'moduleTemplate.desc',
        'moduleTemplate.moduleId',
        'moduleTemplate.version',
        'moduleTemplate.demoImage',
      ])
      .where('moduleTemplate.domain_code = :domainCode', {
        domainCode,
      })
      .orderBy('moduleTemplate.create_time', 'DESC')
      .skip((pageNum - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount()
    return {
      list: list,
      total,
    }
  }
  async findOne({ id }: { id: string }) {
    return await this.moduleTemplateRepo.findOneBy({
      id,
    })
  }
}
