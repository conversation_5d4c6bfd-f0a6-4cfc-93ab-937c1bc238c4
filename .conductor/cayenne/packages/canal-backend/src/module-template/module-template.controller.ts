import { Body, Controller, Post, Session, UseGuards } from '@nestjs/common'
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { UserSession } from 'src/types'
import {
  AddResponseDto,
  AddToTemplateDto,
  GetAllComponentsResponseDto,
  ModuleTemplateListDto,
  OverwriteTemplateDto,
} from './module-template.dto'
import { ModuleTemplateService } from './module-template.service'

@Controller('/rest/canal/template')
@ApiTags('module-template')
@Controller()
@UseGuards(AuthGuard)
export class ModuleTemplateController {
  public constructor(private moduleTemplateService: ModuleTemplateService) {}

  @Post('list')
  @ApiBody({
    type: ModuleTemplateListDto,
  })
  @ApiOperation({
    summary: '获取模版列表',
    operationId: 'getAllModuleTemplate',
  })
  @CommonApiResponse(GetAllComponentsResponseDto)
  public async getAllComponentsUsingPost(
    @Body() templateListDto: ModuleTemplateListDto,
  ) {
    const { list, total } =
      await this.moduleTemplateService.getAll(templateListDto)

    return {
      list,
      pageInfo: {
        total,
        pageSize: templateListDto.pageSize,
        pageNum: templateListDto.pageNum,
      },
    }
  }
  @Post('add')
  @ApiBody({
    type: AddToTemplateDto,
  })
  @ApiOperation({
    summary: '添加模版',
    operationId: 'addTemplate',
  })
  @CommonApiResponse(AddResponseDto)
  public async addTemplateMethod(
    @Session() userSession: UserSession,
    @Body() addToModuleDto: AddToTemplateDto,
  ) {
    const moduleTemplate = await this.moduleTemplateService.create(
      addToModuleDto,
      userSession,
    )

    return {
      id: moduleTemplate.id,
    }
  }

  @Post('overwrite')
  @ApiBody({
    type: OverwriteTemplateDto,
  })
  @ApiOperation({
    summary: '覆盖模版',
    operationId: 'overwriteTemplate',
  })
  @CommonApiResponse(AddResponseDto)
  public async overwriteTemplateMethod(
    @Session() userSession: UserSession,
    @Body() overwriteModuleDto: OverwriteTemplateDto,
  ) {
    return await this.moduleTemplateService.overwrite(
      overwriteModuleDto,
      userSession,
    )
  }
}
