import { HttpException, HttpStatus, ValidationPipe } from '@nestjs/common'
import { HttpAdapterHost, NestFactory } from '@nestjs/core'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import compression from 'compression'
import expressMySqlSession from 'express-mysql-session'
import * as session from 'express-session'
import helmet from 'helmet'

import { ConfigService } from '@nestjs/config'
import bodyParser from 'body-parser'
import { AppModule } from './app.module'
import { PORT } from './common/constants'
import { isLocal } from './common/env'
import { GlobalExceptionsFilter } from './exceptions/exception-filter'
import { ErrorsInterceptor } from './interceptor/error.interceptor'
import { TransformInterceptor } from './interceptor/response.interceptor'
import { initRedis } from './redis'

async function run(): Promise<void> {
  const app = await NestFactory.create(AppModule, {
    cors: true,
  })

  // 前置保障所有内容都使用gzip
  app.use(compression())

  const configService = app.get(ConfigService)
  /**
   * save session
   */
  const MySQLStore = expressMySqlSession(session)
  const sessionStore = new MySQLStore({
    ...configService.get('db'),
    database: 'canal_manage',
    // 登录有效期1天
    expiration: 24 * 60 * 60 * 1000,
    // 默认不创建
    createDatabaseTable: false,
  })

  initRedis(configService)

  app.use(
    session.default({
      secret: 'session_canal_secret',
      name: 'canal.connect.sid',
      store: sessionStore,
      resave: false,
      saveUninitialized: false,
      ...(isLocal
        ? {}
        : {
            cookie: {
              secure: true,
              sameSite: 'none',
            },
            proxy: true,
          }),
    }),
  )

  const httpAdapter = app.get(HttpAdapterHost)
  app.useGlobalFilters(new GlobalExceptionsFilter(httpAdapter))
  app.useGlobalInterceptors(new TransformInterceptor())
  app.useGlobalInterceptors(new ErrorsInterceptor())
  app.useGlobalPipes(
    new ValidationPipe({
      skipMissingProperties: true,
      exceptionFactory: (validationErrors = []) => {
        throw new HttpException(
          {
            result: 400,
            msg: Object.values(validationErrors[0]?.constraints || {})[0],
          },
          HttpStatus.OK,
        )
      },
    }),
  )

  // 设置请求体大小限制
  app.use(bodyParser.json({ limit: '5mb' }))
  app.use(bodyParser.urlencoded({ limit: '5mb', extended: true }))

  /**
   * set up swagger document
   */
  const swaggerOptions = new DocumentBuilder()
    .setTitle('大运河平台')
    .setDescription('大运河接口描述')
    .setVersion('1.0')
    .build()

  const document = SwaggerModule.createDocument(app, swaggerOptions)

  SwaggerModule.setup('/rest/swagger', app, document)

  /**
   * set up middleware
   */
  app.use(helmet())

  app.listen(PORT)
}

run().catch((e) => {
  console.log('run-error', e)
})
