import { CommonRes } from '@ad/canal-shared'
import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios, { AxiosResponse } from 'axios'
import qs from 'querystring'
import { logger } from 'src/common/logger'
import {
  GetMaterialPlatformComponentDetailReq,
  GetMaterialPlatformComponentDetailRes,
  SearchMaterialPlatformComponentReq,
  SearchMaterialPlatformComponentRes,
} from './types'

function now(): number {
  return +new Date()
}

@Injectable()
export class OpenApiService {
  private appKey = ''
  private secretKey = ''
  private url = ''

  private accessToken = ''
  private accessTokenExpire = 0

  constructor(protected readonly configService: ConfigService) {
    const config = configService.get('openApi') as {
      appKey: string
      secretKey: string
      url: string
    }
    this.secretKey = config.secretKey || ''
    this.appKey = config.appKey || ''
    this.url = config.url || ''
  }

  /**
   * 获取accessToken 赋值到内存中
   */
  async getAccessToken() {
    const appKey = this.appKey
    const urlPrefix = this.url
    const param = [`appKey=${appKey}`, `secretKey=${this.secretKey}`].join('&')
    const url = `${urlPrefix}token/get?${param}`
    const ret = await axios.get(url)

    logger.info('[Open Api getAccessToken] 创建新token', {
      res: JSON.stringify(ret?.data),
    })

    if (ret?.data?.code === 0) {
      this.accessToken = ret.data?.result?.accessToken || ''
      const expireTime = ret.data?.result?.expireTime || 0
      this.accessTokenExpire = now() + expireTime * 1000
    }
    return false
  }

  /**
   * 获取带accessToken请求的请求句柄
   */
  async getRequestHandler() {
    let accessToken = this.accessToken
    const accessTokenExpire = this.accessTokenExpire
    const urlPrefix = this.url
    // 检查内容是否有 是否过期
    if (!(accessToken && now() < accessTokenExpire)) {
      logger.info('[Open Api] getRequestHandler，创建新token', {
        accessToken,
        accessTokenExpire,
      })
      // 没有获取
      await this.getAccessToken()
      accessToken = this.accessToken
    }
    // 二次检查
    if (!accessToken) {
      logger.error(
        '[Open Api] getRequestHandler 创建新token error',
        new Error('无法创建请求对象'),
        {
          accessToken,
          accessTokenExpire,
        },
      )
      return false
    }
    const axiosInstance = axios.create({
      baseURL: urlPrefix,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      timeout: 5000,
    })
    return axiosInstance
  }

  /**
   * 获取用户信息
   * @param username
   */
  async getUserInfo(username: string) {
    logger.info('[Open Api] getUserInfo', {
      username,
    })
    try {
      const request = await this.getRequestHandler()
      if (!request) return false
      const param = [`username=${username}`].join('&')
      const url = `mdata/v1/persons/base?${param}`
      const ret = await request.get(url)
      if (ret?.status === 200) {
        return ret.data as {
          name: string
          username: string
          email: string
          photo: string
          photoMax: string
          display_name: string
          zeroDeptCode: string
          zeroDeptName: string
        }
      }
    } catch (error: any) {
      logger.error('[Open Api] getUserInfo error', error, {
        username,
      })
    }
    return false
  }

  /**
   * 获取用户信息
   * @param username
   */
  async getUserInfoByKim(username: string) {
    logger.info('[Open Api] getUserInfoByKim', {
      username,
    })
    try {
      const request = await this.getRequestHandler()
      if (!request) return false
      const url = `/openapi/v2/user/user/${username}`
      const ret = await request.get(url)
      console.log('getUserInfoByKim ret', ret)
      if (ret?.status === 200 && ret?.data?.data) {
        return ret?.data?.data as {
          name: string
          username: string
          email: string
          avatarUrl: string
        }
      }
    } catch (error: any) {
      console.error('getUserInfoByKim error', error)
      logger.error('[Open Api] getUserInfoByKim error', error, {
        username,
      })
    }
    return false
  }
  /**
   * 获取team任务详细信息
   */
  async getTeamDetail(teamId: string, operator: string) {
    logger.info('[Open Api] /pm/api/no-ba/external/task/taskInfos', {
      teamId,
    })
    try {
      const request = await this.getRequestHandler()
      if (!request) return false

      const url = `/pm/api/no-ba/external/task/taskInfos?taskIds=${teamId}&operator=${operator}`

      const ret = await request.get(url)
      logger.info('[Open Api] /pm/api/no-ba/external/task/taskInfos', ret)
      if (ret?.status === 200 && ret?.data) {
        return ret.data
      }
    } catch (error: any) {
      logger.error('[Open Api] /pm/api/no-ba/external/task/taskInfos', error, {
        teamId,
      })
    }
  }
  /**
   * 获取team任务简单信息
   */
  async getTeamSimpleDetail(teamId: string, operator: string) {
    logger.info('[Open Api] /pm/api/no-ba/external/task/taskInfo', {
      teamId,
    })
    try {
      const request = await this.getRequestHandler()
      if (!request) return false

      const url = `/pm/api/no-ba/external/task/taskInfo?taskId=${teamId}&operator=${operator}`

      const ret = await request.get(url)
      logger.info('[Open Api] /pm/api/no-ba/external/task/taskInfo', ret)
      if (ret?.status === 200 && ret?.data) {
        return ret.data
      }
    } catch (error: any) {
      logger.error('[Open Api] /pm/api/no-ba/external/task/taskInfo', error, {
        teamId,
      })
    }
  }

  /**
   * 搜索物料平台组件
   * @param req 请求
   */
  async searchMaterialPlatformComponent(
    req: SearchMaterialPlatformComponentReq,
  ): Promise<SearchMaterialPlatformComponentRes> {
    logger.info('[Open Api] /openapi/component/list', {
      req,
    })
    try {
      const request = await this.getRequestHandler()
      if (!request) {
        throw new Error('无法创建请求')
      }

      const url = `/openapi/component/list?${qs.stringify({ ...req })}`

      const ret = await request.get<SearchMaterialPlatformComponentRes>(url)
      logger.info('[Open Api] /openapi/component/list', ret)
      assertCommonAxiosResponse(ret)
      return ret.data
    } catch (error) {
      console.log('searchMaterialPlatformComponent err', error)
      logger.error('[Open Api] /openapi/component/list', error as Error, req)
      throw error
    }
  }

  /**
   * 获取物料平台组件详情
   * @param req 请求
   */
  async getMaterialPlatformComponentDetail(
    req: GetMaterialPlatformComponentDetailReq,
  ): Promise<GetMaterialPlatformComponentDetailRes> {
    logger.info('[Open Api] /openapi/component/detail', {
      req,
    })
    try {
      const request = await this.getRequestHandler()
      if (!request) {
        throw new Error('无法创建请求')
      }

      const url = `/openapi/component/detail?${qs.stringify({ ...req })}`

      const ret = await request.get<GetMaterialPlatformComponentDetailRes>(url)
      logger.info('[Open Api] /openapi/component/detail', ret)
      assertCommonAxiosResponse(ret)
      return ret.data
    } catch (error) {
      logger.error('[Open Api] /openapi/component/detail', error as Error, req)
      throw error
    }
  }
}

/**
 * 断言常用 axios 响应
 * @param res axios 响应
 */
function assertCommonAxiosResponse<T>(res: AxiosResponse<CommonRes<T>>): void {
  if (res.status !== 200) {
    throw new Error(`无效请求状态 status: ${res.status}`)
  }
  if (res.data.result !== 1) {
    throw new Error(`无效请求状态 result: ${res.data.result}`)
  }
}
