import { CommonRes } from '@ad/canal-shared'

/**
 * 搜索物料平台组件请求
 */
export interface SearchMaterialPlatformComponentReq {
  /**
   * 代码类型
   */
  codeType: string
  /**
   * 搜索字符串
   */
  search: string
  /**
   * 分页大小，不传不分页
   */
  pageSize?: number
  /**
   * 当前页码
   */
  currentPage?: number
}

/**
 * 搜索物料平台组件响应
 */
export type SearchMaterialPlatformComponentRes = CommonRes<
  MaterialPlatformComponentSummary[]
>

/**
 * 搜索物料平台组件响应数据
 */
export interface SearchMaterialPlatformComponentResData {
  /**
   * 列表
   */
  list: MaterialPlatformComponentSummary[]
  /**
   * 总数
   */
  total: number
}

/**
 * 物料平台组件简介
 */
export interface MaterialPlatformComponentSummary {
  id: number
  name: string
  displayName: string
  imgs: string | string[]
  version: string
  description: string
  createAt: number
  updateAt: number
  materialId: number
  packageName: string
  packageType: 'single' | 'multiple'
}

/**
 * 获取物料平台组件详情请求
 */
export interface GetMaterialPlatformComponentDetailReq {
  /**
   * 物料 ID
   */
  materialId: number
  /**
   * 版本
   */
  version: string
  /**
   * 组件名
   */
  componentName: string
}

/**
 * 获取物料平台组件详情响应
 */
export type GetMaterialPlatformComponentDetailRes =
  CommonRes<MaterialPlatformComponentDetail>

/**
 * 物料平台组件详情
 */
export interface MaterialPlatformComponentDetail {
  id: number
  materialId: number
  createAt: number
  updateAt: number
  version: string
  name: string
  type: 'component' | 'block' | 'template'
  packageDescription: string
  displayName: string
  description: string
  imgs: string | string[]
  umdUrls: string[]
  dependencies: {
    [key: string]: string
  }
  devDependencies: {
    [key: string]: string
  }
  peerDependencies: {
    [key: string]: string
  }
  props: {
    type: string
    comments: string
    optional: boolean
    identifier: string
    defaultValue: string
  }[]
  versionList: string[]
  schema: Record<string, unknown>
}
