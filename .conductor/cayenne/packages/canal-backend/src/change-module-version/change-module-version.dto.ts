import { ApiProperty } from '@nestjs/swagger'

export class ChangeModuleVersionDto {
  @ApiProperty()
  changeId: string
  @ApiProperty()
  moduleId: string
  @ApiProperty()
  moduleVersion: number
  @ApiProperty()
  checkoutModuleVersion: number
  @ApiProperty()
  rollbackFrom: number
  @ApiProperty()
  createUser: string
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateUser: string
  @ApiProperty()
  updateTime: number
}
