import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { In, Repository } from 'typeorm'
import { ChangeModuleVersion } from './change-module-version.entity'

@Injectable()
export class ChangeModuleVersionService {
  public constructor(
    @InjectRepository(ChangeModuleVersion)
    public readonly changeModuleVersionRepo: Repository<ChangeModuleVersion>,
  ) {}

  async getListByChangeId(changeId: string) {
    return this.changeModuleVersionRepo
      .createQueryBuilder('changeModuleVersion')
      .where('changeModuleVersion.change_id = :changeId', { changeId })
      .orderBy('changeModuleVersion.createTime', 'DESC')
      .getMany()
  }

  public getListByChangeIds(changeIds: string[]) {
    return this.changeModuleVersionRepo.find({
      where: {
        changeId: In(changeIds),
      },
    })
  }

  async getModuleByChangeIdAndModuleId(changeId: string, moduleId: string) {
    return this.changeModuleVersionRepo
      .createQueryBuilder('changeModuleVersion')
      .where('changeModuleVersion.change_id = :changeId', { changeId })
      .andWhere('changeModuleVersion.module_id = :moduleId', { moduleId })
      .getMany()
  }

  async getByModuleIdAndVersion(moduleId: string, version: number) {
    return this.changeModuleVersionRepo
      .createQueryBuilder('changeModuleVersion')
      .where('changeModuleVersion.module_id = :moduleId', { moduleId })
      .andWhere('changeModuleVersion.module_version = :moduleVersion', {
        moduleVersion: version,
      })
      .getMany()
  }

  async getManyByModuleIdsAndVersion(
    conditions: { moduleId: string; version: string }[],
  ) {
    const whereConditions = conditions
      .map((condition, index) => {
        return `(changeModuleVersion.module_id = :moduleId${index} AND changeModuleVersion.module_version = :moduleVersion${index})`
      })
      .join(' OR ')
    // 创建查询
    const queryBuilder = this.changeModuleVersionRepo.createQueryBuilder(
      'changeModuleVersion',
    )
    // 添加条件
    conditions.forEach((condition, index) => {
      queryBuilder.setParameter(`moduleId${index}`, condition.moduleId)
      queryBuilder.setParameter(`moduleVersion${index}`, condition.version)
    })

    return await queryBuilder.where(whereConditions).getMany()
  }

  /**
   * 获取签出模块版本
   * @param moduleId 模块 id
   * @param moduleVersion 模块版本
   */
  async getCheckoutModuleVersion(
    moduleId: string,
    moduleVersion: number,
  ): Promise<number> {
    const versions = await this.getByModuleIdAndVersion(moduleId, moduleVersion)
    // TODO: checkoutModuleVersion 目前拿到的是 string 不是 ts 里标注的 number，后面需要改过来
    return +(versions[0]?.checkoutModuleVersion || 1) // 默认 1
  }

  /**
   * 获取变更模块版本
   * @param changeId 变更
   * @param moduleId 模块
   */
  async getChangeModuleVersion(changeId: string, moduleId: string) {
    return this.changeModuleVersionRepo.findOne({
      where: {
        changeId,
        moduleId,
      },
    })
  }

  async deleteModuleFromChange(changeId: string, moduleId: string) {
    return await this.changeModuleVersionRepo.delete({
      changeId,
      moduleId,
    })
  }
}
