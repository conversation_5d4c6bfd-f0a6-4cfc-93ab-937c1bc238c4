import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'change_module_version' })
export class ChangeModuleVersion {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  id: string

  @Column({
    name: 'module_id',
    type: 'bigint',
    comment: '模块id',
  })
  moduleId: string

  @Column({
    name: 'change_id',
    type: 'varchar',
    length: 255,
    comment: '变更id',
  })
  changeId: string

  @Column({
    name: 'module_version',
    type: 'bigint',
    comment: '当前模块的版本',
  })
  moduleVersion: number

  @Column({
    name: 'checkout_module_version',
    type: 'bigint',
    comment: '检出的模块的版本',
  })
  checkoutModuleVersion?: number

  @Column({
    name: 'rollback_from',
    type: 'bigint',
    comment: 'rollback版本，只有回滚的时候才会使用',
  })
  rollbackFrom?: number

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
    select: false,
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
    select: false,
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    select: false,
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    select: false,
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number
}
