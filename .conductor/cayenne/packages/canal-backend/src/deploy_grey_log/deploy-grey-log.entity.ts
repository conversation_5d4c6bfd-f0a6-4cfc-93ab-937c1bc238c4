import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity('deploy_grey_log')
export class DeployGreyLog {
  @PrimaryGeneratedColumn('increment', { type: 'bigint', unsigned: true })
  id: number

  @Column({
    type: 'bigint',
    nullable: true,
    comment: '灰度单ID',
    name: 'grey_id',
  })
  greyId: number

  @Column({
    type: 'varchar',
    length: 100,
    default: '',
    comment: '灰度策略类型',
    name: 'grey_type',
  })
  greyType: string

  @Column({
    type: 'varchar',
    length: 255,
    default: '',
    comment: '灰度值',
    name: 'grey_value',
  })
  greyValue: string

  @Column({
    type: 'varchar',
    length: 255,
    default: '',
    comment: '灰度键',
    name: 'grey_key',
  })
  greyKey: string

  @Column({
    type: 'varchar',
    length: 100,
    default: '',
    comment: '创建者',
    name: 'create_user',
  })
  createUser: string

  @Column({
    type: 'varchar',
    length: 100,
    default: '',
    comment: '修改者',
    name: 'update_user',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    default: 0,
    comment: '创建时间',
    name: 'create_time',
  })
  createTime: number

  @Column({
    type: 'bigint',
    default: 0,
    comment: '更新时间',
    name: 'update_time',
  })
  updateTime: number
}
