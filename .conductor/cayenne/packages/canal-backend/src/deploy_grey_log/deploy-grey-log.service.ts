import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import {
  CreateDeployGreyLogDto,
  UpdateDeployGreyLogDto,
} from './deploy-grey-log.dto'
import { DeployGreyLog } from './deploy-grey-log.entity'

@Injectable()
export class DeployGreyLogService {
  constructor(
    @InjectRepository(DeployGreyLog)
    private readonly deployGreyLogRepository: Repository<DeployGreyLog>,
  ) {}

  async create(
    createDeployGreyLogDto: CreateDeployGreyLogDto,
  ): Promise<DeployGreyLog> {
    const deployGreyLog = this.deployGreyLogRepository.create(
      createDeployGreyLogDto,
    )
    return await this.deployGreyLogRepository.save(deployGreyLog)
  }

  async update(
    id: number,
    updateDeployGreyLogDto: UpdateDeployGreyLogDto,
  ): Promise<DeployGreyLog | null> {
    await this.deployGreyLogRepository.update(id, updateDeployGreyLogDto)
    return await this.deployGreyLogRepository.findOneBy({ id })
  }
  async findAll(greyId: number): Promise<DeployGreyLog[]> {
    return await this.deployGreyLogRepository.find({
      where: [
        {
          greyId,
        },
      ],
      order: {
        createTime: 'DESC',
      },
    })
  }
}
