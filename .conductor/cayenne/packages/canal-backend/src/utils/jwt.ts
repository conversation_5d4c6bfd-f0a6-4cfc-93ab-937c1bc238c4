import axios from 'axios'
import jwt from 'jsonwebtoken'
import { logger } from 'src/common/logger'
interface ICONFIG {
  issuer: string
  secret: string
  host: string
  checkAPIHost: string
  params: {
    tasks: [
      {
        node_id: string
        stage: string
      },
    ]
  }
}
interface IResponse {
  check_result: {
    is_audit: boolean // 是否命中策略, true:命中; false:未命中
    warning: '' // 警告信息，若漏传了参数会在这里提示
    request_id: string // 类似trace-id, 贯穿本次请求所涉及到所有逻辑处理,可用于分析定位问题
  }
  is_audit: boolean // 是否命中策略, true:命中; false:未命中
  strategy_id: number // 命中的策略ID
  has_flow: number // 是否需要审批
  flow_result: {
    // 审批动作结果
    flow_id: string // 流程ID
    flow_status: 'uncommitted' | 'submitted' // 流程状态，uncommitted为未提交(需要用户手动跳转过去，去选择审批人)；  submitted 已提交
    flow_url: string // 审批的链接
  }
  has_block: boolean // 是否需要阻断
  block_result: {
    // 阻断动作信息
    content: string // 阻断提示信息
  }
  has_notify: boolean // 是否会执行通知
  notify_result: {
    // 通知结果
    is_done: boolean
  }
}

export const GreyLabelMap = {
  white: '白名单',
  lane: '泳道',
  percentage: '流量比例',
  host: '跟随宿主',
}

export const checkOrStartFlow = async (
  config: ICONFIG,
  changeName: string,
  changeAddress: string,
  user: string,
  hasGrey: boolean,
  greyType?: 'white' | 'lane' | 'percentage' | 'host',
  greyValue?: string,
): Promise<IResponse | undefined> => {
  const params = {
    tasks: config.params.tasks,
    deploy_type: 'update',
    platform: 'ad_canal',
    display: {
      title: '[大运河平台上线]变更审批',
      modules: [
        {
          components: [
            {
              component: 'label',
              label: '变更名称',
              value: changeName,
            },
            {
              label: '大运河变更地址',
              value: changeAddress,
              component: 'url',
              tooltip: '',
            },
            {
              component: 'label',
              label: '发布方式',
              value:
                hasGrey && greyType
                  ? '【灰度发布】灰度方式' + GreyLabelMap[greyType] ||
                    '' + ':' + greyValue
                  : '全量发布',
            },
          ],
        },
      ],
    },
  }
  const token = jwt.sign(
    {
      user: user,
      iss: config.issuer,
    },
    config.secret,
    {
      expiresIn: 600, //秒到期时间
    },
  )
  const axiosInstance = axios.create({
    baseURL: `${config.host}`,
    headers: {
      'X-Halo-Token': token,
      'Content-Type': 'application/json',
    },
  })
  const ret = await axiosInstance.post(
    '/api/changes/v1/maintenance/maintenances/checkOrStartFlow',
    params,
  )

  if (ret?.status === 200 && ret?.data) {
    if (ret.data.code === 0) {
      return ret.data.data
    }
  }
}

export const getFlowState = async (
  flowId: string,
  config: ICONFIG,
  user: string,
): Promise<
  | {
      state: string
      overState: string
    }
  | undefined
> => {
  const token = jwt.sign(
    {
      user: user,
      iss: config.issuer,
    },
    config.secret,
    {
      expiresIn: 600, //秒到期时间
    },
  )
  const axiosInstance = axios.create({
    baseURL: `${config.checkAPIHost}`,
    headers: {
      'X-Halo-Token': `${token}`,
      'Content-Type': 'application/json',
    },
  })
  const ret = await axiosInstance.get(
    `/api/flow/center/v1/load?entityId=${flowId}`,
  )
  logger.info('flowRet............', { t: ret })

  if (ret?.status === 200 && ret?.data) {
    if (ret.data.entity?.state) {
      return {
        state: ret.data.entity.state,
        overState: ret.data.entity.overState,
      }
    }
  }
}

/**
 * 上报事件
 * @param config 审批配置
 * @param user 当前用户
 * @param params 上报参数
 */
export async function reportEvent(
  config: ICONFIG,
  user: string,
  params: Record<string, unknown>,
) {
  const token = jwt.sign(
    {
      user,
      iss: config.issuer,
    },
    config.secret,
    {
      expiresIn: 600, //秒到期时间
    },
  )
  const axiosInstance = axios.create({
    baseURL: `${config.host}`,
    headers: {
      'X-Halo-Token': token,
      'Content-Type': 'application/json',
    },
  })
  const ret = await axiosInstance.post(
    '/api/changes/v1/event/reportEvent',
    params,
  )
  if (ret?.status === 200) {
    return ret.data
  }
}
