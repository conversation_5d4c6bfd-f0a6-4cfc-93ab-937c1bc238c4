import _ from 'lodash'

export function deepSearch(data: unknown, targets: Record<string, unknown>[]) {
  const results: Record<string, unknown>[] = []

  // 辅助函数用于递归搜索
  function search(value: unknown) {
    if (_.isObject(value) && value !== null) {
      // 检查当前对象是否包含目标结构
      if (containsAnyTarget(value as Record<string, unknown>)) {
        results.push(value as Record<string, unknown>)
      }

      // 遍历对象的每个属性
      _.forEach(_.values(value), (val) => search(val))
    }

    // 如果是数组，遍历数组元素
    if (Array.isArray(value)) {
      _.forEach(value, (item) => search(item))
    }
  }

  // 检查对象是否包含目标数组中的任何一个目标结构
  function containsAnyTarget(obj: Record<string, unknown>) {
    return targets.some((target) =>
      Object.keys(target).every(
        (key) => key in obj && _.isEqual(obj[key], target[key]),
      ),
    )
  }

  search(data)
  return results
}
