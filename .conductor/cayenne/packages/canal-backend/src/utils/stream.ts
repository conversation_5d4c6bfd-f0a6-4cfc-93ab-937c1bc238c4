import _ from 'lodash'

/**
 * LangBridge 流字符串转内容
 * @param str 流字符串
 */
export function langBridgeStreamStr2Content(str: string): string {
  return str
    .split('\n')
    .map((line) => {
      if (line.startsWith('data:')) {
        try {
          const data = JSON.parse(line.slice(5))
          const content = _.get(data, 'data.choices[0].message.content')
          if (_.isString(content)) {
            return content
          }
        } catch (err) {
          // noop
        }
      }
      return ''
    })
    .join('')
}
