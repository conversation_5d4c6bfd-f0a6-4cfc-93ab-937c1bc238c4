import _ from 'lodash'
import { FindOperator, ValueTransformer } from 'typeorm'

/**
 * 转换器：大数字字符串 -> 整型数字
 */
export const bigNumberStringToIntTransformer: ValueTransformer = {
  from(
    value?: string | number | FindOperator<unknown>,
  ): number | undefined | FindOperator<unknown> {
    if (value instanceof FindOperator) {
      return value
    }
    if (_.isNumber(value)) {
      return value
    }
    if (_.isString(value)) {
      return parseInt(value)
    }
    return undefined
  },
  to(
    value?: string | number | FindOperator<unknown>,
  ): string | undefined | FindOperator<unknown> {
    if (value instanceof FindOperator) {
      return value
    }
    if (_.isNumber(value)) {
      return `${value}`
    }
    if (_.isString(value)) {
      return value
    }
    return undefined
  },
}
