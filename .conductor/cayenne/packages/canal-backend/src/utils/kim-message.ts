import axios from 'axios'
export interface IMessage {
  changeAddress: string
  publisher: string
  changeName: string
  type: string
}
async function sendMessage(params: IMessage) {
  return await axios.post(
    'http://kim-robot.internal/api/robot/send?key=27f53168-e0fb-4c93-af10-a56e5c493026',
    generateMarkdown(params),
  )
}

function generateMarkdown({
  changeAddress,
  publisher,
  changeName,
  type,
}: IMessage) {
  return {
    msgtype: 'markdown',
    markdown: {
      content: `大运河上线通知\n>变更名称：${changeName}\n>发布人<@=username(${publisher})=> \n>类型:<font color=\"comment\">${type}</font>\n>变更地址:[大运河地址](${changeAddress})`,
    },
  }
}

export default sendMessage
