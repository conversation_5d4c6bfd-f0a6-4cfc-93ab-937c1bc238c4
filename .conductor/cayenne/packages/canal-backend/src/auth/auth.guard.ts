import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common'

@Injectable()
export class AuthGuard implements CanActivate {
  public canActivate(context: ExecutionContext): boolean {
    const httpContext = context.switchToHttp()
    const request = httpContext.getRequest()
    if (request.session.userInfo) {
      return true
    } else {
      throw new HttpException(
        {
          result: 403,
          msg: '用户未登录',
        },
        HttpStatus.OK,
      )
    }
  }
}
