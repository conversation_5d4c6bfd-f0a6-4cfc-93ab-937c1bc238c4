import { ApiErrorCode } from '@ad/canal-shared'
import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common'
import { Request } from 'express'

@Injectable()
export class HostGuard implements CanActivate {
  public canActivate(context: ExecutionContext): boolean {
    const httpContext = context.switchToHttp()
    const request = httpContext.getRequest() as Request
    const host = request.header('host') as string
    const hosts = [
      'test.corp.kuaishou.com:9527',
      'test.corp.kuaishou.com',
      'canal.staging.kuaishou.com',
      'canal.corp.kuaishou.com',
      'sso.corp.kuaishou.com',
    ]
    console.log(host)
    if (hosts.includes(host)) {
      return true
    } else {
      throw new HttpException(
        {
          result: ApiErrorCode.PARAM_ERROR,
          msg: '非法请求',
        },
        HttpStatus.OK,
      )
    }
  }
}
