import { applyDecorators } from '@nestjs/common'
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger'
import { ResponseType } from 'src/types'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function CommonApiResponse(
  model: any,
  isArray = false,
  arrayExtra?: { type: string },
) {
  // 检查 model 是否为 Boolean
  const isBoolean = model === Boolean

  if (isArray) {
    return applyDecorators(
      ApiExtraModels(model),
      ApiOkResponse({
        schema: {
          allOf: [
            { $ref: getSchemaPath(ResponseType) },
            {
              properties: {
                data: {
                  type: 'array',
                  items: arrayExtra
                    ? arrayExtra
                    : isBoolean
                    ? { type: 'boolean' } // 如果是布尔值，返回布尔类型
                    : { $ref: getSchemaPath(model) },
                },
              },
            },
          ],
        },
      }),
    )
  } else {
    return applyDecorators(
      ApiExtraModels(model),
      ApiOkResponse({
        schema: {
          allOf: [
            { $ref: getSchemaPath(ResponseType) },
            {
              properties: {
                data: isBoolean
                  ? { type: 'boolean' } // 如果是布尔值，返回布尔类型
                  : { $ref: getSchemaPath(model) },
              },
            },
          ],
        },
      }),
    )
  }
}
