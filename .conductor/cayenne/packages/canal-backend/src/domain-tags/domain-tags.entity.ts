import { ApiProperty } from '@nestjs/swagger'
import { Column, Entity, PrimaryColumn } from 'typeorm'

@Entity({ name: 'domain_tags' })
export class DomainTags {
  @ApiProperty()
  @PrimaryColumn()
  id: number

  @ApiProperty({
    description: '业务域code',
  })
  @Column({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    comment: '业务域code',
  })
  domainCode: string

  @ApiProperty({
    description: '一级标签',
  })
  @Column({
    name: 'l1_tag',
    type: 'varchar',
    length: 255,
    comment: '一级标签',
  })
  l1Tag: string

  @ApiProperty({
    description: '二级标签',
  })
  @Column({
    name: 'l2_tag',
    type: 'varchar',
    length: 255,
    comment: '二级标签',
  })
  l2Tag: string

  @ApiProperty({
    description: '估算时间',
  })
  @Column({
    name: 'estimate_time',
    type: 'bigint',
    comment: '估算时间',
  })
  estimateTime: number

  @ApiProperty({
    description: '创建者',
  })
  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建者',
  })
  creator: string

  @ApiProperty({
    description: '修改者',
  })
  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '修改者',
  })
  updater: string

  @ApiProperty({
    description: '创建时间',
  })
  @Column({
    name: 'create_time',
    type: 'bigint',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || +new Date(),
    },
    comment: '创建时间',
  })
  createTime: number

  @ApiProperty({
    description: '修改时间',
  })
  @Column({
    name: 'update_time',
    type: 'bigint',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || +new Date(),
    },
    comment: '修改时间',
  })
  updateTime: number
}
