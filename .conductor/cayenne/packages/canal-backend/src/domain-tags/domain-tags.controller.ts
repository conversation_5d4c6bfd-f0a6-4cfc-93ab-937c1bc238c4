// 给service文件里的几个方法补全controller
import { Body, Controller, Get, Post, Query, Session } from '@nestjs/common'
import { ApiQuery } from '@nestjs/swagger'
import { UserSession } from 'src/types'
import { DomainTagsService } from './domain-tags.service'

@Controller('/rest/canal/tags')
export class DomainTagsController {
  public constructor(private readonly domainTagsService: DomainTagsService) {}

  @Get('list')
  @ApiQuery({
    name: 'domainCode',
    required: true,
    description: '获取单独业务域的tags',
    type: String,
  })
  public async getDomainTags(@Query('domainCode') domainCode: string) {
    return this.domainTagsService.getDomainTags(domainCode)
  }

  @Post('add')
  public async addOneTag(
    @Body()
    addDto: {
      domainCode: string
      l1Tag: string
      l2Tag: string
      estimateTime?: number
    },
    @Session() userSession: UserSession,
  ) {
    return this.domainTagsService.addTag(
      userSession.userInfo?.userCode,
      addDto.domainCode,
      addDto.l1Tag,
      addDto.l2Tag,
      addDto.estimateTime,
    )
  }

  @Post('edit')
  public async editTagEstimateTime(
    @Body()
    editDto: {
      id: number
      domainCode: string
      l1Tag: string
      l2Tag: string
      estimateTime?: number
    },
    @Session() userSession: UserSession,
  ) {
    return this.domainTagsService.editTagEstimateTime(
      userSession.userInfo?.userCode,
      editDto,
    )
  }
  @Post('getInfo')
  public async getTagsByIds(
    @Body()
    getDto: {
      ids: number[]
    },
  ) {
    return this.domainTagsService.getTagsByIds(getDto.ids)
  }
}
