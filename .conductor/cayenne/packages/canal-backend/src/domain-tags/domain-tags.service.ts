import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { logger } from 'src/common/logger'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { In, Repository } from 'typeorm'
import { DomainTagsDto } from './domain-tags.dto'
import { DomainTags } from './domain-tags.entity'

@Injectable()
export class DomainTagsService {
  public constructor(
    @InjectRepository(DomainTags)
    public readonly domainTagsRepo: Repository<DomainTags>,
  ) {}
  /**
   * 获取domainCode下的所有标签
   * @param domainCode
   * @returns
   */
  async getDomainTags(domainCode: string) {
    logger.info('canal_git_search_result_tag', { r: domainCode })
    const tags = await this.domainTagsRepo.find({
      where: {
        domainCode,
      },
    })
    if (!tags.length) {
      return null
    }
    // 循环遍历tags，返回一个map， key是l1Tag, 值是l2Tag组成的数组
    const tagsMap: Record<
      string,
      {
        l2Tag: string
        estimateTime?: number
        id: number
      }[]
    > = {}
    tags.forEach((tag) => {
      if (!tagsMap[tag.l1Tag]) {
        tagsMap[tag.l1Tag] = [
          {
            id: tag.id,
            l2Tag: tag.l2Tag,
            estimateTime: tag.estimateTime,
          },
        ]
      } else {
        tagsMap[tag.l1Tag].push({
          id: tag.id,
          l2Tag: tag.l2Tag,
          estimateTime: tag.estimateTime,
        })
      }
    })
    return tagsMap
  }
  /**
   *
   * @param ids
   * @returns
   */
  async getTagsByIds(ids: number[]) {
    return await this.domainTagsRepo.find({
      where: {
        id: In(ids),
      },
    })
  }
  // 删除一条标签)
  async deleteOneTag(domainCode: string, l1Tag: string, l2Tag: string) {
    return await this.domainTagsRepo.delete({
      domainCode,
      l1Tag,
      l2Tag,
    })
  }
  // 增加一条标签,如果没有预估时间，则默认是0
  async addTag(
    userCode: string,
    domainCode: string,
    l1Tag: string,
    l2Tag: string,
    estimateTime?: number,
  ) {
    const tag = await this.domainTagsRepo.findOne({
      where: {
        domainCode,
        l1Tag,
        l2Tag,
      },
    })
    if (tag) {
      throw new CommonException(ExceptionCodes.Domain_tag_Duplicate)
    }
    return await this.domainTagsRepo.save({
      domainCode,
      l1Tag,
      l2Tag,
      estimateTime: estimateTime || 0,
      createTime: new Date().getTime(),
      updater: userCode,
      updateTime: new Date().getTime(),
      creator: userCode,
    })
  }
  /**
   * 修改预估时间
   * @param domainCode
   * @param l1Tag
   * @param l2Tag
   * @param estimateTime
   * @returns
   */
  async editTagEstimateTime(userCode: string, domainTagsDto: DomainTagsDto) {
    return await this.domainTagsRepo.update(
      {
        id: domainTagsDto.id,
      },
      {
        l2Tag: domainTagsDto.l2Tag,
        estimateTime: domainTagsDto.estimateTime,
        updater: userCode,
        updateTime: new Date().getTime(),
      },
    )
  }
}
