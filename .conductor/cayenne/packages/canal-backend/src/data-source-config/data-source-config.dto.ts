export class DataSourceConfigDto {
  readonly id?: string
  readonly domainCode: string
  readonly path: string
  readonly type: string
  readonly method: string
  readonly name?: string
  readonly stagingDomain?: string
  readonly prtDomain?: string
  readonly betaDomain?: string
  readonly productionDomain?: string
  /**
   * Mock 响应
   */
  readonly mockRes?: string
  readonly createUser: string
  readonly updateUser: string
  readonly createTime: number
  readonly updateTime: number
}

export class DataSourceListDto {
  readonly domainCode: string
  readonly pageNum: number
  readonly pageSize: number
  readonly path?: string
}
