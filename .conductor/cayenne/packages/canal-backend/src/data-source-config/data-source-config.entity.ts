import { Column, Entity, PrimaryColumn } from 'typeorm'

@Entity('data_source_config')
export class DataSourceConfig {
  @PrimaryColumn({
    type: 'varchar',
    length: 36,
    comment: '数据源id',
  })
  id: string

  @Column({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    default: '',
    comment: '业务域code',
  })
  domainCode: string

  @Column({ type: 'varchar', length: 1024, default: '', comment: '接口path' })
  path: string

  @Column({ type: 'varchar', length: 255, default: '', comment: '方法' })
  method: string

  @Column({ type: 'varchar', length: 255, default: '', comment: '' })
  name: string

  @Column({
    type: 'varchar',
    length: 100,
    default: 'http',
    comment: '请求类型',
  })
  type: string

  @Column({
    name: 'staging_domain',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'staging域名',
  })
  stagingDomain?: string

  @Column({
    name: 'prt_domain',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'prt域名',
  })
  prtDomain?: string

  @Column({
    name: 'beta_domain',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'beta域名',
  })
  betaDomain?: string

  @Column({
    name: 'production_domain',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '线上域名',
  })
  productionDomain?: string

  @Column({
    name: 'mock_res',
    type: 'longtext',
    comment: 'Mock 响应',
    nullable: true,
  })
  mockRes?: string

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number
}
