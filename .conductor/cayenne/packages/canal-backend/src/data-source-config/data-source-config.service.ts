import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { BusinessDomainService } from 'src/business-domain/business-domain.service'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { Repository } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import {
  DataSourceConfigDto,
  DataSourceListDto,
} from './data-source-config.dto'
import { DataSourceConfig } from './data-source-config.entity'

@Injectable()
export class DataSourceConfigService {
  constructor(
    @InjectRepository(DataSourceConfig)
    private readonly dataSourceConfigRepository: Repository<DataSourceConfig>,
    private readonly businessDomainService: BusinessDomainService,
  ) {}

  async create(
    data: DataSourceConfigDto,
    userCode: string,
  ): Promise<DataSourceConfig> {
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      data.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    const now = new Date().getTime()
    const newDataSourceConfig = this.dataSourceConfigRepository.create({
      ...data,
      createUser: userCode,
      createTime: now,
      updateUser: userCode,
      updateTime: now,
      id: uuidv4(),
    })
    return this.dataSourceConfigRepository.save(newDataSourceConfig)
  }

  async createStaging(data: DataSourceConfigDto): Promise<DataSourceConfig> {
    const newDataSourceConfig = this.dataSourceConfigRepository.create(data)
    return this.dataSourceConfigRepository.save(newDataSourceConfig)
  }
  /**
   * 分页查询某一业务域下的数据源列表
   */
  async getListByPage({
    domainCode,
    pageNum,
    pageSize,
    path,
  }: DataSourceListDto) {
    try {
      const queryBuilder = await this.dataSourceConfigRepository
        .createQueryBuilder('dataSourceConfig')
        .where('dataSourceConfig.domainCode = :domainCode', {
          domainCode,
        })
      if (path) {
        queryBuilder.andWhere('dataSourceConfig.path LIKE :path', {
          path: `%${path}%`,
        })
      }
      const [list, total] = await queryBuilder
        .orderBy('dataSourceConfig.createTime', 'DESC')
        .skip((pageNum - 1) * (pageSize || 10))
        .take(pageSize)
        .getManyAndCount()

      return {
        list,
        total,
      }
    } catch (e) {
      throw new CommonException(ExceptionCodes.DATA_SOURCE_LIST_BY_PAGE)
    }
  }

  async findAll(domainCode: string): Promise<DataSourceConfig[]> {
    return this.dataSourceConfigRepository.find({
      where: {
        domainCode,
      },
    })
  }

  async getProductionDomains(domainCode: string): Promise<string[]> {
    const results = await this.dataSourceConfigRepository.find({
      where: {
        domainCode,
      },
    })
    // 提取每一条记录的 productionDomain
    const productionDomains: string[] = results
      .map((item) => item.productionDomain) // 先提取 productionDomain
      .filter((url) => url && typeof url === 'string' && url.trim() !== '')
      .map((url) => (url as string).replace(/https?:\/\//, ''))

    return productionDomains
  }

  async findOne(id: string): Promise<DataSourceConfig | null> {
    return await this.dataSourceConfigRepository.findOneBy({ id })
  }

  async update(
    data: {
      id: number
      config: DataSourceConfigDto
    },
    userCode: string,
  ): Promise<void> {
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      data.config?.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    const now = new Date().getTime()
    await this.dataSourceConfigRepository.update(data.id, {
      ...data.config,
      updateUser: userCode,
      updateTime: now,
    })
  }

  async updateStaging(data: {
    id: string
    config: DataSourceConfigDto
  }): Promise<void> {
    const now = new Date().getTime()
    const _dataSource = await this.dataSourceConfigRepository.findOneBy({
      id: data.id,
    })
    if (_dataSource) {
      await this.dataSourceConfigRepository.update(data.id, {
        ...data.config,
        updateTime: now,
      })
    } else {
      const newDataSourceConfig = this.dataSourceConfigRepository.create({
        id: data.id,
        ...data.config,
        createTime: now,
      })
      this.dataSourceConfigRepository.save(newDataSourceConfig)
    }
  }
  async checkDataSourceBeforePublish(
    ids: string[],
  ): Promise<PromiseSettledResult<string>[]> {
    return Promise.allSettled(
      ids.map(async (id) => {
        const dataSourceObject =
          await this.dataSourceConfigRepository.findOneBy({ id })
        if (dataSourceObject) {
          if (
            dataSourceObject.productionDomain &&
            dataSourceObject.path &&
            dataSourceObject.method
          ) {
            return ''
          } else {
            return Promise.reject(
              `数据源${dataSourceObject.name}-${dataSourceObject.id}信息不全，请检查`,
            )
          }
        } else {
          return Promise.reject(`数据源${id}不存在，请检查`)
        }
      }),
    )
  }
}
