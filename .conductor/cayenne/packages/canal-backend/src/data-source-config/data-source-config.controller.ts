import { Body, Controller, Get, Post, Query, Session } from '@nestjs/common'
import { UserSession } from 'src/types'
import {
  DataSourceConfigDto,
  DataSourceListDto,
} from './data-source-config.dto'
import { DataSourceConfig } from './data-source-config.entity'
import { DataSourceConfigService } from './data-source-config.service'

@Controller('/rest/canal/data-source-config')
export class DataSourceConfigController {
  constructor(
    private readonly dataSourceConfigService: DataSourceConfigService,
  ) {}

  @Post('add')
  async create(
    @Body() data: DataSourceConfigDto,
    @Session() userSession: UserSession,
  ): Promise<DataSourceConfig> {
    return this.dataSourceConfigService.create(
      data,
      userSession?.userInfo?.userCode,
    )
  }
  @Post('staging/add')
  async createStaging(
    @Body() data: DataSourceConfigDto,
  ): Promise<DataSourceConfig> {
    return this.dataSourceConfigService.createStaging(data)
  }

  @Get('all')
  async findAll(
    @Query('domainCode') domainCode: string,
  ): Promise<DataSourceConfig[]> {
    return this.dataSourceConfigService.findAll(domainCode)
  }
  @Get('all/prod/domain')
  async findAllDomain(
    @Query('domainCode') domainCode: string,
  ): Promise<string[]> {
    return this.dataSourceConfigService.getProductionDomains(domainCode) || []
  }

  @Post('list')
  async list(
    @Body() data: DataSourceListDto,
  ): Promise<{ list: DataSourceConfig[]; total: number }> {
    return this.dataSourceConfigService.getListByPage(data)
  }

  @Get('one')
  async findOne(@Query('id') id: string): Promise<DataSourceConfig | null> {
    return this.dataSourceConfigService.findOne(id)
  }

  @Post('edit')
  async update(
    @Body()
    data: {
      id: number
      config: DataSourceConfigDto
    },
    @Session() userSession: UserSession,
  ): Promise<void> {
    return this.dataSourceConfigService.update(
      data,
      userSession?.userInfo?.userCode,
    )
  }
  @Post('staging/edit')
  async editStaging(
    @Body()
    data: {
      id: string
      config: DataSourceConfigDto
    },
  ): Promise<void> {
    return this.dataSourceConfigService.updateStaging(data)
  }
}
