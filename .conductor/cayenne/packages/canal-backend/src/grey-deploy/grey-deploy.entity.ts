import { IsNotEmpty } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'grey_deploy' })
export class GreyDeploy {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    unsigned: true,
    comment: 'id',
    name: 'id',
  })
  id: number

  @Column({ type: 'bigint', comment: '发布单ID', name: 'publish_id' })
  publishId: number

  @IsNotEmpty()
  @Column({
    type: 'varchar',
    length: 100,
    comment: '灰度策略类型',
    name: 'grey_type',
  })
  greyType: 'white' | 'lane' | 'percentage' | 'host'

  @IsNotEmpty()
  @Column({
    type: 'varchar',
    length: 255,
    default: '',
    comment: '灰度值',
    name: 'grey_value',
  })
  greyValue: string

  @Column({
    type: 'varchar',
    length: 255,
    default: '',
    comment: '灰度键',
    name: 'grey_key',
  })
  greyKey: string

  @Column({ type: 'tinyint', comment: '状态', nullable: true, name: 'status' })
  status: number

  @Column({
    type: 'varchar',
    length: 100,
    default: '',
    comment: '创建者',
    name: 'create_user',
  })
  createUser: string

  @Column({
    type: 'varchar',
    length: 100,
    default: '',
    comment: '修改者',
    name: 'update_user',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    default: 0,
    comment: '创建时间',
    name: 'create_time',
  })
  createTime: number

  @Column({
    type: 'bigint',
    default: 0,
    comment: '更新时间',
    name: 'update_time',
  })
  updateTime: number
}

export enum GreyDeployStatus {
  INIT = 0,
  EFFECTIVE = 1,
  SUCCESS = 2,
  INVALID = 3,
}
