import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { In, Repository } from 'typeorm'
import { UpdateGreyDeployDto } from './grey-deploy.dto'
import { GreyDeploy } from './grey-deploy.entity'

@Injectable()
export class GreyDeployService {
  public constructor(
    @InjectRepository(GreyDeploy)
    public readonly greyDeployRepo: Repository<GreyDeploy>,
  ) {}

  async getOne(publishId: number, status: number[]) {
    return await this.greyDeployRepo.findOne({
      where: {
        publishId,
        status: In(status),
      },
      order: {
        createTime: 'DESC',
      },
    })
  }
  async update(updateGreyDeployDto: UpdateGreyDeployDto) {
    if (!updateGreyDeployDto.greyValue || !updateGreyDeployDto.greyType) {
      throw new CommonException(ExceptionCodes.GREY_GREY_UPDATE_FAILED)
    }
    if (updateGreyDeployDto.greyType === 'percentage') {
      if (
        !(
          Number.isInteger(+updateGreyDeployDto.greyValue) &&
          +updateGreyDeployDto.greyValue > 0 &&
          +updateGreyDeployDto.greyValue < 100
        )
      ) {
        throw new CommonException(ExceptionCodes.GREY_GREY_UPDATE_FAILED)
      }
    }
    return await this.greyDeployRepo.update(
      updateGreyDeployDto.id,
      updateGreyDeployDto,
    )
  }
}
