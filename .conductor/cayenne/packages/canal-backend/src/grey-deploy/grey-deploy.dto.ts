import { LingZhuAdditionalParameters } from 'src/common/dto'

export class CreateGreyDeployDto extends LingZhuAdditionalParameters {
  publishId: number
  greyType: 'white' | 'lane' | 'percentage' | 'host'
  greyValue: string
  greyKey: string
  status: number
  createUser: string
  updateUser: string
  createTime: number
  updateTime: number
}

export class UpdateGreyDeployDto extends LingZhuAdditionalParameters {
  id: number
  publishId: number
  greyType: 'white' | 'lane' | 'percentage' | 'host'
  greyValue: string
  greyKey: string
  status: number
}
