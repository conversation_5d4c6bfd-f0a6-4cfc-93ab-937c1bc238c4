import { ApiProperty } from '@nestjs/swagger'

export class ResponseType<T> {
  @ApiProperty()
  public readonly result?: number

  @ApiProperty()
  public readonly msg?: string

  public readonly data: T | T[]
}

export interface ISession {
  redirectUrl?: string
  userInfo?: {
    userName: string
    userCode: string
    avatar: string
    department: string
  }
}

export interface UserSession {
  userInfo: {
    userName: string
    userCode: string
    avatar: string
    department: string
  }
}
