import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import _ from 'lodash'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { DataSource, Repository } from 'typeorm'
import { UpdateExpressionTemplateDto } from './expression-template.dto'
import {
  EXPRESSION_TEMPLATE_CONFLICT_PATH,
  ExpressionTemplate,
} from './expression-template.entity'

@Injectable()
export class ExpressionTemplateService {
  public constructor(
    private readonly dataSource: DataSource,
    @InjectRepository(ExpressionTemplate)
    public readonly expressionTemplateRepo: Repository<ExpressionTemplate>,
  ) {}

  /**
   * 查找
   * @param domainCode 业务域编码
   * @param expressionType 表达式类型
   */
  public async find(
    domainCode: string,
    expressionType: BackExpressionType | FrontExpressionType,
  ): Promise<ExpressionTemplate[]> {
    return this.expressionTemplateRepo.find({
      where: {
        domainCode,
        expressionType,
      },
    })
  }

  /**
   * 更新
   * @param updateExpressionTemplateDto 更新表达式模板 DTO
   * @param updater 更新者
   */
  public async update(
    updateExpressionTemplateDto: UpdateExpressionTemplateDto,
    updater: string,
  ): Promise<void> {
    const now = Date.now()

    const queryRunner = this.dataSource.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()
    try {
      const findOneRet = await queryRunner.manager.findOne(ExpressionTemplate, {
        where: _.pick(
          updateExpressionTemplateDto,
          EXPRESSION_TEMPLATE_CONFLICT_PATH,
        ),
      })
      if (!findOneRet) {
        await queryRunner.manager.insert(ExpressionTemplate, {
          ...updateExpressionTemplateDto,
          creator: updater,
          createdAt: now,
          updater,
          updatedAt: now,
        })
      } else {
        await queryRunner.manager.update(ExpressionTemplate, findOneRet.id, {
          ...updateExpressionTemplateDto,
          creator: updater,
          createdAt: now,
        })
      }
      await queryRunner.commitTransaction()
    } catch (err) {
      await queryRunner.rollbackTransaction()
      console.error('EXPRESSION_TEMPLATE_UPDATE_FAILED err', err)
      throw new CommonException(
        ExceptionCodes.EXPRESSION_TEMPLATE_UPDATE_FAILED,
      )
    } finally {
      await queryRunner.release()
    }
  }
}
