import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'

/**
 * 更新表达式模板 DTO
 */
export class UpdateExpressionTemplateDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '名称' })
  readonly name: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '表达式' })
  readonly expression: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域编码' })
  readonly domainCode: string

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({
    description:
      '表达式类型, 0: 后端 JSONata, 1: 后端 JavaScript, 2: 后端 TypeScript, 100: 前端 JavaScript, 101: 前端 TypeScript',
  })
  readonly expressionType: number
}
