import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Session,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { UserSession } from 'src/types'
import { UpdateExpressionTemplateDto } from './expression-template.dto'
import { ExpressionTemplate } from './expression-template.entity'
import { ExpressionTemplateService } from './expression-template.service'

@Controller('/rest/canal/expression-template')
@ApiTags('expression-template')
@Controller()
@UseGuards(AuthGuard)
export class ExpressionTemplateController {
  public constructor(
    private expressionTemplateService: ExpressionTemplateService,
  ) {}

  @Get()
  @ApiOperation({
    summary: '查找表达式模板',
    operationId: 'findExpressionTemplates',
  })
  @CommonApiResponse(ExpressionTemplate, true)
  public async findExpressionTemplates(
    @Query('domainCode') domainCode: string,
    @Query('expressionType')
    expressionType: BackExpressionType | FrontExpressionType,
  ): Promise<ExpressionTemplate[]> {
    return this.expressionTemplateService.find(domainCode, expressionType)
  }

  @Post('update')
  @ApiBody({
    type: UpdateExpressionTemplateDto,
  })
  @ApiOperation({
    summary: '更新表达式模板',
    operationId: 'updateExpressionTemplateUsingPost',
  })
  public async updateExpressionTemplateUsingPost(
    @Body() updateExpressionTemplateDto: UpdateExpressionTemplateDto,
    @Session() userSession: UserSession,
  ): Promise<void> {
    await this.expressionTemplateService.update(
      updateExpressionTemplateDto,
      userSession.userInfo.userCode,
    )
  }
}
