import { BackExpressionType, FrontExpressionType } from '@ad/canal-shared'
import { ApiProperty } from '@nestjs/swagger'
import { bigNumberStringToIntTransformer } from 'src/utils/transform'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

/**
 * 表达式模板冲突路径
 */
export const EXPRESSION_TEMPLATE_CONFLICT_PATH = [
  'domainCode',
  'expressionType',
  'name',
]

/**
 * 表达式模板
 */
@Entity({ name: 'expression_template' })
export class ExpressionTemplate {
  @ApiProperty({
    description: '唯一标识',
  })
  @PrimaryGeneratedColumn({
    type: 'bigint',
    unsigned: true,
  })
  id: string

  @ApiProperty({
    description: '名称',
  })
  @Column({
    type: 'varchar',
    length: 255,
  })
  name: string

  @ApiProperty({
    description: '表达式',
  })
  @Column({
    type: 'text',
  })
  expression: string

  @ApiProperty({
    description: '业务域编码',
  })
  @Column({
    type: 'varchar',
    length: 255,
    name: 'domain_code',
  })
  domainCode: string

  @ApiProperty({
    description: '表达式类型',
  })
  @Column({
    type: 'smallint',
    name: 'expression_type',
  })
  expressionType: BackExpressionType | FrontExpressionType

  @ApiProperty({
    description: '创建时间戳',
  })
  @Column({
    type: 'bigint',
    transformer: bigNumberStringToIntTransformer,
    name: 'created_at',
  })
  public createdAt!: number

  @ApiProperty({
    description: '更新时间戳',
  })
  @Column({
    type: 'bigint',
    transformer: bigNumberStringToIntTransformer,
    name: 'updated_at',
  })
  public updatedAt!: number

  @ApiProperty({
    description: '创建者',
  })
  @Column({
    type: 'varchar',
    length: 128,
  })
  creator: string

  @ApiProperty({
    description: '更新者',
  })
  @Column({
    type: 'varchar',
    length: 128,
  })
  updater: string
}
