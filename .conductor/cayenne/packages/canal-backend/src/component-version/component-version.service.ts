import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { UpdateComponentDto } from '../component/component.dto'
import { ComponentVersion } from './component-version.entity'

@Injectable()
export class ComponentVersionService {
  public constructor(
    @InjectRepository(ComponentVersion)
    public readonly componentVersionRepo: Repository<ComponentVersion>,
  ) {}

  async getLatestVersionByComponentId(componentId: string) {
    return this.componentVersionRepo
      .createQueryBuilder('componentVersion')
      .where('componentVersion.component_id = :componentId', { componentId })
      .orderBy('componentVersion.create_time', 'DESC')
      .getOne()
  }

  async getDetailByComponent(componentId: string, componentVersion: string) {
    return this.componentVersionRepo
      .createQueryBuilder('componentVersion')
      .where('componentVersion.component_id = :componentId', { componentId })
      .andWhere('componentVersion.version = :componentVersion', {
        componentVersion,
      })
      .getOne()
  }

  async create(updateComponentDto: UpdateComponentDto, userCode: string) {
    const componentVersion = new ComponentVersion()
    const now = new Date().getTime()
    let version = 1

    const latestVersion = await this.getLatestVersionByComponentId(
      updateComponentDto.id,
    )

    if (latestVersion) {
      version = +latestVersion.version + 1
    }

    version = 1

    componentVersion.componentId = updateComponentDto.id
    componentVersion.coverUrl = updateComponentDto.coverUrl
    componentVersion.associatedComponentId =
      updateComponentDto.associatedComponentId
    componentVersion.associatedComponentVersion =
      updateComponentDto.associatedComponentVersion
    componentVersion.resourceUrl = updateComponentDto.resourceUrl
    componentVersion.propsConfig = updateComponentDto.propsConfig
    componentVersion.eventKey = updateComponentDto.eventKey
    componentVersion.resourceUrl = updateComponentDto.resourceUrl
    componentVersion.createUser = userCode
    componentVersion.updateUser = userCode
    componentVersion.createTime = now
    componentVersion.updateTime = now
    componentVersion.version = version
    componentVersion.group = updateComponentDto.group

    try {
      await this.componentVersionRepo.save(componentVersion)
    } catch (err: unknown) {
      if ((err as { code: string }).code === 'ER_DUP_ENTRY') {
        // 版本重复
        const newLatestVersion = await this.getLatestVersionByComponentId(
          updateComponentDto.id,
        )

        if (newLatestVersion) {
          version = +newLatestVersion.version + 1
        }
        componentVersion.version = version
        await this.componentVersionRepo.save(componentVersion)
      } else {
        throw err
      }
    }
  }
}
