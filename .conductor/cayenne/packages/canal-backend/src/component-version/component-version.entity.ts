import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'component_version' })
export class ComponentVersion {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  id: string

  @Column({
    name: 'component_id',
    type: 'bigint',
    comment: '组件id',
  })
  componentId: string

  @Column({
    type: 'bigint',
    comment: '版本',
  })
  version: number

  @Column({
    name: 'demo_image_url',
    type: 'varchar',
    length: 255,
    comment: '示例图',
  })
  coverUrl?: string

  @Column({
    name: 'associated_component_id',
    type: 'varchar',
    length: 100,
    comment: '关联物料中心组件id',
  })
  associatedComponentId?: string

  @Column({
    name: 'associated_version',
    type: 'varchar',
    length: 100,
    comment: '关联物料中心组件版本',
  })
  associatedComponentVersion?: string

  @Column({
    name: 'resource_url',
    type: 'varchar',
    length: 255,
    comment: '资源url',
  })
  resourceUrl?: string

  @Column({
    name: 'props_config',
    type: 'varchar',
    length: 255,
    comment: '属性配置',
  })
  propsConfig?: string

  @Column({
    name: 'event_key',
    type: 'varchar',
    length: 255,
    comment: '事件key',
  })
  eventKey?: string

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
    select: false,
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
    select: false,
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    select: false,
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    select: false,
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number

  @Column({
    name: 'group',
    type: 'varchar',
    length: 100,
    comment: '组件分类，即物料 Schema 里的 group',
  })
  group?: string
}
