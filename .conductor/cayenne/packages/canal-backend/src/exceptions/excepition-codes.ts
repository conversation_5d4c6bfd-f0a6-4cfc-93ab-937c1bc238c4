export enum ExceptionCodes {
  // 组件: Component_xxx = 1xxx
  Component_Create_Error = 1000,
  Component_Not_Found = 1001,

  // 模块: Module_xxx = 2xxx
  Module_Create_Error = 2000,
  Module_Not_Found = 2001,
  Module_Version_Not_Found = 2002,
  Module_Update_Error = 2003,
  Module_Get_All_By_ChangeId_Error = 2004,
  Module_Have_No_Online_Version = 2005,
  Module_Online_Cannot_Edit = 2006,
  Module_Change_Not_Found = 2007,
  Module_Save_Change_Status_Error = 2008,
  Module_Deploy_History_Fail = 2009,
  Module_Get_Previous_Fail = 2010,
  Module_Global_Already_Exists = 2011,
  // 用户: User_xxx = 9xxx
  User_Permission_Deined = 9001,

  // 变更： Change_xxx = 6xxx
  Change_Create_fail = 6001,
  Domain_ALL_CHANGE_GET = 6002,
  Change_Create_fail_No_Team = 6003,
  CHANGE_LIST_BY_PAGE = 6004,
  CHANGE_CLOSE_FAIL = 6005,
  Change_Deploy_Fail = 6007,
  Change_Publish_Params_Miss = 6008,
  Change_Publish_Change_Not_Found = 6009,
  Change_Publish_Params_Error = 6010,
  Change_Publish_Change_Status_Error = 6011,
  Change_Publish_Have_Unfinished = 6012,
  Change_Publish_Create_Error = 6013,
  Change_Publish_Not_Found = 6014,
  Change_Publish_Status_Error = 6015,
  Change_Publish_Has_No_Module = 6016,
  Change_Publish_Fail = 6017,
  Change_Module_Duplicate = 6018,
  Change_Publish_Already_Online = 6019,
  Change_Publish_Not_Published = 6020,
  Change_Publish_Flow_Update_Fail = 6021,
  Change_Info_Change_Error = 6022,
  Change_Rollback_no_content = 6023,
  Change_Rollback_Forbidden = 6033,
  Rollback_Need_Online_Status = 6034,
  Change_Publish_Finished = 6035,

  //#region 表达式模板: EXPRESSION_TEMPLATE_xxx = 7xxx
  EXPRESSION_TEMPLATE_UPDATE_FAILED = 7000,

  Module_Template_Create_Fail = 8000,
  Module_Template_Duplicate_Name = 8001,
  //#endregion

  // tag相关
  Domain_tag_Duplicate = 9000,
  DATA_SOURCE_LIST_BY_PAGE = 10001,
  // 灰度相关
  GREY_CREATE_FAILED_NO_PUBLISH = 10002,
  GREY_NOT_FOUND = 10003,
  GREY_GREY_UPDATE_FAILED = 10004,

  GREY_NOT_CHANGE = 10005,
  HAS_GREY_NOT_DEPLOY = 10006,
  GREY_CONFIG_PARAMS_ERROR = 1007,
  DEPLOY_GREY_FAILED = 10008,
  DOMAIN_PERMISSION_ADD_AUTHOR = 11000,

  LINGZHU_PUSH_STATUS_ERROR = 12000,
  LINGZHU_CHANGE_STATUS_FAIL = 12001,

  //#region AI 相关
  AI_INVALID_OUTPUT = 16000,
  //#endregion
}

export const ExceptionMsg = {
  // 组件: Component_xxx = 1xxx
  [ExceptionCodes.Component_Create_Error]: '组件创建失败',
  [ExceptionCodes.Component_Not_Found]: '组件不存在',

  // 模块: Module_xxx = 2xxx
  [ExceptionCodes.Module_Create_Error]: '模块创建失败',
  [ExceptionCodes.Module_Not_Found]: '模块不存在',
  [ExceptionCodes.Module_Version_Not_Found]: '模块版本不存在',
  [ExceptionCodes.Module_Update_Error]: '模块更新失败',
  [ExceptionCodes.Module_Get_All_By_ChangeId_Error]:
    '获取变更下所有模块失败，请重试',
  [ExceptionCodes.Module_Online_Cannot_Edit]:
    '已经上线的模块版本不允许变更，请新建变更',
  [ExceptionCodes.Module_Change_Not_Found]: '找不到关联的变更',
  [ExceptionCodes.Module_Save_Change_Status_Error]:
    '关联的变更已经完结或者发布中，不能进行编辑',
  [ExceptionCodes.Module_Global_Already_Exists]: '全局模块已存在，请勿重复创建',

  // 用户: User_xxx = 9xxx
  [ExceptionCodes.User_Permission_Deined]: '用户无权限',

  // 变更
  [ExceptionCodes.Change_Create_fail]: '变更创建失败',
  [ExceptionCodes.Domain_ALL_CHANGE_GET]: '查询变更列表失败',
  [ExceptionCodes.Change_Create_fail_No_Team]: '找不到对应的Team任务',
  [ExceptionCodes.CHANGE_LIST_BY_PAGE]: '变更列表查询失败',
  [ExceptionCodes.CHANGE_CLOSE_FAIL]: '变更关闭操作失败',
  [ExceptionCodes.Change_Deploy_Fail]: '变更部署失败',
  [ExceptionCodes.Change_Publish_Params_Miss]: '参数缺失',
  [ExceptionCodes.Change_Publish_Change_Not_Found]: '找不到对应的变更',
  [ExceptionCodes.Change_Publish_Params_Error]:
    '参数校验错误，变更和业务域不匹配',
  [ExceptionCodes.Change_Publish_Change_Status_Error]:
    '请检查变更的状态，是否已经结束或正在发布中',
  [ExceptionCodes.Change_Publish_Have_Unfinished]:
    '该业务域有未完成的发布单，请先退出发布或等待发布完成',
  [ExceptionCodes.Change_Publish_Create_Error]: '发布单创建失败',
  [ExceptionCodes.Change_Publish_Not_Found]: '找不到对应的发布单',
  [ExceptionCodes.Change_Publish_Status_Error]: '发布单状态不正确',
  [ExceptionCodes.Change_Publish_Has_No_Module]: '发布单对应的变更没有变更模块',
  [ExceptionCodes.Change_Publish_Fail]: '发布失败',
  [ExceptionCodes.Module_Have_No_Online_Version]: '该模块没有线上版本',
  [ExceptionCodes.Change_Module_Duplicate]:
    '该变更已经包含此模块，不能重复添加',
  [ExceptionCodes.Module_Deploy_History_Fail]: '查找历史上线版本失败',
  [ExceptionCodes.Module_Get_Previous_Fail]: '查找前序线上版本失败',
  [ExceptionCodes.Change_Publish_Already_Online]: '发布单已经是online状态',
  [ExceptionCodes.Change_Publish_Not_Published]:
    '回滚的发布单不曾发上线，不可用于回滚',
  [ExceptionCodes.Change_Publish_Flow_Update_Fail]: '更新审批流状态动作失败',
  [ExceptionCodes.Change_Info_Change_Error]: '更新变更单失败',
  [ExceptionCodes.Change_Publish_Finished]: '发布单已经发布结束',

  //#region 表达式模板
  [ExceptionCodes.EXPRESSION_TEMPLATE_UPDATE_FAILED]: '表达式模板更新失败',
  //#endregion
  [ExceptionCodes.Module_Template_Create_Fail]: '模块模版创建失败',
  [ExceptionCodes.Module_Template_Duplicate_Name]:
    '已存在相同名称的模版，请重新填写',
  [ExceptionCodes.Domain_tag_Duplicate]: '已存在相同标签',
  [ExceptionCodes.DATA_SOURCE_LIST_BY_PAGE]: '数据源列表查询失败',
  [ExceptionCodes.GREY_CREATE_FAILED_NO_PUBLISH]: '找不到对应的发布单',
  [ExceptionCodes.GREY_NOT_FOUND]: '找不到对应的灰度单',
  [ExceptionCodes.GREY_GREY_UPDATE_FAILED]: '灰度单更新失败',
  [ExceptionCodes.GREY_NOT_CHANGE]: '灰度配置没有变动',
  [ExceptionCodes.HAS_GREY_NOT_DEPLOY]:
    '该模块有未部署的灰度单，请先部署灰度单',
  [ExceptionCodes.DEPLOY_GREY_FAILED]: '部署灰度单失败',
  [ExceptionCodes.GREY_CONFIG_PARAMS_ERROR]: '灰度配置参数错误',
  [ExceptionCodes.DOMAIN_PERMISSION_ADD_AUTHOR]: '仅管理员才可以新增人员',
  [ExceptionCodes.LINGZHU_PUSH_STATUS_ERROR]: 'lingzhu推状态执行失败',
  [ExceptionCodes.LINGZHU_CHANGE_STATUS_FAIL]:
    'lingzhu推变更状态失败,请检查当前变更单状态是否正确，并且检查参数是否有误，是否存在未关闭的发布单',
  [ExceptionCodes.Change_Rollback_no_content]:
    '没有可回滚的内容，请检查是否之前有线上版本',
  [ExceptionCodes.Change_Rollback_Forbidden]: '回滚单不允许再次回滚',
  [ExceptionCodes.Rollback_Need_Online_Status]:
    '最新的上线单且变更类型非回滚才可以回滚',

  //#region AI 相关
  [ExceptionCodes.AI_INVALID_OUTPUT]: 'AI 生成结果不合法',
  //#endregion
}
