import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { getTreacId, logger } from 'src/common/logger';
import { CommonException } from './common-exception';

@Catch()
export class GlobalExceptionsFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const { httpAdapter } = this.httpAdapterHost;
    const ctx = host.switchToHttp();
    const traceId = getTreacId();
    const httpStatus =
      exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
    let responseBody: object;

    logger.error('[Global Exception Catch]', exception as Error);

    if (exception instanceof CommonException) {
      const response = exception.getResponse() as { result: number; msg: string };
      responseBody = {
        result: response.result,
        msg: response.msg,
      };
    } else if (exception instanceof HttpException) {
      const res = exception.getResponse();
      if (typeof res === 'string') {
        responseBody = {
          result: httpStatus,
          msg: res,
        };
      } else {
        responseBody = res;
      }
    } else {
      responseBody = {
        result: HttpStatus.INTERNAL_SERVER_ERROR,
        msg: '服务异常',
      };
    }
    httpAdapter.reply(ctx.getResponse(), { ...responseBody, traceId }, httpStatus);
  }
}
