import { promiseValue } from '@ad/canal-shared'
import Redis from '@infra-node/redis'
import { ConfigService } from '@nestjs/config'

/**
 * Redis 异步对象
 */
const [redisPromise, resolveRedis] = promiseValue<Redis.Redis>()

/**
 * 初始化 Redis
 * @param configService 配置服务
 */
export function initRedis(configService: ConfigService) {
  const redis = new Redis({
    clusterName: 'adInfraBFF',
    kconfEnv: configService.get('redis.env'),
  })
  redis.defineCommand('setGetUserInfo', {
    numberOfKeys: 1,
    lua: `
local ret = redis.call('get', KEYS[1])
if ARGV[1] ~= '' and (ret == false or cjson.decode(ret).roomId == cjson.decode(ARGV[1]).roomId)
then
  redis.call('set', KEYS[1], ARGV[1], 'EX', 10)
end
return redis.call('get', KEYS[1])
`,
  })
  redis.defineCommand('delUserInfo', {
    numberOfKeys: 1,
    lua: `
local ret = redis.call('get', KEYS[1])
if ret ~= false and cjson.decode(ret).roomId == cjson.decode(ARGV[1]).roomId
then
  redis.call('del', KEYS[1])
  return true
end
return false
`,
  })
  resolveRedis(redis)
}

/**
 * 封装后的 redis
 */
export const redis = new Proxy(
  {},
  {
    get(o, p) {
      return async (...args: unknown[]) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return ((await redisPromise) as any)[p](...args)
      }
    },
  },
) as RedisCommands

/**
 * Redis 命令
 */
export interface RedisCommands {
  /**
   * 设置并获取（最新的）用户信息
   * @param key 键值
   * @param room 房间，false 表示不设置
   */
  // eslint-disable-next-line @typescript-eslint/ban-types
  setGetUserInfo(key: string, room: (string & {}) | ''): Promise<string | null>
  /**
   * 删除用户信息
   * @param key 键值
   * @param room 房间
   */
  delUserInfo(key: string, room: string): Promise<boolean>
}

declare module '@infra-node/redis' {
  interface Commands {
    setGetUserInfo: RedisCommands['setGetUserInfo']
    delUserInfo: RedisCommands['delUserInfo']
  }
}
