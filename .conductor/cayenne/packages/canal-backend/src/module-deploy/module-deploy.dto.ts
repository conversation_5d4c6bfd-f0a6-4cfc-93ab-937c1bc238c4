import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'

export class ModuleDeployDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块id' })
  readonly moduleId: string

  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '版本号' })
  readonly version: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: 'schema内容' })
  readonly content: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '发布阶段' })
  readonly stage: string

  @ApiProperty({ description: '前端泳道' })
  readonly frontLane?: string
  @ApiProperty({ description: '泳道' })
  readonly lane?: string
  @ApiProperty()
  createUser?: string
  @ApiProperty()
  updateUser?: string
  @ApiProperty()
  createTime?: number
  @ApiProperty()
  updateTime?: number
  @ApiProperty()
  greyType?: 'white' | 'lane' | 'percentage' | 'host'
  @ApiProperty()
  greyValue?: string
  @ApiProperty()
  greyKey?: string
  @ApiProperty()
  greyStatus?: number
  @ApiProperty({ description: '全局模块 ID' })
  globalModuleId?: string
}

export class SaveResponseDto {
  @ApiProperty({ description: 'id' })
  readonly id: string
}

export class DeployHistoryDto {
  @ApiProperty()
  moduleId: string
  @ApiProperty()
  domainCode: string
  @ApiProperty()
  pageNum: number
  @ApiProperty()
  pageSize: number
}

export class AutoDeployDto {
  @ApiProperty()
  module: ModuleDeployDto
}
export class StagingDeployDto {
  @ApiProperty()
  module: ModuleDeployDto
  @ApiProperty()
  isNewDeploy: boolean
}

export class ModuleDeployHistoryDto {
  @ApiProperty()
  moduleId: string
  @ApiProperty()
  domainCode: string
  @ApiProperty()
  pageNum: number
  @ApiProperty()
  pageSize: number
}

export class ModuleDeployInfoDto {
  @ApiProperty()
  moduleId: string
  @ApiProperty()
  version: number
  @ApiProperty()
  changeId: string
  @ApiProperty()
  changeName: string
  @ApiProperty()
  id: string
  @ApiProperty()
  createTime: number
  @ApiProperty()
  createUser: string
}

export class ModuleDeployInfoResponseDto {
  @ApiProperty({ type: [ModuleDeployInfoDto] })
  list: ModuleDeployInfoDto[]
  @ApiProperty()
  total: number
}
export class DeployDetailReqDto {
  @ApiProperty()
  id: string
  @ApiProperty()
  domainCode: string
}

export class PreviousModuleDeployDetailReqDto {
  @ApiProperty()
  id: string
}
