import { Body, Controller, Get, Post, Query, Session } from '@nestjs/common'
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { UserSession } from 'src/types'
import {
  AutoDeployDto,
  DeployDetailReqDto,
  ModuleDeployDto,
  ModuleDeployHistoryDto,
  ModuleDeployInfoResponseDto,
  PreviousModuleDeployDetailReqDto,
  SaveResponseDto,
  StagingDeployDto,
} from './module-deploy.dto'
import { ModuleDeployService } from './module-deploy.service'

@Controller('/rest/canal/deploy')
@ApiTags('module-deploy')
@Controller()
export class ModuleDeployController {
  public constructor(private moduleDeployService: ModuleDeployService) {}

  @Post('staging')
  @ApiBody({
    type: StagingDeployDto,
  })
  @ApiOperation({
    summary: '发布staging环境',
    operationId: 'doDeployStaging',
  })
  @CommonApiResponse(SaveResponseDto)
  public async deployStaging(
    @Session() userSession: UserSession,
    @Body()
    moduleDeployDto: StagingDeployDto,
  ) {
    const deploy = await this.moduleDeployService.create(
      moduleDeployDto.module,
      moduleDeployDto.isNewDeploy,
      userSession?.userInfo?.userCode,
    )

    return {
      id: deploy.id,
    }
  }

  @Post('module/auto')
  @ApiBody({
    type: AutoDeployDto,
  })
  @ApiOperation({
    summary: '自动部署, 找到最新的部署staging记录再次部署',
    operationId: 'autoDeploy',
  })
  @CommonApiResponse(ModuleDeployDto)
  public async autoDeploy(
    @Session() userSession: UserSession,
    @Body()
    moduleDeployDto: AutoDeployDto,
  ) {
    return await this.moduleDeployService.autoDeploy(
      moduleDeployDto.module,
      userSession,
    )
  }

  @Post('module/deploy/history')
  @ApiBody({
    type: ModuleDeployHistoryDto,
  })
  @ApiOperation({
    summary: '获取单个模块的线上部署历史信息',
    operationId: 'deployHistory',
  })
  @CommonApiResponse(ModuleDeployInfoResponseDto)
  public async deployHistory(
    @Session() userSession: UserSession,
    @Body()
    moduleDeployHistoryDto: ModuleDeployHistoryDto,
  ) {
    return await this.moduleDeployService.deployHistory(
      moduleDeployHistoryDto,
      userSession.userInfo?.userCode,
    )
  }

  @Post('module/schema')
  @ApiOperation({
    summary: '获取一条部署详情',
    operationId: 'getOneModuleDeployDetail',
  })
  @ApiBody({
    type: DeployDetailReqDto,
  })
  @CommonApiResponse(ModuleDeployDto)
  public async deployDetail(
    @Session() userSession: UserSession,
    @Body()
    detailDto: DeployDetailReqDto,
  ) {
    return await this.moduleDeployService.getOne(
      detailDto.id,
      detailDto.domainCode,
      userSession.userInfo?.userCode,
    )
  }

  @Get('module')
  @ApiOperation({
    summary: '通过环境获取模块部署',
    operationId: 'getModuleDeployByEnv',
  })
  @ApiBody({
    type: DeployDetailReqDto,
  })
  @CommonApiResponse(ModuleDeployDto)
  public async getModuleDeployByEnv(
    @Query('moduleId') moduleId: string,
    @Query('env') env: string,
  ) {
    return await this.moduleDeployService.findNewestByModuleIdAndVersion(
      moduleId,
      env,
    )
  }

  @Post('previous')
  @ApiBody({
    type: PreviousModuleDeployDetailReqDto,
  })
  @ApiOperation({
    summary: '获取前一条部署的详情',
    operationId: 'getPreviousModuleDeployDetail',
  })
  @CommonApiResponse(ModuleDeployDto)
  public async getPreviousRecordById(
    @Body()
    detailDto: PreviousModuleDeployDetailReqDto,
  ) {
    return await this.moduleDeployService.getPreviousRecordById(detailDto.id)
  }
}
