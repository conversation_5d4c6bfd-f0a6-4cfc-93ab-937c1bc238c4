import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { BusinessDomainService } from 'src/business-domain/business-domain.service'
import { ChangeModuleVersionService } from 'src/change-module-version/change-module-version.service'
import { ChangeService } from 'src/change/change.service'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { ModuleService } from 'src/module/module.service'
import { UserSession } from 'src/types'
import { Repository } from 'typeorm'
import { DeployHistoryDto, ModuleDeployDto } from './module-deploy.dto'
import { ModuleDeployEntity } from './module-deploy.entity'

@Injectable()
export class ModuleDeployService {
  public constructor(
    @InjectRepository(ModuleDeployEntity)
    public readonly moduleDeployRepo: Repository<ModuleDeployEntity>,
    private readonly businessDomainService: BusinessDomainService,
    private readonly moduleService: ModuleService,
    private readonly changeModuleVersionService: ChangeModuleVersionService,
    private readonly changeService: ChangeService,
  ) {}

  async create(
    moduleDeployDto: ModuleDeployDto,
    isNewDeploy: boolean,
    userCode: string,
  ) {
    if (
      !moduleDeployDto?.moduleId ||
      typeof moduleDeployDto?.content !== 'string' ||
      !moduleDeployDto?.stage
    ) {
      throw new CommonException(ExceptionCodes.Change_Deploy_Fail)
    }
    const now = new Date().getTime()
    const globalModuleIdInQuery = moduleDeployDto.globalModuleId
    if (moduleDeployDto.stage === 'staging' && !isNewDeploy) {
      // 覆盖最新的staging部署
      const queryBuilder = this.moduleDeployRepo
        .createQueryBuilder('moduleDeployEntity')
        .where('moduleDeployEntity.module_id = :moduleId', {
          moduleId: moduleDeployDto.moduleId,
        })
        .andWhere('moduleDeployEntity.module_version = :version', {
          version: moduleDeployDto.version,
        })
        .andWhere('moduleDeployEntity.stage = :stage', {
          stage: 'staging',
        })
      //如果是带明确泳道的情况下
      if (moduleDeployDto.lane || moduleDeployDto.frontLane) {
        queryBuilder
          .andWhere('moduleDeployEntity.lane = :lane', {
            lane: moduleDeployDto.lane || '',
          })
          .andWhere('moduleDeployEntity.front_lane = :frontLane', {
            frontLane: moduleDeployDto.frontLane || '',
          })
      }
      const existTarget = await queryBuilder
        .orderBy('moduleDeployEntity.id', 'DESC')
        .getOne()

      if (existTarget) {
        existTarget.content = moduleDeployDto.content
        existTarget.updateUser = userCode
        existTarget.updateTime = now
        await this.setGlobalModuleId(existTarget)
        if (!existTarget.globalModuleId) {
          // 线上部署 staging 时，查不到全局模块 ID，用请求里携带的值
          existTarget.globalModuleId = globalModuleIdInQuery
        }
        return await this.moduleDeployRepo.save(existTarget)
      }
    }
    moduleDeployDto.createTime = now
    moduleDeployDto.updateTime = now
    moduleDeployDto.createUser = userCode
    moduleDeployDto.updateUser = userCode
    await this.setGlobalModuleId(moduleDeployDto)
    if (!moduleDeployDto.globalModuleId) {
      // 线上部署 staging 时，查不到全局模块 ID，用请求里携带的值
      moduleDeployDto.globalModuleId = globalModuleIdInQuery
    }
    return await this.moduleDeployRepo.save(moduleDeployDto)
  }

  async autoDeploy(module: ModuleDeployDto, userSession: UserSession) {
    if (!module?.moduleId || !['prt', 'beta'].includes(module.stage)) {
      throw new CommonException(ExceptionCodes.Change_Deploy_Fail)
    }
    const moduleInfo = await this.moduleService.getDetailByIdWithVersion(
      module.moduleId,
      +module.version,
    )
    if (moduleInfo && moduleInfo.businessDomainCode) {
      /**
       * 检查是否有权限
       */
      const userCode = userSession.userInfo?.userCode
      const hasPermission = await this.businessDomainService.hasPermission(
        moduleInfo.businessDomainCode,
        userCode,
      )
      if (!hasPermission) {
        throw new CommonException(ExceptionCodes.User_Permission_Deined)
      }
      const now = new Date().getTime()
      /**
       * 如果有发布记录直接覆盖最新的
       * 如果没有则发布主干
       */
      const queryBuilder = this.moduleDeployRepo
        .createQueryBuilder('moduleDeployEntity')
        .where('moduleDeployEntity.module_id = :moduleId', {
          moduleId: module.moduleId,
        })
        .andWhere('moduleDeployEntity.module_version = :version', {
          version: module.version,
        })
        .andWhere('moduleDeployEntity.stage = :stage', {
          stage: module.stage,
        })
      const existTarget = await queryBuilder
        .orderBy('moduleDeployEntity.id', 'DESC')
        .getOne()
      if (existTarget) {
        existTarget.content = module.content
        existTarget.updateUser = userSession.userInfo?.userCode
        existTarget.updateTime = now
        await this.setGlobalModuleId(existTarget)
        return await this.moduleDeployRepo.save(existTarget)
      } else {
        module.createTime = now
        module.updateTime = now
        module.createUser = userCode
        module.updateUser = userCode
        await this.setGlobalModuleId(module)
        return await this.moduleDeployRepo.save(module)
      }
    }
  }

  async findNewestByModuleIdAndVersion(moduleId: string, stage: string) {
    return await this.moduleDeployRepo.findOne({
      where: {
        moduleId,
        stage,
      },
      order: {
        createTime: 'DESC',
      },
    })
  }

  async deployHistory(deployHistoryDto: DeployHistoryDto, userCode: string) {
    const hasPermission = await this.businessDomainService.hasPermission(
      deployHistoryDto.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    try {
      const queryBuilder = this.moduleDeployRepo
        .createQueryBuilder('moduleDeployEntity')
        .select([
          'moduleDeployEntity.id', // 假设你需要的字段
          'moduleDeployEntity.moduleId',
          'moduleDeployEntity.stage',
          'moduleDeployEntity.createTime',
          'moduleDeployEntity.version',
          'moduleDeployEntity.createUser',
        ])
        .where('moduleDeployEntity.module_id = :moduleId', {
          moduleId: deployHistoryDto.moduleId,
        })
        .andWhere('moduleDeployEntity.stage = :stage', {
          stage: 'production',
        })
      const [list, total] = await queryBuilder
        .orderBy('moduleDeployEntity.createTime', 'DESC')
        .skip(
          (deployHistoryDto.pageNum - 1) * (deployHistoryDto.pageSize || 10),
        )
        .take(deployHistoryDto.pageSize)
        .getManyAndCount()

      if (list?.length) {
        const changeModuleInfos =
          await this.changeModuleVersionService.getManyByModuleIdsAndVersion(
            list.map((item) => ({
              moduleId: item.moduleId,
              version: item.version.toString(),
            })),
          )
        if (changeModuleInfos?.length) {
          const changeInfos = await this.changeService.getMany(
            changeModuleInfos.map((change) => change.changeId),
          )
          if (changeInfos?.length) {
            const changeMap = Object.fromEntries(
              changeInfos.map(({ changeId, changeName }) => [
                changeId,
                changeName,
              ]),
            )
            const flattenedArray = list.flatMap(
              ({ moduleId, version, id, createTime, createUser }) =>
                changeModuleInfos
                  .filter(
                    ({ moduleId: _id, moduleVersion: _ver }) =>
                      _id === moduleId && _ver === version,
                  )
                  .map(({ changeId }) => ({
                    moduleId,
                    version,
                    changeId,
                    changeName: changeMap[changeId] || null, // 通过映射查找 changeName
                    id,
                    createTime,
                    createUser,
                  })),
            )
            return {
              list: flattenedArray,
              total,
            }
          }
        }
      }
      return {
        list,
        total,
      }
    } catch (e) {
      throw new CommonException(ExceptionCodes.Module_Deploy_History_Fail)
    }
  }

  /**
   * 获取确定的版本和模块的发布记录的上一次线上部署版本
   */
  async getPreviousRecordRecordByModuleIdAndVersion(
    moduleId: string,
    moduleVersion: number,
  ) {
    try {
      const deploy = await this.moduleDeployRepo
        .createQueryBuilder('moduleDeployEntity')
        .select(['moduleDeployEntity.id', 'moduleDeployEntity.createTime'])
        .where('moduleDeployEntity.module_id = :moduleId', {
          moduleId: moduleId,
        })
        .andWhere('moduleDeployEntity.version = :version', {
          version: moduleVersion,
        })
        .andWhere('moduleDeployEntity.stage = :stage', {
          stage: 'production',
        })
        .orderBy('moduleDeployEntity.createTime', 'DESC')
        .limit(1)
        .getOne()
      if (deploy?.id && deploy?.createTime) {
        return await this.moduleDeployRepo
          .createQueryBuilder('moduleDeployEntity')
          .where('moduleDeployEntity.createTime < :createTime', {
            createTime: deploy.createTime,
          })
          .andWhere('moduleDeployEntity.moduleId = :moduleId', {
            moduleId: moduleId,
          })
          .andWhere('moduleDeployEntity.stage = :stage', {
            stage: 'production',
          })
          .orderBy('moduleDeployEntity.createTime', 'DESC')
          .take(1)
          .getOne()
      } else {
        throw new CommonException(ExceptionCodes.Module_Get_Previous_Fail)
      }
    } catch (e) {
      throw new CommonException(ExceptionCodes.Module_Get_Previous_Fail)
    }
  }
  async getPreviousRecordById(currentId: string) {
    try {
      // 首先，获取当前记录的创建时间
      const currentRecord = await this.moduleDeployRepo
        .createQueryBuilder('moduleDeployEntity')
        .select([
          'moduleDeployEntity.createTime',
          'moduleDeployEntity.moduleId',
        ])
        .where('moduleDeployEntity.id = :currentId', { currentId })
        .getOne()

      if (!currentRecord) {
        throw new CommonException(ExceptionCodes.Module_Not_Found)
      }

      const createTime = currentRecord.createTime
      // 然后，查询上一条记录
      const previousRecord = await this.moduleDeployRepo
        .createQueryBuilder('moduleDeployEntity')
        .select(['moduleDeployEntity.id'])
        .where('moduleDeployEntity.createTime < :createTime', { createTime })
        .andWhere('moduleDeployEntity.moduleId = :moduleId', {
          moduleId: currentRecord.moduleId,
        })
        .andWhere('moduleDeployEntity.stage = :stage', { stage: 'production' })
        .orderBy('moduleDeployEntity.createTime', 'DESC')
        .take(1)
        .getOne()

      return previousRecord || null // 如果没有找到上一条记录，则返回 null
    } catch (e) {
      throw new CommonException(ExceptionCodes.Module_Get_Previous_Fail)
    }
  }

  async getOne(id: string, domainCode: string, userCode: string) {
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    return this.moduleDeployRepo.findOne({
      where: {
        id,
      },
    })
  }

  /**
   * 设置全局模块 ID
   * @param moduleDeploy 模块部署
   */
  async setGlobalModuleId(moduleDeploy: {
    moduleId: string
    globalModuleId?: string
  }) {
    moduleDeploy.globalModuleId =
      await this.moduleService.getGlobalModuleIdByModuleId(
        moduleDeploy.moduleId,
      )
    if (moduleDeploy.globalModuleId === moduleDeploy.moduleId) {
      delete moduleDeploy.globalModuleId
    }
  }
}
