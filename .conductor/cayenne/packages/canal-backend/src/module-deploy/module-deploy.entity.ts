import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'module_deploy' })
export class ModuleDeployEntity {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  id: string

  @Column({
    name: 'module_id',
    type: 'char',
    length: 36,
    comment: '模块id',
  })
  moduleId: string

  @Column({
    name: 'module_version',
    type: 'bigint',
    comment: '版本号',
  })
  version: number

  @Column({
    name: 'global_module_id',
    type: 'char',
    length: 36,
    comment: '全局模块 ID',
  })
  globalModuleId?: string

  @Column({
    name: 'version_context',
    type: 'text',
    comment: '内容',
  })
  content: string

  @Column({
    type: 'varchar',
    length: 255,
    comment: '发布阶段',
  })
  stage: string

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
  })
  createUser: string

  @Column({
    name: 'lane',
    type: 'varchar',
    length: 100,
    comment: '泳道',
  })
  lane?: string

  @Column({
    name: 'front_lane',
    type: 'varchar',
    length: 100,
    comment: '前端泳道',
  })
  frontLane?: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    select: false,
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number

  @Column({
    type: 'varchar',
    length: 100,
    comment: '灰度策略类型',
    name: 'grey_type',
  })
  greyType: 'white' | 'lane' | 'percentage' | 'host'

  @Column({
    type: 'varchar',
    length: 255,
    default: '',
    comment: '灰度值',
    name: 'grey_value',
  })
  greyValue: string

  @Column({
    type: 'varchar',
    length: 255,
    default: '',
    comment: '灰度键',
    name: 'grey_key',
  })
  greyKey: string

  @Column({
    type: 'tinyint',
    comment: '灰度状态',
    nullable: true,
    name: 'grey_status',
  })
  greyStatus: number
}
