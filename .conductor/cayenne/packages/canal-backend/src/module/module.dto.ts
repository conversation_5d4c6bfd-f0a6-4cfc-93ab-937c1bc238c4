import { ModuleType } from '@ad/canal-shared'
import { ApiProperty } from '@nestjs/swagger'
import { IsInt, IsNotEmpty, IsNumber, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
import { DBTablePageInfo, LingZhuAdditionalParameters } from 'src/common/dto'

export class ModuleListDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域code' })
  readonly businessDomainCode: string

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '页码' })
  readonly pageNum: number

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '分页大小' })
  readonly pageSize: number
}

export class ModuleListUnderChangeDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '变更id' })
  readonly changeId: string
  @ApiProperty({ description: '是否需要携带模块内容' })
  readonly needContent?: boolean
}

export class CreateModuleDto extends LingZhuAdditionalParameters {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块名称' })
  readonly name: string

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块类型' })
  readonly type: ModuleType

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '容器类型：web-0，H5-1，RN-2，Native-3' })
  readonly containerType: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域code' })
  readonly businessDomainCode: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '变更id' })
  readonly changeId: string

  @ApiProperty({ description: '模版ID' })
  readonly templateId?: string
}

export class UpdateModuleDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块id' })
  readonly id: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块版本' })
  readonly version: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块内容' })
  readonly content: string

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @ApiProperty({ description: '工作量' })
  readonly workload?: number
}

/**
 * 更新模块元信息 DTO
 */
export class UpdateModuleMetaDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块id' })
  readonly id: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块名称' })
  readonly name: string
}

export class CreateOrUpdateModuleResponse {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块id' })
  readonly id: string
}
export class ModuleEntityDto extends CreateModuleDto {
  @ApiProperty({ description: '模块id' })
  readonly id: string
}
export class ModuleDetail extends CreateModuleDto {
  @ApiProperty({ description: '模块id' })
  readonly id: string

  @ApiProperty({ description: '模块版本' })
  readonly version: number

  @ApiProperty({ description: '模块内容' })
  readonly content: string

  @ApiProperty({ description: '是否可编辑' })
  readonly canEdit?: boolean

  @ApiProperty()
  readonly updateTime: number

  @ApiProperty()
  readonly updateUser: string
}
export class AddOrDeleteModuleDto extends LingZhuAdditionalParameters {
  @ApiProperty({ description: '业务域code' })
  readonly domainCode: string
  @ApiProperty({ description: '变更id' })
  readonly changeId: string
  @ApiProperty({ description: '模块id' })
  readonly moduleId: string
}

/**
 * 获取模块的权限 body 数据传输对象
 */
export class GetPermissionBodyDto {
  @ApiProperty({ description: '模块 ID' })
  readonly id: string
  @ApiProperty({ description: '模块版本' })
  readonly version: number
  @ApiProperty({ description: '当前用户没有权限时，是否直接授予权限' })
  readonly grant: boolean
  @ApiProperty({ description: '房间 ID，用于区分设计器页面，由前端自动生成' })
  readonly roomId: string
}

/**
 * 用户信息
 */
export class UserInfo {
  @ApiProperty({ description: '邮箱前缀' })
  readonly userCode: string
  @ApiProperty({ description: '用户名' })
  readonly userName: string
  @ApiProperty({ description: '头像' })
  readonly avatar: string
  @ApiProperty({ description: '部门名称' })
  readonly department: string
}

/**
 * 房间
 */
export class Room {
  @ApiProperty({ description: '用户' })
  readonly user: UserInfo
  @ApiProperty({ description: '房间 ID' })
  readonly roomId: string
}

/**
 * 获取模块的权限响应数据传输对象
 */
export class GetPermissionResDto {
  @ApiProperty({ description: '正在编辑的房间', nullable: true })
  readonly editingRoom?: Room
}

/**
 * 取消模块的权限 body 数据传输对象
 */
export class CancelPermissionBodyDto {
  @ApiProperty({ description: '模块 ID' })
  readonly id: string
  @ApiProperty({ description: '模块版本' })
  readonly version: number
  @ApiProperty({ description: '房间 ID，用于区分设计器页面，由前端自动生成' })
  readonly roomId: string
}

/**
 * 校验是否可以或者需要进行merge动作
 */
export class CheckMergeDto {
  @ApiProperty({ description: '模块 ID' })
  readonly moduleId: string

  @ApiProperty({ description: '变更id' })
  readonly changeId: string
}

export class CheckMergeResDto {
  @ApiProperty()
  hasOnlineVersion: boolean
  @ApiProperty()
  onlineVersion?: number
  @ApiProperty()
  checkoutVersion?: number
}

export class UpdateModuleComponent {
  @ApiProperty()
  moduleId: string
  @ApiProperty()
  moduleVersion: number
  @ApiProperty()
  componentId: string
  @ApiProperty()
  latestVersion: number
  @ApiProperty()
  currentVersion: number
}

export class MixModuleDto {
  @ApiProperty()
  updateUser: string
  @ApiProperty()
  updateTime: number
  @ApiProperty()
  version: number
  @ApiProperty()
  name: string
  @ApiProperty()
  id: string
  @ApiProperty()
  moduleId: string
  @ApiProperty()
  changeId: string
  @ApiProperty()
  moduleVersion: number
  @ApiProperty()
  checkoutModuleVersion?: number | undefined
  @ApiProperty()
  createUser: string
  @ApiProperty()
  createTime: number
  @ApiProperty()
  content?: string
  type: ModuleType
}
export class PublishListItemDto {
  @ApiProperty()
  version: number
  @ApiProperty()
  id: string
  @ApiProperty()
  name: string
  @ApiProperty()
  type: number
  @ApiProperty()
  containerType: number
  @ApiProperty()
  businessDomainCode: string
  @ApiProperty()
  createUser: string
  @ApiProperty()
  updateUser: string
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateTime: number
}

export class PublishListResponseDto {
  @ApiProperty({ description: '分页信息', type: DBTablePageInfo })
  pageInfo: {
    pageNum: number
    pageSize: number
    total: number
  }
  @ApiProperty({ description: '发布单列表', type: [PublishListItemDto] })
  list: PublishListItemDto[]
}

export class ModuleSimpleDto {
  @ApiProperty({ description: '模块ID' })
  readonly id: string

  @ApiProperty({ description: '模块名称' })
  readonly name: string
}

export class OnlineModuleItemDto {
  @ApiProperty({ description: '模块版本id' })
  readonly id: string
  @ApiProperty({ description: '模块版本' })
  readonly version: number
  @ApiProperty({ description: '模块版本状态' })
  readonly status: number
  @ApiProperty()
  createUser: string
  @ApiProperty()
  updateUser: string
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateTime: number
  @ApiProperty({ description: '模块信息', type: ModuleSimpleDto })
  readonly module: ModuleSimpleDto
}

export class OnlineModuleListDto {
  @ApiProperty({ type: [OnlineModuleItemDto] })
  list: OnlineModuleItemDto[]
  @ApiProperty()
  total: number
}

export class SearchModuleListItemDto {
  @ApiProperty({ description: 'moduleVersion唯一标识 ID' })
  readonly id: string

  @ApiProperty({ description: '变更 ID' })
  readonly changeId: string

  @ApiProperty({ description: '变更名称' })
  readonly changeName: string

  @ApiProperty({ description: '变更状态' })
  readonly changeStatus: number

  @ApiProperty({ description: '版本号' })
  readonly version: string

  @ApiProperty({ description: '模块 ID' })
  readonly moduleId: string

  @ApiProperty({ description: '模块名称' })
  readonly moduleName: string

  @ApiProperty({ description: '创建时间（时间戳）' })
  readonly createTime: string

  @ApiProperty({ description: '创建用户' })
  readonly createUser: string

  @ApiProperty({ description: '更新时间（时间戳）' })
  readonly updateTime: string

  @ApiProperty({ description: '更新用户' })
  readonly updateUser: string

  @ApiProperty({ description: '状态' })
  readonly status: number

  @ApiProperty({ description: '组件信息，可能为空' })
  readonly components: string
}

export class SearchModuleListDto {
  @ApiProperty({ type: [SearchModuleListItemDto] })
  list: SearchModuleListItemDto[]
  @ApiProperty()
  total: number
}

export class ModuleVersionDTO {
  @ApiProperty({ description: '模块版本ID' })
  id: string

  @ApiProperty({ description: '模块ID' })
  moduleId: string

  @ApiProperty({ description: '版本号' })
  version: number

  @ApiProperty({ description: '状态' })
  status: number

  @ApiProperty({ description: '内容' })
  content: string

  @ApiProperty({ description: '工作量' })
  workload: number

  @ApiProperty({ description: '创建人' })
  createUser: string

  @ApiProperty({ description: '更新人' })
  updateUser: string

  @ApiProperty({ description: '创建时间' })
  createTime: number

  @ApiProperty({ description: '更新时间' })
  updateTime: number
}

export class ComponentVersionDTO {
  @ApiProperty({ description: '组件版本ID' })
  id: string

  @ApiProperty({ description: '组件ID' })
  componentId: string

  @ApiProperty({ description: '版本' })
  version: number

  @ApiProperty({ description: '示例图', required: false })
  coverUrl?: string

  @ApiProperty({ description: '关联物料中心组件ID', required: false })
  associatedComponentId?: string

  @ApiProperty({ description: '关联物料中心组件版本', required: false })
  associatedComponentVersion?: string

  @ApiProperty({ description: '资源URL', required: false })
  resourceUrl?: string

  @ApiProperty({ description: '属性配置', required: false })
  propsConfig?: string

  @ApiProperty({ description: '事件key', required: false })
  eventKey?: string

  @ApiProperty({ description: '创建人' })
  createUser: string

  @ApiProperty({ description: '更新人' })
  updateUser: string

  @ApiProperty({ description: '创建时间' })
  createTime: number

  @ApiProperty({ description: '更新时间' })
  updateTime: number

  @ApiProperty({
    description: '组件分类，即物料 Schema 里的 group',
    required: false,
  })
  group?: string
}
export class UpdateModuleComponentCheckResultDto {
  @ApiProperty({ description: '老版本的组件版本内容' })
  old: ComponentVersionDTO
  @ApiProperty({ description: '最新版本的组件版本内容' })
  newest: ComponentVersionDTO
  @ApiProperty()
  schema: ModuleVersionDTO
}
