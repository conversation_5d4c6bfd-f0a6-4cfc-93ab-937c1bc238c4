import { ModuleType } from '@ad/canal-shared'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { BusinessDomainService } from 'src/business-domain/business-domain.service'
import { ChangeModuleVersion } from 'src/change-module-version/change-module-version.entity'
import { ChangeModuleVersionService } from 'src/change-module-version/change-module-version.service'
import { Change, ChangeStatus } from 'src/change/change.entity'
import { ChangeService } from 'src/change/change.service'
import { logger } from 'src/common/logger'
import { ComponentVersionService } from 'src/component-version/component-version.service'
import { ModuleTemplateService } from 'src/module-template/module-template.service'
import { ModuleVersionLogService } from 'src/module-version-log/module-version-log.service'
import { ModuleVersion } from 'src/module-version/module-version.entity'
import { ModuleVersionService } from 'src/module-version/module-version.service'
import { redis } from 'src/redis'
import { getCoeditRedisKey } from 'src/redis/keys'
import { Brackets, DataSource, Repository } from 'typeorm'
import { CommonException } from '../exceptions/common-exception'
import { ExceptionCodes, ExceptionMsg } from '../exceptions/excepition-codes'
import {
  AddOrDeleteModuleDto,
  CheckMergeDto,
  CreateModuleDto,
  MixModuleDto,
  ModuleDetail,
  ModuleListDto,
  ModuleListUnderChangeDto,
  UpdateModuleComponent,
  UpdateModuleDto,
  UpdateModuleMetaDto,
} from './module.dto'
import { ModuleEntity } from './module.entity'

const ModuleStatus = {
  Online: 1,
  NotOnline: 0,
}

@Injectable()
export class ModuleService {
  public constructor(
    private readonly dataSource: DataSource,
    private readonly businessDomainService: BusinessDomainService,
    private readonly moduleVersionService: ModuleVersionService,
    private readonly changeModuleVersionService: ChangeModuleVersionService,
    private readonly changeService: ChangeService,
    private readonly moduleTemplateService: ModuleTemplateService,
    private readonly moduleVersionLogService: ModuleVersionLogService,
    private readonly componentVersionService: ComponentVersionService,
    @InjectRepository(ModuleEntity)
    public readonly moduleRepo: Repository<ModuleEntity>,
    @InjectRepository(ModuleVersion)
    public readonly moduleVersionRepo: Repository<ModuleVersion>,
  ) {}

  async getAll({ businessDomainCode, pageNum, pageSize }: ModuleListDto) {
    const [list, total] = await this.moduleRepo
      .createQueryBuilder('module')
      .where('module.domain_code = :businessDomainCode', {
        businessDomainCode,
      })
      .select()
      .addSelect(`module.page_type = ${ModuleType.GLOBAL}`, 'isGlobal')
      .orderBy('isGlobal', 'DESC')
      .addOrderBy('module.create_time', 'DESC')
      .skip((pageNum - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount()

    const moduleList = []
    for (let i = 0; i < list.length; i++) {
      const module = list[i]
      const moduleVersion =
        await this.moduleVersionService.getLatestVersionByModuleId(module.id)

      if (moduleVersion) {
        moduleList.push({
          ...module,
          version: moduleVersion.version,
        })
      }
    }

    return {
      list: moduleList,
      total,
    }
  }

  async create(createModuleDto: CreateModuleDto, userCode: string) {
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      createModuleDto.businessDomainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    let templateContent = ''
    /**
     * 如果有模版，则从模版内获取schema
     */
    if (createModuleDto.templateId) {
      const template = await this.moduleTemplateService.findOne({
        id: createModuleDto.templateId,
      })
      templateContent = template?.content || ''
    }
    /**
     * 创建模块实体
     */
    const module = new ModuleEntity()
    const now = new Date().getTime()

    module.name = createModuleDto.name
    module.type = createModuleDto.type
    module.containerType = createModuleDto.containerType
    module.businessDomainCode = createModuleDto.businessDomainCode
    module.createUser = userCode
    module.updateUser = userCode
    module.createTime = now
    module.updateTime = now
    if (module.type === ModuleType.GLOBAL) {
      // 使用 UNIQUE INDEX 强制业务域下唯一
      module.globalModuleLock = createModuleDto.businessDomainCode
    }

    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction('SERIALIZABLE')

    let newModule: ModuleEntity | null = null
    const version = 1

    try {
      if (module.type === ModuleType.GLOBAL) {
        const oldGlobalModule = await queryRunner.manager.findOne(
          ModuleEntity,
          {
            where: {
              type: ModuleType.GLOBAL,
              businessDomainCode: module.businessDomainCode,
            },
          },
        )
        if (oldGlobalModule) {
          throw new CommonException(ExceptionCodes.Module_Global_Already_Exists)
        }
      }
      newModule = await queryRunner.manager.save(module)
      /**
       * 模块版本
       */
      const moduleVersion = new ModuleVersion()

      moduleVersion.moduleId = newModule.id
      moduleVersion.content = templateContent
      moduleVersion.status = ModuleStatus.NotOnline
      moduleVersion.createUser = userCode
      moduleVersion.updateUser = userCode
      moduleVersion.createTime = now
      moduleVersion.updateTime = now
      moduleVersion.version = version
      /**
       * 与变更关联
       */
      const changeModuleVersion = new ChangeModuleVersion()
      changeModuleVersion.moduleId = newModule.id
      changeModuleVersion.changeId = createModuleDto.changeId
      changeModuleVersion.moduleVersion = 1
      // 初始版本
      changeModuleVersion.checkoutModuleVersion = 1
      changeModuleVersion.createUser = userCode
      changeModuleVersion.updateUser = userCode
      changeModuleVersion.createTime = now
      changeModuleVersion.updateTime = now

      await queryRunner.manager.save(moduleVersion)
      await queryRunner.manager.save(changeModuleVersion)

      await queryRunner.commitTransaction()
    } catch (err) {
      logger.error('[Module Service] create error', err as unknown as Error)
      await queryRunner.rollbackTransaction()
      if (err instanceof CommonException) throw err
      throw new CommonException(ExceptionCodes.Module_Create_Error)
    } finally {
      await queryRunner.release()
    }

    /**
     * 加上版本供lingzhu消费数据
     */
    return {
      id: newModule.id,
      version: version,
    }
  }

  async update(updateModuleDto: UpdateModuleDto, userCode: string) {
    const module = await this.getDetailById(updateModuleDto.id)

    if (!module) {
      throw new CommonException(ExceptionCodes.Component_Not_Found)
    }

    const hasPermission = await this.businessDomainService.hasPermission(
      module.businessDomainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    const now = new Date().getTime()
    module.updateTime = now
    module.updateUser = userCode

    const moduleVersion = await this.moduleVersionService.getDetailByModule(
      module.id,
      updateModuleDto.version,
    )

    if (!moduleVersion) {
      throw new CommonException(ExceptionCodes.Module_Version_Not_Found)
    }

    // todo： 已经上线的，发布中的不可以进行编辑
    if (Number(moduleVersion.status) === ModuleStatus.Online) {
      throw new CommonException(ExceptionCodes.Module_Online_Cannot_Edit)
    }

    //查询对应的变更
    const changeModuleVersion =
      await this.changeModuleVersionService.getByModuleIdAndVersion(
        module.id,
        updateModuleDto.version,
      )
    if (Array.isArray(changeModuleVersion) && changeModuleVersion.length) {
      const changeId = changeModuleVersion?.[0]?.changeId
      /**
       * 变更的状态必须是未完成的
       */
      const changeObject = await this.changeService.get(changeId)
      if (!changeObject) {
        throw new CommonException(ExceptionCodes.Module_Change_Not_Found)
      }
      if (
        [
          ChangeStatus.PUBLISHING,
          ChangeStatus.PUBLISHED,
          ChangeStatus.CLOSED,
        ].indexOf(Number(changeObject.status)) > -1
      ) {
        throw new CommonException(
          ExceptionCodes.Module_Save_Change_Status_Error,
        )
      }
    } else {
      throw new CommonException(ExceptionCodes.Module_Change_Not_Found)
    }

    moduleVersion.content = updateModuleDto.content
    moduleVersion.workload += updateModuleDto.workload || 0
    moduleVersion.updateTime = now
    moduleVersion.updateUser = userCode

    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction()

    try {
      await queryRunner.manager.save(module)
      await queryRunner.manager.save(moduleVersion)
      await this.moduleVersionLogService.create(
        updateModuleDto.id,
        updateModuleDto.version,
        updateModuleDto.content,
        now,
        userCode,
      )
      await queryRunner.commitTransaction()
    } catch (e) {
      logger.error('[Module Service] update error', e as unknown as Error)
      await queryRunner.rollbackTransaction()
      throw new CommonException(ExceptionCodes.Module_Create_Error)
    } finally {
      await queryRunner.release()
    }
  }

  /**
   * 更新元信息
   */
  async updateMeta(updateModuleMetaDto: UpdateModuleMetaDto, userCode: string) {
    const module = await this.getDetailById(updateModuleMetaDto.id)

    if (!module) {
      throw new CommonException(ExceptionCodes.Component_Not_Found)
    }

    const hasPermission = await this.businessDomainService.hasPermission(
      module.businessDomainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    module.name = updateModuleMetaDto.name
    module.updateTime = Date.now()
    module.updateUser = userCode
    await this.moduleRepo.save(module)
  }

  async getDetailById(id: string) {
    return await this.moduleRepo.findOneBy({ id })
  }

  async getDetailByIdWithVersion(
    id: string,
    version?: number,
  ): Promise<Partial<ModuleDetail>> {
    const module = await this.getDetailById(id)
    let moduleVersion: ModuleVersion | null

    if (!module) {
      throw new CommonException(ExceptionCodes.Module_Not_Found)
    }

    if (version) {
      moduleVersion = await this.moduleVersionService.getDetailByModule(
        id,
        version,
      )
    } else {
      moduleVersion =
        await this.moduleVersionService.getLatestVersionByModuleId(id)
    }

    if (!moduleVersion) {
      throw new CommonException(ExceptionCodes.Module_Not_Found)
    }

    // 获取module的状态
    //查询对应的变更
    const changeModuleVersion =
      await this.changeModuleVersionService.getByModuleIdAndVersion(
        module.id,
        moduleVersion.version,
      )
    // 获取是否能编辑的状态
    let canEdit = false
    let changeId = ''
    if (Array.isArray(changeModuleVersion) && changeModuleVersion.length) {
      changeId = changeModuleVersion?.[0]?.changeId
      /**
       * 获取变更状态
       */
      const changeObject = await this.changeService.get(changeId)
      if (changeObject) {
        if (
          [
            ChangeStatus.PUBLISHING,
            ChangeStatus.PUBLISHED,
            ChangeStatus.CLOSED,
          ].indexOf(Number(changeObject.status)) > -1
        ) {
          canEdit = false
        } else {
          canEdit = true
        }
      }
    }
    return {
      ...module,
      version: moduleVersion.version,
      content: moduleVersion.content,
      canEdit: canEdit,
      changeId,
      updateTime: moduleVersion.updateTime,
      updateUser: moduleVersion.updateUser,
    }
  }

  /**
   * 获取全局模块详情
   * @param domainCode （业务）域代码
   */
  async getGlobalDetail(domainCode: string) {
    const id = await this.getGlobalModuleId(domainCode)
    if (!id) {
      throw new CommonException(ExceptionCodes.Module_Not_Found)
    }
    return await this.getDetailByIdWithVersion(id)
  }

  async updateStatus(
    moduleId: string,
    moduleVersion: number,
    status: number,
    userCode: string,
  ) {
    const module = await this.getDetailById(moduleId)

    if (!module) {
      throw new CommonException(ExceptionCodes.Component_Not_Found)
    }

    const hasPermission = await this.businessDomainService.hasPermission(
      module.businessDomainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    const moduleVersionEntity =
      await this.moduleVersionService.getDetailByModule(moduleId, moduleVersion)

    if (!moduleVersionEntity) {
      throw new CommonException(ExceptionCodes.Module_Version_Not_Found)
    }

    moduleVersionEntity.status = status
    moduleVersionEntity.updateTime = new Date().getTime()
    moduleVersionEntity.updateUser = userCode
    await this.moduleVersionRepo.save(moduleVersionEntity)
  }
  /**
   * 获取一个变更下的所有模块
   */
  async getAllByChangId({ changeId, needContent }: ModuleListUnderChangeDto) {
    try {
      const list =
        await this.changeModuleVersionService.getListByChangeId(changeId)
      if (Array.isArray(list)) {
        const moduleList: MixModuleDto[] = []
        await Promise.all(
          list.map(async (item) => {
            if (item.moduleId) {
              const module = await this.moduleRepo.findOneBy({
                id: item.moduleId,
              })
              const moduleVersion =
                await this.moduleVersionService.getDetailByModule(
                  item.moduleId,
                  item.moduleVersion,
                )
              if (module && moduleVersion) {
                const _m: MixModuleDto = {
                  ...item,
                  updateUser: moduleVersion.updateUser,
                  updateTime: moduleVersion.updateTime,
                  version: moduleVersion.version,
                  name: module.name,
                  id: module.id,
                  type: module.type,
                }
                // 标识是否需要携带模块的schema
                if (needContent) {
                  _m.content = moduleVersion.content
                }
                moduleList.push(_m)
              }
            }
          }),
        )

        return {
          list: moduleList.sort((a, b) => {
            const d =
              +(b.type === ModuleType.GLOBAL) - +(a.type === ModuleType.GLOBAL)
            if (d) return d
            if (a.updateTime < b.updateTime) {
              return 1
            } else {
              return -1
            }
          }),
        }
      }
    } catch (error) {
      throw new CommonException(ExceptionCodes.Module_Get_All_By_ChangeId_Error)
    }
  }
  async getAllOnlineModule({
    domainCode,
    pageNum,
    pageSize,
    name,
    createUser,
    moduleId,
  }: {
    domainCode: string
    pageNum: number
    pageSize: number
    name?: string
    createUser?: string
    moduleId?: string
  }) {
    const queryBuilder = this.moduleVersionRepo
      .createQueryBuilder('moduleVersion')
      .leftJoinAndMapOne(
        'moduleVersion.module',
        ModuleEntity,
        'module',
        'module.id = moduleVersion.module_id',
      )
      .where('module.domain_code = :domainCode', { domainCode })
      .select([
        'moduleVersion.id',
        'moduleVersion.version',
        'moduleVersion.createTime',
        'moduleVersion.createUser',
        'moduleVersion.updateTime',
        'moduleVersion.updateUser',
        'moduleVersion.status',
        'module.id',
        'module.name',
        'module.type',
        'module.businessDomainCode',
      ])
      .addSelect(`module.page_type = ${ModuleType.GLOBAL}`, 'isGlobal')
      .orderBy('isGlobal', 'DESC')
      .addOrderBy('moduleVersion.createTime', 'DESC')
      .skip((pageNum - 1) * pageSize)
      .take(pageSize)

    queryBuilder.andWhere('moduleVersion.status IN (:...status)', {
      status: [ModuleStatus.Online],
    })

    // 如果提供了模糊查询条件，则添加模糊查询条件
    if (name) {
      queryBuilder.andWhere('module.name LIKE :name', {
        name: `%${name}%`,
      })
    }
    if (createUser) {
      queryBuilder.andWhere('moduleVersion.createUser LIKE :createUser', {
        createUser: `%${createUser}%`,
      })
    }
    if (moduleId) {
      queryBuilder.andWhere('moduleVersion.module_id LIKE :moduleId', {
        moduleId: `%${moduleId}%`,
      })
    }
    const [moduleVersions, total] = await queryBuilder.getManyAndCount()

    return {
      list: moduleVersions,
      total,
    }
  }

  /**
   * 获取当前业务域下所有已上线的模块列表
   * @param param0
   * @returns
   */
  async getModuleList({
    domainCode,
    pageNum,
    pageSize,
    name,
    createUser,
    moduleId,
    componentId,
    dataSourceId,
  }: {
    domainCode: string
    pageNum: number
    pageSize: number
    name?: string
    createUser?: string
    moduleId?: string
    componentId?: string
    dataSourceId?: string
    status?: number[]
  }) {
    const queryBuilder = this.moduleVersionRepo
      .createQueryBuilder('moduleVersion')
      .leftJoinAndMapOne(
        'moduleVersion.module',
        ModuleEntity,
        'module',
        'module.id = moduleVersion.module_id',
      )
      .leftJoinAndMapOne(
        'moduleVersion.changeModuleVersion',
        ChangeModuleVersion,
        'changeModuleVersion',
        'changeModuleVersion.moduleId = moduleVersion.module_id AND changeModuleVersion.moduleVersion = moduleVersion.version',
      )
      .leftJoinAndMapOne(
        'changeModuleVersion.change',
        Change,
        'change',
        'change.changeId = changeModuleVersion.change_id',
      )
      .where('module.domain_code = :domainCode', { domainCode })
      .andWhere('JSON_VALID(moduleVersion.content)')
      .andWhere(
        new Brackets((qb) => {
          qb.where('moduleVersion.status = :onlineStatus', {
            onlineStatus: 1, // moduleVersion.status = 1
          }).orWhere('change.status IN (:...developStatuses)', {
            developStatuses: [
              ChangeStatus.DEVELOPING,
              ChangeStatus.PUBLISHING, // 添加您需要匹配的其他状态
            ],
          })
        }),
      )

    // 如果提供了模糊查询条件，则添加模糊查询条件
    if (name) {
      queryBuilder.andWhere('module.name LIKE :name', {
        name: `%${name}%`,
      })
    }
    if (createUser) {
      queryBuilder.andWhere('moduleVersion.createUser LIKE :createUser', {
        createUser: `%${createUser}%`,
      })
    }
    if (moduleId) {
      queryBuilder.andWhere('moduleVersion.module_id LIKE :moduleId', {
        moduleId: `%${moduleId}%`,
      })
    }
    if (componentId) {
      queryBuilder.andWhere(
        'MATCH(moduleVersion.content) AGAINST(:componentId IN NATURAL LANGUAGE MODE)',
        {
          componentId: `"@ad/canal-biz-components::${componentId}"`,
        },
      )
    }

    if (dataSourceId) {
      queryBuilder.andWhere(
        'MATCH(moduleVersion.content) AGAINST(:dataSourceId IN NATURAL LANGUAGE MODE)',
        {
          dataSourceId: dataSourceId,
        },
      )
    }

    queryBuilder.select([
      'moduleVersion.id AS id',
      'changeModuleVersion.changeId AS changeId',
      'change.changeName AS changeName',
      'change.status AS changeStatus',
      'moduleVersion.version AS version',
      'module.id AS moduleId',
      'module.name AS moduleName',
      'moduleVersion.createTime AS createTime',
      'moduleVersion.createUser AS createUser',
      'moduleVersion.updateTime AS updateTime',
      'moduleVersion.updateUser AS updateUser',
      'moduleVersion.status AS status',
      'JSON_UNQUOTE(JSON_EXTRACT(moduleVersion.version_context, "$.componentCodes")) AS components',
    ])

    queryBuilder
      .addSelect(`module.page_type = ${ModuleType.GLOBAL}`, 'isGlobal')
      .orderBy('isGlobal', 'DESC')
      .addOrderBy('moduleVersion.createTime', 'DESC')
      .skip((pageNum - 1) * pageSize)
      .take(pageSize)

    const moduleVersions = await queryBuilder.getRawMany()
    const total = await queryBuilder.getCount()

    return {
      list: moduleVersions,
      total,
    }
  }

  /**
   * 选择模块加入当前变更
   */
  async addToChange(
    { moduleId, changeId, domainCode }: AddOrDeleteModuleDto,
    userCode: string,
  ) {
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    /**
     * 检查变更是否已经有对应的module
     */
    const changeModuleList =
      await this.changeModuleVersionService.getModuleByChangeIdAndModuleId(
        changeId,
        moduleId,
      )
    if (Array.isArray(changeModuleList) && changeModuleList?.length) {
      return {
        result: ExceptionCodes.Change_Module_Duplicate,
        msg:
          ExceptionMsg[ExceptionCodes.Change_Module_Duplicate] ||
          '变更中已存在该模块',
        data: {
          id: moduleId,
          version: changeModuleList[0].moduleVersion,
        },
      }
    }

    let version = 1
    /**
     * 获取线上版本
     */
    const onlineVersion =
      await this.moduleVersionService.getOnlineVersionByModuleId(moduleId)
    if (!onlineVersion) {
      throw new CommonException(ExceptionCodes.Module_Have_No_Online_Version)
    }

    /**
     * 获取最新版本
     */
    const latestVersion =
      await this.moduleVersionService.getLatestVersionByModuleId(moduleId)
    if (
      latestVersion &&
      Number(latestVersion?.version) > Number(onlineVersion?.version)
    ) {
      version = Number(latestVersion?.version) + 1
    } else {
      version = Number(onlineVersion.version) + 1
    }
    /**
     * 创建模块实体
     */
    const moduleVersionObj = new ModuleVersion()
    const now = new Date().getTime()

    moduleVersionObj.moduleId = moduleId
    moduleVersionObj.status = 0
    moduleVersionObj.version = version
    moduleVersionObj.createUser = userCode
    moduleVersionObj.updateUser = userCode
    moduleVersionObj.createTime = now
    moduleVersionObj.updateTime = now
    moduleVersionObj.content = onlineVersion.content

    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction()

    let newModuleVersion: ModuleVersion | null = null

    try {
      newModuleVersion = await queryRunner.manager.save(moduleVersionObj)
      /**
       * 与变更关联
       */
      const changeModuleVersion = new ChangeModuleVersion()
      changeModuleVersion.moduleId = moduleId
      changeModuleVersion.changeId = changeId
      changeModuleVersion.moduleVersion = version
      // 从线上版本检出
      changeModuleVersion.checkoutModuleVersion = onlineVersion.version
      changeModuleVersion.createUser = userCode
      changeModuleVersion.updateUser = userCode
      changeModuleVersion.createTime = now
      changeModuleVersion.updateTime = now
      await queryRunner.manager.save(changeModuleVersion)

      await queryRunner.commitTransaction()
    } catch (e) {
      logger.error('[Module Service] create error', e as unknown as Error)
      await queryRunner.rollbackTransaction()
      throw new CommonException(ExceptionCodes.Module_Create_Error)
    } finally {
      await queryRunner.release()
    }
    return {
      id: newModuleVersion.moduleId,
      version: version,
    }
  }

  /**
   * 检查模块是否需要merge
   */
  async checkModuleMerge(
    { moduleId, changeId }: CheckMergeDto,
    userCode: string,
  ) {
    const change = await this.changeService.getOne(changeId)
    if (!change) {
      throw new CommonException(ExceptionCodes.Change_Publish_Change_Not_Found)
    }
    if (
      [ChangeStatus.PUBLISHED, ChangeStatus.CLOSED].indexOf(
        Number(change.status),
      ) > -1
    ) {
      throw new CommonException(ExceptionCodes.Module_Save_Change_Status_Error)
    }
    const domainCode = change.domainCode
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    const moduleVersionConfig =
      await this.changeModuleVersionService.getModuleByChangeIdAndModuleId(
        changeId,
        moduleId,
      )
    if (!moduleVersionConfig?.length) {
      throw new CommonException(ExceptionCodes.Module_Not_Found)
    }
    const checkoutVersion = moduleVersionConfig[0].checkoutModuleVersion
    // 读取最新的线上版本
    const onlineVersion =
      await this.moduleVersionService.getOnlineVersionByModuleId(moduleId)

    let compareOnlineVersion = onlineVersion?.version
    if (onlineVersion) {
      const resp =
        await this.changeModuleVersionService.getByModuleIdAndVersion(
          onlineVersion.moduleId,
          onlineVersion.version,
        )
      if (resp?.[0]) {
        if (resp[0].rollbackFrom) {
          // 说明线上模块是一个回滚模块，而且回滚来源和当前的是同一个parent，那就不是有冲突的情况可以不提示
          if (checkoutVersion === resp[0].rollbackFrom) {
            compareOnlineVersion = resp[0].rollbackFrom
          }
        }
      }
    }

    return {
      hasOnlineVersion: !!onlineVersion,
      onlineVersion: compareOnlineVersion,
      checkoutVersion,
    }
  }

  /**
   * 从变更里删除模块
   * * 如果模块并非当前变更创建，而且当前变更从线上模块引入：则只删除 change_module_version 的记录，保留 module、module_version、module_deploy 的记录。（原有逻辑）
   * * 如果模块是当前变更创建：则删除 module、module_version、change_module_version 的记录，保留 module_deploy 的记录。（全局模块引入的新逻辑）
   */
  async deleteModuleFromChange(
    { moduleId, changeId, domainCode }: AddOrDeleteModuleDto,
    userCode: string,
  ) {
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userCode,
    )

    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    /**
     * 变更的状态必须是未完成的
     */
    const changeObject = await this.changeService.get(changeId)
    if (!changeObject) {
      throw new CommonException(ExceptionCodes.Module_Change_Not_Found)
    }
    if (
      [
        ChangeStatus.PUBLISHING,
        ChangeStatus.PUBLISHED,
        ChangeStatus.CLOSED,
      ].indexOf(Number(changeObject.status)) > -1
    ) {
      throw new CommonException(ExceptionCodes.Module_Save_Change_Status_Error)
    }
    const cmv = await this.changeModuleVersionService.getChangeModuleVersion(
      changeId,
      moduleId,
    )
    // FIXME: cmv.moduleVersion 和 cmv.checkoutModuleVersion 的类型是 string，和 ts 声明的不一样
    if (cmv && cmv.moduleVersion == 1 && cmv.checkoutModuleVersion == 1) {
      // 如果是当前变更创建的，清理 module、module_version
      await Promise.all([
        this.moduleRepo.delete(moduleId),
        this.moduleVersionService.deleteByModule(moduleId),
      ])
    }
    return await this.changeModuleVersionService.deleteModuleFromChange(
      changeId,
      moduleId,
    )
  }

  /**
   * 搜索哦
   * @param searchText
   * @returns
   */
  async search(searchText: string) {
    const queryBuilder = this.moduleRepo.createQueryBuilder('module')
    queryBuilder
      .where('module.name = :name', { name: searchText })
      .orWhere('module.id = :id', { id: searchText })
    const moduleList = await queryBuilder.getMany()
    if (moduleList.length) {
      // 使用 Promise.all 来并行获取每个模块的最新版本信息
      const versionPromises = moduleList.map(async (module) => {
        const latestVersion =
          await this.moduleVersionService.getLatestVersionByModuleId(module.id)
        if (latestVersion) {
          const changeModuleVersion =
            await this.changeModuleVersionService.getByModuleIdAndVersion(
              module.id,
              latestVersion.version,
            )
          return {
            version: latestVersion.version,
            ...module,
            changeId: changeModuleVersion?.[0]?.changeId,
          }
        }
      })
      // 等待所有版本信息的获取完成
      return await Promise.all(versionPromises)
    }
  }

  async updateModuleComponent(
    updateModuleComponent: UpdateModuleComponent,
    userCode: string,
  ) {
    //根据moduleId moduleVersion获取
    //查询对应的变更
    const changeModuleVersion =
      await this.changeModuleVersionService.getByModuleIdAndVersion(
        updateModuleComponent.moduleId,
        updateModuleComponent.moduleVersion,
      )
    if (Array.isArray(changeModuleVersion) && changeModuleVersion.length) {
      const changeId = changeModuleVersion?.[0]?.changeId
      /**
       * 变更的状态必须是未完成的
       */
      const changeObject = await this.changeService.get(changeId)
      if (!changeObject) {
        throw new CommonException(ExceptionCodes.Module_Change_Not_Found)
      }

      /**
       * 检查是否有权限
       */
      const hasPermission = await this.businessDomainService.hasPermission(
        changeObject.domainCode,
        userCode,
      )
      if (!hasPermission) {
        throw new CommonException(ExceptionCodes.User_Permission_Deined)
      }
      //判断是否是在开发中的变更
      if (
        [
          ChangeStatus.PUBLISHING,
          ChangeStatus.PUBLISHED,
          ChangeStatus.CLOSED,
        ].indexOf(Number(changeObject.status)) > -1
      ) {
        throw new CommonException(
          ExceptionCodes.Module_Save_Change_Status_Error,
        )
      }
      //判断是否没有正在被编辑
      const ret = await redis.setGetUserInfo(
        getCoeditRedisKey(
          updateModuleComponent.moduleId,
          updateModuleComponent.moduleVersion,
        ),
        '',
      )
      if (ret) {
        return {
          editingRoom: JSON.parse(ret),
        }
      } else {
        //执行更新事务
        const old = await this.componentVersionService.getDetailByComponent(
          updateModuleComponent.componentId,
          String(updateModuleComponent.currentVersion),
        )
        const newest =
          await this.componentVersionService.getLatestVersionByComponentId(
            updateModuleComponent.componentId,
          )
        const schema = await this.moduleVersionService.getDetailByModule(
          updateModuleComponent.moduleId,
          updateModuleComponent.moduleVersion,
        )
        return {
          old,
          newest,
          schema,
        }
      }
    }
  }

  /**
   * 获取全局模块
   * @param domainCode （业务）域代码
   */
  async getGlobalModule(domainCode: string) {
    return await this.moduleRepo.findOne({
      where: {
        type: ModuleType.GLOBAL,
        businessDomainCode: domainCode,
      },
    })
  }

  /**
   * 获取全局模块 ID
   * @param domainCode （业务）域代码
   */
  async getGlobalModuleId(domainCode: string) {
    return (await this.getGlobalModule(domainCode))?.id
  }

  /**
   * 根据模块 ID 获取全局模块 ID
   * @param moduleId 模块 ID
   */
  async getGlobalModuleIdByModuleId(moduleId: string) {
    const module = await this.moduleRepo.findOne({
      where: {
        id: moduleId,
      },
    })
    return module?.businessDomainCode
      ? await this.getGlobalModuleId(module.businessDomainCode)
      : undefined
  }
}
