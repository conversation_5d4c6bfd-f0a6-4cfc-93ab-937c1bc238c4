import { ModuleType } from '@ad/canal-shared'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'module' })
export class ModuleEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    name: 'module_name',
    type: 'varchar',
    length: 255,
    comment: '模块名称',
  })
  name: string

  @Column({
    name: 'page_type',
    type: 'int',
    comment: '模块类型',
  })
  type: ModuleType

  @Column({
    name: 'container_type',
    type: 'int',
    comment: '容器类型',
  })
  containerType: number

  @Column({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    comment: '业务域code',
  })
  businessDomainCode: string

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    select: false,
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number

  @Column({
    name: 'global_module_lock',
    type: 'varchar',
    length: 255,
    comment: '全局模块锁，使用业务域作为值，并利用 UNIQUE INDEX 约束确保唯一',
  })
  globalModuleLock?: string
}
