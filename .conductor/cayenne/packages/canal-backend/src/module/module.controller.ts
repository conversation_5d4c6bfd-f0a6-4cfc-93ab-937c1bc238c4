import {
  Body,
  Controller,
  Get,
  Header,
  Post,
  Query,
  Session,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { redis } from 'src/redis'
import { getCoeditRedisKey } from 'src/redis/keys'
import { UserSession } from 'src/types'
import { ModuleVersionService } from '../module-version/module-version.service'
import {
  AddOrDeleteModuleDto,
  CancelPermissionBodyDto,
  CheckMergeDto,
  CheckMergeResDto,
  CreateModuleDto,
  CreateOrUpdateModuleResponse,
  GetPermissionBodyDto,
  GetPermissionResDto,
  MixModuleDto,
  ModuleDetail,
  ModuleEntityDto,
  ModuleListDto,
  ModuleListUnderChangeDto,
  OnlineModuleListDto,
  PublishListResponseDto,
  Room,
  SearchModuleListDto,
  UpdateModuleComponent,
  UpdateModuleComponentCheckResultDto,
  UpdateModuleDto,
  UpdateModuleMetaDto,
} from './module.dto'
import { ModuleService } from './module.service'

@Controller('/rest/canal/module')
@ApiTags('module')
@Controller()
export class ModuleController {
  public constructor(
    private moduleService: ModuleService,
    private moduleVersionService: ModuleVersionService,
  ) {}

  @UseGuards(AuthGuard)
  @Post('check')
  @ApiOperation({
    summary: '检查模块配置',
    operationId: 'checkModulesUsingPost',
  })
  @ApiBody({
    schema: {
      properties: {
        moduleVersions: {
          type: 'array',
          description: '模块ID和版本号的数组',
          items: {
            type: 'object',
            properties: {
              moduleId: { type: 'string' },
              version: { type: 'string' },
            },
          },
        },
      },
    },
  })
  @CommonApiResponse({
    schema: {
      properties: {
        checkError: {
          type: 'array',
          description: '检查错误列表，空数组表示没有错误',
          items: {
            type: 'object',
            properties: {
              key: { type: 'string' },
              content: { type: 'array' },
            },
          },
        },
      },
    },
  })
  public async checkModulesUsingPost(
    @Body()
    body: {
      moduleVersions: { moduleId: string; version: string | number }[]
    },
  ) {
    return await this.moduleVersionService.checkModulesByIdAndVersion(
      body.moduleVersions,
    )
  }

  @Post('list')
  @ApiBody({
    type: ModuleListDto,
  })
  @ApiOperation({
    summary: '获取模块列表',
    operationId: 'getAllModulesUsingPost',
  })
  @CommonApiResponse(PublishListResponseDto)
  public async getAllModulesUsingPost(@Body() moduleListDto: ModuleListDto) {
    const { list, total } = await this.moduleService.getAll(moduleListDto)

    return {
      list,
      pageInfo: {
        total,
        pageSize: moduleListDto.pageSize,
        pageNum: moduleListDto.pageNum,
      },
    }
  }

  @Post('create')
  @ApiBody({
    type: CreateModuleDto,
  })
  @ApiOperation({
    summary: '创建模块',
    operationId: 'createModuleUsingPost',
  })
  @CommonApiResponse(CreateOrUpdateModuleResponse)
  public async createModuleUsingPost(
    @Session() userSession: UserSession,
    @Body() createModuleDto: CreateModuleDto,
  ) {
    const moduleInfo = await this.moduleService.create(
      createModuleDto,
      userSession?.userInfo?.userCode || createModuleDto.lingzhuUserName || '',
    )

    return {
      id: moduleInfo.id,
      version: moduleInfo.version,
    }
  }

  @UseGuards(AuthGuard)
  @Post('update')
  @ApiBody({
    type: UpdateModuleDto,
  })
  @ApiOperation({
    summary: '更新模块',
    operationId: 'updateModuleUsingPost',
  })
  public async updateModuleUsingPost(
    @Session() userSession: UserSession,
    @Body() updateModuleDto: UpdateModuleDto,
  ) {
    await this.moduleService.update(
      updateModuleDto,
      userSession.userInfo.userCode,
    )
  }

  @UseGuards(AuthGuard)
  @Post('update-meta')
  @ApiBody({
    type: UpdateModuleMetaDto,
  })
  @ApiOperation({
    summary: '更新模块元信息',
    operationId: 'updateModuleMetaUsingPost',
  })
  public async updateModuleMetaUsingPost(
    @Session() userSession: UserSession,
    @Body() updateModuleMetaDto: UpdateModuleMetaDto,
  ) {
    await this.moduleService.updateMeta(
      updateModuleMetaDto,
      userSession.userInfo.userCode,
    )
  }

  @Get('detail')
  @Header('Cache-Control', 'no-store')
  @ApiOperation({
    summary: '获取模块详情',
    operationId: 'getModuleDetail',
  })
  @ApiQuery({
    name: 'id',
    required: true,
    description: '模块id',
    type: String,
  })
  @ApiQuery({
    name: 'version',
    required: false,
    description: '模块版本，不传返回最新版本',
    type: String,
  })
  @CommonApiResponse(ModuleDetail)
  async getModuleDetail(
    @Query('id') id: string,
    @Query('version') version: number,
  ) {
    return await this.moduleService.getDetailByIdWithVersion(id, version)
  }

  @Get('global-detail')
  @Header('Cache-Control', 'no-store')
  @ApiOperation({
    summary: '获取全局模块详情',
    operationId: 'getGlobalModuleDetail',
  })
  @ApiQuery({
    name: 'domainCode',
    required: true,
    description: '（业务）域代码',
    type: String,
  })
  @CommonApiResponse(ModuleDetail)
  async getGlobalModuleDetail(@Query('domainCode') domainCode: string) {
    return await this.moduleService.getGlobalDetail(domainCode)
  }

  @Post('/listByChangeId')
  @ApiBody({
    type: ModuleListUnderChangeDto,
  })
  @ApiOperation({
    summary: '获取单个变更下所有模块',
    operationId: 'getAllModulesUsingPostByChangeId',
  })
  @CommonApiResponse(MixModuleDto, true)
  public async getAllModulesUsingPostByChangeId(
    @Body() moduleListUnderChangeDto: ModuleListUnderChangeDto,
  ) {
    return await this.moduleService.getAllByChangId(moduleListUnderChangeDto)
  }

  @Get('onlineList')
  @ApiQuery({
    name: 'domainCode',
    required: true,
    description: '业务域module',
    type: String,
  })
  @ApiQuery({
    name: 'pageSize',
    required: true,
    description: '分页大小',
    type: Number,
  })
  @ApiQuery({
    name: 'pageNum',
    required: true,
    description: '页码',
    type: Number,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: '模块名称',
    type: String,
  })
  @ApiQuery({
    name: 'createUser',
    required: false,
    description: '创建人',
    type: String,
  })
  @ApiOperation({
    summary: '获取业务域下所有线上模块列表',
    operationId: 'getAllOnlineModule',
  })
  @CommonApiResponse(OnlineModuleListDto)
  public async getAllOnlineModule(
    @Query('domainCode') domainCode: string,
    @Query('pageSize') pageSize: number,
    @Query('pageNum') pageNum: number,
    @Query('name') name?: string,
    @Query('createUser') createUser?: string,
    @Query('moduleId') moduleId?: string,
  ) {
    return await this.moduleService.getAllOnlineModule({
      domainCode,
      pageNum,
      pageSize,
      name,
      createUser,
      moduleId,
    })
  }

  @UseGuards(AuthGuard)
  @Get('search/list')
  @ApiQuery({
    name: 'domainCode',
    required: true,
    description: '业务域module',
    type: String,
  })
  @ApiQuery({
    name: 'pageSize',
    required: true,
    description: '分页大小',
    type: Number,
  })
  @ApiQuery({
    name: 'pageNum',
    required: true,
    description: '页码',
    type: Number,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: '模块名称',
    type: String,
  })
  @ApiQuery({
    name: 'createUser',
    required: false,
    description: '创建人',
    type: String,
  })
  @ApiQuery({
    name: 'moduleId',
    required: false,
    description: '模块id',
    type: String,
  })
  @ApiQuery({
    name: 'componentId',
    required: false,
    description: '组件id',
    type: String,
  })
  @ApiQuery({
    name: 'dataSourceId',
    required: false,
    description: '数据源id',
    type: String,
  })
  @ApiOperation({
    summary: '搜索业务域下的模块，带多种条件的模糊搜索',
    operationId: 'searchAllOnlineModule',
  })
  @CommonApiResponse(SearchModuleListDto)
  public async getModuleList(
    @Query('domainCode') domainCode: string,
    @Query('pageSize') pageSize: number,
    @Query('pageNum') pageNum: number,
    @Query('name') name?: string,
    @Query('createUser') createUser?: string,
    @Query('moduleId') moduleId?: string,
    @Query('componentId') componentId?: string,
    @Query('dataSourceId') dataSourceId?: string,
  ) {
    return await this.moduleService.getModuleList({
      domainCode,
      pageNum,
      pageSize,
      name,
      createUser,
      moduleId,
      componentId,
      dataSourceId,
    })
  }

  @Post('add/change')
  @ApiBody({
    type: AddOrDeleteModuleDto,
  })
  @ApiOperation({
    summary: '添加模块到变更',
    operationId: 'addToChange',
  })
  @CommonApiResponse(CreateOrUpdateModuleResponse)
  public async addToChangeMethod(
    @Session() userSession: UserSession,
    @Body() addToModuleDto: AddOrDeleteModuleDto,
  ) {
    return await this.moduleService.addToChange(
      addToModuleDto,
      userSession?.userInfo?.userCode || addToModuleDto.lingzhuUserName || '',
    )
  }

  @UseGuards(AuthGuard)
  @Post('getPermission')
  @ApiOperation({
    summary: '获取模块的权限',
    operationId: 'getPermission',
  })
  @CommonApiResponse(GetPermissionResDto)
  public async getPermission(
    @Session() userSession: UserSession,
    @Body() body: GetPermissionBodyDto,
  ): Promise<GetPermissionResDto> {
    // console.log('getPermission', body)
    try {
      const room: Room = {
        user: userSession.userInfo,
        roomId: body.roomId,
      }
      const ret = await redis.setGetUserInfo(
        getCoeditRedisKey(body.id, body.version),
        body.grant ? JSON.stringify(room) : '',
      )
      // console.log('getPermission ret', body, ret)
      return {
        editingRoom: ret ? JSON.parse(ret) : undefined,
      }
    } catch (err) {
      console.error('getPermission err', err)
    }
    return {}
  }

  @UseGuards(AuthGuard)
  @Post('cancelPermission')
  @ApiOperation({
    summary: '取消模块的权限',
    operationId: 'cancelPermission',
  })
  @CommonApiResponse(Boolean)
  public async cancelPermission(
    @Session() userSession: UserSession,
    @Body() body: CancelPermissionBodyDto,
  ): Promise<boolean> {
    // console.log('cancelPermission', body)
    try {
      const room: Room = {
        user: userSession.userInfo,
        roomId: body.roomId,
      }
      const ret = await redis.delUserInfo(
        getCoeditRedisKey(body.id, body.version),
        JSON.stringify(room),
      )
      // console.log('cancelPermission ret', body, ret)
      return ret
    } catch (err) {
      console.error('cancelPermission err', err)
    }
    return false
  }

  @UseGuards(AuthGuard)
  @Post('check/merge')
  @ApiOperation({
    summary: '检查merge的条件',
    operationId: 'checkMerge',
  })
  @CommonApiResponse(CheckMergeResDto)
  public async checkMerge(
    @Session() userSession: UserSession,
    @Body() body: CheckMergeDto,
  ): Promise<CheckMergeResDto> {
    return await this.moduleService.checkModuleMerge(
      body,
      userSession.userInfo.userCode,
    )
  }

  @Post('delete/from/change')
  @ApiOperation({
    summary: '从变更列表中删除module',
    operationId: 'delete module',
  })
  public async deleteModuleFromChange(
    @Body()
    deleteDto: AddOrDeleteModuleDto,
    @Session() userSession: UserSession,
  ) {
    return await this.moduleService.deleteModuleFromChange(
      deleteDto,
      deleteDto.lingzhuUserName || userSession.userInfo?.userCode,
    )
  }

  @UseGuards(AuthGuard)
  @Get('search/global')
  @ApiOperation({
    summary: '在大运河平台全局搜索模块',
    operationId: 'global search module',
  })
  @ApiQuery({
    name: 'searchText',
    required: true,
    description: '搜索文字',
    type: String,
  })
  @CommonApiResponse(ModuleEntityDto, true)
  public async search(@Query('searchText') searchText: string) {
    return await this.moduleService.search(searchText)
  }

  @UseGuards(AuthGuard)
  @Post('update/component/version/check')
  @ApiOperation({
    summary: '升级模块中组件版本的前置信息check',
    operationId: 'update component version',
  })
  @ApiBody({
    type: UpdateModuleComponent,
  })
  @CommonApiResponse(UpdateModuleComponentCheckResultDto)
  public async updateModuleComponent(
    @Body()
    updateModuleComponent: UpdateModuleComponent,
    @Session() userSession: UserSession,
  ) {
    return await this.moduleService.updateModuleComponent(
      updateModuleComponent,
      userSession.userInfo?.userCode,
    )
  }
}
