import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
// `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
// `change_id` varchar(255) NOT NULL DEFAULT '' COMMENT '变更id',
// `stage` varchar(255) NOT NULL DEFAULT '' COMMENT '发布阶段',
// `operator` varchar(255) NOT NULL DEFAULT '' COMMENT '发布者',
// `lane` varchar(100) DEFAULT '' COMMENT '泳道',
// `front_lane` varchar(100) DEFAULT '' COMMENT '前端泳道',
// `status` bigint(4) DEFAULT '0' COMMENT '状态',
// `publish_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '发布时间',
// `create_user` varchar(100) NOT NULL DEFAULT '' COMMENT '创建者',
// `update_user` varchar(100) NOT NULL DEFAULT '' COMMENT '修改者',
// `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
// `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',

@Entity({ name: 'change_deploy' })
export class ChangeDeployEntity {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  id: number

  @Column({
    name: 'change_id',
    type: 'varchar',
    length: 255,
    comment: '变更id',
  })
  changeId: string

  @Column({
    type: 'varchar',
    length: 255,
    comment: '发布阶段',
  })
  stage: string

  @Column({
    type: 'varchar',
    length: 255,
    comment: '发布者',
  })
  operator: string

  @Column({
    name: 'lane',
    type: 'varchar',
    length: 100,
    comment: '泳道',
  })
  lane?: string

  @Column({
    name: 'front_lane',
    type: 'varchar',
    length: 100,
    comment: '前端泳道',
  })
  frontLane?: string

  @Column({
    name: 'status',
    type: 'int',
    comment: '状态',
  })
  status: number

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
    select: false,
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
    select: false,
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number

  @Column({
    type: 'bigint',
    name: 'publish_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  publishTime: number
}
