import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Session,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { UserSession } from 'src/types'
import {
  ChangeDeployCreateDto,
  ChangeDeployDto,
  DeployOnlineVersionToTrunkDto,
} from './change-deploy.dto'
import { ChangeDeployService } from './change-deploy.service'

@Controller('/rest/canal/change/deploy')
@ApiTags('change-deploy')
@Controller()
export class ChangeDeployController {
  constructor(private readonly changeDeployService: ChangeDeployService) {}

  @Get('list')
  @ApiOperation({
    summary: '获取变更部署列表',
    operationId: 'getCanalChangeDeployList',
  })
  @ApiQuery({
    name: 'changeId',
    required: true,
    description: '变更名称',
    type: String,
  })
  @CommonApiResponse(ChangeDeployDto, true)
  public async getAllList(@Query('changeId') changeId: string) {
    return await this.changeDeployService.getAll(changeId)
  }

  @Post('create')
  @ApiOperation({
    summary: '部署变更',
    operationId: 'deploy',
  })
  @ApiBody({
    type: ChangeDeployCreateDto,
  })
  @CommonApiResponse(ChangeDeployDto)
  public async create(
    @Body() changeDeployCreateDto: ChangeDeployCreateDto,
    @Session() userSession: UserSession,
  ) {
    const changeDeployInstance = await this.changeDeployService.deploy(
      changeDeployCreateDto,
      changeDeployCreateDto.lingzhuUserName || userSession?.userInfo?.userCode,
    )
    return {
      id: changeDeployInstance?.id,
      status: changeDeployInstance?.status,
    }
  }

  @Post('online/version')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '部署变更的模块的线上版本到prt/beta主干',
    operationId: 'deployOnlineVersion',
  })
  @ApiBody({
    type: DeployOnlineVersionToTrunkDto,
  })
  public async deployOnlineVersionToTrunk(
    @Body() deployOnlineVersionToTrunkDto: DeployOnlineVersionToTrunkDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changeDeployService.deployOnlineVersionToTrunk(
      deployOnlineVersionToTrunkDto.changeId,
      deployOnlineVersionToTrunkDto.domainCode,
      userSession,
    )
  }
}
