import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import dayjs from 'dayjs'
import { BusinessDomainService } from 'src/business-domain/business-domain.service'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { ModuleDeployService } from 'src/module-deploy/module-deploy.service'
import { ModuleVersionService } from 'src/module-version/module-version.service'
import { ModuleService } from 'src/module/module.service'
import { UserSession } from 'src/types'
import { Repository } from 'typeorm'
import { ChangeDeployCreateDto } from './change-deploy.dto'
import { ChangeDeployEntity } from './change-deploy.entity'
@Injectable()
export class ChangeDeployService {
  public constructor(
    @InjectRepository(ChangeDeployEntity)
    public readonly changeDeployRepo: Repository<ChangeDeployEntity>,
    private readonly moduleService: ModuleService,
    private readonly moduleDeployService: ModuleDeployService,
    private readonly businessDomainService: BusinessDomainService,
    private readonly moduleVersionService: ModuleVersionService,
  ) {}
  /**
   * 获取一个变更下所有的部署记录
   */
  async getAll(changeId: string) {
    return await this.changeDeployRepo.find({
      where: {
        changeId,
      },
      order: {
        createTime: 'DESC',
      },
    })
  }
  /**
   * 获取部署详情
   */
  async getDetail(id: number) {
    return await this.changeDeployRepo.findOneBy({ id })
  }
  /**
   * 进行部署
   */
  async deploy(changeDeployCreateDto: ChangeDeployCreateDto, userCode: string) {
    if (
      !changeDeployCreateDto?.changeId ||
      !changeDeployCreateDto.stage ||
      ['staging', 'prt', 'beta'].indexOf(changeDeployCreateDto.stage) < 0
    ) {
      throw new CommonException(ExceptionCodes.Change_Deploy_Fail)
    }
    /**
     * 创建变更部署实体
     */
    const createTime = dayjs().valueOf()
    changeDeployCreateDto.createTime = createTime
    changeDeployCreateDto.updateTime = createTime
    changeDeployCreateDto.createUser = userCode
    changeDeployCreateDto.updateUser = userCode
    /**
     * 一期的发布比较简单，暂无判断逻辑，后续需要进行发布检查
     */
    changeDeployCreateDto.publishTime = createTime
    changeDeployCreateDto.operator = userCode
    changeDeployCreateDto.status = changeDeployCreateDto.status || 0 //1 为成功 0 为失败
    // staging为单独走api解决
    if (changeDeployCreateDto.stage === 'staging') {
      return await this.changeDeployRepo.save(changeDeployCreateDto)
    }
    try {
      // 获取所有的模块
      const moduleList = await this.moduleService.getAllByChangId({
        changeId: changeDeployCreateDto.changeId,
        needContent: true,
      })
      const list = moduleList?.list
      if (Array.isArray(list) && list.length) {
        await Promise.all(
          list.map(async (module) => {
            return await this.moduleDeployService.create(
              {
                moduleId: module.moduleId,
                version: module.version,
                content: module.content || '',
                stage: changeDeployCreateDto.stage,
                frontLane: changeDeployCreateDto.frontLane,
                lane: changeDeployCreateDto.lane,
              },
              true,
              userCode,
            )
          }),
        ).then(() => {
          changeDeployCreateDto.status = 1
        })
      }
    } catch (e) {
      changeDeployCreateDto.status = 0
    }
    return await this.changeDeployRepo.save(changeDeployCreateDto)
  }

  /**
   *
   */
  async deployOnlineVersionToTrunk(
    changeId: string,
    domainCode: string,
    userSession: UserSession,
  ) {
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userSession.userInfo.userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    // 获取所有的模块
    const moduleList = await this.moduleService.getAllByChangId({
      changeId: changeId,
      needContent: false,
    })
    const onlineArr: {
      moduleId: string
      version: number
    }[] = []
    if (moduleList?.list?.length) {
      for (const item of moduleList.list) {
        const onlineModule =
          await this.moduleVersionService.getOnlineVersionByModuleId(
            item.moduleId,
          )

        if (onlineModule) {
          const params = {
            moduleId: item.moduleId,
            version: onlineModule.version,
          }
          onlineArr.push(params)
          for (const stage of ['prt', 'beta']) {
            await this.moduleDeployService.create(
              {
                ...params,
                stage: stage,
                content: onlineModule.content || '',
              },
              true,
              userSession?.userInfo?.userCode,
            )
          }
        }
      }
    }
    return onlineArr
  }
}
