import { OmitType } from '@nestjs/mapped-types'
import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
export class ChangeDeployDto {
  @ApiProperty()
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  id: number
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  changeId: string

  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  stage: string

  @ApiProperty()
  operator: string
  @ApiProperty()
  lane?: string
  @ApiProperty()
  frontLane?: string
  @ApiProperty()
  status: number

  @ApiProperty()
  createUser: string
  @ApiProperty()
  publishTime: number
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateTime: number
  @ApiProperty()
  updateUser: string
}

export class ChangeDeployCreateDto extends OmitType(ChangeDeployDto, [
  'id',
] as const) {
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  changeId: string

  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  stage: string

  @ApiProperty()
  operator: string
  @ApiProperty()
  lane?: string
  @ApiProperty()
  frontLane?: string
  @ApiProperty()
  status: number

  @ApiProperty()
  createUser: string
  @ApiProperty()
  publishTime: number
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateTime: number
  @ApiProperty()
  updateUser: string

  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  lingzhuUserName?: string
}

export class DeployOnlineVersionToTrunkDto {
  changeId: string
  domainCode: string
}
