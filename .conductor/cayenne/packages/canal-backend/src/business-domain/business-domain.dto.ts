import { OmitType } from '@nestjs/mapped-types'
import { ApiProperty } from '@nestjs/swagger'

export class BusinessDomainDetail {
  @ApiProperty()
  changeCount: number
  @ApiProperty()
  componentCount: number
  @ApiProperty()
  moduleCount: number

  @ApiProperty()
  id: string
  @ApiProperty()
  name: string
  @ApiProperty()
  desc: string
  @ApiProperty()
  greyConfig: string
  @ApiProperty()
  serviceNodeId: number
  @ApiProperty()
  radarProjects: string
  @ApiProperty()
  schemaDomains: string
  @ApiProperty()
  collaborativeModel: string
}

export class SimpleBusinessDomainDetail extends OmitType(BusinessDomainDetail, [
  'changeCount',
  'componentCount',
  'moduleCount',
  'desc',
] as const) {
  @ApiProperty()
  id: string
  @ApiProperty()
  name: string
  @ApiProperty()
  greyConfig: string
  @ApiProperty()
  radarProjects: string
  @ApiProperty()
  schemaDomains: string
  @ApiProperty()
  collaborativeModel: string
}

export class EditGreyDto {
  @ApiProperty()
  code: string
  @ApiProperty()
  greyConfig: string
}

export class AddMemberDto {
  @ApiProperty()
  domainCode: string
  @ApiProperty()
  userId: string
  @ApiProperty()
  permissionLevel: string
}

export class AddSingleDto {
  @ApiProperty()
  domainCode: string
  @ApiProperty()
  value: unknown
  @ApiProperty()
  key: string
}
