import {
  Body,
  Controller,
  Get,
  ParseBoolPipe,
  Post,
  Query,
  Session,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import kconf from 'src/common/kconf'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { DomainPermission } from 'src/domain-permissions/domain-permissions.entity'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { UserSession } from 'src/types'
import {
  AddMemberDto,
  AddSingleDto,
  BusinessDomainDetail,
  EditGreyDto,
  SimpleBusinessDomainDetail,
} from './business-domain.dto'
import { BusinessDomain } from './business-domain.entity'
import { BusinessDomainService } from './business-domain.service'

@Controller('/rest/canal/domain')
@ApiTags('business_domain')
@Controller()
export class BusinessDomainController {
  public constructor(private businessDomainService: BusinessDomainService) {}

  @Get('list')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: '获取所有业务域',
    operationId: 'getAllBusinessDomain',
  })
  @ApiQuery({
    name: 'type',
    required: true,
    description: '获取所有业务域',
    type: String,
  })
  @ApiQuery({
    name: 'filterByUserPermission',
    required: false,
    description: '是否只返回当前用户有权限的业务域',
    type: Boolean,
  })
  @CommonApiResponse(BusinessDomain, true)
  public getAll(
    @Query('filterByUserPermission', new ParseBoolPipe({ optional: true }))
    filterByUserPermission?: boolean,
    @Session() userSession?: UserSession,
  ) {
    return this.businessDomainService.getAll(
      filterByUserPermission,
      userSession?.userInfo?.userCode,
    )
  }

  @Get('detail')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: '获取业务域详情',
    operationId: 'getDetailBusinessDomain',
  })
  @ApiQuery({
    name: 'code',
    required: true,
    description: '获取业务域详情',
    type: String,
  })
  @CommonApiResponse(BusinessDomainDetail)
  async getDetail(@Query('code') code: string) {
    const obj = await this.businessDomainService.getDetail(code)
    if (obj) {
      return obj
    }
  }

  @Get('member')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: '获取业务域成员',
    operationId: 'getMemberBusinessDomain',
  })
  @ApiQuery({
    name: 'code',
    required: true,
    description: '业务域code',
    type: String,
  })
  @CommonApiResponse(DomainPermission, true)
  async getMember(@Query('code') code: string) {
    return await this.businessDomainService.getAllMember(code)
  }

  @Get('simple/detail')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: '获取业务域简化信息，不包含一些统计值',
    operationId: 'getSimpleDetailBusinessDomain',
  })
  @ApiQuery({
    name: 'code',
    required: true,
    description: '获取业务域详情',
    type: String,
  })
  @CommonApiResponse(SimpleBusinessDomainDetail)
  async getSimpleDetail(@Query('code') code: string) {
    const obj = await this.businessDomainService.getSimpleDetail(code)
    if (obj) {
      return {
        id: obj.domainCode,
        name: obj.domainName,
        greyConfig: obj.greyConfig,
        radarProjects: obj.radarProjects,
        schemaDomains: obj.schemaDomains,
        collaborativeModel: obj.collaborativeModel,
      }
    }
  }

  @Get('check/permission')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: '检查权限',
    operationId: 'checkPermission',
  })
  @ApiQuery({
    name: 'code',
    required: true,
    description: '业务域code',
    type: String,
  })
  @CommonApiResponse(Boolean)
  async checkPermission(
    @Query('code') code: string,
    @Session() userSession: UserSession,
  ) {
    return await this.businessDomainService.hasPermission(
      code,
      userSession?.userInfo?.userCode,
    )
  }

  @Post('edit/grey')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: '修改灰度配置',
    operationId: 'editGrey',
  })
  @ApiBody({ type: EditGreyDto })
  @CommonApiResponse(Boolean)
  async editGrey(
    @Body()
    editGreyDto: EditGreyDto,
    @Session() userSession: UserSession,
  ) {
    const hasPermission = await this.businessDomainService.hasPermission(
      editGreyDto.code,
      userSession?.userInfo?.userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    if (hasPermission) {
      return await this.businessDomainService.editGreyConfig(
        editGreyDto.code,
        editGreyDto.greyConfig,
      )
    }
  }
  @Post('add/member')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: '增加人员成员',
    operationId: 'addDomainMember',
  })
  @ApiBody({ type: AddMemberDto })
  @CommonApiResponse(DomainPermission)
  async addMember(
    @Body()
    addMemberDto: AddMemberDto,
    @Session() userSession: UserSession,
  ) {
    const hasPermission = await this.businessDomainService.hasPermission(
      addMemberDto.domainCode,
      userSession?.userInfo?.userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    if (hasPermission) {
      return await this.businessDomainService.addDomainMember(
        addMemberDto.domainCode,
        addMemberDto.userId,
        addMemberDto.permissionLevel,
        userSession?.userInfo?.userCode,
      )
    }
  }

  @Post('set/single')
  @UseGuards(AuthGuard)
  @ApiOperation({
    description: '设置单项信息,雷达项目信息等',
    operationId: 'setSingle',
  })
  @ApiBody({ type: AddSingleDto })
  @CommonApiResponse(Boolean)
  async setSingle(
    @Body()
    addMemberDto: AddSingleDto,
    @Session() userSession: UserSession,
  ) {
    const hasPermission = await this.businessDomainService.hasPermission(
      addMemberDto.domainCode,
      userSession?.userInfo?.userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    if (hasPermission) {
      return await this.businessDomainService.setSingleInfo(
        addMemberDto.domainCode,
        addMemberDto.value,
        addMemberDto.key,
      )
    }
  }
  @Get('route/config')
  async getRouteConfig() {
    try {
      const ret = await kconf.getJSONValue('ad.canal.canalRouteRule')
      return ret
    } catch (e) {
      console.error(e)
    }
  }
}
