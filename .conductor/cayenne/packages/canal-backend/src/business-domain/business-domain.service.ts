import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Change } from 'src/change/change.entity'
import { ComponentEntity } from 'src/component/component.entity'
import { DomainPermissionService } from 'src/domain-permissions/domain-permissions.service'
import { ModuleEntity } from 'src/module/module.entity'
import { DataSource, Repository } from 'typeorm'
import { BusinessDomain } from './business-domain.entity'
@Injectable()
export class BusinessDomainService {
  public constructor(
    @InjectRepository(BusinessDomain)
    public readonly businessDomainRepo: Repository<BusinessDomain>,
    private readonly dataSource: DataSource,
    private readonly domainPermissionService: DomainPermissionService,
  ) {}

  async getAll(filterByUserPermission?: boolean, userCode?: string) {
    if (filterByUserPermission && userCode) {
      // 使用原生SQL查询，一次性获取用户有权限的所有业务域
      const domainsWithPermission = await this.dataSource.manager
        .createQueryBuilder(BusinessDomain, 'domain')
        .innerJoin(
          'domain_permissions',
          'permission',
          'domain.domain_code = permission.domain_code AND permission.user_id = :userCode',
          { userCode },
        )
        .getMany()

      return domainsWithPermission
    }

    return await this.businessDomainRepo.find()
  }

  async getDetail(domainCode: string) {
    const detail = {
      changeCount: 0,
      componentCount: 0,
      moduleCount: 0,
      id: '',
      name: '',
      desc: '',
      radarProjects: '',
      schemaDomains: '',
      collaborativeModel: 'single',
    }

    const changeCount = await this.dataSource.manager
      .createQueryBuilder(Change, 'change')
      .where('change.domain_code = :domainCode', { domainCode })
      .getCount()
    if (!isNaN(changeCount)) {
      detail.changeCount = changeCount
    }
    const componentCount = await this.dataSource.manager
      .createQueryBuilder(ComponentEntity, 'component')
      .where('component.domain_code = :domainCode', { domainCode })
      .getCount()
    if (!isNaN(componentCount)) {
      detail.componentCount = componentCount
    }

    const moduleCount = await this.dataSource.manager
      .createQueryBuilder(ModuleEntity, 'module')
      .where('module.domain_code = :domainCode', { domainCode })
      .getCount()
    if (!isNaN(moduleCount)) {
      detail.moduleCount = moduleCount
    }

    const obj = await this.businessDomainRepo.findOneBy({ domainCode })
    if (obj) {
      detail.id = obj.domainCode
      detail.name = obj.domainName
      detail.desc = obj.desc
      detail.radarProjects = obj.radarProjects
      detail.schemaDomains = obj.schemaDomains
      detail.collaborativeModel = obj.collaborativeModel
    }
    return detail
  }

  async getSimpleDetail(domainCode: string) {
    return await this.businessDomainRepo.findOneBy({ domainCode })
  }

  async editGreyConfig(domainCode: string, config: string) {
    return await this.businessDomainRepo.update(
      { domainCode },
      {
        greyConfig: config,
      },
    )
  }

  async hasPermission(domainCode: string, userCode: string) {
    const target = await this.domainPermissionService.findOne(
      userCode,
      domainCode,
    )
    return !!target
  }
  async getAllMember(domainCode: string) {
    return await this.domainPermissionService.findAll(domainCode)
  }
  async addDomainMember(
    domainCode: string,
    userId: string,
    permissionLevel: string,
    userCode: string,
  ) {
    return await this.domainPermissionService.create(
      {
        domainCode,
        userId,
        permissionLevel,
      },
      userCode,
    )
  }
  async setSingleInfo(domainCode: string, value: unknown, key: string) {
    return await this.businessDomainRepo.update(
      {
        domainCode,
      },
      {
        [key]: value,
      },
    )
  }
}
