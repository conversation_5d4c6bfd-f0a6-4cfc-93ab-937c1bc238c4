import { ApiProperty } from '@nestjs/swagger'
import { Column, Entity, PrimaryColumn } from 'typeorm'

@Entity({ name: 'business_domain' })
export class BusinessDomain {
  @ApiProperty({
    description: '业务域code',
  })
  @PrimaryColumn({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    comment: '业务域code',
  })
  domainCode: string

  @ApiProperty({
    description: '业务域名称',
  })
  @PrimaryColumn({
    name: 'domain_name',
    type: 'varchar',
    length: 255,
    comment: '业务域名称',
  })
  domainName: string

  @ApiProperty({
    description: '业务域描述',
  })
  @PrimaryColumn({
    type: 'varchar',
    length: 255,
    comment: '业务域描述',
  })
  desc: string

  @ApiProperty({
    description: '业务域logo',
  })
  @PrimaryColumn({
    type: 'varchar',
    length: 255,
    comment: '业务域logo',
  })
  logo: string

  @ApiProperty({
    description: '一级部门',
  })
  @PrimaryColumn({
    type: 'varchar',
    length: 100,
    comment: '一级部门',
  })
  level1: string

  @ApiProperty({
    description: '二级部门',
  })
  @PrimaryColumn({
    type: 'varchar',
    length: 100,
    comment: '二级部门',
  })
  level2: string

  @ApiProperty({
    description: '三级部门',
  })
  @PrimaryColumn({
    type: 'varchar',
    length: 100,
    comment: '三级部门',
  })
  level3: string

  @ApiProperty({
    description: '灰度配置',
  })
  @PrimaryColumn({
    name: 'grey_config',
    type: 'text',
    comment: '灰度配置',
  })
  greyConfig: string

  @ApiProperty({
    description: '创建者',
  })
  @PrimaryColumn({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建者',
  })
  creator: string

  @ApiProperty({
    description: '修改者',
  })
  @PrimaryColumn({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '修改者',
  })
  updater: string

  @ApiProperty({
    description: '创建时间',
  })
  @PrimaryColumn({
    name: 'create_time',
    type: 'bigint',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || +new Date(),
    },
    comment: '创建时间',
  })
  createTime: number

  @ApiProperty({
    description: '修改时间',
  })
  @PrimaryColumn({
    name: 'update_time',
    type: 'bigint',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || +new Date(),
    },
    comment: '修改时间',
  })
  updateTime: number

  @ApiProperty({
    description: '服务节点',
  })
  @PrimaryColumn({
    name: 'service_node_id',
    type: 'bigint',
    comment: '服务节点id',
  })
  serviceNodeId: number

  @ApiProperty({
    description: '关联雷达项目',
  })
  @Column({
    name: 'radar_projects',
    type: 'text',
    comment: '关联雷达项目',
  })
  radarProjects: string

  @ApiProperty({
    description: '业务发出schema请求的域名',
  })
  @Column({
    name: 'schema_domains',
    type: 'text',
    comment: '请求schema域名',
  })
  schemaDomains: string

  @Column({
    name: 'collaborative_model',
    type: 'varchar',
    length: 255,
    comment: '协作模式',
  })
  collaborativeModel: string
}

// 	`create_user` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '创建者',
//   `update_user` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '修改者',
//   `create_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建时间',
//   `update_time` bigint(20) NOT NULL DEFAULT 0 COMMENT
