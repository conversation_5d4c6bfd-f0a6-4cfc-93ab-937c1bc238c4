import { Controller, Get, UseGuards } from '@nestjs/common'
import { AuthGuard } from 'src/auth/auth.guard'
import kconf from 'src/common/kconf'

// 容器云心跳检测
@Controller('/rest/canal/upload')
@UseGuards(AuthGuard)
export class UploadController {
  @Get('token')
  public async getUploadToken() {
    const upConfig = await kconf.getJSONValue<{
      [k: string]: Record<string, string>
    }>('ad.canal.uploadConfig')
    return upConfig.upload?.token
  }
}
