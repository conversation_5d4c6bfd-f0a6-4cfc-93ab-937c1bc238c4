import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Session,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { UserSession } from 'src/types'
import sendMessage, { type IMessage } from 'src/utils/kim-message'
import { UpdateGreyDeployDto } from '../grey-deploy/grey-deploy.dto'
import {
  ChangePublishCommonDto,
  ChangePublishCreateCheckDto,
  ChangePublishCreateDto,
  ChangePublishDto,
  ChangePublishListDto,
  ChangePublishRefreshFlowStateDto,
  LingzhuUpdateChangeStatusDto,
  PublishListResponseDto,
  UpStatusByLingzhuDto,
} from './change-publish.dto'
import { ChangePublishService } from './change-publish.service'

@Controller('/rest/canal/change/publish')
@ApiTags('publish')
@Controller()
export class ChangePublishController {
  constructor(private readonly changePublishService: ChangePublishService) {}

  @Post('list')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '获取发布列表',
    operationId: 'getCanalPublishList',
  })
  @ApiBody({
    type: ChangePublishListDto,
  })
  @CommonApiResponse(PublishListResponseDto)
  public async getAllList(@Body() changePublishListDto: ChangePublishListDto) {
    const { list, total } =
      await this.changePublishService.getAll(changePublishListDto)
    return {
      list,
      pageInfo: {
        total,
        pageSize: changePublishListDto.pageSize,
        pageNum: changePublishListDto.pageNum,
      },
    }
  }

  @Get('newest')
  @ApiOperation({
    summary: '获取最新的发布单',
    operationId: 'getNewestPublish',
  })
  @ApiQuery({
    name: 'domainCode',
    required: true,
    description: '业务域code',
    type: String,
  })
  @CommonApiResponse(ChangePublishDto)
  public async getNewestPublish(@Query('domainCode') domainCode: string) {
    return await this.changePublishService.getNewestPublish(domainCode)
  }

  @Post('create')
  @ApiOperation({
    summary: '创建发布单',
    operationId: 'publishCreate',
  })
  @ApiBody({
    type: ChangePublishCreateDto,
  })
  @CommonApiResponse(ChangePublishDto)
  public async create(
    @Body() changePublishCreateDto: ChangePublishCreateDto,
    @Session() userSession: UserSession,
  ) {
    const changePublishInstance = await this.changePublishService.createPublish(
      changePublishCreateDto,
      changePublishCreateDto.lingzhuUserName || userSession?.userInfo?.userCode,
    )
    return {
      id: changePublishInstance.publishId,
      greyId: changePublishInstance.greyId,
    }
  }
  @Post('before/create/check')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '创建发布单前检查',
    operationId: 'beforeCreate',
  })
  public async beforeCreateCheck(
    @Body()
    changePublishCreateDto: ChangePublishCreateCheckDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.checkBeforePublish(
      changePublishCreateDto.changeId,
      !!changePublishCreateDto.ignoreComponentCheck,
      userSession,
    )
  }

  @Post('quit')
  @ApiOperation({
    summary: '退出发布',
    operationId: 'quitPublish',
  })
  @ApiBody({
    type: ChangePublishCommonDto,
  })
  public async quit(
    @Body() changePublishQuitDto: ChangePublishCommonDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.quitPublish(
      changePublishQuitDto.id,
      changePublishQuitDto.lingzhuUserName || userSession?.userInfo?.userCode,
    )
  }

  @Post('doPublish')
  @ApiOperation({
    summary: '执行发布',
    operationId: 'publish',
  })
  @ApiBody({
    type: ChangePublishCommonDto,
  })
  public async publish(
    @Body() changePublishQuitDto: ChangePublishCommonDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.doPublish(
      changePublishQuitDto.id,
      changePublishQuitDto.lingzhuUserName || userSession?.userInfo?.userCode,
    )
  }

  @Post('start')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '开始发布',
    operationId: 'startPublish',
  })
  @ApiBody({
    type: ChangePublishCommonDto,
  })
  public async startPublish(
    @Body() changePublishQuitDto: ChangePublishCommonDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.startPublishFlow(
      changePublishQuitDto.id,
      changePublishQuitDto.lingzhuUserName || userSession?.userInfo?.userCode,
    )
  }
  @Post('refresh/flow/state')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '刷新审批流状态',
    operationId: 'refresh_flow',
  })
  @ApiBody({
    type: ChangePublishRefreshFlowStateDto,
  })
  public async refreshFlowState(
    @Body() changePublishRefreshFlowStateDto: ChangePublishRefreshFlowStateDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.checkApprovalFlow(
      changePublishRefreshFlowStateDto.id,
      changePublishRefreshFlowStateDto.flowId,
      userSession?.userInfo?.userCode,
    )
  }

  /**
   * 回退到指定的变更
   * @param changePublishQuitDto
   * @param userSession
   * @returns
   */
  @Post('rollBack')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '回滚',
    operationId: 'rollBack',
  })
  @ApiBody({
    type: ChangePublishCommonDto,
  })
  public async rollBack(
    @Body() changePublishQuitDto: ChangePublishCommonDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.rollBack(
      changePublishQuitDto.id,
      userSession,
    )
  }

  /**
   * 回退上线单，回退到当前变更单模块版本的前一个线上版本
   * @param changePublishQuitDto
   * @param userSession
   * @returns
   */
  @Post('rollBack/current')
  @ApiOperation({
    summary: '回滚',
    operationId: 'rollBack/current',
  })
  @ApiBody({
    type: ChangePublishCommonDto,
  })
  public async rollBackCurrent(
    @Body() changePublishQuitDto: ChangePublishCommonDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.rollBackCurrent(
      changePublishQuitDto.id,
      changePublishQuitDto.lingzhuUserName || userSession?.userInfo?.userCode,
    )
  }

  @Post('deploy/grey')
  @ApiBody({
    type: ChangePublishCommonDto,
  })
  @ApiOperation({
    summary: '灰度部署',
    operationId: 'deployGrey',
  })
  public async deployGrey(
    @Body() params: ChangePublishCommonDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.deployGrey(
      params.id,
      params.lingzhuUserName || userSession?.userInfo?.userCode,
    )
  }
  @Post('send/message')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '发送消息',
    operationId: 'sendMessage',
  })
  public sendMessage(@Body() params: IMessage) {
    sendMessage(params)
  }

  @Post('edit/grey')
  @ApiOperation({
    summary: '编辑灰度配置',
    operationId: 'updatePublishGrey',
  })
  @ApiBody({
    type: UpdateGreyDeployDto,
  })
  public async updatePublishGrey(
    @Body() params: UpdateGreyDeployDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changePublishService.updatePublishGrey(
      params,
      params?.lingzhuUserName || userSession?.userInfo?.userCode,
    )
  }
  @Get('/grey/history')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '灰度部署历史',
    operationId: 'greyHistory',
  })
  @ApiQuery({
    name: 'greyId',
    required: true,
    description: '灰度id',
    type: Number,
  })
  public async getGreyHistory(@Query('greyId') greyId: number) {
    return await this.changePublishService.getGreyDeployList(greyId)
  }

  @Post('/lingzhu/update/status')
  @ApiOperation({
    summary: 'lingzhu推状态',
    operationId: 'lingzhuUpdateStatus',
  })
  @ApiBody({
    type: UpStatusByLingzhuDto,
  })
  public async UpStatusByLingzhu(@Body() params: UpStatusByLingzhuDto) {
    return await this.changePublishService.upStatusByLingzhu(
      params.id,
      params?.lingzhuUserName,
    )
  }

  @Get('/lingzhu/check/rollback/info')
  @ApiOperation({
    summary: 'lingzhu查询',
    operationId: 'getLingzhuCheckRollbackInfo',
  })
  @ApiQuery({
    name: 'publishId',
    required: true,
    description: '发布单id',
    type: Number,
  })
  public async getLingzhuCheckRollbackInfo(
    @Query('publishId') publishId: number,
  ) {
    const target =
      await this.changePublishService.getRollbackInfoByPublishId(publishId)
    return {
      rollBackPublish: target,
    }
  }

  @Post('/lingzhu/update/change/publishing/status')
  @ApiOperation({
    summary: 'lingzhu推发布单状态',
    operationId: 'lingzhuUpdateStatus',
  })
  @ApiBody({
    type: LingzhuUpdateChangeStatusDto,
  })
  public async drivingChangeStatusByLingzhu(
    @Body() params: LingzhuUpdateChangeStatusDto,
  ) {
    console.log(params)
    return await this.changePublishService.drivingChangeStatusByLingzhu(
      params.changeId,
      params.direction,
      params?.lingzhuUserName || '',
    )
  }
  @Get('/get/one')
  @ApiOperation({
    summary: '获取发布单',
    operationId: 'getById',
  })
  @ApiQuery({
    name: 'id',
    required: true,
    description: '发布单id',
    type: Number,
  })
  public async getById(@Query('id') id: number) {
    return await this.changePublishService.getOne(id)
  }
}
