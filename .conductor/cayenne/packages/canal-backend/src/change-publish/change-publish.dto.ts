import { OmitType } from '@nestjs/mapped-types'
import { ApiProperty } from '@nestjs/swagger'
import { IsInt, IsNotEmpty, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
import { DBTablePageInfo, LingZhuAdditionalParameters } from 'src/common/dto'

export class ChangePublishDto {
  @ApiProperty()
  id: number
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  changeId: string
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  domainCode: string
  @ApiProperty()
  @IsInt({ message: ErrorMessage.IS_NUMBER })
  status?: number
  @ApiProperty()
  @IsInt({ message: ErrorMessage.IS_NUMBER })
  isOnline?: number
  @ApiProperty()
  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  type: number
  @ApiProperty()
  createUser: string
  @ApiProperty()
  updateUser: string
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateTime: number
  @ApiProperty()
  publishTime: number
  @ApiProperty()
  publisher: number
  @ApiProperty()
  flowId: string
  @ApiProperty()
  flowUrl: string
  @ApiProperty()
  greyType: 'white' | 'lane' | 'percentage' | 'host'
  @ApiProperty()
  greyValue: string
  @ApiProperty()
  greyKey: string
}

export class ChangePublishCreateDto extends OmitType(ChangePublishDto, [
  'id',
  'createTime',
  'updateTime',
  'publishTime',
  'publisher',
] as const) {
  @ApiProperty()
  changeId: string
  @ApiProperty()
  domainCode: string
  @ApiProperty()
  status?: number
  @ApiProperty()
  isOnline?: number
  @ApiProperty()
  type: number
  @ApiProperty()
  createUser: string
  @ApiProperty()
  updateUser: string
  @ApiProperty()
  flowId: string
  @ApiProperty()
  flowUrl: string
  @ApiProperty()
  greyType: 'white' | 'lane' | 'percentage' | 'host'
  @ApiProperty()
  greyValue: string
  @ApiProperty()
  greyKey: string
  @ApiProperty()
  lingzhuUserName?: string
}

export class ChangePublishListDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域code' })
  readonly domainCode: string

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @ApiProperty({ description: '页码' })
  readonly pageNum: number

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @ApiProperty({ description: '分页大小' })
  readonly pageSize: number
}

export class ChangePublishCommonDto extends LingZhuAdditionalParameters {
  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @ApiProperty({ description: '发布单id' })
  readonly id: number
}

export class ChangePublishRefreshFlowStateDto {
  @ApiProperty({ description: '发布单id' })
  readonly id: number
  @ApiProperty({ description: '流程Id' })
  readonly flowId: string
}

export class PublishListResponseDto {
  @ApiProperty({ description: '分页信息', type: DBTablePageInfo })
  pageInfo: {
    pageNum: number
    pageSize: number
    total: number
  }
  @ApiProperty({ description: '发布单列表', type: [ChangePublishDto] })
  list: ChangePublishDto[]
}

export class ChangePublishCreateCheckDto {
  @ApiProperty()
  changeId: string
  @ApiProperty()
  ignoreComponentCheck?: boolean
}

export class UpStatusByLingzhuDto extends LingZhuAdditionalParameters {
  id: number
}

export class LingzhuUpdateChangeStatusDto extends LingZhuAdditionalParameters {
  @ApiProperty()
  @IsString()
  changeId: string
  @ApiProperty()
  direction: number
}
