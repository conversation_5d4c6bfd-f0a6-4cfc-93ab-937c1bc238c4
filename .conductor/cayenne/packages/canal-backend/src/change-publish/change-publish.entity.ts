import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
// `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
// `change_id` archar(255) NOT NULL DEFAULT '' COMMENT '变更id',
// `status` bigint(4) NOT NULL DEFAULT '' COMMENT '发布状态',
// `is_online` bigint(4) DEFAULT '0' COMMENT '是否在线上环境',
// `type` bigint(4) DEFAULT '0' COMMENT '类型',
// `create_user` varchar(100) NOT NULL DEFAULT '' COMMENT '创建者',
// `update_user` varchar(100) NOT NULL DEFAULT '' COMMENT '修改者',
// `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
// `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',

@Entity({ name: 'change_publish' })
export class ChangePublishEntity {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  id: number

  @Column({
    name: 'change_id',
    type: 'varchar',
    length: 255,
    comment: '变更id',
  })
  changeId: string

  @Column({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    comment: '业务域code',
  })
  domainCode: string

  @Column({
    name: 'publisher',
    type: 'varchar',
    length: 255,
    comment: '业务域code',
  })
  publisher?: string

  @Column({
    type: 'int',
    comment: '状态',
  })
  status: number

  @Column({
    name: 'is_online',
    type: 'int',
    comment: '是否上线',
  })
  isOnline: number

  @Column({
    type: 'int',
    comment: '类型',
  })
  type: number

  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建人',
  })
  createUser: string

  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '更新人',
  })
  updateUser: string

  @Column({
    type: 'bigint',
    name: 'create_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '创建时间',
  })
  createTime: number

  @Column({
    type: 'bigint',
    name: 'update_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '更新时间',
  })
  updateTime: number

  @Column({
    type: 'bigint',
    name: 'publish_time',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || 0,
    },
    comment: '发布时间',
  })
  publishTime?: number

  @Column({
    name: 'flow_id',
    type: 'varchar',
    length: 100,
    comment: '审批单id',
  })
  flowId?: string

  @Column({
    name: 'flow_url',
    type: 'varchar',
    length: 255,
    comment: '审批单地址',
  })
  flowUrl?: string

  @Column({
    name: 'rollback_from',
    type: 'varchar',
    length: 255,
    comment: 'rollback 发布单',
  })
  rollbackFrom?: string
}
