import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { InjectRepository } from '@nestjs/typeorm'
import dayjs from 'dayjs'
import { BusinessDomainService } from 'src/business-domain/business-domain.service'
import { ChangeModuleVersionService } from 'src/change-module-version/change-module-version.service'
import { Change, ChangeStatus, ChangeType } from 'src/change/change.entity'
import { ChangeService } from 'src/change/change.service'
import { logger } from 'src/common/logger'
import { DeployGreyLogService } from 'src/deploy_grey_log/deploy-grey-log.service'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import {
  GreyDeploy,
  GreyDeployStatus,
} from 'src/grey-deploy/grey-deploy.entity'
import { GreyDeployService } from 'src/grey-deploy/grey-deploy.service'
import { ModuleDeployEntity } from 'src/module-deploy/module-deploy.entity'
import { ModuleDeployService } from 'src/module-deploy/module-deploy.service'
import { ModuleVersion } from 'src/module-version/module-version.entity'
import { ModuleVersionService } from 'src/module-version/module-version.service'
import { ModuleService } from 'src/module/module.service'
import { UserSession } from 'src/types'
import { checkOrStartFlow, getFlowState, reportEvent } from 'src/utils/jwt'
import {
  DataSource,
  EntityManager,
  In,
  Not,
  Repository,
  type UpdateResult,
} from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import { DeployGreyLog } from '../deploy_grey_log/deploy-grey-log.entity'
import { UpdateGreyDeployDto } from '../grey-deploy/grey-deploy.dto'
import {
  ChangePublishCreateDto,
  ChangePublishListDto,
} from './change-publish.dto'
import { ChangePublishEntity } from './change-publish.entity'
export const PUBLISH_STATUS = {
  INIT: 0,
  ING: 1,
  FAIL: 2,
  FINISHED: 3,
  QUIT: 4,
  NO_NEED_FLOW: 5, //无需审批流程，待发布
  FLOWING: 6, //审批中
  FLOW_END: 7, // 审批结束待发布
  FLOW_FAIL: 8, //上线审批流程失败
}

const PUBLISH_LINE = {
  ONLINE: 1,
  OFFLINE: 0,
}

const TYPE = {
  NORMAL: 0,
  ROLL_BACK: 1,
}

const MODULE_DEPLOY_STATUS = {
  EFFECTIVE: 1,
  INEFFECTIVE: 2,
}

enum DrivingStatusDirect {
  DevelopingToPublishing = 1,
  PublishingToDeveloping = 0,
}

@Injectable()
export class ChangePublishService {
  public constructor(
    private readonly dataSource: DataSource,
    private readonly changeService: ChangeService,
    private readonly businessDomainService: BusinessDomainService,
    private readonly changeModuleVersionService: ChangeModuleVersionService,
    private readonly moduleVersionService: ModuleVersionService,
    private readonly moduleService: ModuleService,
    private readonly moduleDeployService: ModuleDeployService,
    private readonly deployGreyLogService: DeployGreyLogService,
    private configService: ConfigService,
    private readonly greyDeployService: GreyDeployService,
    @InjectRepository(ChangePublishEntity)
    public readonly changePublishRepo: Repository<ChangePublishEntity>,
  ) {}
  /**
   * 获取一个业务域下所有的发布单
   */
  async getAll({ domainCode, pageNum, pageSize }: ChangePublishListDto) {
    try {
      const [list, total] = await this.changePublishRepo
        .createQueryBuilder('change_publish')
        .where('change_publish.domainCode = :domainCode', {
          domainCode,
        })
        .orderBy('change_publish.createTime', 'DESC')
        .skip((pageNum - 1) * (pageSize || 10))
        .take(pageSize)
        .getManyAndCount()
      const publishList = []
      for (let i = 0; i < list.length; i++) {
        const publish = list[i]
        const changeObject = await this.changeService.get(publish.changeId)
        const grey = await this.greyDeployService.getOne(publish.id, [
          GreyDeployStatus.INIT,
          GreyDeployStatus.EFFECTIVE,
          GreyDeployStatus.SUCCESS,
          GreyDeployStatus.INVALID,
        ])
        publishList.push({
          ...publish,
          changeName: changeObject?.changeName,
          greyId: grey?.id,
        })
      }
      return {
        list: publishList,
        total,
      }
    } catch (e) {
      throw new CommonException(ExceptionCodes.CHANGE_LIST_BY_PAGE)
    }
  }
  /**
   * 获取最新的发布单
   */
  async getNewestPublish(domainCode: string) {
    const publishList = await this.changePublishRepo
      .createQueryBuilder('changePublish')
      .where('changePublish.domainCode = :domainCode', {
        domainCode,
      })
      .andWhere('changePublish.status != :status', {
        status: PUBLISH_STATUS.QUIT + '',
      })
      .orderBy('changePublish.createTime', 'DESC')
      .getMany()
    const newestPublish = publishList?.[0]
    if (newestPublish) {
      const grey = await this.greyDeployService.getOne(newestPublish.id, [
        GreyDeployStatus.INIT,
        GreyDeployStatus.EFFECTIVE,
      ])
      if (grey) {
        return {
          grey,
          ...newestPublish,
        }
      }
    }
    return newestPublish
  }

  /**
   * checkBeforePublish
   */
  async checkBeforePublish(
    changeId: string,
    ignoreComponentCheck: boolean,
    userSession: UserSession,
  ) {
    const changeObject = await this.changeService.get(changeId)
    if (!changeObject) {
      throw new CommonException(ExceptionCodes.Change_Publish_Change_Not_Found)
    }
    const domainCode = changeObject.domainCode
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userSession.userInfo.userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    /**
     * 此处的条件去掉publishing的判断，现在存在一种中间态为已经进入发布态但是未创建发布单
     * 再下面的判断发布单的条件其实也囊括了publishing并且有发布单的情况的判断
     */
    if (
      [ChangeStatus.PUBLISHED, ChangeStatus.CLOSED].indexOf(
        changeObject.status,
      ) > -1
    ) {
      throw new CommonException(
        ExceptionCodes.Change_Publish_Change_Status_Error,
      )
    }
    /**
     * 该变更有未完成的发布单
     */
    const havePublishing = await this.changePublishRepo.find({
      where: [
        {
          domainCode,
          status: Not(In([PUBLISH_STATUS.QUIT, PUBLISH_STATUS.FINISHED])),
        },
      ],
    })
    if (havePublishing.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Have_Unfinished)
    }
    /**
     * 获取所有的module
     */
    const modules = await this.moduleService.getAllByChangId({
      changeId,
      needContent: true,
    })
    /**
     * 所有模块都没有上过线
     */
    let noHistory = true
    /**
     * 至少有一个模块没有上过线
     */
    let hasNoHistory = false
    // 有模块
    if (Array.isArray(modules?.list) && modules?.list.length) {
      /**
       * 数据源相关检查
       */
      const results = await Promise.allSettled([
        await this.moduleVersionService.checkDataSourceConfig(
          modules.list,
          'dataSource',
        ),
        !ignoreComponentCheck
          ? await this.moduleVersionService.checkComponentsConfig(
              modules.list,
              'component',
            )
          : Promise.resolve({}),
      ])
      const mergedResults = results.map((result) => {
        if (
          result.status === 'fulfilled' &&
          Array.isArray(result.value) &&
          result.value.length
        ) {
          const rejectInfoArray = result.value.filter(
            (item): item is PromiseRejectedResult => item.status === 'rejected',
          )
          if (rejectInfoArray.length) {
            return {
              key: rejectInfoArray[0].reason?.key,
              content: rejectInfoArray.map((item) => item.reason),
            }
          }
        }
      })
      const filterResult = mergedResults.filter((item) => !!item)
      if (filterResult.length) {
        return {
          checkError: filterResult,
        }
      }

      for (const item of modules.list) {
        if (+item.version !== 1) {
          const publishHistory =
            await this.moduleDeployService.findNewestByModuleIdAndVersion(
              item.id,
              'production',
            )
          if (publishHistory) {
            noHistory = false
          } else {
            hasNoHistory = true
          }
        }
      }
    }
    return {
      noHistory,
      hasNoHistory,
      moduleCount: modules?.list?.length,
    }
  }

  /**
   * 获取非退出的一个变更的发布单
   */
  async getUsefulPublishByChangeId(domainCode: string) {
    return await this.changePublishRepo.find({
      where: {
        domainCode,
        status: Not(PUBLISH_STATUS.QUIT),
      },
    })
  }
  /**
   * 进行部署
   */
  async createPublish(
    changePublishCreateDto: ChangePublishCreateDto,
    userCode: string,
  ) {
    /**
     * 参数缺失
     */
    if (
      !changePublishCreateDto?.changeId ||
      !changePublishCreateDto?.domainCode
    ) {
      throw new CommonException(ExceptionCodes.Change_Publish_Params_Miss)
    }
    const changeId = changePublishCreateDto.changeId
    const domainCode = changePublishCreateDto.domainCode
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    /**
     * 变更的状态必须是未完成的
     */
    const changeObject = await this.changeService.get(changeId)

    if (!changeObject) {
      throw new CommonException(ExceptionCodes.Change_Publish_Change_Not_Found)
    }

    if (changeObject.domainCode !== domainCode) {
      throw new CommonException(ExceptionCodes.Change_Publish_Params_Error)
    }
    if (
      [ChangeStatus.PUBLISHED, ChangeStatus.CLOSED].indexOf(
        changeObject.status,
      ) > -1
    ) {
      throw new CommonException(
        ExceptionCodes.Change_Publish_Change_Status_Error,
      )
    }
    /**
     * 该变更有未完成的发布单
     */
    const havePublishing = await this.changePublishRepo.find({
      where: [
        {
          domainCode,
          status: Not(In([PUBLISH_STATUS.QUIT, PUBLISH_STATUS.FINISHED])),
        },
      ],
    })
    if (havePublishing.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Have_Unfinished)
    }

    if (changePublishCreateDto.greyType) {
      if (!changePublishCreateDto.greyValue) {
        throw new CommonException(ExceptionCodes.GREY_CONFIG_PARAMS_ERROR)
      }
      if (changePublishCreateDto.greyType === 'percentage') {
        if (
          !(
            Number.isInteger(+changePublishCreateDto.greyValue) &&
            +changePublishCreateDto.greyValue > 0 &&
            +changePublishCreateDto.greyValue < 100
          )
        ) {
          throw new CommonException(ExceptionCodes.GREY_CONFIG_PARAMS_ERROR)
        }
      }
    }

    /**
     * 更改变更状态
     * 创建发布单
     */
    const createTime = dayjs().valueOf()
    /**
     * 创建发布单实体
     */
    const publish = new ChangePublishEntity()
    publish.changeId = changeId
    publish.status = PUBLISH_STATUS.INIT
    publish.type = changePublishCreateDto.type || TYPE.NORMAL
    publish.isOnline = PUBLISH_LINE.OFFLINE
    publish.domainCode = domainCode
    publish.createTime = createTime
    publish.updateTime = createTime
    publish.createUser = userCode
    publish.updateUser = userCode

    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction()

    let newPublish: ChangePublishEntity | null = null
    let greyId
    try {
      newPublish = await queryRunner.manager.save(publish)
      await queryRunner.manager.update(
        Change,
        { changeId },
        { status: ChangeStatus.PUBLISHING },
      )
      /**
       * 如果有灰度单信息,则创建灰度单
       */
      if (changePublishCreateDto.greyType && changePublishCreateDto.greyValue) {
        const greyDeploy = new GreyDeploy()
        greyDeploy.publishId = newPublish.id
        greyDeploy.greyType = changePublishCreateDto.greyType
        greyDeploy.greyValue = changePublishCreateDto.greyValue
        greyDeploy.greyKey = changePublishCreateDto.greyKey
        greyDeploy.createTime = createTime
        greyDeploy.updateTime = createTime
        greyDeploy.createUser = userCode
        greyDeploy.updateUser = userCode
        greyDeploy.status = GreyDeployStatus.INIT
        const grey = await queryRunner.manager.save(greyDeploy)
        if (grey) {
          greyId = grey.id
        }
      }

      await queryRunner.commitTransaction()
    } catch (e) {
      await queryRunner.rollbackTransaction()
      logger.error('[Publish Service] create error', e as unknown as Error)
      throw new CommonException(ExceptionCodes.Change_Publish_Create_Error)
    } finally {
      await queryRunner.release()
    }
    return {
      publishId: newPublish?.id,
      greyId,
    }
  }

  /**
   * 退出部署
   */
  async quitPublish(publishId: number, userCode: string) {
    /**
     * 参数缺失
     */
    if (!publishId) {
      throw new CommonException(ExceptionCodes.Change_Publish_Params_Miss)
    }

    const _publishArray = await this.changePublishRepo.find({
      where: {
        id: publishId,
      },
    })
    if (!_publishArray?.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Found)
    }
    const _publish = _publishArray[0]
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      _publish.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    /**
     * 检查发布单状态,
     */
    if (
      [PUBLISH_STATUS.FINISHED, PUBLISH_STATUS.QUIT].indexOf(+_publish.status) >
      -1
    ) {
      throw new CommonException(ExceptionCodes.Change_Publish_Status_Error)
    }
    /**
     * 变更
     */
    const changeObject = await this.changeService.get(_publish.changeId)

    if (!changeObject) {
      throw new CommonException(ExceptionCodes.Change_Publish_Change_Not_Found)
    }

    /**
     * 查找有效灰度单
     */
    const grey = await this.greyDeployService.getOne(publishId, [
      GreyDeployStatus.INIT,
      GreyDeployStatus.EFFECTIVE,
      GreyDeployStatus.SUCCESS,
      GreyDeployStatus.INVALID,
    ])
    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction()
    const _now = dayjs().valueOf()
    try {
      await queryRunner.manager.update(
        Change,
        { changeId: changeObject.changeId },
        {
          status: ChangeStatus.DEVELOPING,
          updateTime: _now,
          updateUser: userCode,
        },
      )
      await queryRunner.manager.update(
        ChangePublishEntity,
        { id: _publish.id },
        {
          status: PUBLISH_STATUS.QUIT,
          isOnline: PUBLISH_LINE.OFFLINE,
          updateTime: _now,
          updateUser: userCode,
        },
      )
      /**
       * 如果有灰度单，则更新灰度单状态为失效，并且更新module_deploy内的灰度状态
       */
      if (grey) {
        await queryRunner.manager.update(
          GreyDeploy,
          { id: grey.id },
          {
            status: GreyDeployStatus.INVALID,
            updateTime: _now,
          },
        )
        const moduleVersionList =
          await this.changeModuleVersionService.getListByChangeId(
            changeObject.changeId,
          )
        if (Array.isArray(moduleVersionList) && moduleVersionList.length) {
          await Promise.all(
            moduleVersionList.map(async (moduleVersionItem) => {
              return await queryRunner.manager.update(
                ModuleDeployEntity,
                {
                  moduleId: moduleVersionItem.moduleId,
                  version: moduleVersionItem.moduleVersion,
                  greyStatus: MODULE_DEPLOY_STATUS.EFFECTIVE,
                },
                {
                  greyStatus: MODULE_DEPLOY_STATUS.INEFFECTIVE,
                  updateTime: _now,
                  updateUser: userCode,
                  stage: 'grey',
                },
              )
            }),
          ).catch(() => {
            throw new CommonException(ExceptionCodes.Change_Publish_Fail)
          })
        }
      }
      await queryRunner.commitTransaction()
    } catch (e) {
      await queryRunner.rollbackTransaction()
    } finally {
      await queryRunner.release()
    }
  }
  /**
   * 发布前检查
   */
  async checkAndGetPublish(publishId: number, userCode: string) {
    if (!publishId) {
      throw new CommonException(ExceptionCodes.Change_Publish_Params_Miss)
    }

    const _publishArray = await this.changePublishRepo.find({
      where: {
        id: publishId,
      },
    })
    if (!_publishArray?.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Found)
    }
    const _publish = _publishArray[0]
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      _publish.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    return {
      publish: _publish,
    }
  }

  /**
   * 开始发布流程
   */
  async startPublishFlow(publishId: number, userCode: string) {
    let _publish
    const ret = await this.checkAndGetPublish(publishId, userCode)
    if (ret.publish) {
      _publish = ret.publish
    } else {
      throw new CommonException(ExceptionCodes.Change_Publish_Fail)
    }
    /**
     * 只有审批失败或者还未开始的状态才可以发起审批
     */
    if (
      [PUBLISH_STATUS.INIT, PUBLISH_STATUS.FLOW_FAIL].indexOf(
        +_publish.status,
      ) < 0
    ) {
      throw new CommonException(ExceptionCodes.Change_Publish_Status_Error)
    }
    /**
     * 变更
     */
    const changeObject = await this.changeService.get(_publish.changeId)

    if (!changeObject) {
      throw new CommonException(ExceptionCodes.Change_Publish_Change_Not_Found)
    }
    try {
      // 如果有过流程，则不用修改
      if (
        [
          PUBLISH_STATUS.FLOWING,
          PUBLISH_STATUS.FLOW_END,
          PUBLISH_STATUS.NO_NEED_FLOW,
        ].indexOf(+_publish.status) > -1
      ) {
        return _publish
      }
      /**
       * 查询关联的灰度单
       */
      const grey = await this.greyDeployService.getOne(publishId, [
        GreyDeployStatus.INIT,
      ])

      /**
       * 审批流
       */
      const approvalConfig = await this._getApprovalConfig(_publish.domainCode)
      const flowResult = await checkOrStartFlow(
        approvalConfig,
        changeObject.changeName,
        `https://canal.corp.kuaishou.com/change/detail?changeId=${changeObject.changeId} &domainCode=${changeObject.domainCode}`,
        userCode,
        !!grey,
        grey?.greyType,
        grey?.greyValue,
      )
      let publishStatus = _publish.status
      // 如果未命中策略，response中check_result是不一样的，response中 is_audit 为false。不建议根据flow_id是否为空进行判断，可能存在误判的情况。而且接入方最好根据上面提到的错误码进行降级策略
      if (flowResult) {
        //命中策略且需要审批, 或者根本没有命中策略，
        if (flowResult.has_flow && flowResult.flow_result.flow_id) {
          publishStatus = PUBLISH_STATUS.FLOWING
        }
        // 没有命中策略，或者不需要审批则放行
        if (!flowResult.is_audit || !flowResult.has_flow) {
          publishStatus = PUBLISH_STATUS.NO_NEED_FLOW
        }

        if (flowResult.has_block) {
          publishStatus = PUBLISH_STATUS.FLOW_FAIL
        }
        const updatedPublish = await this.changePublishRepo.update(
          {
            id: _publish.id,
          },
          {
            status: publishStatus,
            flowId: flowResult.flow_result?.flow_id || '',
            flowUrl: flowResult.flow_result?.flow_url || '',
          },
        )
        return updatedPublish
      }
    } catch (e) {
      throw new CommonException(ExceptionCodes.Change_Publish_Fail)
    }
  }

  /** 检查流程 */
  async checkApprovalFlow(publishId: number, flowId: string, userCode: string) {
    let _publish: ChangePublishEntity
    const ret = await this.checkAndGetPublish(publishId, userCode)
    if (ret.publish) {
      _publish = ret.publish
    } else {
      throw new CommonException(ExceptionCodes.Change_Publish_Fail)
    }
    /**
     * 正在进行中的审批流才能更新状态
     */
    if ([PUBLISH_STATUS.FLOWING].indexOf(+_publish.status) < 0) {
      throw new CommonException(ExceptionCodes.Change_Publish_Status_Error)
    }

    const approvalConfig = await this._getApprovalConfig(_publish.domainCode)

    /**
     * DRAFT 未提交
     * SUBMIT 已提交
     * WITHDAW 已撤回
     * BACK 被驳回
     * CANCEL 已撤销
     * OVER 已完结
     * NULL 未知错误
     */
    const handleOverError = async () => {
      try {
        await this.changePublishRepo.update(
          {
            id: _publish.id,
          },
          {
            status: PUBLISH_STATUS.FLOW_FAIL,
          },
        )
      } catch {
        throw new CommonException(
          ExceptionCodes.Change_Publish_Flow_Update_Fail,
        )
      }
    }
    const flowState = await getFlowState(flowId, approvalConfig, userCode)
    switch (flowState?.state) {
      // 流程已完结状态
      case 'OVER':
        // 正常完结
        if (flowState.overState === 'Normal') {
          try {
            await this.changePublishRepo.update(
              {
                id: _publish.id,
              },
              {
                status: PUBLISH_STATUS.FLOW_END,
              },
            )
          } catch {
            throw new CommonException(
              ExceptionCodes.Change_Publish_Flow_Update_Fail,
            )
          }
        } else {
          await handleOverError()
        }
        break
      case 'NULL':
        throw new CommonException(
          ExceptionCodes.Change_Publish_Flow_Update_Fail,
        )
      case 'WITHDAW':
      case 'BACK':
      case 'CANCEL':
        await handleOverError()
    }
    return {
      state: flowState,
    }
  }

  /**
   * 重新设置发布单的灰度策略
   */
  async updatePublishGrey(
    updateGreyDeployDto: UpdateGreyDeployDto,
    userCode: string,
  ) {
    const _publish = await this.changePublishRepo.findOneBy({
      id: updateGreyDeployDto.publishId,
    })
    if (!_publish) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Found)
    }
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      _publish.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    /**
     * 查找发布单下是否有有效的灰度单，是否和更新详细match
     */
    const grey = await this.greyDeployService.getOne(
      updateGreyDeployDto.publishId,
      [GreyDeployStatus.INIT, GreyDeployStatus.EFFECTIVE],
    )
    if (!grey || grey.id !== updateGreyDeployDto.id) {
      throw new CommonException(ExceptionCodes.GREY_NOT_FOUND)
    }
    if (
      grey.greyType === updateGreyDeployDto.greyType &&
      grey.greyValue === updateGreyDeployDto.greyValue
    ) {
      throw new CommonException(ExceptionCodes.GREY_NOT_CHANGE)
    }
    if (updateGreyDeployDto.lingzhuUserName) {
      delete updateGreyDeployDto.lingzhuUserName
    }
    return await this.greyDeployService.update(updateGreyDeployDto)
  }

  /**
   * 真正的发布步骤, 审批流程后发布动作
   */
  async doPublish(publishId: number, userCode: string) {
    let _publish
    const ret = await this.checkAndGetPublish(publishId, userCode)
    if (ret.publish) {
      _publish = ret.publish
    } else {
      throw new CommonException(ExceptionCodes.Change_Publish_Fail)
    }
    /**
     * 在审批流check那步骤通过后才可以进行推全发布
     */
    if (
      [
        PUBLISH_STATUS.NO_NEED_FLOW,
        PUBLISH_STATUS.FLOW_END,
        PUBLISH_STATUS.ING,
      ].indexOf(+_publish.status) < 0
    ) {
      throw new CommonException(ExceptionCodes.Change_Publish_Status_Error)
    }
    if (Number(_publish.status) === PUBLISH_STATUS.FINISHED) {
      throw new CommonException(ExceptionCodes.Change_Publish_Finished)
    }
    /**
     * 变更
     */
    const changeObject = await this.changeService.get(_publish.changeId)

    if (!changeObject) {
      throw new CommonException(ExceptionCodes.Change_Publish_Change_Not_Found)
    }

    const moduleVersionList =
      await this.changeModuleVersionService.getListByChangeId(
        changeObject.changeId,
      )
    if (!Array.isArray(moduleVersionList) || !moduleVersionList.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Has_No_Module)
    }
    const grey = await this.greyDeployService.getOne(_publish.id, [
      GreyDeployStatus.INIT,
      GreyDeployStatus.EFFECTIVE,
    ])
    if (grey?.status == GreyDeployStatus.INIT) {
      throw new CommonException(ExceptionCodes.HAS_GREY_NOT_DEPLOY)
    }

    const _now = dayjs().valueOf()

    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction()
    try {
      await queryRunner.manager.update(
        Change,
        { changeId: changeObject.changeId },
        {
          status: ChangeStatus.PUBLISHED,
          updateTime: _now,
          updateUser: userCode,
        },
      )
      // 在回滚时候将原有的变更退回编辑状态
      if (_publish.rollbackFrom && !isNaN(Number(_publish.rollbackFrom))) {
        const _rollbackPublish = await this.getOne(
          Number(_publish.rollbackFrom),
        )
        if (_rollbackPublish) {
          await queryRunner.manager.update(
            Change,
            { changeId: _rollbackPublish?.changeId },
            {
              status: ChangeStatus.DEVELOPING,
              updateTime: _now,
              updateUser: userCode,
            },
          )
        }
      }
      // 需要找出该变更下的所有的模块
      const moduleList = await this.moduleService.getAllByChangId({
        changeId: changeObject.changeId,
        needContent: true,
      })
      const list = moduleList?.list
      if (Array.isArray(list) && list.length) {
        await Promise.all(
          list.map(async (module) => {
            const moduleDeploy = new ModuleDeployEntity()
            moduleDeploy.moduleId = module.moduleId
            moduleDeploy.version = module.version
            moduleDeploy.content = module.content || ''
            moduleDeploy.stage = 'production'
            moduleDeploy.createUser = userCode
            moduleDeploy.updateUser = userCode
            moduleDeploy.createTime = _now
            moduleDeploy.updateTime = _now
            await this.moduleDeployService.setGlobalModuleId(moduleDeploy)
            return await queryRunner.manager.save(moduleDeploy)
          }),
        )
      }
      await queryRunner.manager.update(
        ChangePublishEntity,
        { domainCode: _publish.domainCode },
        {
          isOnline: PUBLISH_LINE.OFFLINE,
          updateTime: _now,
          updateUser: userCode,
        },
      )
      await queryRunner.manager.update(
        ChangePublishEntity,
        { id: _publish.id },
        {
          status: PUBLISH_STATUS.FINISHED,
          isOnline: PUBLISH_LINE.ONLINE,
          publisher: userCode,
          publishTime: _now,
          updateTime: _now,
          updateUser: userCode,
        },
      )
      /**
       * 重置所有的状态
       */
      const promises: Promise<UpdateResult>[] = []
      moduleVersionList.forEach(async (moduleVersionItem) => {
        promises.push(
          queryRunner.manager.update(
            ModuleVersion,
            { moduleId: moduleVersionItem.moduleId },
            {
              status: 0,
              updateTime: _now,
              updateUser: userCode,
            },
          ),
        )
      })
      await Promise.all(promises).catch(() => {
        throw new CommonException(ExceptionCodes.Change_Publish_Has_No_Module)
      })
      const promisesSetAllPublished: Promise<UpdateResult>[] = []
      moduleVersionList.forEach(async (moduleVersionItem) => {
        promisesSetAllPublished.push(
          queryRunner.manager.update(
            ModuleVersion,
            {
              moduleId: moduleVersionItem.moduleId,
              version: moduleVersionItem.moduleVersion,
            },
            {
              status: 1,
              updateTime: _now,
              updateUser: userCode,
            },
          ),
        )
      })
      await Promise.all(promises).catch(() => {
        throw new CommonException(ExceptionCodes.Change_Publish_Has_No_Module)
      })
      /**
       * 如果有灰度单，则更新灰度单状态，并且更新module_deploy内的灰度状态
       */
      if (grey) {
        await queryRunner.manager.update(
          GreyDeploy,
          { id: grey.id },
          {
            status: GreyDeployStatus.SUCCESS,
            updateTime: _now,
          },
        )
        await Promise.all(
          moduleVersionList.map(async (moduleVersionItem) => {
            return queryRunner.manager.update(
              ModuleDeployEntity,
              {
                moduleId: moduleVersionItem.moduleId,
                version: moduleVersionItem.moduleVersion,
                greyStatus: MODULE_DEPLOY_STATUS.EFFECTIVE,
              },
              {
                greyStatus: MODULE_DEPLOY_STATUS.INEFFECTIVE,
                updateTime: _now,
                updateUser: userCode,
                stage: 'grey',
              },
            )
          }),
        ).catch(() => {
          throw new CommonException(ExceptionCodes.Change_Publish_Fail)
        })
      }

      await queryRunner.commitTransaction()

      this._reportEventAfterPublish(publishId)
    } catch (e) {
      await queryRunner.rollbackTransaction()
      throw new CommonException(ExceptionCodes.Change_Publish_Fail)
    } finally {
      await queryRunner.release()
    }
  }

  /**
   * 回滚，实质是创建新的发布单
   * 检查参数
   * 检查权限
   * 发布单是否发布过
   * 发布单是否不是在线上的
   * 是否有正在进行的发布单？如果是那不能回滚
   * 创建新的发布单， 但是类型是回滚和正常的不同
   */
  async rollBack(publishId: number, userSession: UserSession) {
    /**
     * 参数缺失
     */
    if (!publishId) {
      throw new CommonException(ExceptionCodes.Change_Publish_Params_Miss)
    }
    /**
     * 获取发布单实例
     */
    const _publishArray = await this.changePublishRepo.find({
      where: {
        id: publishId,
      },
    })
    /**
     * 无效的publishID
     */
    if (!_publishArray?.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Found)
    }
    const _publish = _publishArray[0]

    // 如果本身就是在线的就不用回滚了
    if (Number(_publish.isOnline) === PUBLISH_LINE.ONLINE) {
      throw new CommonException(ExceptionCodes.Change_Publish_Already_Online)
    }
    // 没有发上线过的也不能回滚
    if (Number(_publish.status) !== PUBLISH_STATUS.FINISHED) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Published)
    }
    const domainCode = _publish.domainCode

    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userSession.userInfo.userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    /**
     * 检查是否有正在执行的发布单
     */
    const havePublishing = await this.changePublishRepo.find({
      where: [
        {
          domainCode,
          status: Not(In([PUBLISH_STATUS.QUIT, PUBLISH_STATUS.FINISHED])),
        },
      ],
    })
    if (havePublishing.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Have_Unfinished)
    }
    /**
     * 更改变更状态
     * 创建发布单
     */
    const createTime = dayjs().valueOf()
    /**
     * 创建发布单实体
     */
    const publish = new ChangePublishEntity()
    publish.changeId = _publish.changeId
    publish.status = PUBLISH_STATUS.INIT
    publish.type = TYPE.ROLL_BACK
    publish.isOnline = PUBLISH_LINE.OFFLINE
    publish.domainCode = domainCode
    publish.createTime = createTime
    publish.updateTime = createTime
    publish.createUser = userSession.userInfo?.userCode
    publish.updateUser = userSession.userInfo?.userCode

    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction()

    let newPublish: ChangePublishEntity | null = null
    try {
      newPublish = await queryRunner.manager.save(publish)
      await queryRunner.manager.update(
        Change,
        { changeId: _publish.changeId },
        { status: ChangeStatus.PUBLISHING },
      )
      await queryRunner.commitTransaction()
    } catch (e) {
      await queryRunner.rollbackTransaction()
      throw new CommonException(ExceptionCodes.Change_Publish_Create_Error)
    } finally {
      await queryRunner.release()
    }
    return newPublish?.id
  }

  /**
   * 获取一个变更单最新的回滚信息，查到回滚的变更单
   */
  async getRollbackInfoByPublishId(publishId: number) {
    const publishObj = await this.changePublishRepo.findOne({
      where: {
        rollbackFrom: publishId.toString(),
      },
      order: {
        createTime: 'DESC',
      },
    })

    return publishObj
  }

  async rollBackCurrent(publishId: number, userCode: string) {
    if (!publishId) {
      throw new CommonException(ExceptionCodes.Change_Publish_Params_Miss)
    }
    /**
     * 获取发布单实例
     */
    const _publishArray = await this.changePublishRepo.find({
      where: {
        id: publishId,
      },
    })
    /**
     * 无效的publishID
     */
    if (!_publishArray?.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Found)
    }
    const _publish = _publishArray[0]

    // 没有发上线过的不能回滚, 检查发布单状态
    if (Number(_publish.status) !== PUBLISH_STATUS.FINISHED) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Published)
    }

    const domainCode = _publish.domainCode

    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    // 回滚的单子不能再次进行回滚
    if (_publish.rollbackFrom) {
      throw new CommonException(ExceptionCodes.Change_Rollback_Forbidden)
    }

    const existPublish = await this.getRollbackInfoByPublishId(publishId)
    // 最新的线上发布单才行.此时不是最新的发布单，但是用户仍然进行了点击操作，则去查询一下是不是重试的情况
    if (Number(_publish.isOnline) !== PUBLISH_LINE.ONLINE) {
      if (existPublish) {
        // 已经回滚
        if (Number(existPublish.status) === PUBLISH_STATUS.FINISHED) {
          // 表示该发布已经回滚过了，
          return {
            publishId: existPublish.id,
          }
        }
      } else {
        throw new CommonException(ExceptionCodes.Rollback_Need_Online_Status)
      }
    }

    const changeInfo = await this.changeService.getOne(_publish.changeId)
    if (!changeInfo) {
      throw new CommonException(ExceptionCodes.Module_Change_Not_Found)
    }
    /**
     * 判断是否已经有回滚的单子，供lingzhu来异步查询，作幂等
     */
    if (existPublish) {
      const existStatus = Number(existPublish.status)
      const existId = Number(existPublish.id)
      // 已经回滚
      if (existStatus === PUBLISH_STATUS.FINISHED) {
        // 表示该发布已经回滚过了，
        return {
          publishId: existPublish.id,
        }
      } else if (existStatus === PUBLISH_STATUS.ING) {
        try {
          // 如果不是已经完结状态,待发布状态的话，则直接执行发布重试
          await this.doPublish(existId, userCode)
          return {
            publishId: existPublish.id,
          }
        } catch (e) {
          logger.error(
            '[Publish Service] rollback has exist and publish fail',
            e as unknown as Error,
          )
          throw new CommonException(ExceptionCodes.Change_Publish_Fail)
        }
      } else {
        // 若是其他状态则进行关闭
        if (existStatus !== PUBLISH_STATUS.QUIT) {
          try {
            await this.quitPublish(existId, userCode)
          } catch (e) {
            logger.error(
              '[Publish Service] rollback has exist and close fail',
              e as unknown as Error,
            )
            throw new CommonException(ExceptionCodes.Change_Publish_Fail)
          }
        }
      }
    }

    /**
     * 检查是否有正在执行的发布单
     */
    const havePublishing = await this.changePublishRepo.find({
      where: [
        {
          domainCode,
          status: Not(In([PUBLISH_STATUS.QUIT, PUBLISH_STATUS.FINISHED])),
        },
      ],
    })
    if (havePublishing.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Have_Unfinished)
    }

    //获取该变更下的所有模块
    const modules = await this.moduleService.getAllByChangId({
      changeId: _publish.changeId,
      needContent: false,
    })
    const moduleList = Array.isArray(modules?.list) ? modules?.list : []
    const tasks: Promise<ModuleDeployEntity | null>[] = []
    moduleList?.forEach?.((module) => {
      if (Number(module.version) !== 1) {
        tasks.push(
          this.moduleDeployService.getPreviousRecordRecordByModuleIdAndVersion(
            module.moduleId,
            module.moduleVersion,
          ),
        )
      }
    })
    if (!moduleList?.length) {
      throw new CommonException(ExceptionCodes.Change_Rollback_no_content)
    }

    const results = await Promise.all(tasks)
    console.log('All tasks completed successfully:', results)
    if (!results?.length) {
      throw new CommonException(ExceptionCodes.Change_Rollback_no_content)
    }
    try {
      const time = dayjs().valueOf()
      const commonInfo = {
        createUser: userCode,
        updateUser: userCode,
        createTime: time,
        updateTime: time,
      }
      let newPublishId
      await this.dataSource.manager.transaction(
        async (transactionalEntityManager: EntityManager) => {
          const changeId = domainCode + uuidv4()
          /**回滚不需要插入teamid */
          await transactionalEntityManager.getRepository('change').insert({
            changeId: changeId,
            changeName: changeInfo.changeName,
            type: ChangeType.ROLLBACK,
            domainCode: changeInfo.domainCode,
            status: ChangeStatus.PUBLISHING,
            createUser: userCode,
            updateUser: userCode,
            createTime: time,
            updateTime: time,
          })
          // 2. 批量处理模块版本（优化循环效率）
          const operations = results.map(async (moduleData) => {
            if (moduleData) {
              // 使用悲观锁确保版本号唯一性
              const maxVersionResult = await transactionalEntityManager
                .getRepository('module_version')
                .createQueryBuilder()
                .select('MAX(module_version)', 'maxVersion')
                .where('module_id = :moduleId', {
                  moduleId: moduleData.moduleId,
                })
                .setLock('pessimistic_write') // 新增行锁
                .getRawOne()

              const newVersion = Number(maxVersionResult?.maxVersion || 0) + 1

              // 插入新版本（优化对象结构）
              await transactionalEntityManager
                .getRepository('module_version')
                .insert({
                  moduleId: moduleData.moduleId,
                  version: newVersion,
                  content: moduleData.content,
                  status: 0,
                  ...commonInfo,
                })

              // 3. 关联变更与版本（优化关联操作）
              await transactionalEntityManager
                .getRepository('change_module_version')
                .insert({
                  moduleId: moduleData.moduleId,
                  changeId: changeId,
                  moduleVersion: newVersion,
                  checkoutModuleVersion: moduleData.version,
                  rollbackFrom: moduleData.version,
                  ...commonInfo,
                })
            }
          })
          await Promise.all(operations)

          const insertResult = await transactionalEntityManager
            .getRepository('change_publish')
            .insert({
              changeId: changeId,
              domainCode: changeInfo.domainCode,
              status: PUBLISH_STATUS.ING,
              isOnline: PUBLISH_LINE.OFFLINE,
              type: TYPE.ROLL_BACK,
              rollbackFrom: publishId,
              ...commonInfo,
            })

          if (!insertResult.identifiers?.[0]?.id) {
            throw new CommonException(ExceptionCodes.Change_Publish_Fail)
          }
          newPublishId = insertResult.identifiers?.[0]?.id
        },
      )
      newPublishId && (await this.doPublish(newPublishId, userCode))
      return {
        publishId: newPublishId,
      }
    } catch (error) {
      logger.error(
        '[Publish Service] rollback current err',
        error as unknown as Error,
      )
      // 处理错误
      throw new CommonException(ExceptionCodes.Change_Publish_Fail)
    }
  }
  async getOne(id: number) {
    return await this.changePublishRepo.findOneBy({ id })
  }

  async deployGrey(id: number, userCode: string) {
    const _publish = await this.getOne(id)
    if (!_publish) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Found)
    }
    /**
     * 未完结的变更单
     */
    if (
      [
        PUBLISH_STATUS.FINISHED,
        PUBLISH_STATUS.QUIT,
        PUBLISH_STATUS.FAIL,
      ].indexOf(+_publish.status) > -1
    ) {
      throw new CommonException(ExceptionCodes.Change_Publish_Status_Error)
    }
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      _publish.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    /**
     * 查找有效灰度单
     */
    const grey = await this.greyDeployService.getOne(_publish.id, [
      GreyDeployStatus.INIT,
      GreyDeployStatus.EFFECTIVE,
    ])
    if (!grey) {
      throw new CommonException(ExceptionCodes.GREY_NOT_FOUND)
    }
    /**
     * 检查灰度单的状态
     */
    if (
      [GreyDeployStatus.INIT, GreyDeployStatus.EFFECTIVE].indexOf(
        _publish?.status,
      ) > -1
    ) {
      throw new CommonException(ExceptionCodes.Change_Publish_Status_Error)
    }

    /**
     * 变更
     */
    const changeObject = await this.changeService.get(_publish.changeId)

    if (!changeObject) {
      throw new CommonException(ExceptionCodes.Change_Publish_Change_Not_Found)
    }

    /**
     * 查找所有变更单
     */
    const moduleVersionList =
      await this.changeModuleVersionService.getListByChangeId(
        changeObject.changeId,
      )
    if (!Array.isArray(moduleVersionList) || !moduleVersionList.length) {
      throw new CommonException(ExceptionCodes.Change_Publish_Has_No_Module)
    }
    // 需要找出该变更下的所有的模块
    const moduleList = await this.moduleService.getAllByChangId({
      changeId: changeObject.changeId,
      needContent: true,
    })
    /**
     * 更新
     */
    const now = new Date().getTime()
    const deployLog = new DeployGreyLog()
    deployLog.greyId = grey.id
    deployLog.greyType = grey.greyType
    deployLog.greyValue = grey.greyValue
    deployLog.greyKey = grey.greyKey
    deployLog.createTime = now
    deployLog.updateTime = now
    deployLog.createUser = userCode
    deployLog.updateUser = userCode

    const queryRunner = this.dataSource.createQueryRunner()

    await queryRunner.connect()
    await queryRunner.startTransaction()
    try {
      const list = moduleList?.list
      if (Array.isArray(list) && list.length) {
        // 使用 await 确保所有的 Promise 都完成
        await Promise.all(
          list.map(async (module) => {
            try {
              /**
               * 已经有发布的灰度记录
               */
              const result = await queryRunner.query(
                `
                  SELECT * FROM module_deploy WHERE module_id = ? AND module_version = ? AND stage = ? AND grey_status = ?
              `,
                [module.id, module.version, 'grey', 1],
              )
              /**
               * 更新已有记录
               */
              if (result.length) {
                return await queryRunner.manager.update(
                  ModuleDeployEntity,
                  {
                    moduleId: module.id,
                    version: module.version,
                    stage: 'grey',
                    greyStatus: MODULE_DEPLOY_STATUS.EFFECTIVE,
                  },
                  {
                    content: module.content,
                    updateTime: now,
                    updateUser: userCode,
                    greyValue: grey.greyValue,
                    greyType: grey.greyType,
                    greyKey: grey.greyKey,
                  },
                )
              } else {
                const moduleDeploy = new ModuleDeployEntity()
                moduleDeploy.moduleId = module.moduleId
                moduleDeploy.version = module.version
                moduleDeploy.content = module.content || ''
                moduleDeploy.stage = 'grey'
                moduleDeploy.greyStatus = MODULE_DEPLOY_STATUS.EFFECTIVE
                moduleDeploy.greyValue = grey.greyValue
                moduleDeploy.greyType = grey.greyType
                moduleDeploy.greyKey = grey.greyKey
                moduleDeploy.createUser = userCode
                moduleDeploy.updateUser = userCode
                moduleDeploy.createTime = now
                moduleDeploy.updateTime = now
                await this.moduleDeployService.setGlobalModuleId(moduleDeploy)
                return await queryRunner.manager.save(moduleDeploy)
              }
            } catch (error) {
              console.error(`Error processing module ${module.id}:`, error)
              throw error
            }
          }),
        ).catch(() => {
          throw new CommonException(ExceptionCodes.DEPLOY_GREY_FAILED)
        })
      }

      await queryRunner.manager.update(
        GreyDeploy,
        { id: grey.id },
        {
          status: GreyDeployStatus.EFFECTIVE,
          updateTime: now,
        },
      )
      await queryRunner.manager.update(
        ChangePublishEntity,
        { id: _publish.id },
        {
          status: PUBLISH_STATUS.ING,
          updateTime: now,
        },
      )
      // 塞日志
      await queryRunner.manager.save(deployLog)
      await queryRunner.commitTransaction()
    } catch (e) {
      console.error('Transaction failed, rolling back:', e)
      await queryRunner.rollbackTransaction()
    } finally {
      await queryRunner.release()
    }
  }

  async getGreyDeployList(id: number) {
    return await this.deployGreyLogService.findAll(id)
  }

  /**
   * 发布后上报事件
   * @param publishId 发布 ID
   */
  async _reportEventAfterPublish(publishId: number) {
    try {
      const changePublishs = await this.changePublishRepo.find({
        where: {
          id: publishId,
        },
      })
      if (!changePublishs.length) return
      const cp = changePublishs[0]
      const change = await this.changeService.get(cp.changeId)
      if (!cp.publisher || !cp.publishTime || !change?.changeName) {
        return
      }
      const params = {
        platform: 'ad_canal',
        jobId: Number(cp.id),
        jobName: change.changeName,
        jobType: 'ONLINE',
        operator: cp.publisher,
        isAutoDeploy: false,
        startTime: Math.round(cp.createTime / 1000),
        endTime: Math.round(cp.publishTime / 1000),
        status: 'FINISHED',
        isSuccess: true,
        isAudited: true,
        progressUrl: cp.flowUrl,
        environment: 'PROD',
        serviceSet: ['ad-canal-manage-node'],
      }
      const approvalConfig = await this._getApprovalConfig(change.domainCode)
      const ret = await reportEvent(approvalConfig, cp.publisher, params)
      logger.info('[Publish Service] reportEventAfterPublish ret', ret)
    } catch (err) {
      logger.error(
        '[Publish Service] reportEventAfterPublish err',
        err as unknown as Error,
      )
    }
  }

  async _getApprovalConfig(domainCode: string) {
    const approvalConfig = this.configService.get('approval')
    /**
     * 获取业务域配置的服务节点id，如果有的话就覆盖默认kconf内的
     */
    const domainInfo =
      await this.businessDomainService.getSimpleDetail(domainCode)
    if (
      domainInfo &&
      domainInfo.serviceNodeId &&
      approvalConfig &&
      approvalConfig.params?.tasks?.[0]?.node_id
    ) {
      approvalConfig.params.tasks[0].node_id = Number(domainInfo.serviceNodeId)
    }
    return approvalConfig
  }

  async upStatusByLingzhu(id: number, userCode: string | undefined) {
    if (!userCode) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    const _publish = await this.getOne(id)
    if (!_publish) {
      throw new CommonException(ExceptionCodes.Change_Publish_Not_Found)
    }
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      _publish.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }

    if (Number(_publish.status) === PUBLISH_STATUS.INIT) {
      try {
        const updateResult = await this.changePublishRepo.update(
          {
            id,
          },
          {
            status: PUBLISH_STATUS.FLOW_END,
          },
        )
        // 检查更新结果
        if (updateResult.affected === 0) {
          throw new CommonException(ExceptionCodes.LINGZHU_PUSH_STATUS_ERROR)
        }
      } catch (e) {
        throw new CommonException(ExceptionCodes.LINGZHU_PUSH_STATUS_ERROR)
      }
    } else if (Number(_publish.status) !== PUBLISH_STATUS.FLOW_END) {
      throw new CommonException(ExceptionCodes.LINGZHU_PUSH_STATUS_ERROR)
    }
  }

  /**
   * 用changeId查询是否有指定状态的发布单
   * @param changeId
   * @param status
   */
  async getPublishByChangeIdAndPublishStatus(
    changeId: string,
    statuses: number[],
  ) {
    const queryBuilder = this.changePublishRepo
      .createQueryBuilder('changePublish')
      .where('changePublish.changeId = :changeId', {
        changeId,
      })
    if (typeof statuses !== 'undefined') {
      queryBuilder.andWhere('changePublish.status IN (:...statuses)', {
        statuses,
      })
    }
    const list = await queryBuilder
      .orderBy('changePublish.createTime', 'DESC')
      .getMany()
    return list
  }

  /**
   * 这个函数存在的意义是lingzhu流程不完全能match大运河的变更状态，lingzhu测试准出就立刻进入发布态，但是此时并没有创建发布单，大运河并没有进入原有的发布态，虽然很无奈，但是只能给他们开口子
   * @param changeId
   * @param direction
   * @param userCode
   * @returns
   */
  async drivingChangeStatusByLingzhu(
    changeId: string,
    direction: number,
    userCode: string,
  ) {
    const changeInstance = await this.changeService.getOne(changeId)
    if (!changeInstance) {
      throw new CommonException(ExceptionCodes.LINGZHU_CHANGE_STATUS_FAIL)
    }
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      changeInstance.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    const changeStatus = Number(changeInstance.status)
    let fromStatus, toStatus
    if (Number(direction) === DrivingStatusDirect.DevelopingToPublishing) {
      fromStatus = ChangeStatus.DEVELOPING
      toStatus = ChangeStatus.PUBLISHING
    } else if (
      Number(direction) === DrivingStatusDirect.PublishingToDeveloping
    ) {
      fromStatus = ChangeStatus.PUBLISHING
      toStatus = ChangeStatus.DEVELOPING
    } else {
      throw new CommonException(ExceptionCodes.LINGZHU_CHANGE_STATUS_FAIL)
    }
    if (changeStatus === toStatus) {
      return {
        changeId,
        status: toStatus,
      }
    } else if (changeStatus === fromStatus) {
      if (
        toStatus === ChangeStatus.DEVELOPING &&
        fromStatus === ChangeStatus.PUBLISHING
      ) {
        // 检查一下有没有存在发布单, 如果有的话不允许推，按理来说lignzhu方应当先关闭上线单才能操作
        const list = await this.getPublishByChangeIdAndPublishStatus(changeId, [
          PUBLISH_STATUS.FLOWING,
          PUBLISH_STATUS.FLOW_END,
          PUBLISH_STATUS.FLOW_FAIL,
          PUBLISH_STATUS.ING,
          PUBLISH_STATUS.INIT,
          PUBLISH_STATUS.NO_NEED_FLOW,
          PUBLISH_STATUS.FAIL,
        ])
        if (list.length) {
          throw new CommonException(ExceptionCodes.LINGZHU_CHANGE_STATUS_FAIL)
        }
      }
      const result = await this.changeService.updateStatus(
        changeId,
        toStatus,
        userCode,
      )
      if (result.affected && result.affected > 0) {
        return {
          changeId,
          status: toStatus,
        }
      } else {
        throw new CommonException(ExceptionCodes.LINGZHU_CHANGE_STATUS_FAIL)
      }
    } else {
      throw new CommonException(ExceptionCodes.LINGZHU_CHANGE_STATUS_FAIL)
    }
  }
}
