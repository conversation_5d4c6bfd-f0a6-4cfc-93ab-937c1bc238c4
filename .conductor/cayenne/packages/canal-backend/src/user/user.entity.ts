import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Column, Entity, PrimaryColumn } from 'typeorm'

@Entity({ name: 'user' })
export class User {
  @ApiProperty({
    description: 'userCode',
  })
  @PrimaryColumn({ type: 'varchar', length: 100, comment: 'userCode' })
  userCode: string

  @ApiPropertyOptional({
    description: '用户名',
  })
  @Column({ type: 'varchar', length: 100, comment: 'username', nullable: true })
  userName: string

  @ApiPropertyOptional({
    description: '头像',
  })
  @Column({ type: 'varchar', length: 256, comment: '用户头像', default: '' })
  avatar: string

  // @ApiPropertyOptional({
  //   description: 'bu',
  // })
  // @Column({ type: 'varchar', length: 256, comment: '用户头像', default: '' })
  // buId: string
}
