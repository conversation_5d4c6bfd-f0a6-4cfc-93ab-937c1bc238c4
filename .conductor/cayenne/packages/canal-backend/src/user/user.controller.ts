import {
  Controller,
  Get,
  Query,
  Req,
  Res,
  Session,
  UseGuards,
} from '@nestjs/common'
import { Request, Response } from 'express'
import { AuthGuard } from '../auth/auth.guard'
import { UserService } from './user.service'

import {
  ApiExcludeEndpoint,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger'
import axios from 'axios'
import { HostGuard } from 'src/auth/host.guard'
import { isLocal } from 'src/common/env'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { OpenApiService } from 'src/openapi/openapi.service'
import { ISession, ResponseType } from 'src/types'
import { User } from './user.entity'

function getDomain(host: string): string {
  const PROTOCOL = isLocal ? 'http:' : 'https:'
  return `${PROTOCOL}//${host}/rest/user/casback`
}

@Controller('/rest/user')
@ApiTags('user')
export class UserController {
  public constructor(
    protected readonly userService: UserService,
    protected readonly openApiService: OpenApiService,
  ) {}

  @ApiOperation({ description: '登录', operationId: 'loginUsingGet' })
  @Get('/login')
  @ApiQuery({
    name: 'redirectUrl',
    description: '登录后回调地址',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'ok',
    type: ResponseType,
  })
  @UseGuards(HostGuard)
  public login(
    @Query('redirectUrl') redirectUrl: string,
    @Res() rsp: Response,
    @Req() res: Request,
    @Session() session: ISession,
  ): void {
    const host = res.header('host')
    const casLoginUrl = `https://sso.corp.kuaishou.com/cas/login?service=${encodeURIComponent(
      getDomain(host as string),
    )}`

    session.redirectUrl = redirectUrl
    rsp.redirect(casLoginUrl)
  }

  @ApiOperation({ description: '鉴权', operationId: 'casBackUsingGet' })
  @Get('/casback')
  @ApiExcludeEndpoint()
  @UseGuards(HostGuard)
  public async casBack(
    @Query('ticket') ticket: string,
    @Session() session: ISession,
    @Res() rsp: Response,
    @Req() res: Request,
  ): Promise<void> {
    const host = res.header('host')
    if (ticket) {
      const casAuthUrl = `https://sso.corp.kuaishou.com/cas/p3/serviceValidate?service=${encodeURIComponent(
        getDomain(host as string),
      )}&ticket=${ticket}`
      const ret = await axios.get(casAuthUrl)
      console.log(ret.data)
      if (ret && ret.status === 200) {
        const matchName = ret.data.match(
          /<cas:displayName>(.*)<\/cas:displayName>/,
        )
        const matchUnique = ret.data.match(/<cas:user>(.*)<\/cas:user>/)
        const matchAvatar = ret.data.match(/<cas:avatar>(.*)<\/cas:avatar>/)
        const matchDepartment = ret.data.match(
          /<cas:department>(.*)<\/cas:department>/,
        )
        if (matchName && matchUnique) {
          const userName = matchName[1]
          const userCode = matchUnique[1]
          // 头像地址
          let avatar = matchAvatar?.[1] || ''
          // 部门
          const department = matchDepartment?.[1]
          try {
            const uinfo = await this.openApiService.getUserInfoByKim(userCode)
            if (uinfo) {
              // 这个更准确
              avatar = uinfo.avatarUrl
            }
          } catch (error) {
            console.error('casBack getUserInfoByKim error', error)
          }
          session.userInfo = {
            userName,
            userCode,
            avatar,
            department,
          }
          if (session.redirectUrl) {
            rsp.redirect(session.redirectUrl)
          } else {
            rsp.redirect(`/`)
          }
        } else {
          session.userInfo = undefined
        }
      } else {
        session.userInfo = undefined
      }
    }
  }

  @ApiOperation({
    description: '获取用户信息',
    operationId: 'userInfoUsingGet',
  })
  @Get('info')
  @CommonApiResponse(User)
  @UseGuards(AuthGuard)
  public async info(@Session() session: ISession): Promise<User | undefined> {
    if (session && session.userInfo) {
      const { userCode, userName, avatar } = session.userInfo
      const user = await this.userService.getDetail(userCode)
      if (!user || !user.userName) {
        await this.userService.create(userCode, userName, avatar)
      }
      return { userCode, userName, avatar: user?.avatar || avatar } as User
    }
  }
}
