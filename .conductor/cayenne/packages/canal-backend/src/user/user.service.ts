import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { In, Repository } from 'typeorm'
import { User } from './user.entity'

@Injectable()
export class UserService {
  public constructor(
    @InjectRepository(User) public readonly UserEntityRepo: Repository<User>,
  ) {}

  public async create(
    userId: string,
    userName: string,
    avatar: string,
    buId?: string,
  ): Promise<User | undefined> {
    try {
      return await this.UserEntityRepo.save({
        userCode: userId,
        userName: userName,
        avatar,
        buId,
      })
    } catch (e) {
      console.log(e)
    }
  }

  public async getDetail(userCode: string): Promise<User | null> {
    return this.UserEntityRepo.findOneBy({ userCode })
  }

  public async getManyList(userCodes: string[]): Promise<User[]> {
    return this.UserEntityRepo.find({ where: { userCode: In(userCodes) } })
  }
}
