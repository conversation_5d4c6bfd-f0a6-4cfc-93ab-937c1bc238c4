import Raven, { CaptureOptions } from 'raven'
import { isProd, isStaging } from 'src/common/env'
import { getTreacId } from './logger'

Raven.config(
  'https://<EMAIL>/3126',
).install()

export const captureException = (e: Error, options?: CaptureOptions) => {
  let extra = options?.extra || {}
  const traceId = getTreacId()

  extra = {
    ...extra,
    traceId,
  }

  const normaizedOptions = {
    ...(options || {}),
    extra,
  }

  if (isProd || isStaging) {
    Raven.captureException.bind(Raven)(e, normaizedOptions)
  } else {
    console.error(e, normaizedOptions)
  }
}
