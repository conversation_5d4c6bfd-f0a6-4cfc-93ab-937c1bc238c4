import { Logger, TagsInterface } from '@infra-node/logger'
import { AsyncLocalStorage } from 'async_hooks'
import { NextFunction, Request, Response } from 'express'
import { v4 as uuidv4 } from 'uuid'

const context = new AsyncLocalStorage()

const baseLogger = new Logger({
  appName: `ad-canal-server-${process.env.NODE_ENV}`, //项目名，根据它区分不同logger
  isFile: true,
  fileOptions: {
    dirname: './logs',
  },
})

let wrapLogger = {
  info(message: string, tags?: TagsInterface) {
    baseLogger.info({
      message,
      tags,
    })
  },
  warn(message: string, tags?: TagsInterface) {
    baseLogger.warn({
      message,
      tags,
    })
  },
  error(message: string, err: Error, tags?: TagsInterface) {
    baseLogger.error({
      message,
      tags,
      err,
    })
  },
}

wrapLogger = new Proxy(wrapLogger, {
  get(target, property) {
    const traceId = getTreacId()
    switch (property) {
      case 'info':
      case 'warn':
        return new Proxy(target[property], {
          apply(tar, thisArg, args: [string] | [string, TagsInterface]) {
            args[1] = {
              ...args[1],
              traceId,
            }
            tar.apply(thisArg, args)
          },
        })
      case 'error':
        return new Proxy(target[property], {
          apply(
            tar,
            thisArg,
            args: [string, Error] | [string, Error, TagsInterface],
          ) {
            args[2] = {
              ...args[2],
              traceId,
            }
            tar.apply(thisArg, args)
          },
        })
    }
  },
})

export const logger = wrapLogger

export function getTreacId() {
  return (context.getStore() as Map<string, string>)?.get('traceId') || ''
}

export function loggerMiddleware(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  const store = new Map()
  store.set('traceId', uuidv4())
  context.run(store, () => {
    const path = req.baseUrl
    const filterPaths = ['', '/', '/health']
    if (!filterPaths.includes(path)) {
      logger.info(`[Incoming Request] ${path}`, {
        path,
        method: req.method,
        query: JSON.stringify(req.query),
        body: JSON.stringify(req.body),
      })
    }
    next()
  })
}
