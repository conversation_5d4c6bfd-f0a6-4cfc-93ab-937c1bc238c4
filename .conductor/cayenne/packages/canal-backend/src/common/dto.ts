import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
/**
 * 数据库表分页信息
 */
export class DBTablePageInfo {
  @ApiProperty({ description: '总数' })
  readonly total: number

  @ApiProperty({ description: '页码' })
  readonly pageNum: number

  @ApiProperty({ description: '分页大小' })
  readonly pageSize: number
}

export class LingZhuAdditionalParameters {
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  lingzhuUserName?: string
}
