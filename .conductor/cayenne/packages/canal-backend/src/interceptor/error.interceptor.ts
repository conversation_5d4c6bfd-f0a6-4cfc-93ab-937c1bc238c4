import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  HttpException,
  Injectable,
  NestInterceptor,
} from '@nestjs/common'
import { Request } from 'express'
import { catchError } from 'rxjs/operators'
import { logger } from 'src/common/logger'
import { captureException } from 'src/common/sentry'
import { ISession } from 'src/types'

/**
 * @getClientIP
 * @desc 获取用户 ip 地址
 * @param {Object} req - 请求
 */
function getClientIP(req: Request) {
  return (
    req.headers['x-forwarded-for'] || // 判断是否有反向代理 IP
    req.connection.remoteAddress || // 判断 connection 的远程 IP
    req.socket.remoteAddress // 判断后端的 socket 的 IP
  )
}

function getRefer(req: Request) {
  return req.headers['referer'] || req.headers['referrer'] || ''
}

@Injectable()
export class ErrorsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler) {
    const ctx = context.switchToHttp()
    const request = ctx.getRequest<Request>() as Request & { session: ISession }

    const extra = {
      requestBody: request.body,
      ip: getClientIP(request),
      user: request?.session?.userInfo,
      url: request.url,
      referrer: getRefer(request),
    }

    // console.log('extra', JSON.stringify(extra));

    return next.handle().pipe(
      catchError(async (error) => {
        if (error instanceof HttpException) {
          const errorResponse = error.getResponse() as { msg: string }

          const newError = new Error(errorResponse?.msg || error.message)
          newError.stack = error.stack // 捕获堆栈
          newError.name = '[接口逻辑报错]'

          captureException(newError, {
            extra: {
              ...extra,
              errorResponse,
            },
          })
          logger.error('[ErrorsInterceptor error]', error)
          return Promise.reject(error)
        } else if (error.code && error.details) {
          // captureException(error)
          return Promise.resolve({
            code: error.code,
            message: error.details,
          })
        } else {
          logger.error('[ErrorsInterceptor error]', error)
          return Promise.reject(error)
        }
      }),
    )
  }
}
