import { ApiErrorCode } from '@ad/canal-shared'
import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common'
import { map } from 'rxjs/operators'
import { getTreacId } from 'src/common/logger'

@Injectable()
export class TransformInterceptor implements NestInterceptor {
  public intercept(context: ExecutionContext, next: CallHandler) {
    return next.handle().pipe(
      map((data) => {
        const traceId = getTreacId()
        const ret = data
        if (data && data.result) {
          return ret
        }
        return {
          result: ApiErrorCode.OK,
          msg: '请求成功',
          data: ret,
          traceId,
        }
      }),
    )
  }
}
