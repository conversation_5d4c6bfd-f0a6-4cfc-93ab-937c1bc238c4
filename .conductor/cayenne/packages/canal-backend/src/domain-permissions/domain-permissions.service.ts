import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { Repository } from 'typeorm'
import { CreateDomainPermissionDto } from './domain-permissions.dto'
import { DomainPermission } from './domain-permissions.entity'

@Injectable()
export class DomainPermissionService {
  constructor(
    @InjectRepository(DomainPermission)
    private readonly domainPermissionRepository: Repository<DomainPermission>,
  ) {}

  async create(
    createDomainPermissionDto: CreateDomainPermissionDto,
    userCode: string,
  ): Promise<DomainPermission | undefined> {
    const granted = await this.findOne(
      userCode,
      createDomainPermissionDto.domainCode,
    )
    if (granted) {
      // admin 权限的可以加人
      if (granted.permissionLevel === 'admin') {
        const existOne = await this.findOne(
          createDomainPermissionDto.userId,
          createDomainPermissionDto.domainCode,
        )
        // 如果已经存在 仅更新信息
        if (existOne) {
          const updatedDomainPermission = {
            ...existOne,
            ...createDomainPermissionDto,
            grantedBy: userCode,
            grantedTime: new Date().getTime(),
          }
          return await this.domainPermissionRepository.save(
            updatedDomainPermission,
          )
        }
        //  如果不存在，新加
        const domainPermission = this.domainPermissionRepository.create({
          ...createDomainPermissionDto,
          grantedBy: userCode,
          grantedTime: new Date().getTime(),
        })
        return await this.domainPermissionRepository.save(domainPermission)
      } else {
        throw new CommonException(ExceptionCodes.DOMAIN_PERMISSION_ADD_AUTHOR)
      }
    } else {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
  }

  async findAll(domainCode: string): Promise<DomainPermission[]> {
    return this.domainPermissionRepository.findBy({ domainCode })
  }

  async findOne(
    userId: string,
    domainCode: string,
  ): Promise<DomainPermission | null> {
    return this.domainPermissionRepository.findOneBy({ userId, domainCode })
  }
}
