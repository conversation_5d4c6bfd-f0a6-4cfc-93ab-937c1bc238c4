import { ApiProperty } from '@nestjs/swagger'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity('domain_permissions')
export class DomainPermission {
  @PrimaryGeneratedColumn('increment', { type: 'bigint', unsigned: true })
  id: number
  @ApiProperty()
  @Column({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    comment: '业务域code，关联business_domain',
  })
  domainCode: string

  @ApiProperty()
  @Column({ type: 'varchar', length: 100, comment: '用户ID', name: 'user_id' })
  userId: string

  @ApiProperty()
  @Column({
    type: 'varchar',
    length: 50,
    default: 'maintainer',
    comment: '权限级别，例如：admin/maintainer',
    name: 'permission_level',
  })
  permissionLevel: string

  @ApiProperty()
  @Column({
    type: 'varchar',
    length: 100,
    comment: '权限授予者',
    name: 'granted_by',
  })
  grantedBy: string

  @ApiProperty()
  @Column({
    type: 'bigint',
    default: 0,
    comment: '权限授予时间',
    name: 'granted_time',
  })
  grantedTime: number
}
