import { Controller, Get, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { OpenApiService } from 'src/openapi/openapi.service'
import {
  GetMaterialPlatformComponentDetailQuery,
  MaterialPlatformComponentDetailDto,
  SearchMaterialPlatformComponentQuery,
  SearchMaterialPlatformComponentResponse,
} from './material-platform.dto'

@ApiTags('material-platform')
@Controller('/rest/canal/material-platform')
export class MaterialPlatformController {
  public constructor(protected readonly openApiService: OpenApiService) {}

  @Get('search')
  @ApiOperation({
    summary: '搜索物料平台组件',
    operationId: 'searchMaterialPlatformComponent',
  })
  @CommonApiResponse(SearchMaterialPlatformComponentResponse)
  public async search(@Query() query: SearchMaterialPlatformComponentQuery) {
    return this.openApiService.searchMaterialPlatformComponent(query)
  }

  @Get('get')
  @ApiOperation({
    summary: '获取物料平台组件详情',
    operationId: 'getMaterialPlatformComponentDetail',
  })
  @CommonApiResponse(MaterialPlatformComponentDetailDto)
  public async get(@Query() query: GetMaterialPlatformComponentDetailQuery) {
    return this.openApiService.getMaterialPlatformComponentDetail(query)
  }
}
