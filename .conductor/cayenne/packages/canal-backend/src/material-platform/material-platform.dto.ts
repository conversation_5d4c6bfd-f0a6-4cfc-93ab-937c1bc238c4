import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNumber, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'

/**
 * 搜索物料平台组件 Query
 */
export class SearchMaterialPlatformComponentQuery {
  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '代码类型' })
  readonly codeType: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '搜索字符串' })
  readonly search: string

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @ApiPropertyOptional({ description: '分页大小，不传不分页' })
  readonly pageSize?: number

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @ApiPropertyOptional({ description: '当前页码' })
  readonly currentPage?: number
}

/**
 * 搜索物料平台组件响应
 */
export class SearchMaterialPlatformComponentResponse {
  @ApiProperty({ description: '列表' })
  list: MaterialPlatformComponentSummaryDto[]

  @ApiProperty({ description: '总数' })
  total: number
}

/**
 * 物料平台组件简介 DTO
 */
export class MaterialPlatformComponentSummaryDto {
  @ApiProperty()
  id: number

  @ApiProperty({ description: '名称' })
  name: string

  @ApiProperty({ description: '展示名' })
  displayName: string

  @ApiProperty({
    description: '预览图',
    oneOf: [
      { type: 'string' },
      {
        type: 'array',
        items: {
          type: 'string',
        },
      },
    ],
  })
  imgs: string | string[]

  @ApiProperty({ description: '版本' })
  version: string

  @ApiProperty({ description: '描述' })
  description: string

  @ApiProperty({ description: '创建于' })
  createAt: number

  @ApiProperty({ description: '更新于' })
  updateAt: number

  @ApiProperty({ description: '物料 ID' })
  materialId: number

  @ApiProperty({ description: '包名' })
  packageName: string

  @ApiProperty({ description: '包类型' })
  packageType: 'single' | 'multiple'
}

/**
 * 获取物料平台组件详情 Query
 */
export class GetMaterialPlatformComponentDetailQuery {
  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @ApiProperty({ description: '物料 ID' })
  readonly materialId: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '版本' })
  readonly version: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @ApiProperty({ description: '组件名' })
  readonly componentName: string
}

/**
 * 物料平台组件详情
 */
export class MaterialPlatformComponentDetailDto {
  @ApiProperty()
  id: number

  @ApiProperty({ description: '物料 ID' })
  materialId: number

  @ApiProperty({ description: '创建于' })
  createAt: number

  @ApiProperty({ description: '更新于' })
  updateAt: number

  @ApiProperty({ description: '描述' })
  version: string

  @ApiProperty({ description: '名称' })
  name: string

  @ApiProperty({ description: '类型' })
  type: 'component' | 'block' | 'template'

  @ApiProperty({ description: '包描述' })
  packageDescription: string

  @ApiProperty({ description: '展示名' })
  displayName: string

  @ApiProperty({ description: '描述' })
  description: string

  @ApiProperty({
    description: '预览图',
    oneOf: [
      { type: 'string' },
      {
        type: 'array',
        items: {
          type: 'string',
        },
      },
    ],
  })
  imgs: string | string[]

  @ApiProperty({ description: 'UMD 链接' })
  umdUrls: string[]

  @ApiProperty({ description: '依赖' })
  dependencies: Record<string, string>

  @ApiProperty({ description: '开发依赖' })
  devDependencies: Record<string, string>

  @ApiProperty({ description: '同伴依赖' })
  peerDependencies: Record<string, string>

  @ApiProperty({ description: '属性', type: [Object] })
  props: Record<string, unknown>[]

  @ApiProperty({ description: '版本列表' })
  versionList: string[]

  @ApiProperty({ description: '低代码物料 Schema' })
  schema: Record<string, unknown>
}
