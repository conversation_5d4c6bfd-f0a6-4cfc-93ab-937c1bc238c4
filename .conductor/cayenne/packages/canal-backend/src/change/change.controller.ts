import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Session,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { UserSession } from 'src/types'
import {
  ChangeDto,
  ChangeItemDto,
  ChangeListDto,
  ChangeListResponseDto,
  CloseChangeDto,
  CreateOrUpdateChangeResponse,
  ProcessingChangeListDto,
  UpdateChangePartInfoDto,
} from './change.dto'
import { ChangeService } from './change.service'
import { CreateChangeDto } from './create-change.dto'

@Controller('/rest/canal/change')
@ApiTags('change')
@Controller()
export class ChangeController {
  constructor(private readonly changeService: ChangeService) {}

  @Post('list')
  @ApiOperation({
    summary: '获取变更列表',
    operationId: 'getAllChangeList',
  })
  @ApiOperation({
    description: '获取变更列表，支持按字段排序（sortField和sortOrder）',
    operationId: 'getCanalChangeList',
  })
  @ApiBody({
    type: ChangeListDto,
  })
  @CommonApiResponse(ChangeListResponseDto)
  public async getAllList(@Body() changeListDto: ChangeListDto) {
    const { list, total } =
      await this.changeService.getListByPage(changeListDto)
    return {
      list,
      pageInfo: {
        total,
        pageSize: changeListDto.pageSize,
        pageNum: changeListDto.pageNum,
      },
    }
  }

  @Post('create')
  @ApiOperation({
    summary: '创建变更',
    operationId: 'createChangeUsingPost',
  })
  @ApiBody({
    type: CreateChangeDto,
  })
  @CommonApiResponse(CreateOrUpdateChangeResponse)
  public async create(
    @Body() createChangeDto: CreateChangeDto,
    @Session() userSession: UserSession,
  ) {
    // Logger.log(`增加用户接收参数：${JSON.stringify(createChangeDto)}`);
    const userCode =
      createChangeDto.lingzhuUserName || userSession?.userInfo?.userCode
    createChangeDto.createUser = userCode
    createChangeDto.updateUser = userCode
    const changeInstance = await this.changeService.save(
      createChangeDto,
      userCode,
    )
    return {
      changeId: changeInstance.changeId,
    }
  }

  // @Post('update/status')
  // public updateStatus(
  //   @Body() updateChangeDto: UpdateStatusChangeDto,
  //   @Session() userSession: UserSession,
  // ) {
  //   // Logger.log(`增加用户接收参数：${JSON.stringify(createChangeDto)}`)
  //   updateChangeDto.updateUser = userSession.userInfo.userCode
  //   return this.changeService.save(
  //     updateChangeDto,
  //     userSession.userInfo.userCode,
  //   )
  // }

  @Get('detail')
  @ApiOperation({
    summary: '获取变更详情',
    operationId: 'getChangeDetail',
  })
  @ApiQuery({
    name: 'changeId',
    required: true,
    description: '变更ID',
    type: String,
  })
  @CommonApiResponse(ChangeDto)
  public getDetail(
    @Query('changeId') changeId: string,
    @Session() userSession: UserSession,
  ) {
    return this.changeService.get(changeId, userSession?.userInfo?.userCode)
  }

  @Get('processing/change/list')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: '获取一个业务域下正在进行的变更列表',
    operationId: 'getCanalProcessingChangeList',
  })
  @ApiQuery({
    name: 'domainCode',
    required: true,
    description: '业务域code',
    type: String,
  })
  @CommonApiResponse(ProcessingChangeListDto)
  public async getCanalProcessingChangeList(
    @Query('domainCode') domainCode: string,
  ) {
    const list = await this.changeService.getAllProcessingList(domainCode)
    return {
      list: list || [],
    }
  }

  @Post('close')
  @ApiOperation({
    summary: '关闭变更',
    operationId: 'change',
  })
  @ApiBody({
    type: CloseChangeDto,
  })
  @CommonApiResponse(ChangeItemDto)
  public async close(
    @Body() closeDto: CloseChangeDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changeService.close(
      closeDto?.changeId,
      closeDto.lingzhuUserName || userSession?.userInfo?.userCode,
    )
  }
  @Post('update/info')
  @ApiOperation({
    summary: '变更部分信息',
    operationId: 'update',
  })
  @ApiBody({
    type: UpdateChangePartInfoDto,
  })
  public async updateInfo(
    @Body()
    updateDto: UpdateChangePartInfoDto,
    @Session() userSession: UserSession,
  ) {
    return await this.changeService.updateInfo(
      updateDto?.changeId,
      updateDto.lingzhuUserName || userSession?.userInfo?.userCode,
      updateDto.updateValue,
      updateDto.updateKey,
    )
  }
}
