import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import dayjs from 'dayjs'
import { BusinessDomainService } from 'src/business-domain/business-domain.service'
import { logger } from 'src/common/logger'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { OpenApiService } from 'src/openapi/openapi.service'
import { In, Not, Repository } from 'typeorm'
import { ChangeListDto } from './change.dto'
import { Change, ChangeStatus, ChangeType } from './change.entity'
import { CreateChangeDto } from './create-change.dto'

@Injectable()
export class ChangeService {
  public constructor(
    @InjectRepository(Change)
    public readonly changeRepo: Repository<Change>,
    protected readonly openApiService: OpenApiService,
    private readonly businessDomainService: BusinessDomainService,
  ) {}

  /**
   * 新建/更新状态
   * @param changeInfo
   */
  async save(changeInfo: CreateChangeDto, userCode: string) {
    if (!changeInfo?.domainCode || !changeInfo?.teamId) {
      throw new CommonException(ExceptionCodes.Change_Publish_Params_Miss)
    }
    /**
     * 检查是否有权限
     */
    const hasPermission = await this.businessDomainService.hasPermission(
      changeInfo.domainCode,
      userCode,
    )
    if (!hasPermission) {
      throw new CommonException(ExceptionCodes.User_Permission_Deined)
    }
    // 操作戳
    const createTime = dayjs().valueOf()

    if (!changeInfo?.changeId) {
      let teamInfo
      /**
       * 从外部传入的暂时不校验team，因为lingzhu和大运河使用team的环境不一致
       */
      if (!changeInfo.outChangeId) {
        teamInfo = await this.openApiService.getTeamSimpleDetail(
          changeInfo.teamId,
          changeInfo.createUser,
        )
        if (!teamInfo?.result) {
          throw new CommonException(ExceptionCodes.Change_Create_fail_No_Team)
        }
      }

      changeInfo.changeName =
        teamInfo?.result?.title || changeInfo.outChangeName || ''
      changeInfo.changeId =
        changeInfo.outChangeId ||
        changeInfo.domainCode + '_' + dayjs().valueOf() + '_' + randomInt(6)
      changeInfo.status = ChangeStatus.DEVELOPING
      changeInfo.type = ChangeType.DEVELOP
      changeInfo.createTime = createTime
      changeInfo.updateTime = createTime
    }
    try {
      return await this.changeRepo.save(changeInfo)
    } catch (e) {
      throw new CommonException(ExceptionCodes.Change_Create_fail)
    }
  }
  /**
   * 查询正在进行中的变更列表
   */
  async getAllProcessingList(domainCode: string) {
    try {
      return await this.changeRepo.find({
        where: [
          {
            status: ChangeStatus.DEVELOPING,
            domainCode: domainCode,
            type: Not(ChangeType.ROLLBACK),
          },
          {
            status: ChangeStatus.TESTING,
            domainCode: domainCode,
            type: Not(ChangeType.ROLLBACK),
          },
          {
            status: ChangeStatus.PUBLISHING,
            domainCode: domainCode,
            type: Not(ChangeType.ROLLBACK),
          },
        ],
        order: {
          createTime: 'DESC',
        },
      })
    } catch (e) {
      throw new CommonException(ExceptionCodes.Domain_ALL_CHANGE_GET)
    }
  }

  /**
   * 获取所有已发布
   */
  async getAllPublished(domainCode?: string) {
    try {
      return await this.changeRepo.find({
        where: [
          {
            status: ChangeStatus.PUBLISHED,
            domainCode: domainCode,
          },
        ],
        order: {
          createTime: 'DESC',
        },
      })
    } catch (e) {
      throw new CommonException(ExceptionCodes.Domain_ALL_CHANGE_GET)
    }
  }

  /**
   * 分页查询某一业务域下的变更列表
   */
  async getListByPage({
    domainCode,
    pageNum,
    pageSize,
    changeName,
    createUser,
    sortField,
    sortOrder,
  }: ChangeListDto) {
    try {
      const queryBuilder = this.changeRepo.createQueryBuilder('change')
      queryBuilder.where('change.domainCode = :domainCode', {
        domainCode,
      })
      if (changeName) {
        queryBuilder.andWhere('change.changeName LIKE :changeName', {
          changeName: `%${changeName}%`,
        })
      }
      if (createUser) {
        queryBuilder.andWhere('change.createUser LIKE :createUser', {
          createUser,
        })
      }
      if (sortField && sortOrder) {
        const fieldMapping: Record<string, string> = {
          changeName: 'change.changeName',
          createTime: 'change.createTime',
          updateTime: 'change.updateTime',
          createUser: 'change.createUser',
          status: 'change.status',
          type: 'change.type',
        }

        const dbField = fieldMapping[sortField] || `change.${sortField}`
        const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC'

        queryBuilder.orderBy(dbField, order)

        if (sortField !== 'createTime') {
          queryBuilder.addOrderBy('change.createTime', 'DESC')
        }
      } else {
        queryBuilder.orderBy('change.createTime', 'DESC')
      }

      const [list, total] = await queryBuilder
        .skip((pageNum - 1) * (pageSize || 10))
        .take(pageSize)
        .getManyAndCount()

      return {
        list,
        total,
      }
    } catch (e) {
      throw new CommonException(ExceptionCodes.CHANGE_LIST_BY_PAGE)
    }
  }

  /**
   * 关闭
   * @param changeId
   */
  async close(changeId: string, userCode: string) {
    try {
      const targetChange = await this.changeRepo.findOneBy({ changeId })
      if (targetChange) {
        /**
         * 检查是否有权限
         */
        const hasPermission = await this.businessDomainService.hasPermission(
          targetChange.domainCode,
          userCode,
        )
        if (!hasPermission) {
          throw new CommonException(ExceptionCodes.User_Permission_Deined)
        }
      }
      if (!!targetChange && targetChange.status < ChangeStatus.TESTING) {
        targetChange.status = ChangeStatus.CLOSED
        targetChange.updateTime = dayjs().valueOf()
        targetChange.updateUser = userCode
        return await this.changeRepo.save(targetChange)
      } else {
        throw new CommonException(ExceptionCodes.CHANGE_CLOSE_FAIL)
      }
    } catch (e) {
      throw new CommonException(ExceptionCodes.CHANGE_CLOSE_FAIL)
    }
  }
  /**
   *
   * @param n
   * @returns
   */
  async getOne(changeId: string) {
    return await this.changeRepo.findOneBy({ changeId })
  }
  /**
   * 查询一组
   * @param changeIds
   * @returns
   */
  async getMany(changeIds: string[]) {
    return await this.changeRepo.findBy({ changeId: In(changeIds) })
  }
  /**
   * 查询具体的变更信息
   */
  async get(changeId: string, userCode?: string) {
    const changeInstance = await this.changeRepo.findOneBy({ changeId })
    const developer: string[] = []
    const tester: string[] = []
    if (changeInstance && userCode) {
      try {
        const teamInfo = await this.openApiService.getTeamDetail(
          changeInstance.teamId,
          userCode,
        )
        if (teamInfo.code === 0 && teamInfo.result?.length) {
          const targetTeam = teamInfo.result[0]
          const taskFieldValues = targetTeam.taskFieldValues
          if (Array.isArray(taskFieldValues)) {
            taskFieldValues.forEach((taskField) => {
              if (taskField?.fieldKey === 'rd') {
                taskField.fieldValue?.forEach?.(
                  (field: { taskValueName: string; taskValueId: string }) => {
                    developer.push(
                      field.taskValueName + '-' + field.taskValueId,
                    )
                  },
                )
              }
              if (taskField?.fieldKey === 'qa') {
                taskField.fieldValue?.forEach?.(
                  (field: { taskValueName: string; taskValueId: string }) => {
                    tester.push(field.taskValueName + '-' + field.taskValueId)
                  },
                )
              }
            })
          }
        }
      } catch (e) {
        logger.info('get team info error', {
          e,
        })
      }
    } else {
      return changeInstance
    }
    return {
      ...changeInstance,
      developer,
      tester,
    }
  }

  async updateInfo(
    changeId: string,
    userCode: string,
    updateValue: string,
    updateKey: string,
  ) {
    const targetChange = await this.changeRepo.findOneBy({ changeId })
    if (targetChange) {
      /**
       * 检查是否有权限
       */
      const hasPermission = await this.businessDomainService.hasPermission(
        targetChange.domainCode,
        userCode,
      )
      if (!hasPermission) {
        throw new CommonException(ExceptionCodes.User_Permission_Deined)
      }
    }
    /**
     * 仅允许修改这些
     */
    if (updateKey && updateValue && ['changeName'].includes(updateKey)) {
      return await this.changeRepo.update(
        {
          changeId,
        },
        {
          [updateKey]: updateValue,
        },
      )
    } else {
      throw new CommonException(ExceptionCodes.Change_Info_Change_Error)
    }
  }

  /**
   * 仅供服务内部调用
   * @param changeId
   * @param status
   */
  async updateStatus(changeId: string, status: number, userCode: string) {
    return await this.changeRepo.update(
      {
        changeId,
      },
      {
        status,
        updateTime: dayjs().valueOf(),
        updateUser: userCode,
      },
    )
  }
}

function randomInt(n: number) {
  if (n <= 0) return -1
  const limit = Math.pow(10, n)
  const value = Math.floor(Math.random() * limit)
  if (value < limit / 10 && value !== 0) {
    return randomInt(n)
  }
  return value
}
