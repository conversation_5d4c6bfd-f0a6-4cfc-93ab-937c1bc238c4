import { ApiProperty } from '@nestjs/swagger'
import { IsInt, IsNotEmpty, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
import { LingZhuAdditionalParameters } from 'src/common/dto'

export class CreateChangeDto extends LingZhuAdditionalParameters {
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  changeName: string

  @ApiProperty()
  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  type: number

  @ApiProperty()
  status: number

  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  teamId: string

  @ApiProperty()
  createUser: string
  @ApiProperty()
  updateUser?: string
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateTime: number
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: '缺失对应的业务域code' })
  domainCode: string

  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  changeId?: string

  // 外部变更id, 灵筑会传入一个生成的changeId,如有这个字段,则直接使用这个变更id
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  outChangeId?: string

  @ApiProperty()
  @IsString()
  outChangeName?: string
}
