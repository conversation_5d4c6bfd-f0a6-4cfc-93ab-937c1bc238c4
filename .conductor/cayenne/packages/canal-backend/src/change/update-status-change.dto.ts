import { PickType } from '@nestjs/mapped-types'
import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
import { CreateChangeDto } from './create-change.dto'
export class UpdateStatusChangeDto extends PickType(CreateChangeDto, [
  'status',
  'updateUser',
]) {
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  changeId: string
}
