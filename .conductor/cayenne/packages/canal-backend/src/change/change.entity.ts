import { ApiProperty } from '@nestjs/swagger'
import { Column, Entity, PrimaryColumn } from 'typeorm'

// `id` varchar(255) NOT NULL DEFAULT '' COMMENT '变更ID',
// `domain_code` varchar(255) NOT NULL DEFAULT '' COMMENT '业务域code',
// `team_id` varchar(100) DEFAULT '' COMMENT 'teamId',
// `status` int(4) NOT NULL DEFAULT '0' COMMENT '变更状态',
// `type` int(4) NOT NULL DEFAULT '0' COMMENT '变更类型',
// `create_user` varchar(100) NOT NULL DEFAULT '' COMMENT '创建者',
// `update_user` varchar(100) NOT NULL DEFAULT '' COMMENT '修改者',
// `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
// `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
export enum ChangeStatus {
  DEVELOPING = 0,
  TESTING = 1,
  PUBLISHING = 2,
  PUBLISHED = 3,
  CLOSED = 4,
}

export enum ChangeType {
  DEVELOP = 0,
  HOTFIX = 1,
  ROLLBACK = 3,
}

@Entity({ name: 'change' })
export class Change {
  @ApiProperty({
    description: '变更ID',
  })
  @PrimaryColumn({
    name: 'id',
    type: 'varchar',
    length: 255,
    comment: '变更ID',
  })
  changeId: string

  @PrimaryColumn({
    name: 'change_name',
    type: 'varchar',
    length: 255,
    comment: '变更名称',
  })
  changeName: string

  @ApiProperty({
    description: '业务域code',
  })
  @Column({
    name: 'domain_code',
    type: 'varchar',
    length: 255,
    comment: '业务域Code',
  })
  domainCode: string

  @ApiProperty({
    description: 'teamId',
  })
  @Column({
    name: 'team_id',
    type: 'varchar',
    length: 100,
    comment: 'teamId',
  })
  teamId: string

  @ApiProperty({
    description: '变更状态',
  })
  @Column({
    comment: '变更状态',
    enum: ChangeStatus,
    default: ChangeStatus.DEVELOPING,
  })
  status: number

  @ApiProperty({
    description: '变更类型',
  })
  @Column({
    comment: '变更类型',
  })
  type: number

  @ApiProperty({
    description: '创建者',
  })
  @PrimaryColumn({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建者',
  })
  createUser: string

  @ApiProperty({
    description: '修改者',
  })
  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '修改者',
  })
  updateUser: string

  @ApiProperty({
    description: '创建时间',
  })
  @Column({
    name: 'create_time',
    type: 'bigint',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || +new Date(),
    },
    comment: '创建时间',
  })
  createTime: number

  @ApiProperty({
    description: '修改时间',
  })
  @Column({
    name: 'update_time',
    type: 'bigint',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || +new Date(),
    },
    comment: '修改时间',
  })
  updateTime: number
}
