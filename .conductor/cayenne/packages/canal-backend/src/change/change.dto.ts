import { OmitType } from '@nestjs/mapped-types'
import { ApiProperty } from '@nestjs/swagger'
import { IsInt, IsNotEmpty, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'
import { DBTablePageInfo, LingZhuAdditionalParameters } from 'src/common/dto'
export class ChangeDto {
  @ApiProperty()
  changeId: string
  @ApiProperty()
  changeName: string
  @ApiProperty()
  type: number
  @ApiProperty()
  domainCode: string
  @ApiProperty()
  createUser: string
  @ApiProperty()
  publishTime: number
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateTime: number
  @ApiProperty()
  status: number
  @ApiProperty()
  isOnline: number
  @ApiProperty()
  teamId: string
  @ApiProperty()
  developer?: string[]
  @ApiProperty()
  tester?: string[]
}

export class ChangeCloseDto {
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  changeId: string
}

export class CreateOrUpdateChangeResponse {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块id' })
  readonly changeId: string
  readonly status?: number
}

export class ChangeListDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '业务域code' })
  readonly domainCode: string

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @ApiProperty({ description: '页码' })
  readonly pageNum: number

  @IsInt({ message: ErrorMessage.IS_NUMBER })
  @ApiProperty({ description: '分页大小' })
  readonly pageSize: number

  @ApiProperty({ description: '创建者' })
  readonly createUser?: string

  @ApiProperty({ description: '变更名称' })
  readonly changeName?: string

  @ApiProperty({ description: '排序字段', required: false })
  readonly sortField?: string

  @ApiProperty({ description: '排序方向 (asc/desc)', required: false })
  readonly sortOrder?: string
}

export class ChangeListResponseDto {
  @ApiProperty({ description: '分页信息', type: DBTablePageInfo })
  pageInfo: {
    pageNum: number
    pageSize: number
    total: number
  }
  @ApiProperty({ description: '变更列表', type: [ChangeDto] })
  list: ChangeItemDto[]
}

export class ChangeItemDto extends OmitType(ChangeDto, [
  'developer',
  'tester',
] as const) {
  @ApiProperty()
  changeId: string
  @ApiProperty()
  changeName: string
  @ApiProperty()
  type: number
  @ApiProperty()
  domainCode: string
  @ApiProperty()
  createUser: string
  @ApiProperty()
  publishTime: number
  @ApiProperty()
  createTime: number
  @ApiProperty()
  updateTime: number
  @ApiProperty()
  status: number
  @ApiProperty()
  isOnline: number
  @ApiProperty()
  teamId: string
}
export class ProcessingChangeListDto {
  @ApiProperty({ type: [ChangeItemDto] })
  list: ChangeItemDto[]
}

export class CloseChangeDto extends LingZhuAdditionalParameters {
  @ApiProperty()
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  changeId: string
}

export class UpdateChangePartInfoDto extends LingZhuAdditionalParameters {
  @ApiProperty()
  changeId: string
  @ApiProperty()
  updateValue: string
  @ApiProperty()
  updateKey: string
}

export enum DrivingStatusDirect {
  DevelopingToPublishing = 1,
  PublishingToDeveloping = 0,
}

export class LingzhuUpdateChangeStatusDto extends LingZhuAdditionalParameters {
  @ApiProperty()
  @IsString()
  changeId: string
  @ApiProperty()
  direct:
    | DrivingStatusDirect.DevelopingToPublishing
    | DrivingStatusDirect.PublishingToDeveloping
}
