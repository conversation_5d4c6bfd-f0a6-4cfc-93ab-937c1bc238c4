import { ApiProperty } from '@nestjs/swagger'
import { bigNumberStringToIntTransformer } from 'src/utils/transform'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'

/**
 * 模块版本日志
 */
@Entity({ name: 'module_version_log' })
export class ModuleVersionLog {
  @ApiProperty({
    description: 'id',
  })
  @PrimaryGeneratedColumn({
    type: 'bigint',
    comment: 'id',
    unsigned: true,
  })
  id: string

  @ApiProperty({
    description: '模块 id',
  })
  @Column({
    name: 'module_id',
    type: 'char',
    length: 36,
    comment: '模块 id',
  })
  moduleId: string

  @ApiProperty({
    description: '模块版本',
  })
  @Column({
    name: 'module_version',
    type: 'bigint',
    comment: '模块版本',
  })
  moduleVersion: number

  @ApiProperty({
    description: '模块内容',
  })
  @Column({
    name: 'module_content',
    type: 'longtext',
    comment: '模块内容',
  })
  moduleContent: string

  @ApiProperty({
    description: '创建时间戳',
  })
  @Column({
    type: 'bigint',
    transformer: bigNumberStringToIntTransformer,
    name: 'created_at',
  })
  public createdAt: number

  @ApiProperty({
    description: '创建者',
  })
  @Column({
    type: 'varchar',
    length: 128,
  })
  creator: string
}
