import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, IsString } from 'class-validator'
import { ErrorMessage } from 'src/common/apiError/error-message.enum'

/**
 * 模块版本日志简介 DTO
 */
export class ModuleVersionLogBriefDto {
  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: 'id' })
  readonly id: string

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块 id' })
  readonly moduleId: string

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '模块版本' })
  readonly moduleVersion: number

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '创建时间戳' })
  readonly createdAt: number

  @IsString({ message: ErrorMessage.IS_STRING })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '创建者' })
  readonly creator: string
}

/**
 * 模块版本日志查找简介响应 DTO
 */
export class ModuleVersionLogFindBriefResDto {
  @ApiProperty({ description: '日志', type: [ModuleVersionLogBriefDto] })
  readonly logs: ModuleVersionLogBriefDto[]

  @IsNumber({}, { message: ErrorMessage.IS_NUMBER })
  @IsNotEmpty({ message: ErrorMessage.IS_NOT_EMPTY })
  @ApiProperty({ description: '签出模块版本' })
  readonly checkoutModuleVersion: number
}
