import { Controller, Get, Query, UseGuards } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { AuthGuard } from 'src/auth/auth.guard'
import { CommonApiResponse } from 'src/decorators/common.response.decorator'
import { ModuleVersionLogFindBriefResDto } from './module-version-log.dto'
import { ModuleVersionLog } from './module-version-log.entity'
import { ModuleVersionLogService } from './module-version-log.service'

@Controller('/rest/canal/module-version-log')
@ApiTags('module-version-log')
@Controller()
@UseGuards(AuthGuard)
export class ModuleVersionLogController {
  public constructor(
    private readonly moduleVersionLogService: ModuleVersionLogService,
  ) {}

  @Get('brief')
  @ApiOperation({
    summary: '查找模块版本日志简介',
    operationId: 'findModuleVersionLogBrief',
  })
  @CommonApiResponse(ModuleVersionLogFindBriefResDto)
  public async findModuleVersionLogBrief(
    @Query('moduleId') moduleId: string,
    @Query('moduleVersion')
    moduleVersion: number,
  ): Promise<ModuleVersionLogFindBriefResDto> {
    return this.moduleVersionLogService.findBrief(moduleId, moduleVersion)
  }

  @Get('detail')
  @ApiOperation({
    summary: '查找模块版本日志详情',
    operationId: 'findModuleVersionLogDetail',
  })
  @CommonApiResponse(ModuleVersionLog)
  public async findModuleVersionLogDetail(
    @Query('logId') logId: string,
  ): Promise<ModuleVersionLog | null> {
    return this.moduleVersionLogService.findDetail(logId)
  }
}
