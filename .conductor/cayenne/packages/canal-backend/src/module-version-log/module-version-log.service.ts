import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { ChangeModuleVersionService } from 'src/change-module-version/change-module-version.service'
import { Repository } from 'typeorm'
import { ModuleVersionLogFindBriefResDto } from './module-version-log.dto'
import { ModuleVersionLog } from './module-version-log.entity'

@Injectable()
export class ModuleVersionLogService {
  public constructor(
    private readonly changeModuleVersionService: ChangeModuleVersionService,
    @InjectRepository(ModuleVersionLog)
    public readonly moduleVersionLogRepo: Repository<ModuleVersionLog>,
  ) {}

  /**
   * 创建
   * @param moduleId 模块 id
   * @param moduleVersion 模块版本
   * @param moduleContent 模块内容
   * @param createdAt 创建时间戳
   * @param creator 创建者
   */
  public async create(
    moduleId: string,
    moduleVersion: number,
    moduleContent: string,
    createdAt: number,
    creator: string,
  ): Promise<ModuleVersionLog | null> {
    const log = this.moduleVersionLogRepo.create({
      moduleId,
      moduleVersion,
      moduleContent,
      createdAt,
      creator,
    })
    const savedLog = await this.moduleVersionLogRepo.save(log)
    return savedLog
  }

  /**
   * 查找简介
   * @param moduleId 模块 id
   * @param moduleVersion 模块版本
   */
  public async findBrief(
    moduleId: string,
    moduleVersion: number,
  ): Promise<ModuleVersionLogFindBriefResDto> {
    const [logs, checkoutModuleVersion] = await Promise.all([
      this.moduleVersionLogRepo.find({
        where: {
          moduleId,
          moduleVersion,
        },
        select: {
          id: true,
          moduleId: true,
          moduleVersion: true,
          createdAt: true,
          creator: true,
        },
        order: {
          createdAt: 'DESC',
        },
      }),
      this.changeModuleVersionService.getCheckoutModuleVersion(
        moduleId,
        moduleVersion,
      ),
    ])
    return {
      logs,
      checkoutModuleVersion,
    }
  }

  /**
   * 查找详情
   * @param logId 日志 ID
   */
  public async findDetail(logId: string): Promise<ModuleVersionLog | null> {
    return this.moduleVersionLogRepo.findOne({
      where: {
        id: logId,
      },
    })
  }
}
