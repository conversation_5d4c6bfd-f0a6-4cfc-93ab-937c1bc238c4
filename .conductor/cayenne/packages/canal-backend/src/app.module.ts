import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ChangeModuleVersion } from 'src/change-module-version/change-module-version.entity'
import { ChangeModuleVersionService } from 'src/change-module-version/change-module-version.service'
import { AiLog } from './ai/ai-log.entity'
import { AiController } from './ai/ai.controller'
import { AiService } from './ai/ai.service'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { BusinessDomainController } from './business-domain/business-domain.controller'
import { BusinessDomain } from './business-domain/business-domain.entity'
import { BusinessDomainService } from './business-domain/business-domain.service'
import { ChangeDeployController } from './change-deploy/change-deploy.controller'
import { ChangeDeployEntity } from './change-deploy/change-deploy.entity'
import { ChangeDeployService } from './change-deploy/change-deploy.service'
import { ChangePublishController } from './change-publish/change-publish.controller'
import { ChangePublishEntity } from './change-publish/change-publish.entity'
import { ChangePublishService } from './change-publish/change-publish.service'
import { ChangeController } from './change/change.controller'
import { Change } from './change/change.entity'
import { ChangeService } from './change/change.service'
import { loggerMiddleware } from './common/logger'
import { ComponentVersion } from './component-version/component-version.entity'
import { ComponentVersionService } from './component-version/component-version.service'
import { ComponentController } from './component/component.controller'
import { ComponentEntity } from './component/component.entity'
import { ComponentService } from './component/component.service'
import configuration from './config/configuration'
import { DataSourceConfigController } from './data-source-config/data-source-config.controller'
import { DataSourceConfig } from './data-source-config/data-source-config.entity'
import { DataSourceConfigService } from './data-source-config/data-source-config.service'
import { DeployGreyLog } from './deploy_grey_log/deploy-grey-log.entity'
import { DeployGreyLogService } from './deploy_grey_log/deploy-grey-log.service'
import { DomainPermission } from './domain-permissions/domain-permissions.entity'
import { DomainPermissionService } from './domain-permissions/domain-permissions.service'
import { DomainTagsController } from './domain-tags/domain-tags.controller'
import { DomainTags } from './domain-tags/domain-tags.entity'
import { DomainTagsService } from './domain-tags/domain-tags.service'
import { ExpressionTemplateController } from './expression-template/expression-template.controller'
import { ExpressionTemplate } from './expression-template/expression-template.entity'
import { ExpressionTemplateService } from './expression-template/expression-template.service'
import { GreyDeploy } from './grey-deploy/grey-deploy.entity'
import { GreyDeployService } from './grey-deploy/grey-deploy.service'
import { HealthController } from './health/health.controller'
import { MaterialPlatformController } from './material-platform/material-platform.controller'
import { ModuleDeployController } from './module-deploy/module-deploy.controller'
import { ModuleDeployEntity } from './module-deploy/module-deploy.entity'
import { ModuleDeployService } from './module-deploy/module-deploy.service'
import { ModuleTagsController } from './module-tags/module-tags.controller'
import { ModuleTags } from './module-tags/module-tags.entity'
import { ModuleTagsService } from './module-tags/module-tags.service'
import { ModuleTemplateController } from './module-template/module-template.controller'
import { ModuleTemplateEntity } from './module-template/module-template.entity'
import { ModuleTemplateService } from './module-template/module-template.service'
import { ModuleVersionLogController } from './module-version-log/module-version-log.controller'
import { ModuleVersionLog } from './module-version-log/module-version-log.entity'
import { ModuleVersionLogService } from './module-version-log/module-version-log.service'
import { ModuleVersion } from './module-version/module-version.entity'
import { ModuleVersionService } from './module-version/module-version.service'
import { ModuleController } from './module/module.controller'
import { ModuleEntity } from './module/module.entity'
import { ModuleService } from './module/module.service'
import { OpenApiService } from './openapi/openapi.service'
import { UploadController } from './upload/upload.controller'
import { UserController } from './user/user.controller'
import { User } from './user/user.entity'
import { UserService } from './user/user.service'
import { WorkloadController } from './workload/workload.controller'
import { WorkloadService } from './workload/workload.service'
const entities = [
  User,
  BusinessDomain,
  ComponentEntity,
  ComponentVersion,
  ModuleTemplateEntity,
  Change,
  ModuleEntity,
  ModuleVersion,
  ModuleDeployEntity,
  ChangeModuleVersion,
  ChangeDeployEntity,
  ChangePublishEntity,
  ExpressionTemplate,
  DomainTags,
  ModuleTags,
  DataSourceConfig,
  GreyDeploy,
  DeployGreyLog,
  DomainPermission,
  ModuleVersionLog,
  AiLog,
]
const importConfigModule = ConfigModule.forRoot({
  load: [configuration],
})
@Module({
  imports: [
    importConfigModule,
    TypeOrmModule.forRootAsync({
      imports: [importConfigModule],
      useFactory: async (configService: ConfigService) => {
        try {
          const dbConfig = await configService.get('db')
          const r = {
            ...dbConfig,
            username: dbConfig?.user,
            database: 'canal_manage',
            type: 'mysql',
            synchronize: false,
            logging: true,
            poolSize: 100,
            entities,
          }
          return r
        } catch (error) {
          console.log(error)
        }
        return {}
      },
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature(entities),
  ],
  providers: [
    AppService,
    UserService,
    BusinessDomainService,
    OpenApiService,
    ComponentService,
    ComponentVersionService,
    ChangeService,
    ModuleService,
    ModuleDeployService,
    ModuleVersionService,
    ChangeModuleVersionService,
    ChangeDeployService,
    ChangePublishService,
    ExpressionTemplateService,
    ModuleTemplateService,
    WorkloadService,
    DomainTagsService,
    ModuleTagsService,
    DataSourceConfigService,
    GreyDeployService,
    DeployGreyLogService,
    DomainPermissionService,
    ModuleVersionLogService,
    AiService,
  ],
  controllers: [
    AppController,
    UserController,
    HealthController,
    BusinessDomainController,
    ComponentController,
    ChangeController,
    ModuleController,
    ModuleDeployController,
    ChangeDeployController,
    ChangePublishController,
    ExpressionTemplateController,
    ModuleTemplateController,
    MaterialPlatformController,
    WorkloadController,
    DomainTagsController,
    ModuleTagsController,
    DataSourceConfigController,
    UploadController,
    ModuleVersionLogController,
    AiController,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(loggerMiddleware).forRoutes('*')
  }
}
