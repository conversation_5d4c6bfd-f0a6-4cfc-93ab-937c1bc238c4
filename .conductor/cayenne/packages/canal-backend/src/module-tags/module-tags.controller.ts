// 给service文件里的几个方法补全controller
import { Body, Controller, Get, Post, Query, Session } from '@nestjs/common'
import { ApiQuery } from '@nestjs/swagger'
import { UserSession } from 'src/types'
import { ModuleTagsService } from './module-tags.service'

@Controller('/rest/canal/moduleTags')
export class ModuleTagsController {
  public constructor(private readonly moduleTagsService: ModuleTagsService) {}

  @Get('list')
  @ApiQuery({
    name: 'moduleId',
    required: true,
    description: '模块id',
    type: String,
  })
  public async getDomainTags(@Query('moduleId') moduleId: string) {
    return this.moduleTagsService.getModuleTags(moduleId)
  }

  @Post('add')
  public async addOneTag(
    @Body()
    addDto: {
      moduleId: string
      tagId: number
    },
    @Session() userSession: UserSession,
  ) {
    return this.moduleTagsService.addTag(
      addDto.moduleId,
      addDto.tagId,
      userSession.userInfo?.userCode,
    )
  }

  @Post('delete')
  public async deleteOneTag(@Body() deleteDto: { id: number }) {
    return this.moduleTagsService.deleteOneTag(deleteDto.id)
  }
}
