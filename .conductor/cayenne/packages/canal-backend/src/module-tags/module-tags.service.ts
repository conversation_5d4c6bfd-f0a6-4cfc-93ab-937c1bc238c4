import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { CommonException } from 'src/exceptions/common-exception'
import { ExceptionCodes } from 'src/exceptions/excepition-codes'
import { Repository } from 'typeorm'
import { ModuleTags } from './module-tags.entity'

@Injectable()
export class ModuleTagsService {
  public constructor(
    @InjectRepository(ModuleTags)
    public readonly moduleTagsRepo: Repository<ModuleTags>,
  ) {}
  /**
   * 获取domainCode下的所有标签
   * @param domainCode
   * @returns
   */
  async getModuleTags(moduleId: string) {
    const tags = await this.moduleTagsRepo.find({
      where: {
        moduleId,
      },
    })
    return tags
  }

  // 删除一条标签)
  async deleteOneTag(id: number) {
    return await this.moduleTagsRepo.delete({
      id,
    })
  }

  async addTag(moduleId: string, tagId: number, userCode: string) {
    const tag = await this.moduleTagsRepo.findOne({
      where: {
        moduleId,
        tagId,
      },
    })
    if (tag) {
      throw new CommonException(ExceptionCodes.Domain_tag_Duplicate)
    }
    const now = new Date().getTime()
    return await this.moduleTagsRepo.save({
      moduleId,
      tagId,
      createTime: now,
      creator: userCode,
      updateTime: now,
      updater: userCode,
    })
  }
}
