import { ApiProperty } from '@nestjs/swagger'
import { Column, Entity, PrimaryColumn } from 'typeorm'

@Entity({ name: 'module_tags' })
export class ModuleTags {
  @ApiProperty()
  @PrimaryColumn()
  id: number

  @Column({
    name: 'module_id',
    type: 'char',
    length: 36,
    comment: '模块id',
  })
  moduleId: string

  @ApiProperty({
    description: '标签id',
  })
  @Column({
    name: 'tag_id',
    type: 'bigint',
    comment: '标签id',
  })
  tagId: number

  @ApiProperty({
    description: '创建者',
  })
  @Column({
    name: 'create_user',
    type: 'varchar',
    length: 100,
    comment: '创建者',
  })
  creator: string

  @ApiProperty({
    description: '修改者',
  })
  @Column({
    name: 'update_user',
    type: 'varchar',
    length: 100,
    comment: '修改者',
  })
  updater: string

  @ApiProperty({
    description: '创建时间',
  })
  @Column({
    name: 'create_time',
    type: 'bigint',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || +new Date(),
    },
    comment: '创建时间',
  })
  createTime: number

  @ApiProperty({
    description: '修改时间',
  })
  @Column({
    name: 'update_time',
    type: 'bigint',
    transformer: {
      from: (value: string) => +value,
      to: (v) => v || +new Date(),
    },
    comment: '修改时间',
  })
  updateTime: number
}
