{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "commonjs", "target": "es6", "declaration": true, "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "noFallthroughCasesInSwitch": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "noUnusedParameters": false}}