module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  testMatch: ['**/__tests__/**/*.test.ts?(x)'],
  moduleNameMapper: {
    '^@ks/weblogger$': '<rootDir>/src/__tests__/mock/mock-cls.ts',
    '^@ks-radar/(.*)$': '<rootDir>/src/__tests__/mock/mock-cls.ts',
    '^nanoid$': '<rootDir>/node_modules/nanoid/index.cjs',
    '^@kael/runtime-plugin-react-renderer/esm/(.*)$':
      '@kael/runtime-plugin-react-renderer/lib/$1',
    '^rc-util/es/ref$': 'rc-util/lib/ref',
    '^lodash-es$': 'lodash',
  },
}
