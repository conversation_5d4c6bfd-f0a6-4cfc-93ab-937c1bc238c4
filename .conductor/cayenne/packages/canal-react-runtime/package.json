{"name": "@ad/canal-react-runtime", "version": "2.6.3", "description": "大运河 React 运行时", "repository": {"type": "git", "url": "*************************:ks-ad/ad-fe/grandcanal/canal.git"}, "license": "MIT", "author": "AD", "sideEffects": false, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && rollup --config rollup.config.ts --configPlugin typescript", "start": "rollup --config rollup.config.ts --configPlugin typescript --watch", "test": "jest --passWithNoTests", "test:w": "jest --watch"}, "dependencies": {"@ad/canal-react-component-context": "workspace:^", "@ad/canal-react-components": "workspace:^", "@ad/canal-shared": "workspace:^", "@ad/e2e-material-schema": "workspace:^", "@ad/e2e-material-schema-utils": "workspace:^", "@ad/e2e-schema": "workspace:^", "@ad/e2e-schema-utils": "workspace:^", "@kael/material-schema": "1.0.0-rc.190", "@kael/runtime": "1.0.0-rc.190", "@kael/runtime-plugin-expr-ext-array": "1.0.0-rc.190", "@kael/runtime-plugin-expr-ext-js": "1.0.0-rc.190", "@kael/runtime-plugin-expr-ext-object": "1.0.0-rc.190", "@kael/runtime-plugin-id": "1.0.0-rc.190", "@kael/runtime-plugin-mobx-container-view": "1.0.0-rc.190", "@kael/runtime-plugin-mobx-react-renderer": "1.0.0-rc.190", "@kael/runtime-plugin-react-renderer": "1.0.0-rc.190", "@kael/schema": "1.0.0-rc.190", "@kael/schema-utils": "1.0.0-rc.190", "@ks-radar/radar-component-collect": "^1.2.14", "@ks-radar/radar-core": "^1.2.14", "@ks-radar/radar-error-collect": "^1.2.14", "@ks-radar/radar-util": "^1.2.14", "@ks/weblogger": "^3.10.30", "@remote-ui/rpc": "^1.4.5", "@types/lodash": "^4.14.199", "is-promise": "^4.0.0", "lodash": "^4.17.21", "rc-util": "^5.43.0", "react-error-boundary": "^4.0.11", "react-use": "^17.4.0", "styled-components": "^6.0.8"}, "devDependencies": {"@m-ui/react": "^1.12.2", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.4", "@types/jest": "^29.5.5", "@types/react": "^18.2.23", "@types/react-test-renderer": "^18.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mobx": "^6.10.2", "mobx-react-lite": "^4.0.5", "nanoid": "^3.3.7", "react": "^18.2.0", "react-test-renderer": "^18.2.0", "rollup": "^3.29.3", "rollup-plugin-exclude-dependencies-from-bundle": "1.1.23", "ts-jest": "^29.1.1"}, "peerDependencies": {"@m-ui/react": ">=1.0.0", "mobx": ">=6.0.0", "mobx-react-lite": ">=3.0.0", "react": ">=16.8.0"}}