# canal-react-runtime

大运河 React 运行时

协议文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcACTE6Bh5U0SlepEk1WRfd5C

# 使用示例

## 常用

### 基础

```tsx
import { CanalReactRuntimeFC } from '@ad/canal-react-runtime'
;<CanalReactRuntimeFC
  // 模块 ID
  moduleId="abc"
  // 业务组件库中使用到但又在编译时作为 externals 的依赖包
  dependencies={{
    react: require('react'),
    '@m-ui/react': require('@m-ui/react'),
    // ...
  }}
/>
```

### 更多属性

```tsx
import {
  CanalReactRuntimeFC,
  createCommonFetchSchema,
  fetchSchemaWithModuleIdInUrl,
} from '@ad/canal-react-runtime'
;<CanalReactRuntimeFC
  // ...

  // 环境，即大运河变更里部署的环境
  env="staging"
  // 请求 Schema 文件时，需要携带的外部参数，_.isEqual 发现变化的话，运行时会自动更新 Schema 文件
  params={{
    userId: 'tom',
  }}
  // div 包装样式
  style={{
    minHeight: 500,
  }}
  // 运行时加载 Schema 文件时，自动展示加载中图标
  Loading={AutoLoading}
  // 请求 Schema 文件时，URL 里带上模块 ID
  fetchSchema={fetchSchemaWithModuleIdInUrl}
/>
;<CanalReactRuntimeFC
  // ...

  // 请求 Schema 文件时，添加一些自定义的 Header
  fetchSchema={createCommonFetchSchema({
    headers: {
      Xabc: '234',
    },
  })}
/>
;<CanalReactRuntimeFC
  // ...

  // 请求 Schema 文件时，替换业务组件的 JS URL
  fetchSchema={createCommonFetchSchema({
    componentJsUrlMap: {
      'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js':
        'http://localhost:8371/button/index.js',
    },
  })}
/>
;<CanalReactRuntimeFC
  // ...

  // 本地组件库（物料 Schema 文件链接），配置后会自动替换 js url
  fetchSchema={createCommonFetchSchema({
    localCl: 'http://localhost:7001/component.library.material.json',
  })}
/>
```

## 高级

### 通过指定 Schema 文件加载

```tsx
import { CanalReactRuntimeFC } from '@ad/canal-react-runtime'
;<CanalReactRuntimeFC
  schema={schema}
  // 默认组件库
  defaultComponentLib={{
    Root,
    Container,
    // ...
  }}
/>
```

### 监听内部组件值

```tsx
import {
  CanalReactRuntimeFC,
  type CanalReactRuntime,
} from '@ad/canal-react-runtime'
import { autorun } from 'mobx'
import { useEffect, useRef } from 'react'

export const App = () => {
  const refCanalReactRuntime = useRef<CanalReactRuntime>()
  useEffect(() => {
    return autorun(() => {
      console.log('abc', refCanalReactRuntime.current?.container.data.组件ID)
    })
  }, [])
  return (
    <CanalReactRuntimeFC
      // ...

      // 传入运行时 ref
      refCanalReactRuntime={refCanalReactRuntime}
    />
  )
}
```

### CDN 容灾

```tsx
import { setCanalCdnDt } from '@ad/canal-react-runtime'

setCanalCdnDt({
  replaceDomains: ['p1.adkwai.com', 'https://js-ad.a.yximgs.com/kos/nlav12572'],
})
```

# 开发

## 脚本

| 脚本   | 说明                  |
| ------ | --------------------- |
| build  | 编译                  |
| start  | 开始开发              |
| test   | 运行单测              |
| test:w | 以 watch 模式运行单测 |
