import { AnyObject } from '@ad/canal-shared'
import { Track } from '@ad/e2e-schema'
import { noop } from 'lodash'
import {
  defaultFetchGlobalSchema,
  FetchGlobalSchema,
  FetchSchemaEnv,
} from '../apis'
import { EvalInfo } from '../plugins/plugin-canal-eval-expression'
import { captureException } from '../weblog'

/**
 * 加载全局模块选项
 */
export interface LoadGlobalModuleOptions {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 获取全局 Schema 文件
   */
  fetchGlobalSchema?: FetchGlobalSchema
  /**
   * 环境，默认走生产环境
   */
  env?: FetchSchemaEnv
  /**
   * 依赖包
   */
  dependencies?: Record<string, unknown>
}

/**
 * 全局模型
 */
export interface GlobalModule {
  /**
   * 全局（前端）模型
   */
  globalModel?: AnyObject
}

/**
 * 加载全局模块
 * @param options 选项
 */
export async function loadGlobalModule({
  moduleId: schemaId,
  fetchGlobalSchema = defaultFetchGlobalSchema,
  env = 'production',
  dependencies,
}: LoadGlobalModuleOptions): Promise<GlobalModule> {
  let globalSchema
  try {
    globalSchema = await fetchGlobalSchema({
      schemaId,
      env,
    })
  } catch (err) {
    if (err instanceof Error) {
      captureException(err)
    }
    throw err
  }
  const { globalFrontModel } = globalSchema
  const globalModule: GlobalModule = {}
  if (globalFrontModel) {
    const { container } = createMockRuntime(dependencies)
    const evalInfo = (container.evalInfo = {
      moduleId: schemaId,
      relativePath: ['globalModel'],
      expressionType: 'js',
    })
    try {
      const globalModelCls = evalJs(globalFrontModel.code, container)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      globalModule.globalModel = new (globalModelCls as any)(container)
    } catch (err) {
      if (err instanceof Error) {
        err.message = `[${JSON.stringify(evalInfo)}] ${err.message}`
        captureException(err)
      }
      throw err
    }
  }
  return globalModule
}

// globalFrontModel.code 示例
// "(function(deps) {\n  var exports = {}, module = {}\n  module.exports = exports\n  ;(function() {\n    \"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : String(i); }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 全局前端模型\n */\nvar GlobalFrontModel = exports[\"default\"] = /*#__PURE__*/_createClass(function GlobalFrontModel(ctx) {\n  _classCallCheck(this, GlobalFrontModel);\n  _defineProperty(this, \"jjx\", \"abc\" + 3);\n  _defineProperty(this, \"jjs\", \"abcss\" + 3);\n  this.ctx = ctx;\n  console.log('GlobalFrontModel init', ctx);\n});\n  })()\n  return module.exports.default\n  function require(name) {\n    return deps[name]\n  }\n})(ctx.runtime.container.dependencies)"

/**
 * 模拟运行时
 */
interface MockRuntime {
  /**
   * 容器
   */
  container: MockContainer
}

/**
 * 模拟容器
 */
interface MockContainer {
  /**
   * 运行时
   */
  runtime: MockRuntime
  /**
   * 依赖包
   */
  dependencies?: Record<string, unknown>

  // 以下跟 packages/canal-platform/src/utils/monaco.ts 对齐

  /**
   * 数据，组件 ID -> 组件数据
   */
  data: AnyObject
  /**
   * 设置数据
   * @param partialData 部分数据
   */
  setData(partialData: Partial<AnyObject>): void
  /**
   * 初始化参数，指业务方传给运行时的 params 字段
   */
  initParams?: Record<string, unknown>
  /**
   * 触发埋点
   * @param trackId 埋点 ID
   * @param transformTrack 转换埋点
   */
  track(
    trackId: string,
    transformTrack?: Partial<Track> | ((track: Track) => Track | void),
  ): void
  /**
   * 全局模型
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  globalModel?: any
  /**
   * 模型
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  model?: any
  /**
   * 父模块上下文
   */
  parentModuleCtx?: MockContainer
  /**
   * 子模块上下文，子模块组件 ID -> 上下文
   */
  childModuleCtxs: Record<string, MockContainer>
  /**
   * 执行信息
   */
  evalInfo?: EvalInfo
}

/**
 * 创建模拟运行时
 * @param dependencies 依赖包
 */
function createMockRuntime(
  dependencies?: Record<string, unknown>,
): MockRuntime {
  const runtime = {} as MockRuntime
  const container: MockContainer = {
    runtime,
    dependencies,
    data: {},
    setData: noop,
    track: noop,
    childModuleCtxs: {},
  }
  runtime.container = container
  return runtime
}

/**
 * JS 代码 -> 函数
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const jsCodeMap = new Map<string, any>()

/**
 * 执行 JS
 * @param code 代码
 * @param ctx 上下文
 */
function evalJs(code: string, ctx: unknown): unknown {
  let fn = jsCodeMap.get(code)
  if (!fn) {
    fn = new Function('ctx', `return (${code})`)
    jsCodeMap.set(code, fn)
  }
  return fn(ctx)
}
