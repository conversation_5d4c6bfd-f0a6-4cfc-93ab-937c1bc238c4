import {
  CommonRes,
  isResponseSuccess,
  PLATFORM_ORIGIN_STAGING,
  Promisable,
} from '@ad/canal-shared'
import { E2ESchema, E2EServerSchema, RefreshType } from '@ad/e2e-schema'
import type { RemoteComponentLibraryMaterialSchema } from '@kael/material-schema'
import { decodeComponentType } from '@kael/schema-utils'
import { entries, get, isString, keys, values } from 'lodash'
import {
  BIZ_COMPONENT_LIB_NAME,
  CAPI_ORIGIN,
  CAPI_STAGING_ORIGIN,
  FETCH_SCHEMA_DEFAULT_PATHNAME,
  FETCH_SCHEMA_PREVIEW_PATHNAME,
  RUNTIME_CANAL_ORIGIN,
  RUNTIME_DROW_ORIGIN,
} from '../constants'
import {
  getCanalDomainCode,
  getCanalModuleId,
  getRawSchemaFromDesigner,
} from '../local-preview'
import { wrapFetch } from '../utils'
import { customEvent } from '../weblog'

/**
 * 获取 Schema 文件函数
 */
export type FetchSchema = (
  options: FetchSchemaOptions,
) => Promise<FetchSchemaRet>

/**
 * 获取 Schema 文件函数结果
 */
export type FetchSchemaRet = Partial<E2ESchema | E2EServerSchema> &
  Partial<FetchSchemaExtraRet>

/**
 * 获取 Schema 文件函数额外结果
 */
export interface FetchSchemaExtraRet {
  /**
   * 日志
   */
  logs: string[]
  /**
   * Schema 生成过程中的错误
   */
  schemaError: FetchSchemaExtraSchemaError
  /**
   * 天问 URL
   */
  traceUrl: string
  /**
   * 内部渲染耗时
   */
  renderInnerTimeCost: number
  /**
   * 业务请求
   */
  httpDataList: FetchSchemaExtraBizHttp[]
}

/**
 * 获取 Schema 文件函数额外结果，Schema 报错
 */
export interface FetchSchemaExtraSchemaError {
  /**
   * 接口参数表达式报错
   */
  argsError: string[]
  /**
   * 接口报错
   */
  apiError: string[]
  /**
   * 渲染组件属性表达式报错
   */
  renderError: string[]
}

/**
 * 获取 Schema 文件函数额外结果，业务请求
 */
export interface FetchSchemaExtraBizHttp {
  /**
   * curl 命令
   */
  curl: string
  /**
   * 错误消息
   */
  errMsg: string | null
  /**
   * 请求
   */
  request: FetchSchemaExtraBizHttpReq
  /**
   * 响应
   */
  response: FetchSchemaExtraBizHttpRes
}

/**
 * 获取 Schema 文件函数额外结果，业务请求信息
 */
export interface FetchSchemaExtraBizHttpReq {
  body: Record<string, unknown> | null
  headers: Record<string, string>
  method: string
  url: string
}

/**
 * 获取 Schema 文件函数额外结果，业务请求响应信息
 */
export interface FetchSchemaExtraBizHttpRes {
  body: Record<string, unknown> | null
  headers: Record<string, string>
  status: number
  statusText: string
}

/**
 * Java 常用响应
 */
export interface CommonResOfJava<Data = unknown> {
  /**
   * 编码
   */
  result: number
  /**
   * 数据
   */
  data: Data
  /**
   * （错误）信息
   */
  message: string
  /**
   * 日志 ID
   */
  ktrace: string
}

/**
 * 获取 Schema 文件函数选项
 */
export interface FetchSchemaOptions {
  /**
   * 运行时 ID
   */
  runtimeId: string
  /**
   * Schema 文件 ID
   */
  schemaId: string | number
  /**
   * 刷新类型
   */
  refreshType: RefreshType
  /**
   * 参数
   */
  params: Record<string, unknown>
  /**
   * 环境
   */
  env: FetchSchemaEnv
  /**
   * 开启 Mock
   */
  mock: boolean
  /**
   * 版本，后端会设置为 E2EServerSchema['version']
   */
  version: string
}

/**
 * 获取 Schema 文件请求
 */
export interface FetchSchemaReq extends Omit<FetchSchemaOptions, 'schemaId'> {
  /**
   * 模块 ID
   */
  moduleId: string | number
  /**
   * KFX 版本，用于灰度
   */
  kfxVersion?: string
}

/**
 * 获取 Schema 文件响应
 */
export type FetchSchemaRes = E2EServerSchema & FetchSchemaExtraRet

/**
 * 获取 Schema 文件环境
 * * production: 生产环境
 * * staging: staging 环境
 * * beta: beta 环境
 * * prt: 生产回归测试环境
 */
export type FetchSchemaEnv = 'production' | 'staging' | 'beta' | 'prt'

/**
 * 创建获取 Schema 文件函数选项
 */
export interface CreateFetchSchemaOptions {
  /**
   * URL 里的源，默认为空字符串，即当前页面的源
   */
  origin?: string
  /**
   * URL 里的路径名，默认为：`'/rest/ad/canal/render/schema'`
   */
  pathname?: string
  /**
   * 转换 fetch 的参数，可以直接原地修改
   * @param params fetch 的参数
   * @param options 选项
   * @returns 新的 fetch 的参数，返回 undefined 表示用旧的值
   */
  transformFetchParams?(
    params: CreateFetchSchemaFetchParams,
    options: FetchSchemaOptions,
  ): Promisable<CreateFetchSchemaFetchParams | void>
  /**
   * 转换结果（Schema 文件）
   * @param ret 结果（Schema 文件）
   */
  transformRet?(ret: FetchSchemaRet): Promisable<FetchSchemaRet | void>
  /**
   * fetch 标准函数
   */
  fetch?: typeof fetch
  /**
   * 消费预请求
   * @param params fetch 的参数
   * @param options 选项
   * @returns null 表示没有可消费的预请求
   */
  consumePrefetch?(
    params: CreateFetchSchemaFetchParams,
    options: FetchSchemaOptions,
  ): Promise<null | CommonResOfJava<FetchSchemaRes>>
}

/**
 * createFetchSchema 里所用到的 fetch 函数参数
 */
export type CreateFetchSchemaFetchParams = [string, RequestInit]

/**
 * 创建获取 Schema 文件函数
 * @param options 选项
 */
export function createFetchSchema({
  origin = '',
  pathname = FETCH_SCHEMA_DEFAULT_PATHNAME,
  transformFetchParams,
  transformRet,
  fetch = window.fetch,
  consumePrefetch,
}: CreateFetchSchemaOptions = {}): FetchSchema {
  return async (options) => {
    const {
      runtimeId,
      schemaId: moduleId,
      refreshType,
      params,
      env,
      mock,
      version,
    } = options
    const url = `${origin}${pathname}`
    let fetchParams: CreateFetchSchemaFetchParams = [
      url,
      {
        method: 'POST',
        body: JSON.stringify({
          runtimeId,
          moduleId,
          refreshType,
          params,
          env,
          mock,
          version,
          kfxVersion: window?.kfxEnv?.version,
        } satisfies FetchSchemaReq),
        headers: {
          ['Content-Type']: 'application/json',
        },
        credentials: 'include',
      },
    ]
    fetchParams =
      (await transformFetchParams?.(fetchParams, options)) || fetchParams
    let res = await consumePrefetch?.(fetchParams, options)
    if (!res) {
      const fetchRes = await wrapFetch(fetch, (err) => {
        err.message = `${err.message} | [fetchSchema] moduleId: ${moduleId}, refreshType: ${refreshType}, env: ${env}, url: ${url}`
      })(...fetchParams)
      if (!isResponseSuccess(fetchRes)) {
        throw new Error(
          `[fetchSchema] invalid response status: ${fetchRes.status}, moduleId: ${moduleId}, refreshType: ${refreshType}, env: ${env}, url: ${url}`,
        )
      }
      res = (await fetchRes.json()) as CommonResOfJava<FetchSchemaRes>
    }
    if (res.result !== 1) {
      throw new Error(
        `[fetchSchema] invalid response result: ${res.result}, message: ${res.message}, moduleId: ${moduleId}, refreshType: ${refreshType}, env: ${env}, url: ${url}`,
      )
    }
    let ret: FetchSchemaRet = res.data
    sendCustomEvent(ret, moduleId)
    ret = (await transformRet?.(ret)) || ret
    return ret
  }
}

/**
 * 创建自动的获取 Schema 文件函数，可以自动切换为预览接口
 */
export const createAutoFetchSchema =
  process.env.NODE_ENV !== 'development'
    ? createFetchSchema
    : (createFetchSchemaOptions?: CreateFetchSchemaOptions): FetchSchema => {
        return createFetchSchema({
          ...createFetchSchemaOptions,
          async transformFetchParams(params, options) {
            if (options.schemaId === getCanalModuleId()) {
              // 切换为预览接口
              const rawSchema = await getRawSchemaFromDesigner()
              await addRawSchemaToFetchParams(rawSchema, params)
              if (params[0] === FETCH_SCHEMA_DEFAULT_PATHNAME) {
                params[0] = `${getPreviewOriginByEnv(
                  options.env,
                )}${FETCH_SCHEMA_PREVIEW_PATHNAME}`
              }
            }
            return createFetchSchemaOptions?.transformFetchParams?.(
              params,
              options,
            )
          },
        })
      }

/**
 * 把原始 Schema 文件添加到 fetch 参数内
 * @param rawSchema 原始 Schema 文件
 * @param params fetch 的参数
 */
async function addRawSchemaToFetchParams(
  rawSchema: E2EServerSchema,
  params: CreateFetchSchemaFetchParams,
): Promise<void> {
  const body = params[1].body
  if (isString(body)) {
    const req = JSON.parse(body) as FetchSchemaReq & {
      rawSchema?: E2EServerSchema
      rawGlobalSchema?: unknown
    }
    req.rawSchema = rawSchema
    try {
      // 填充全局模块
      const globalModuleUrl = `/rest/canal/module/global-detail?domainCode=${getCanalDomainCode()}&env=${
        req.env
      }`
      const globalModule: CommonRes<{ id: string } | undefined> = await (
        await wrapFetch(fetch, (err) => {
          err.message = `${err.message} | [addRawSchemaToFetchParams] moduleId: ${req.moduleId}, env: ${req.env}, url: ${globalModuleUrl}`
        })(globalModuleUrl)
      ).json()
      const globalModuleId = globalModule.data?.id
      if (globalModuleId) {
        let origin = ''
        if (req.env === 'staging') {
          // 因为 staging 通过 AP 部署到 staging 的服务（数据库），所以获取时也需要切换域名
          origin = PLATFORM_ORIGIN_STAGING
        }
        const globalModuleDeployUrl = `${origin}/rest/canal/deploy/module?moduleId=${globalModuleId}&env=${req.env}`
        const globalModuleDeploy: CommonRes<{ content: string } | undefined> =
          await (
            await wrapFetch(fetch, (err) => {
              err.message = `${err.message} | [addRawSchemaToFetchParams] moduleId: ${req.moduleId}, env: ${req.env}, url: ${globalModuleDeployUrl}`
            })(globalModuleDeployUrl)
          ).json()
        const content = globalModuleDeploy.data?.content
        if (content) {
          req.rawGlobalSchema = JSON.parse(content)
        }
      }
    } catch (err) {
      console.error('addRawSchemaToFetchParams fetch global module err', err)
    }
    params[1].body = JSON.stringify(req)
  }
}

/**
 * 通过环境获取预览源
 * @param env 环境
 */
function getPreviewOriginByEnv(env: FetchSchemaEnv): string {
  switch (env) {
    case 'staging': {
      return PLATFORM_ORIGIN_STAGING
    }
    case 'prt': {
      return 'https://capi.prt.kuaishou.com'
    }
    case 'beta': {
      return 'https://capi-beta.corp.kuaishou.com'
    }
  }
  return ''
}

/**
 * 上报三个error的内容
 */
function sendCustomEvent(
  data: FetchSchemaRet,
  moduleId: string | number,
): void {
  const maxLength = 1000
  try {
    const safeRequestIdleCallback =
      window.requestIdleCallback ||
      function (callback): void {
        // 如果不支持 requestIdleCallback，则用 setTimeout 模拟
        setTimeout(callback, 0)
      }
    const sendCustomError = (name: string, errorInfo: string[]): void => {
      if (Array.isArray(errorInfo) && errorInfo.length) {
        // 因为上报长度有限制，先做一定的限制,减少被抛弃的概率
        const info = errorInfo.map((str) => {
          return str.length > maxLength ? str.slice(0, maxLength) : str
        })
        customEvent(
          name,
          window.location.origin + '/' + moduleId,
          JSON.stringify({
            errorDetail: info,
            traceUrl: data.traceUrl || '',
          }),
        )
      }
    }
    if (data.schemaError) {
      const errorTypes = [
        { type: 'apiError', value: data.schemaError.apiError },
        { type: 'argsError', value: data.schemaError.argsError },
        { type: 'renderError', value: data.schemaError?.renderError },
      ]
      errorTypes.forEach(({ type, value }) => {
        safeRequestIdleCallback(() => sendCustomError(type, value))
      })
    }
  } catch (e) {
    // 在这里处理错误 不处理失败
    console.error('上报接口scheme字段schemaError信息失败', e)
  }
}

/**
 * 默认的获取 Schema 文件函数，默认从业务方的域名获取
 */
export const defaultFetchSchema = /*#__PURE__*/ createAutoFetchSchema()

/**
 * 从 capi 获取 Schema 文件函数
 */
export const fetchSchemaFromCapiE = /*#__PURE__*/ createAutoFetchSchema({
  origin: CAPI_ORIGIN,
})

/**
 * 从 capi staging 获取 Schema 文件函数
 */
export const fetchSchemaFromCapiStaging = /*#__PURE__*/ createAutoFetchSchema({
  origin: CAPI_STAGING_ORIGIN,
})

/**
 * 通过组件 js url 对照表替换 Schema 文件里的 js url
 * @param ret 获取 Schema 文件函数结果
 * @param componentJsUrlMap 组件 js url 对照表
 */
export function replaceByComponentJsUrlMap(
  ret: FetchSchemaRet,
  componentJsUrlMap?: Record<string, string> | null,
): void {
  if (componentJsUrlMap && keys(componentJsUrlMap).length) {
    for (const code of values(ret.componentCodes)) {
      if (code.type !== 'low-code' && code.code.type !== 'multi') {
        if (isString(code.code.js)) {
          code.code.js = componentJsUrlMap[code.code.js] || code.code.js
        } else {
          code.code.js = code.code.js.map((js) => componentJsUrlMap[js] || js)
        }
      }
    }
  }
}

/**
 * 通过本地组件库加载组件 js url 对照表
 * @param ret 获取 Schema 文件函数结果
 * @param localCl 本地组件库（物料 Schema 文件链接）
 */
async function loadComponentJsUrlMapByLocalCl(
  ret: FetchSchemaRet,
  localCl?: string,
): Promise<Record<string, string>> {
  const { componentCodes } = ret
  const componentJsUrlMap: Record<string, string> = {}
  if (localCl && componentCodes) {
    const clSchema = (await (
      await wrapFetch(fetch, (err) => {
        err.message = `${err.message} | [loadComponentJsUrlMapByLocalCl] localCl: ${localCl}`
      })(localCl)
    ).json()) as RemoteComponentLibraryMaterialSchema
    const materialComponentJsMap: Record<
      string,
      Record<string, string> | undefined
    > = {}
    await Promise.all(
      entries(componentCodes).map(async ([key, code]) => {
        const [libName, componentId] = decodeComponentType(key)
        if (libName === BIZ_COMPONENT_LIB_NAME) {
          const { version } = code
          const bizJsUrl = get(code, 'code.js')
          if (version && isString(bizJsUrl)) {
            const componentDetailResUrl = `${RUNTIME_CANAL_ORIGIN}/rest/canal/component/detail?id=${componentId}&version=${version}`
            const componentDetailRes: CommonRes<
              | {
                  associatedComponentId: string
                }
              | undefined
            > = await (
              await wrapFetch(fetch, (err) => {
                err.message = `${err.message} | [loadComponentJsUrlMapByLocalCl] componentDetailResUrl: ${componentDetailResUrl}`
              })(componentDetailResUrl)
            ).json()
            const associatedComponentId =
              componentDetailRes.data?.associatedComponentId
            if (associatedComponentId) {
              const [materialId, componentType] = JSON.parse(
                associatedComponentId,
              ) as [number, string]
              let componentJsMap = materialComponentJsMap[materialId]
              if (!componentJsMap) {
                componentJsMap = materialComponentJsMap[materialId] = {}
              }
              componentJsMap[componentType] = bizJsUrl
            }
          }
        }
      }),
    )
    const materialIds = keys(materialComponentJsMap)
    if (materialIds.length) {
      const drowMaterialUrl = `${RUNTIME_DROW_ORIGIN}/openapi/material/list?ids=${materialIds.join(
        ',',
      )}`
      const {
        data: pkgInfos,
      }: CommonRes<
        {
          id: number
          packageName: string
        }[]
      > = await (
        await wrapFetch(fetch, (err) => {
          err.message = `${err.message} | [loadComponentJsUrlMapByLocalCl] drowMaterialUrl: ${drowMaterialUrl}`
        })(drowMaterialUrl)
      ).json()
      const materialId = pkgInfos.find((p) => p.packageName === clSchema.name)
        ?.id
      if (materialId) {
        const componentJsMap = materialComponentJsMap[materialId]
        if (componentJsMap) {
          for (const component of clSchema.components) {
            const bizJsUrl = componentJsMap[component.type]
            const localJsUrl = get(component, 'code.code.js')
            if (isString(bizJsUrl) && isString(localJsUrl)) {
              componentJsUrlMap[bizJsUrl] = localJsUrl
            }
          }
        }
      }
    }
  }
  return componentJsUrlMap
}

/**
 * 创建常用的获取 Schema 文件函数选项
 */
export interface CreateCommonFetchSchemaOptions
  extends CreateFetchSchemaOptions {
  /**
   * 将 moduleId 加入 URL，默认：false
   */
  moduleIdInUrl?: boolean
  /**
   * （更多的）请求 headers
   */
  headers?: HeadersInit
  /**
   * 组件 js url 对照表
   */
  componentJsUrlMap?: Record<string, string>
  /**
   * 本地组件库（物料 Schema 文件链接），配置后会自动替换 js url
   */
  localCl?: string
}

/**
 * 创建常用的获取 Schema 文件函数
 * @param options 选项
 */
export function createCommonFetchSchema({
  moduleIdInUrl,
  headers,
  componentJsUrlMap,
  localCl,
  ...createFetchSchemaOptions
}: CreateCommonFetchSchemaOptions = {}): FetchSchema {
  return createAutoFetchSchema({
    ...createFetchSchemaOptions,
    transformFetchParams(params, options) {
      if (moduleIdInUrl) {
        params[0] += `?moduleId=${options.schemaId}`
      }
      if (headers) {
        params[1].headers = {
          ...params[1].headers,
          ...headers,
        }
      }
      return createFetchSchemaOptions.transformFetchParams?.(params, options)
    },
    async transformRet(ret) {
      replaceByComponentJsUrlMap(
        ret,
        process.env.NODE_ENV === 'development'
          ? {
              ...(await loadComponentJsUrlMapByLocalCl(ret, localCl)),
              ...componentJsUrlMap,
            }
          : componentJsUrlMap,
      )
      return createFetchSchemaOptions.transformRet?.(ret)
    },
  })
}

/**
 * URL 带模块 ID 的获取 Schema 文件函数
 */
export const fetchSchemaWithModuleIdInUrl =
  /*#__PURE__*/ createCommonFetchSchema({
    moduleIdInUrl: true,
  })

/**
 * 创建预览获取 Schema 文件函数
 * @param rawSchema 原始 Schema 文件
 */
export function createPreviewFetchSchema(
  rawSchema: E2EServerSchema,
  createCommonFetchSchemaOptions: CreateCommonFetchSchemaOptions,
): FetchSchema {
  return createCommonFetchSchema({
    pathname: FETCH_SCHEMA_PREVIEW_PATHNAME,
    ...createCommonFetchSchemaOptions,
    async transformFetchParams(params, options) {
      await addRawSchemaToFetchParams(rawSchema, params)
      if (
        !createCommonFetchSchemaOptions.origin &&
        !location.host.includes('localhost')
      ) {
        params[0] = `${getPreviewOriginByEnv(options.env)}${params[0]}`
      }
      return createCommonFetchSchemaOptions.transformFetchParams?.(
        params,
        options,
      )
    },
  })
}

/**
 * `@ad/kmi-plugin-api-prefetch` 预请求消费
 */
export const consumeKmiPluginApiPrefetch: NonNullable<
  CreateFetchSchemaOptions['consumePrefetch']
> = async (params, options) => {
  const { refreshType } = options
  if (refreshType !== 'default') {
    // 非首次请求不使用
    return null
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const adapter = (window as any)?.ApiPerformanceUtils?.prefetchUtil
    ?.prefetchAdapter
  if (!adapter) return null
  const [url, { method, headers, body }] = params
  if (!method || !headers || !isString(body)) return null
  const adapterOptions = {
    url,
    method: method.toLowerCase(),
    headers,
    data: JSON.parse(body),
  }
  let res = null
  try {
    const notSend = '_notSend' // 避免编译被压缩
    res =
      (
        (await adapter({ ...adapterOptions, [notSend]: true })) as {
          data: CommonResOfJava<FetchSchemaRes>
        } | null
      )?.data || null
  } catch (err) {
    console.error('consumeKmiPluginApiPrefetch err', err)
  }
  if (process.env.NODE_ENV === 'development' && !res) {
    console.log(
      `检测到安装了 @ad/kmi-plugin-api-prefetch，但没有配置大运河预请求，如需配置，可以参考下方 JSON 配置 apiPerformance：\n${JSON.stringify(
        {
          webAPI: 'fetch',
          urls: {
            [url]: {
              ...adapterOptions,
              ignoreDataKeyList: ['runtimeId', 'kfxVersion'],
              prefetch: true,
            },
          },
        },
        null,
        2,
      )}`,
    )
  }
  return res
}
