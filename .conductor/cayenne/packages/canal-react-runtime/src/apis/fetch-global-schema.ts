import { isResponseSuccess, Promisable } from '@ad/canal-shared'
import { E2EGlobalSchema } from '@ad/e2e-schema'
import { isString } from 'lodash'
import {
  CAPI_ORIGIN,
  CAPI_STAGING_ORIGIN,
  FETCH_GLOBAL_SCHEMA_DEFAULT_PATHNAME,
} from '../constants'
import { wrapFetch } from '../utils'
import {
  CommonResOfJava,
  CreateFetchSchemaFetchParams,
  FetchSchemaEnv,
} from './fetch-schema'

/**
 * 获取全局 Schema 文件函数
 */
export type FetchGlobalSchema = (
  options: FetchGlobalSchemaOptions,
) => Promise<FetchGlobalSchemaRet>

/**
 * 获取全局 Schema 文件函数结果
 */
export type FetchGlobalSchemaRet = E2EGlobalSchema

/**
 * 获取全局 Schema 文件函数选项
 */
export interface FetchGlobalSchemaOptions {
  /**
   * Schema 文件 ID
   */
  schemaId: string
  /**
   * 环境
   */
  env: FetchSchemaEnv
}

/**
 * 获取全局 Schema 文件请求
 */
export interface FetchGlobalSchemaReq
  extends Omit<FetchGlobalSchemaOptions, 'schemaId'> {
  /**
   * 模块 ID
   */
  moduleId: string | number
  /**
   * KFX 版本，用于灰度
   */
  kfxVersion?: string
}

/**
 * 获取全局 Schema 文件响应
 */
export type FetchGlobalSchemaRes = E2EGlobalSchema

/**
 * 创建获取全局 Schema 文件函数选项
 */
export interface CreateFetchGlobalSchemaOptions {
  /**
   * URL 里的源，默认为空字符串，即当前页面的源
   */
  origin?: string
  /**
   * URL 里的路径名，默认为：`'/rest/ad/canal/render/schema'`
   */
  pathname?: string
  /**
   * 转换 fetch 的参数，可以直接原地修改
   * @param params fetch 的参数
   * @param options 选项
   * @returns 新的 fetch 的参数，返回 undefined 表示用旧的值
   */
  transformFetchParams?(
    params: CreateFetchGlobalSchemaFetchParams,
    options: FetchGlobalSchemaOptions,
  ): Promisable<CreateFetchGlobalSchemaFetchParams | void>
  /**
   * 转换结果（Schema 文件）
   * @param ret 结果（Schema 文件）
   */
  transformRet?(
    ret: FetchGlobalSchemaRet,
  ): Promisable<FetchGlobalSchemaRet | void>
  /**
   * fetch 标准函数
   */
  fetch?: typeof fetch
  /**
   * 消费预请求
   * @param params fetch 的参数
   * @param options 选项
   * @returns null 表示没有可消费的预请求
   */
  consumePrefetch?(
    params: CreateFetchGlobalSchemaFetchParams,
    options: FetchGlobalSchemaOptions,
  ): Promise<null | CommonResOfJava<FetchGlobalSchemaRes>>
}

/**
 * createFetchGlobalSchema 里所用到的 fetch 函数参数
 */
export type CreateFetchGlobalSchemaFetchParams = CreateFetchSchemaFetchParams

/**
 * 创建获取全局 Schema 文件函数
 * @param options 选项
 */
export function createFetchGlobalSchema({
  origin = '',
  pathname = FETCH_GLOBAL_SCHEMA_DEFAULT_PATHNAME,
  transformFetchParams,
  transformRet,
  fetch = window.fetch,
  consumePrefetch,
}: CreateFetchGlobalSchemaOptions = {}): FetchGlobalSchema {
  return async (options) => {
    const { schemaId: moduleId, env } = options
    const url = `${origin}${pathname}`
    let fetchParams: CreateFetchGlobalSchemaFetchParams = [
      url,
      {
        method: 'POST',
        body: JSON.stringify({
          moduleId,
          env,
          kfxVersion: window?.kfxEnv?.version,
        } satisfies FetchGlobalSchemaReq),
        headers: {
          ['Content-Type']: 'application/json',
        },
        credentials: 'include',
      },
    ]
    fetchParams =
      (await transformFetchParams?.(fetchParams, options)) || fetchParams
    let res = await consumePrefetch?.(fetchParams, options)
    if (!res) {
      const fetchRes = await wrapFetch(fetch, (err) => {
        err.message = `${err.message} | [fetchGlobalSchema] moduleId: ${moduleId}, env: ${env}, url: ${url}`
      })(...fetchParams)
      if (!isResponseSuccess(fetchRes)) {
        throw new Error(
          `[fetchGlobalSchema] invalid response status: ${fetchRes.status}, moduleId: ${moduleId}, env: ${env}, url: ${url}`,
        )
      }
      res = (await fetchRes.json()) as CommonResOfJava<FetchGlobalSchemaRes>
    }
    if (res.result !== 1) {
      throw new Error(
        `[fetchGlobalSchema] invalid response result: ${res.result}, message: ${res.message}, moduleId: ${moduleId}, env: ${env}, url: ${url}`,
      )
    }
    let ret: FetchGlobalSchemaRet = res.data
    ret = (await transformRet?.(ret)) || ret
    return ret
  }
}

/**
 * 默认的获取全局 Schema 文件函数，默认从业务方的域名获取
 */
export const defaultFetchGlobalSchema = createFetchGlobalSchema()

/**
 * 从 capi 获取全局 Schema 文件函数
 */
export const fetchGlobalSchemaFromCapiE = createFetchGlobalSchema({
  origin: CAPI_ORIGIN,
})

/**
 * 从 capi staging 获取全局 Schema 文件函数
 */
export const fetchGlobalSchemaFromCapiStaging = createFetchGlobalSchema({
  origin: CAPI_STAGING_ORIGIN,
})

/**
 * `@ad/kmi-plugin-api-prefetch` 预请求消费，全局 Schema
 */
export const consumeKmiPluginApiPrefetchForGlobalSchema: NonNullable<
  CreateFetchGlobalSchemaOptions['consumePrefetch']
> = async (params) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const adapter = (window as any)?.ApiPerformanceUtils?.prefetchUtil
    ?.prefetchAdapter
  if (!adapter) return null
  const [url, { method, headers, body }] = params
  if (!method || !headers || !isString(body)) return null
  const adapterOptions = {
    url,
    method: method.toLowerCase(),
    headers,
    data: JSON.parse(body),
  }
  let res = null
  try {
    const notSend = '_notSend' // 避免编译被压缩
    res =
      (
        (await adapter({ ...adapterOptions, [notSend]: true })) as {
          data: CommonResOfJava<FetchGlobalSchemaRes>
        } | null
      )?.data || null
  } catch (err) {
    console.error('consumeKmiPluginApiPrefetchForGlobalSchema err', err)
  }
  if (process.env.NODE_ENV === 'development' && !res) {
    console.log(
      `检测到安装了 @ad/kmi-plugin-api-prefetch，但没有配置大运河预请求，如需配置，可以参考下方 JSON 配置 apiPerformance：\n${JSON.stringify(
        {
          webAPI: 'fetch',
          urls: {
            [url]: {
              ...adapterOptions,
              ignoreDataKeyList: ['kfxVersion'],
              prefetch: true,
            },
          },
        },
        null,
        2,
      )}`,
    )
  }
  return res
}
