export * from './cdn-dt'

/**
 * 断言 weblog 是否已禁用
 */
let assertWeblogIsDisabled = false

/**
 * 禁用断言 weblog
 */
export function disableAssertWeblog(): void {
  assertWeblogIsDisabled = true
}

/**
 * 应该断言 weblog
 */
export function shouldAssertWeblog(): boolean {
  return !assertWeblogIsDisabled
}

/**
 * 捕获异常是否已禁用
 */
let captureExceptionIsDisabled = false

/**
 * 禁用捕获异常
 */
export function disableCaptureException(): void {
  captureExceptionIsDisabled = true
}

/**
 * 应该捕获异常
 */
export function shouldCaptureException(): boolean {
  return !captureExceptionIsDisabled
}
