import { isString } from 'lodash'

/**
 * CDN 容灾配置
 */
export interface CdnDtConfig {
  /**
   * 需要被替换的域名
   */
  replaceDomains: string[]
  /**
   * 替换后的 cdn 路径，默认读取 window.cdn_public_path
   */
  cdnPublicPath?: string
}

/**
 * CDN 容灾替换，替换字符串里的所有相关域名
 * @param str 字符串
 */
export function cdnDtReplace(str: string): string {
  return internalCdnDtReplace ? internalCdnDtReplace(str) : str
}

/**
 * CDN 容灾替换任意 JSON
 */
export function cdnDtReplaceJson<T>(json: T): T {
  return internalCdnDtReplace
    ? JSON.parse(internalCdnDtReplace(JSON.stringify(json)))
    : json
}

/**
 * 内部：CDN 容灾替换
 */
let internalCdnDtReplace: ((str: string) => string) | undefined

/**
 * 设置大运河 CDN 容灾配置
 * @param cdnDT CDN 容灾配置
 */
export function setCanalCdnDt({
  replaceDomains,
  cdnPublicPath = (window as unknown as Record<string, string | undefined>)
    .cdn_public_path,
}: CdnDtConfig): void {
  if (!isString(cdnPublicPath)) return
  const targetPath =
    isHttpOrHttpsUrl(cdnPublicPath) || isRelativeProtocolUrl(cdnPublicPath)
      ? cdnPublicPath
      : `//${cdnPublicPath}`
  const regs = replaceDomains.map(replaceDomainToRegExp)
  internalCdnDtReplace = ((str) => {
    for (const reg of regs) {
      str = str.replace(reg, targetPath)
    }
    return str
  }) as typeof internalCdnDtReplace
}

/**
 * 需要被替换的域名转正则表达式
 * @param replaceDomain 需要被替换的域名
 */
function replaceDomainToRegExp(replaceDomain: string): RegExp {
  if (isHttpOrHttpsUrl(replaceDomain)) {
    return new RegExp(replaceDomain, 'g')
  }
  if (!isRelativeProtocolUrl(replaceDomain)) {
    replaceDomain = `//${replaceDomain}`
  }
  return new RegExp(`(https:)?(http:)?${replaceDomain}`, 'g')
}

/**
 * 是否是 http 或 https 的 url
 * @param url url
 */
function isHttpOrHttpsUrl(url: string): boolean {
  return url.startsWith('http:') || url.startsWith('https:')
}

/**
 * 是否是相对协议的 url
 * @param url url
 */
function isRelativeProtocolUrl(url: string): boolean {
  return url.startsWith('//')
}
