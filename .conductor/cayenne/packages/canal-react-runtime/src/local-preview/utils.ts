import { createQuickEncoder } from '@ad/canal-shared'
import { createEndpoint, fromInsideIframe } from '@remote-ui/rpc'
import { once } from 'lodash'
import { DesignerForLocalPreviewerRpcApis } from './types'

/**
 * 获取大运河模块 ID
 */
export const getCanalModuleId = once((): string | null => {
  return new URLSearchParams(location.search).get('__canalModuleId')
})

/**
 * 获取（业务）域代码
 */
export const getCanalDomainCode = once((): string | null => {
  return new URLSearchParams(location.search).get('domainCode')
})

/**
 * 获取终端
 */
const getEndpoint = once(() =>
  createEndpoint<DesignerForLocalPreviewerRpcApis>(fromInsideIframe(), {
    createEncoder: createQuickEncoder,
  }),
)

/**
 * 从设计器获取原始 Schema 文件
 */
export const getRawSchemaFromDesigner = once(() => {
  return getEndpoint().call.getRawSchemaFromDesigner()
})
