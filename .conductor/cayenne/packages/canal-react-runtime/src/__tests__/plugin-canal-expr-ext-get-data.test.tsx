import { omit } from 'lodash'
import React, { FC } from 'react'
import TestRenderer from 'react-test-renderer'
import { createCanalReactRuntime } from '../runtime'

describe('pluginQxExpressionExtensionGetData', () => {
  it('simple', () => {
    const View: FC = (props) => <div {...omit(props, 'value', 'onChange')} />
    const Text: FC = (props) => <span {...omit(props, 'value', 'onChange')} />
    const runtime = createCanalReactRuntime({
      schema: {
        schemaVersion: '0.0.1',
        view: {
          type: 'View',
          id: 'v1',
          name: 'nv1',
          children: [
            {
              type: 'Text',
              id: 't1',
              name: 'nt1',
              props: {
                color: 'red',
                className: {
                  type: 'get-data',
                  path: ['oB'],
                },
              },
            },
            {
              type: 'Text',
              id: 't2',
              name: 'nt2',
              children: [
                {
                  type: 'Text',
                  id: 't3',
                  name: 'nt3',
                },
              ],
            },
          ],
        },
        data: {
          oB: 'asd',
        },
      },
      config: {
        dependencies: {
          ['']: { View, Text },
        },
      },
    })
    expect(
      TestRenderer.create(<runtime.container.ReactRenderer />).toJSON(),
    ).toEqual(
      TestRenderer.create(
        <div>
          <span color="red" className="asd" />
          <span>
            <span />
          </span>
        </div>,
      ).toJSON(),
    )
  })
})
