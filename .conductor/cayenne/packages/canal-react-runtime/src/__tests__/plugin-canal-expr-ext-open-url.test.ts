/* eslint-disable @typescript-eslint/no-explicit-any */
import { createCanalReactRuntime } from '../runtime'

describe('pluginCanalExpressionExtensionOpenUrl', () => {
  it('simple', () => {
    const openLogs: any[] = []
    const locationLogs: any[] = []
    Object.assign(window, {
      open(...args: any[]) {
        openLogs.push(args)
        return null
      },
    })
    Object.defineProperty(window, 'location', {
      set(v: any) {
        locationLogs.push(v)
      },
    })
    const runtime = createCanalReactRuntime({
      schema: {
        schemaVersion: '0.0.1',
        view: {
          type: 'Root',
          id: 'r1',
          name: '根组件',
          props: {
            onClick1: {
              type: 'open-url',
              url: 'u111',
            },
            onClick2: {
              type: 'open-url',
              url: {
                type: 'get-data',
                path: ['uuuu'],
              },
            },
            onClick3: {
              type: 'open-url',
              url: 'u33333',
              inPlace: true,
            },
          },
        },
        data: {
          uuuu: 'u2222',
        },
      },
    })
    ;(runtime.container.view.props.onClick1 as any)()
    ;(runtime.container.view.props.onClick2 as any)()
    ;(runtime.container.view.props.onClick3 as any)()
    expect(openLogs).toEqual([['u111'], ['u2222']])
    expect(locationLogs).toEqual(['u33333'])
  })
})
