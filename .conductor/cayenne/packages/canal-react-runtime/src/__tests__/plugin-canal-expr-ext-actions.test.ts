/* eslint-disable @typescript-eslint/no-explicit-any */
import { createCanalReactRuntime } from '../runtime'

describe('pluginCanalExpressionExtensionActions', () => {
  it('simple', async () => {
    const runtime = createCanalReactRuntime({
      schema: {
        schemaVersion: '0.0.1',
        view: {
          type: 'Root',
          id: 'r1',
          name: '根组件',
          props: {
            onClick1: {
              type: 'actions',
              fns: [
                {
                  type: 'get-data',
                  path: ['fn1'],
                },
                {
                  type: 'get-data',
                  path: ['fn2'],
                },
              ],
            },
            onClick2: {
              type: 'actions',
              fns: [
                {
                  type: 'get-data',
                  path: ['fn1'],
                },
                {
                  type: 'get-data',
                  path: ['fn2'],
                },
              ],
              parallel: false,
            },
          },
        },
        data: {
          fn1: () => 1,
          fn2: () => Promise.resolve(2),
        },
      },
    })
    expect((runtime.container.view.props.onClick1 as any)()).toEqual([
      1,
      Promise.resolve(),
    ])
    expect(await (runtime.container.view.props.onClick1 as any)()[1]).toEqual(2)
    expect(await (runtime.container.view.props.onClick2 as any)()).toEqual([
      1, 2,
    ])
  })
})
