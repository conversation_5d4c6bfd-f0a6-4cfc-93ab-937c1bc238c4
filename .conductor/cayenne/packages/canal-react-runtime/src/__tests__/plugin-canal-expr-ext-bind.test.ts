/* eslint-disable @typescript-eslint/no-explicit-any */
import { createCanalReactRuntime } from '../runtime'

describe('pluginCanalExpressionExtensionBind', () => {
  it('simple', () => {
    const fn = jest.fn()
    const runtime = createCanalReactRuntime({
      schema: {
        schemaVersion: '0.0.1',
        view: {
          type: 'Root',
          id: 'r1',
          name: '根组件',
          props: {
            onClick1: {
              type: 'bind',
              fn: {
                type: 'get-data',
                path: ['fn'],
              },
            },
            onClick2: {
              type: 'bind',
              fn: {
                type: 'get-data',
                path: ['fn'],
              },
              this: {
                type: 'get-data',
                path: ['uuuu'],
              },
              args: [
                1,
                {
                  type: 'get-data',
                  path: ['uuuu'],
                },
              ],
            },
          },
        },
        data: {
          uuuu: 'u2222',
          fn,
        },
      },
    })
    ;(runtime.container.view.props.onClick1 as any)()
    ;(runtime.container.view.props.onClick2 as any)()
    expect(fn.mock.calls).toEqual([[], [1, 'u2222']])
    expect(fn.mock.contexts).toEqual([undefined, 'u2222'])
  })
})
