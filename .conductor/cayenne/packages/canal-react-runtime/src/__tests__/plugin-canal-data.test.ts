import { autorun } from 'mobx'
import { createCanalReactRuntime } from '../runtime'

describe('pluginCanalData', () => {
  it('simple', () => {
    const runtime = createCanalReactRuntime({
      schema: {
        schemaVersion: '0.0.1',
        view: {
          type: 'Root',
          id: 'r1',
          name: '根组件',
        },
        data: {
          a: 3,
          b: 4,
        },
      },
    })
    expect(runtime.container.data).toEqual({ a: 3, b: 4 })
    const counter = {
      countData: 0,
      countA: 0,
      countB: 0,
      countC: 0,
    }
    autorun(() => {
      void runtime.container.data
      counter.countData++
    })
    autorun(() => {
      void runtime.container.data.a
      counter.countA++
    })
    autorun(() => {
      void runtime.container.data.b
      counter.countB++
    })
    autorun(() => {
      void runtime.container.data.c
      counter.countC++
    })
    expect(counter).toEqual({
      countData: 1,
      countA: 1,
      countB: 1,
      countC: 1,
    })
    runtime.container.setData({ b: 5, c: 6 })
    expect(counter).toEqual({
      countData: 1,
      countA: 1,
      countB: 2,
      countC: 2,
    })
    expect(runtime.container.data).toEqual({ a: 3, b: 5, c: 6 })
  })

  it('config.data', () => {
    expect(
      createCanalReactRuntime({
        schema: {
          schemaVersion: '0.0.1',
          view: {
            type: 'Root',
            id: 'r1',
            name: '根组件',
          },
          data: {
            a: 3,
            b: 4,
          },
        },
        config: {
          data: {
            b: 44,
            c: 55,
          },
        },
      }).container.data,
    ).toEqual({ a: 3, b: 44, c: 55 })
    expect(
      createCanalReactRuntime({
        schema: {
          schemaVersion: '0.0.1',
          view: {
            type: 'Root',
            id: 'r1',
            name: '根组件',
          },
          data: {
            a: 3,
            b: 4,
          },
        },
        config: {
          data: (oldData) => {
            return { xx: 3, oldData }
          },
        },
      }).container.data,
    ).toEqual({
      oldData: {
        a: 3,
        b: 4,
      },
      xx: 3,
    })
  })
})
