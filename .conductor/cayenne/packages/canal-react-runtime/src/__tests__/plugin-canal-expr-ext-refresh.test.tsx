/* eslint-disable @typescript-eslint/no-explicit-any */
import { omit } from 'lodash'
import React, { FC } from 'react'
import TestRenderer from 'react-test-renderer'
import { createCanalReactRuntime } from '../runtime'

describe('canalExpressionExtensionRefresh', () => {
  it('simple', async () => {
    const View: FC = (props) => <div {...omit(props, 'value', 'onChange')} />
    const Text: FC = (props) => <span {...omit(props, 'value', 'onChange')} />
    const runtime = createCanalReactRuntime({
      schema: {
        schemaVersion: '0.0.1',
        view: {
          type: 'View',
          id: 'v1',
          name: 'nv1',
          children: [
            {
              type: 'Text',
              id: 't1',
              name: 'nt1',
              props: {
                color: 'red',
                onClick: {
                  type: 'refresh',
                },
              },
            },
            {
              type: 'Text',
              id: 't2',
              name: 'nt2',
              children: [
                {
                  type: 'Text',
                  id: 't3',
                  name: 'nt3',
                },
              ],
            },
          ],
        },
      },
      config: {
        dependencies: {
          ['']: { View, Text },
        },
        schemaId: 'abc',
        fetchSchema: async (options) => {
          return {
            schemaVersion: '0.0.1',
            view: {
              type: 'View',
              id: 'v1',
              name: 'nv1',
              props: {
                ['data-x']: {
                  type: 'static',
                  value: options,
                },
              },
            },
          }
        },
      },
    })
    const jsx = <runtime.container.ReactRenderer />
    await TestRenderer.act(async () => {
      await (runtime.container.view.children[0] as any).props.onClick()
    })
    expect(TestRenderer.create(jsx).toJSON()).toEqual(
      TestRenderer.create(
        <div
          data-x={{
            runtimeId: 'abc:1',
            schemaId: 'abc',
            refreshType: 'default',
            params: {},
            env: 'production',
            mock: false,
            version: '2',
          }}
        />,
      ).toJSON(),
    )
  })
})
