/* eslint-disable @typescript-eslint/no-explicit-any */
import { createCanalReactRuntime } from '../runtime'

describe('pluginCanalExpressionExtensionTrack', () => {
  it('simple', () => {
    const fn = jest.fn()
    const runtime = createCanalReactRuntime({
      schema: {
        schemaVersion: '0.0.1',
        view: {
          type: 'Root',
          id: 'r1',
          name: '根组件',
          props: {
            onClick1: {
              type: 'track',
              trackId: 'a',
            },
            onClick2: {
              type: 'track',
              trackId: 'b',
            },
          },
        },
        tracks: [
          {
            id: 'a',
            eventType: 'AUTO',
            eventOptions: {
              params: { a: 3 },
            },
          },
          {
            id: 'b',
            eventType: 'CLICK',
            eventOptions: {},
            canRepeat: false,
          },
        ],
      },
      config: {
        weblog: {
          sendImmediately: fn,
        } as any,
      },
    })
    ;(runtime.container.view.props.onClick1 as any)()
    ;(runtime.container.view.props.onClick2 as any)()
    ;(runtime.container.view.props.onClick1 as any)()
    ;(runtime.container.view.props.onClick2 as any)()
    expect(fn.mock.calls).toEqual([
      [
        'AUTO',
        {
          params: { a: 3 },
        },
      ],
      ['CLICK', {}],
      [
        'AUTO',
        {
          params: { a: 3 },
        },
      ],
    ])
  })
})
