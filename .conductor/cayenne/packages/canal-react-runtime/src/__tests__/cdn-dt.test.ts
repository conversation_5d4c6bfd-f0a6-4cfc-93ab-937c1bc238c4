import { cdnDtReplaceJson, setCanalCdnDt } from '../global-configs'

describe('cdn dt', () => {
  it('cdnDtReplace', () => {
    const json = {
      schemaVersion: '0.0.1',
      componentCodes: {
        ['@ad/canal-biz-components::todo']: {
          code: {
            js: 'https://p1.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js',
          },
          version: '5',
        },
        ['@ad/canal-biz-components::17']: {
          code: {
            js: 'https://js-ad.a.yximgs.com/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.02657d05.js',
          },
          version: '18',
        },
      },
    }
    expect(cdnDtReplaceJson(json)).toBe(json)
    setCanalCdnDt({
      replaceDomains: [],
    })
    expect(cdnDtReplaceJson(json)).toBe(json)
    ;(window as unknown as Record<string, string>).cdn_public_path =
      '//var----ad-web----var.adkwai.com'
    setCanalCdnDt({
      replaceDomains: [],
    })
    expect(cdnDtReplaceJson(json)).not.toBe(json)
    expect(cdnDtReplaceJson(json)).toEqual(json)
    setCanalCdnDt({
      replaceDomains: ['p1.adkwai.com'],
    })
    expect(cdnDtReplaceJson(json)).toEqual({
      schemaVersion: '0.0.1',
      componentCodes: {
        ['@ad/canal-biz-components::todo']: {
          code: {
            js: '//var----ad-web----var.adkwai.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js',
          },
          version: '5',
        },
        ['@ad/canal-biz-components::17']: {
          code: {
            js: 'https://js-ad.a.yximgs.com/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.02657d05.js',
          },
          version: '18',
        },
      },
    })
    setCanalCdnDt({
      replaceDomains: [
        'https://p1.adkwai.com/kos/nlav12572/',
        'js-ad.a.yximgs.com/',
      ],
      cdnPublicPath: 'https://a.b.c/jjjx/',
    })
    expect(cdnDtReplaceJson(json)).toEqual({
      schemaVersion: '0.0.1',
      componentCodes: {
        ['@ad/canal-biz-components::todo']: {
          code: {
            js: 'https://a.b.c/jjjx/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js',
          },
          version: '5',
        },
        ['@ad/canal-biz-components::17']: {
          code: {
            js: 'https://a.b.c/jjjx/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.02657d05.js',
          },
          version: '18',
        },
      },
    })
    setCanalCdnDt({
      replaceDomains: [
        'https://p1.adkwai.com/kos/nlav12572/',
        'js-ad.a.yximgs.com/',
      ],
      cdnPublicPath: 'http://a.b.c/jjjx/',
    })
    expect(cdnDtReplaceJson(json)).toEqual({
      schemaVersion: '0.0.1',
      componentCodes: {
        ['@ad/canal-biz-components::todo']: {
          code: {
            js: 'http://a.b.c/jjjx/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js',
          },
          version: '5',
        },
        ['@ad/canal-biz-components::17']: {
          code: {
            js: 'http://a.b.c/jjjx/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.02657d05.js',
          },
          version: '18',
        },
      },
    })
    setCanalCdnDt({
      replaceDomains: ['p1.adkwai.com'],
      cdnPublicPath: 'a.ad.com',
    })
    expect(cdnDtReplaceJson(json)).toEqual({
      schemaVersion: '0.0.1',
      componentCodes: {
        ['@ad/canal-biz-components::todo']: {
          code: {
            js: '//a.ad.com/kos/nlav12572/production/@ad/canal-biz-component-demo@0.0.5/dist/button/index.069f5f93.js',
          },
          version: '5',
        },
        ['@ad/canal-biz-components::17']: {
          code: {
            js: 'https://js-ad.a.yximgs.com/kos/nlav12572/production/@ad/luopan-inner-component@0.0.1/dist/card/index.02657d05.js',
          },
          version: '18',
        },
      },
    })
  })
})
