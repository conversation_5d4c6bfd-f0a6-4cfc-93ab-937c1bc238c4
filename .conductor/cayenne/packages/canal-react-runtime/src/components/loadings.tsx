import { LoadingProps } from '@ad/canal-react-component-context'
import { ContainerProps } from '@ad/canal-react-components'
import { E2ESchema } from '@ad/e2e-schema'
import { dfsGenComponentDetailBySchema } from '@kael/schema-utils'
import { Spin } from '@m-ui/react'
import { isBoolean } from 'lodash'
import React, { FC, memo, useMemo } from 'react'
import styled from 'styled-components'
import { INTERNAL_COMPONENT_TYPE_CONTAINER } from '../constants'

/**
 * MUI 加载中
 */
export const MUILoading: FC = () => {
  return (
    <LoadingContainer>
      <Spin />
    </LoadingContainer>
  )
}

export const LoadingContainer = styled.div`
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.6);
`

/**
 * 模块级加载中，只在模块级显示加载中
 */
export const ModuleLoading: FC<LoadingProps> = memo(({ position }) => {
  return position === 'runtime' ? <MUILoading /> : null
})

/**
 * 初始化和容器级加载中，只在初始化和容器级显示加载中
 */
export const InitAndContainerLoading: FC<LoadingProps> = memo(
  ({ position, refreshingType }) => {
    return (position === 'runtime' && refreshingType === 'default') ||
      position === 'container' ? (
      <MUILoading />
    ) : null
  },
)

/**
 * 有容器启用了加载中（缓存）表
 */
export const hasContainerEnableLoadingMap = new WeakMap<E2ESchema, boolean>()

/**
 * 有容器启用了加载中
 * @param schema
 * @returns
 */
export function hasContainerEnableLoading(schema: E2ESchema): boolean {
  let ret = hasContainerEnableLoadingMap.get(schema)
  if (isBoolean(ret)) {
    return ret
  }
  ret = ((): boolean => {
    for (const cd of dfsGenComponentDetailBySchema(schema)) {
      if (
        cd.component.type === INTERNAL_COMPONENT_TYPE_CONTAINER &&
        (cd.component.props as ContainerProps | undefined)?.enableLoading
      ) {
        return true
      }
    }
    return false
  })()
  hasContainerEnableLoadingMap.set(schema, ret)
  return ret
}

/**
 * 自动加载中，会分析 schema，自动选择 ModuleLoading 或 InitAndContainerLoading
 */
export const AutoLoading: FC<LoadingProps> = memo((props) => {
  const { schema } = props
  const hasEnableLoading = useMemo(
    () => hasContainerEnableLoading(schema),
    [schema],
  )
  return hasEnableLoading ? (
    <InitAndContainerLoading {...props} />
  ) : (
    <ModuleLoading {...props} />
  )
})
