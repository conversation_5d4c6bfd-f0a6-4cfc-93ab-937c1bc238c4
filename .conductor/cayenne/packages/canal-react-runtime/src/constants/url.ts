import { PLATFORM_ORIGIN } from '@ad/canal-shared'

/**
 * 运行时大运河源
 */
export const RUNTIME_CANAL_ORIGIN = process.env.CANAL_ORIGIN || PLATFORM_ORIGIN

/**
 * 运行时物料平台源
 */
export const RUNTIME_DROW_ORIGIN =
  RUNTIME_CANAL_ORIGIN === PLATFORM_ORIGIN
    ? 'https://drow.corp.kuaishou.com'
    : 'https://drow.staging.kuaishou.com'

/**
 * capi 源
 */
export const CAPI_ORIGIN = 'https://capi.e.kuaishou.com'

/**
 * capi staging 源
 */
export const CAPI_STAGING_ORIGIN = 'https://capi.staging.kuaishou.com'

/**
 * 获取 Schema 文件默认的路径名
 */
export const FETCH_SCHEMA_DEFAULT_PATHNAME = '/rest/ad/canal/render/schema'

/**
 * 预览 Schema 文件默认的路径名
 */
export const FETCH_SCHEMA_PREVIEW_PATHNAME = '/rest/ad/canal/preview/schema'

/**
 * 获取全局 Schema 文件默认的路径名
 */
export const FETCH_GLOBAL_SCHEMA_DEFAULT_PATHNAME =
  '/rest/ad/canal/render/global-schema'
