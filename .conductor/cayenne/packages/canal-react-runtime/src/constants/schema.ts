import { INTERNAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import { E2ESchema } from '@ad/e2e-schema'
import { encodeComponentType } from '@kael/schema-utils'

/**
 * 加载中 Schema
 */
export const LOADING_SCHEMA: E2ESchema = {
  schemaVersion: '0.1.0',
  view: {
    type: encodeComponentType(INTERNAL_COMPONENT_LIB_NAME, 'Empty'),
    id: `${INTERNAL_COMPONENT_LIB_NAME}:0`,
    name: '',
  },
}

/**
 * 内部组件类型：容器
 */
export const INTERNAL_COMPONENT_TYPE_CONTAINER = encodeComponentType(
  INTERNAL_COMPONENT_LIB_NAME,
  'Container',
)

/**
 * 业务组件库名
 */
export const BIZ_COMPONENT_LIB_NAME = '@ad/canal-biz-components'
