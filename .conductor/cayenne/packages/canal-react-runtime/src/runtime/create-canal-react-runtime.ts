import { LoadingProps } from '@ad/canal-react-component-context'
import * as canalReactComponents from '@ad/canal-react-components'
import { INTERNAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import { E2ESchema, E2EServerSchema } from '@ad/e2e-schema'
import { b2fE2ESchema, validateE2EServerSchema } from '@ad/e2e-schema-utils'
import {
  DefaultPluginsPropertiesExt,
  Plugin,
  PluginDependenciesPropertiesExt,
  PurePlugin,
  Runtime,
} from '@kael/runtime'
import '@kael/runtime-plugin-react-renderer'
import '@kael/schema'
import type { Weblog } from '@ks/weblogger'
import { FC } from 'react'
import { FetchSchema, FetchSchemaEnv } from '../apis'
import { LOADING_SCHEMA } from '../constants'
import {
  ConfigParams,
  PluginCanalDataPropertiesExt,
  PluginCanalExpressionExtensionRefreshPropertiesExt,
  PluginCanalLoadingPropertiesExt,
  PluginCanalParentRuntimePropertiesExt,
  PluginCanalTrackPropertiesExt,
  canalReactPlugins,
} from '../plugins'
import { ComponentCodeFallback } from '../utils'

/**
 * 创建大运河 React 运行时选项
 */
export interface CreateCanalReactRuntimeOptions {
  /**
   * 端到端 Schema
   */
  schema?: E2ESchema | E2EServerSchema
  /**
   * Schema 文件 ID
   *
   * 会覆盖 config.schemaId
   */
  schemaId?: string | number
  /**
   * 配置
   */
  config?: AutoMergedConfig
  /**
   * 额外需要加载的插件
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  plugins?: PurePlugin<any, any>[]
  /**
   * 获取 Schema 文件
   *
   * 会覆盖 config.fetchSchema
   */
  fetchSchema?: FetchSchema
  /**
   * 配置参数
   *
   * 会覆盖 config.params，整体覆盖
   */
  params?: ConfigParams
  /**
   * 环境，默认走生产环境
   *
   * 会覆盖 config.env
   */
  env?: FetchSchemaEnv
  /**
   * 依赖包
   *
   * 会覆盖 config.dependencies，按键值覆盖
   */
  dependencies?: NonNullable<AutoMergedConfig>['dependencies']
  /**
   * 默认组件库
   *
   * 会覆盖 config.dependencies['']，按键值覆盖
   */
  defaultComponentLib?: Record<string, unknown>
  /**
   * 加载组件
   *
   * 会覆盖 config.dependencies[INTERNAL_COMPONENT_LIB_NAME].Loading
   */
  Loading?: FC<LoadingProps>
  /**
   * （初始）数据，优先级仅高于初始 Schema 里的 data 字段
   *
   * 会覆盖 config.data
   */
  data?:
    | Record<string, unknown>
    | ((oldData: Record<string, unknown>) => Record<string, unknown>)
  /**
   * Weblog 实例，由外部传入
   *
   * 会覆盖 config.weblog
   */
  weblog?: Weblog
  /**
   * 父运行时
   */
  parentRuntime?: CanalReactRuntime
  /**
   * 子模块组件 ID
   */
  submoduleComponentId?: string
  /**
   * 开启 Mock
   */
  mock?: boolean
  /**
   * 组件代码回退
   */
  componentCodeFallback?: ComponentCodeFallback
  /**
   * 加载之后的渲染副作用回调
   */
  onRenderEffectAfterLoading?(moduleId: string): void
  /**
   * 首次加载之后的渲染副作用回调
   */
  onRenderEffectAfterFirstLoading?(moduleId: string): void
}

/**
 * 自动合并后的配置
 */
export type AutoMergedConfig = (PluginDependenciesPropertiesExt &
  PluginCanalDataPropertiesExt &
  PluginCanalTrackPropertiesExt &
  PluginCanalExpressionExtensionRefreshPropertiesExt &
  PluginCanalParentRuntimePropertiesExt &
  PluginCanalLoadingPropertiesExt)['runtimeConstructorOptions']['config']

/**
 * 创建大运河 React 运行时
 * @param options 创建大运河 React 运行时选项
 */
// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export function createCanalReactRuntime({
  schema,
  schemaId,
  config,
  plugins = [],
  fetchSchema,
  params,
  env,
  dependencies,
  defaultComponentLib,
  Loading,
  data,
  weblog,
  parentRuntime,
  submoduleComponentId,
  mock,
  componentCodeFallback,
  onRenderEffectAfterLoading,
  onRenderEffectAfterFirstLoading,
}: CreateCanalReactRuntimeOptions) {
  if (!schema && !schemaId) {
    throw new Error('Please create Runtime with schemaId or schema.')
  }
  const finalSchema = validateE2EServerSchema(schema)
    ? b2fE2ESchema(schema)
    : schema || LOADING_SCHEMA
  const finalConfig = { ...config }
  finalConfig.schemaId = schemaId || finalConfig.schemaId
  finalConfig.fetchSchema = fetchSchema || finalConfig.fetchSchema
  finalConfig.params = params || finalConfig.params
  finalConfig.env = env || finalConfig.env
  finalConfig.dependencies = {
    ...finalConfig.dependencies,
    ...dependencies,
  }
  finalConfig.dependencies[''] = {
    ...(finalConfig.dependencies[''] as Record<string, unknown>),
    ...defaultComponentLib,
  }
  // 内部组件库可以被外部按组件覆盖
  finalConfig.dependencies[INTERNAL_COMPONENT_LIB_NAME] = {
    ...canalReactComponents,
    ...(finalConfig.dependencies[INTERNAL_COMPONENT_LIB_NAME] as Record<
      string,
      unknown
    >),
    ...(Loading ? { Loading } : {}),
  }
  finalConfig.data = data || finalConfig.data
  finalConfig.weblog = weblog || finalConfig.weblog
  finalConfig.parentRuntime = parentRuntime || finalConfig.parentRuntime
  finalConfig.submoduleComponentId =
    submoduleComponentId || finalConfig.submoduleComponentId
  finalConfig.mock = mock ?? finalConfig.mock
  finalConfig.componentCodeFallback =
    componentCodeFallback || finalConfig.componentCodeFallback
  finalConfig.onRenderEffectAfterLoading =
    onRenderEffectAfterLoading || finalConfig.onRenderEffectAfterLoading
  finalConfig.onRenderEffectAfterFirstLoading =
    onRenderEffectAfterFirstLoading ||
    finalConfig.onRenderEffectAfterFirstLoading
  const r = new Runtime<DefaultPluginsPropertiesExt<E2ESchema>, E2ESchema>()
    .use(
      ...canalReactPlugins,
      ...(plugins as Plugin<
        DefaultPluginsPropertiesExt<E2ESchema>,
        E2ESchema
      >[]),
    )
    .start({
      schema: finalSchema,
      config: finalConfig,
    })
  if (!schema) {
    r.container.initRefresh()
  }
  if (process.env.NODE_ENV === 'development' && !parentRuntime) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ;(window as any).debugCanalReactRuntime = r
  }
  return r
}

/**
 * 大运河 React 运行时
 */
export type CanalReactRuntime = ReturnType<typeof createCanalReactRuntime>
