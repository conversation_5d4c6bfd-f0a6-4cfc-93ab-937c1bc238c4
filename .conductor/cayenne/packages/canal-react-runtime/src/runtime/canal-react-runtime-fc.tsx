import {
  CanalReactRuntimeSubmoduleProps,
  canalReactRuntimeComponentContext,
  canalReactRuntimeSubmoduleFCContext,
} from '@ad/canal-react-component-context'
import { isFunction } from 'lodash'
import { fillRef } from 'rc-util/es/ref'
import React, {
  CSSProperties,
  ComponentType,
  FC,
  MutableRefObject,
  Ref,
  createContext,
  useContext,
  useEffect,
  useMemo,
} from 'react'
import {
  ErrorBoundary,
  ErrorBoundaryProps,
  type FallbackProps,
} from 'react-error-boundary'
import { useLatest } from 'react-use'
import { ConfigParams } from '../plugins'
import { captureException } from '../weblog'
import {
  CanalReactRuntime,
  CreateCanalReactRuntimeOptions,
  createCanalReactRuntime,
} from './create-canal-react-runtime'

/**
 * 大运河 React 运行时（React）函数组件属性，基础部分
 */
export type CanalReactRuntimeFCBaseProps = CreateCanalReactRuntimeOptions & {
  /**
   * 模块 ID，alias schemaId
   * * 打开设计器 URL: https://canal.corp.kuaishou.com/open-designer?moduleId=${moduleId}
   *   * 会自动打开最新变更内的该模块的编辑页面
   */
  moduleId?: string | number
  /**
   * 大运河 React 运行时引用
   */
  refCanalReactRuntime?:
    | MutableRefObject<CanalReactRuntime | undefined>
    | Ref<CanalReactRuntime | undefined>
  /**
   * 配置参数
   *
   * 会覆盖 config.params，整体覆盖
   *
   * 而且变化时（使用 lodash 的 isEqual 比较），会自动刷新 Schema，刷新类型: `'outside-params'`
   */
  params?: ConfigParams
  /**
   * 最大嵌套层数，默认为 9
   */
  maxNestCount?: number
  /**
   * 是否添加一个 relative 的 div 作为包装，默认为 true
   */
  divWrapper?: boolean
  /**
   * div 包装样式
   */
  divWrapperStyle?: CSSProperties
  /**
   * 同 divWrapperStyle
   */
  style?: CSSProperties
  /**
   * div 包装 css class
   */
  className?: string
  /**
   * 回退组件，当运行渲染出错时需要渲染的组件
   */
  FallbackComponent?: ComponentType<FallbackProps>
  /**
   * 渲染错误事件
   */
  onRenderError?: ErrorBoundaryProps['onError']
  /**
   * 转换子模块属性
   * @param props 属性
   */
  transformSubmoduleProps?: (
    props: CanalReactRuntimeFCProps,
  ) => CanalReactRuntimeFCProps | void
}

/**
 * 大运河 React 运行时（React）函数组件属性
 */
export type CanalReactRuntimeFCProps =
  | (Omit<CanalReactRuntimeFCBaseProps, 'schema'> &
      Required<Pick<CanalReactRuntimeFCBaseProps, 'schema'>>)
  | (Omit<CanalReactRuntimeFCBaseProps, 'moduleId'> &
      Required<Pick<CanalReactRuntimeFCBaseProps, 'moduleId'>>)
  | (Omit<CanalReactRuntimeFCBaseProps, 'schemaId'> &
      Required<Pick<CanalReactRuntimeFCBaseProps, 'schemaId'>>)

/**
 * 大运河 React 运行时（React）函数组件
 */
export const CanalReactRuntimeFC: FC<CanalReactRuntimeFCProps> = (props) => {
  const {
    schema,
    moduleId,
    schemaId = moduleId,
    config,
    plugins,
    fetchSchema,
    env,
    dependencies,
    defaultComponentLib,
    Loading,
    data,
    weblog,
    mock,
    refCanalReactRuntime,
    params,
    maxNestCount = 9,
    divWrapper = true,
    divWrapperStyle,
    style,
    className,
    FallbackComponent = DefaultFallbackComponent,
    onRenderError,
    transformSubmoduleProps,
    componentCodeFallback,
    onRenderEffectAfterLoading,
    onRenderEffectAfterFirstLoading,
  } = props
  const refLatestConfig = useLatest(config)
  const refLatestPlugins = useLatest(plugins)
  const refLatestFetchSchema = useLatest(fetchSchema)
  const refLatestDependencies = useLatest(dependencies)
  const refLatestDefaultComponentLib = useLatest(defaultComponentLib)
  const refLatestLoading = useLatest(Loading)
  const refData = useLatest(data)
  const refLatestParams = useLatest(params)
  const refLatestOnRenderError = useLatest(onRenderError)
  const refLatestWeblog = useLatest(weblog)
  const componentCtx = useContext(canalReactRuntimeComponentContext)
  const refLatestParentRuntime = useLatest(componentCtx?.runtime)
  const refLatestSubmoduleComponentId = useLatest(
    componentCtx?.schema.id as string | undefined,
  )
  const refLatestMock = useLatest(mock)
  const refLatestComponentCodeFallback = useLatest(componentCodeFallback)
  const refLatestOnRenderEffectAfterLoading = useLatest(
    onRenderEffectAfterLoading,
  )
  const refLatestOnRenderEffectAfterFirstLoading = useLatest(
    onRenderEffectAfterFirstLoading,
  )
  const runtime = useMemo(
    () =>
      createCanalReactRuntime({
        schema,
        // 类型推导有点问题
        schemaId: schemaId as string | number,
        config: refLatestConfig.current,
        plugins: refLatestPlugins.current,
        fetchSchema: refLatestFetchSchema.current,
        params: refLatestParams.current,
        env,
        dependencies: refLatestDependencies.current,
        defaultComponentLib: refLatestDefaultComponentLib.current,
        Loading: refLatestLoading.current,
        data: refData.current,
        weblog: refLatestWeblog.current,
        parentRuntime: refLatestParentRuntime.current as
          | CanalReactRuntime
          | undefined,
        submoduleComponentId: refLatestSubmoduleComponentId.current,
        mock: refLatestMock.current,
        componentCodeFallback: refLatestComponentCodeFallback.current,
        onRenderEffectAfterLoading: refLatestOnRenderEffectAfterLoading.current,
        onRenderEffectAfterFirstLoading:
          refLatestOnRenderEffectAfterFirstLoading.current,
      }),
    // 只在 env、schema、schemaId 变化时重新创建运行时
    [
      env,
      refData,
      refLatestComponentCodeFallback,
      refLatestConfig,
      refLatestDefaultComponentLib,
      refLatestDependencies,
      refLatestFetchSchema,
      refLatestLoading,
      refLatestMock,
      refLatestOnRenderEffectAfterFirstLoading,
      refLatestOnRenderEffectAfterLoading,
      refLatestParams,
      refLatestParentRuntime,
      refLatestPlugins,
      refLatestSubmoduleComponentId,
      refLatestWeblog,
      schema,
      schemaId,
    ],
  )
  useEffect(() => {
    if (!runtime.container.parentRuntime) {
      // 最外层运行时添加到 window.__canalRuntimes
      getCanalRuntimes().add(runtime)
      return () => {
        getCanalRuntimes().delete(runtime)
      }
    }
  }, [runtime])
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      if (schemaId) {
        console.log(
          `%c大运河设计器 URL: %c${schemaId} %c https://canal.corp.kuaishou.com/open-designer?moduleId=${schemaId}`,
          'color: yellow; font-weight: bold; background-color: blue;padding: 2px; font-size: 20px',
          'color: yellow; font-style: italic; background-color: blue;padding: 2px; font-size: 14px',
          'font-weight: bold; font-size: 14px',
        )
      }
    }, [schemaId])
  }
  useEffect(() => {
    runtime.container.hotUpdateConfigParams(params)
  }, [runtime, params])
  if (refCanalReactRuntime && !isFunction(refCanalReactRuntime)) {
    // 兼容旧的写法：不支持
    ;(
      refCanalReactRuntime as MutableRefObject<CanalReactRuntime | undefined>
    ).current = runtime
  }
  useEffect(() => {
    if (refCanalReactRuntime && isFunction(refCanalReactRuntime)) {
      fillRef(refCanalReactRuntime, runtime)
    }
  }, [refCanalReactRuntime, runtime])
  const finalDivWrapperStyle = useMemo(
    () => ({
      ...DIV_WRAPPER_STYLE,
      ...divWrapperStyle,
      ...style,
    }),
    [divWrapperStyle, style],
  )
  const onError = useMemo(() => {
    const oe: ErrorBoundaryProps['onError'] = (...args) => {
      console.error('Runtime render error', ...args)
      const err = args[0]
      captureException(err)
      refLatestOnRenderError.current?.(...args)
      setTimeout(() => {
        // 抛到全局，方便业务方捕获
        throw err
      }, 0)
    }
    return oe
  }, [refLatestOnRenderError])
  const nestCount = useContext(runtimeNestCountContext)
  if (nestCount > maxNestCount) {
    const err = new Error(
      `The runtime nested times should not be greater than ${maxNestCount}.`,
    )
    captureException(err)
    return null
  }
  const ret = (
    <runtimeNestCountContext.Provider value={nestCount + 1}>
      <canalReactRuntimeSubmoduleFCContext.Provider
        value={(ps: CanalReactRuntimeSubmoduleProps): JSX.Element => {
          let submoduleProps = { ...props, ...ps }
          submoduleProps =
            transformSubmoduleProps?.(submoduleProps) || submoduleProps
          return <CanalReactRuntimeFC {...submoduleProps} />
        }}
      >
        <runtime.container.ReactRenderer />
      </canalReactRuntimeSubmoduleFCContext.Provider>
    </runtimeNestCountContext.Provider>
  )

  return (
    <ErrorBoundary FallbackComponent={FallbackComponent} onError={onError}>
      {divWrapper ? (
        <div className={className} style={finalDivWrapperStyle}>
          {ret}
        </div>
      ) : (
        ret
      )}
    </ErrorBoundary>
  )
}

/**
 * div 包装样式
 */
const DIV_WRAPPER_STYLE: CSSProperties = {
  position: 'relative',
}

/**
 * 运行时嵌套层数上下文
 */
export const runtimeNestCountContext = createContext(0)

/**
 * 默认回退组件
 */
export const DefaultFallbackComponent: FC = () => {
  return null
}

/**
 * 获取大运河运行时
 */
export function getCanalRuntimes(): Set<CanalReactRuntime> {
  const runtimes = window.__canalRuntimes
  if (runtimes instanceof Set) {
    return runtimes
  }
  return (window.__canalRuntimes = new Set<CanalReactRuntime>())
}
