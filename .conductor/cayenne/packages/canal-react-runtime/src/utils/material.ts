import {
  Promisable,
  arrayableToArray,
  isResponseSuccess,
} from '@ad/canal-shared'
import { ComponentCode } from '@ad/e2e-schema'
import { decodeComponentType } from '@kael/schema-utils'
import { isObject } from 'lodash'
import { ComponentType } from 'react'
import { cdnDtReplace } from '../global-configs'
import { wrapFetch } from './fetch'

/**
 * 组件代码依赖
 */
export type ComponentCodeDependencies = Record<string, Record<string, unknown>>

/**
 * 组件代码回退
 */
export interface ComponentCodeFallback {
  /**
   * 组件代码回退
   * @param rawComponentType 原始组件类型
   * @param code 组件代码
   */
  (
    rawComponentType: string,
    code: ComponentCode,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): ComponentType<any> | void | null
}

/**
 * js 缓存，js url -> js 模块
 */
const jsCache: Record<string, unknown> = {}

/**
 * js 加载 Promise 缓存，js url -> js 加载 Promise
 */
const jsLoadPromiseCache: Record<string, Promise<unknown>> = {}

/**
 * 加载组件代码
 * @param componentCodes 组件代码
 * @param deps 依赖
 * @param componentCodeFallback 组件代码回退
 */
export function loadComponentCodes(
  componentCodes: Record<string, ComponentCode> = {},
  deps?: Record<string, unknown>,
  componentCodeFallback?: ComponentCodeFallback,
): Promisable<ComponentCodeDependencies> {
  const ret: ComponentCodeDependencies = {}
  const promises: Promise<void>[] = []
  for (const rawComponentType in componentCodes) {
    const code = componentCodes[rawComponentType]
    if (code.type === 'low-code' || code.code.type === 'multi') {
      continue
    }
    const addToRet = (comp: unknown): void => {
      const [libName, componentType] = decodeComponentType(rawComponentType)
      const lib = ret[libName] || (ret[libName] = {})
      lib[componentType] = comp
    }
    const fallback = (err: unknown): void => {
      const comp = componentCodeFallback?.(rawComponentType, code)
      if (comp) {
        addToRet(comp)
      } else {
        throw err
      }
    }
    const url = arrayableToArray(code.code.js)[0]
    if (url in jsCache) {
      try {
        addToRet(getComponentFromModule(jsCache[url], code))
      } catch (err) {
        fallback(err)
      }
    } else {
      if (!jsLoadPromiseCache[url]) {
        jsLoadPromiseCache[url] = (async (): Promise<unknown> => {
          const m = await loadJS(url, deps)
          return (jsCache[url] = m)
        })()
      }
      const p = (async (): Promise<void> => {
        try {
          addToRet(getComponentFromModule(await jsLoadPromiseCache[url], code))
        } catch (err) {
          fallback(err)
        }
      })()
      promises.push(p)
    }
  }
  if (promises.length) {
    return (async (): Promise<ComponentCodeDependencies> => {
      await Promise.all(promises)
      return ret
    })()
  }
  return ret
}

/**
 * 加载 js 代码
 * @param url js 的 URL
 * @param deps 依赖
 */
export async function loadJS(
  url: string,
  deps?: Record<string, unknown>,
): Promise<unknown> {
  const res = await wrapFetch(fetch, (err) => {
    err.message = `${err.message} | [loadJS] url: ${url}`
  })(url)
  if (!isResponseSuccess(res)) {
    throw new Error(
      `[loadJS] invalid response status: ${res.status}, url: ${url}`,
    )
  }
  return loadCJS(cdnDtReplace(await res.text()), deps)
}

/**
 * 加载 cjs 代码
 * @param jsStr cjs 字符串
 * @param deps 所有需要的依赖
 */
export function loadCJS<T>(
  jsStr: string,
  deps: Record<string, unknown> = {},
): T {
  const exports = {} as T
  const module = { exports }
  const ctx = {
    require(name: string): unknown {
      return deps[name]
    },
    exports,
    module,
  }
  new Function(...Object.keys(ctx), jsStr)(...Object.values(ctx))
  return module.exports
}

/**
 * 从模块中获取组件
 * @param m 模块
 * @param code 组件代码
 */
export function getComponentFromModule(
  m: unknown,
  code: ComponentCode,
): unknown {
  let comp = null
  if (code.type !== 'low-code') {
    const { exportIdentifier = 'default' } = code
    comp = m
    if (exportIdentifier === '*') {
      comp = m
    } else if (isObject(m)) {
      comp = (m as Record<string, unknown>)[exportIdentifier]
    }
  }
  if (!comp) {
    throw new Error(
      `[getComponentFromModule] invalid component code: ${JSON.stringify(
        code,
      )}`,
    )
  }
  return comp
}
