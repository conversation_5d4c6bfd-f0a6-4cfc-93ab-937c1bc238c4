/**
 * 修复 fetch，UC SDK 会覆盖全局 fetch，并导致 fetch 把 Error 当 Response 返回。这里兼容修复一下。
 * @param fetch 将要使用的 fetch 函数
 */
function fixFetch(fetch: typeof window.fetch): typeof window.fetch {
  return async (...args) => {
    const res = await fetch(...args)
    if (res instanceof Error) {
      console.error(
        'UC SDK 之类的工具，可能会导致 fetch 把 Error 当 Response 返回，这里兼容修复一下，重新抛出 Error。',
        res,
      )
      throw res
    }
    return res
  }
}

/**
 * 包装 fetch 函数，增加错误信息
 * @param fetch
 * @param appendErrorMessage 添加错误信息，为【Failed to fetch】这种错误加一些上下文报错信息
 */
export function wrapFetch(
  fetch: typeof window.fetch,
  appendErrorMessage: (err: Error) => void,
): typeof window.fetch {
  const f = fixFetch(fetch)
  return async (...args) => {
    try {
      return await f(...args)
    } catch (err) {
      if (err instanceof Error) {
        appendErrorMessage(err)
      }
      throw err
    }
  }
}
