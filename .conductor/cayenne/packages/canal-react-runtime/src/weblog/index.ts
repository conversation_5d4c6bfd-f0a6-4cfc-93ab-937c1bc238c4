import RadarComponentCollect from '@ks-radar/radar-component-collect'
import RadarCore from '@ks-radar/radar-core'
import { Weblog } from '@ks/weblogger'
import { once } from 'lodash'
import { shouldCaptureException } from '../global-configs'

/**
 * 获取错误收集，改成 once，可以更好地摇树
 */
const getErrorCollect = /*#__PURE__*/ once(() => {
  const weblog = new Weblog(
    {
      // 如果项目中有变量替换且值为 production/development，可以直接使用，例如 process.env.NODE_ENV
      env: process.env.NODE_ENV === 'production' ? 'production' : 'development',
      // env: 'production',
    },
    {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      product_name: 'canal',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      user_id: process.env.NODE_ENV === 'test' ? '' : location.origin,
    },
  )

  const radarCore = new RadarCore({
    weblogger: weblog,
    projectId: 'e5e4ec5907',
  })

  const errorCollect = new RadarComponentCollect({
    core: radarCore,
    sampling: 1,
    whiteUrlList: [],
  })
  return errorCollect
})

/**
 * 捕获错误
 */
export const captureException: RadarComponentCollect['captureException'] = (
  ...args
) => {
  if (shouldCaptureException()) {
    const err = args[0]
    err.message = `[canal] ${err.message}`
    console.error('captureException error', ...args)
    getErrorCollect().captureException(...args)
  } else {
    console.warn('captureException is disabled', ...args)
  }
}

/**
 * 自定义事件
 */
export const customEvent = (
  eventName: string,
  src: string,
  extraInfo: string,
): void => {
  try {
    if (shouldCaptureException()) {
      // 过长可能会丢弃
      getErrorCollect().event({
        name: eventName, // 必填
        // eslint-disable-next-line @typescript-eslint/naming-convention
        event_type: 'canalSchemeEndRuntimeErrors',
        src: src,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        extra_info: extraInfo,
      })
    }
  } catch (e) {
    // 不处理
  }
}
