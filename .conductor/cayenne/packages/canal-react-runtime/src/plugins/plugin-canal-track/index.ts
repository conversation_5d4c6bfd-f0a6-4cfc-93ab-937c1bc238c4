import { E2ESchema, Track } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import type { Weblog } from '@ks/weblogger'
import { cloneDeep, get, isFunction, isObject, merge, once } from 'lodash'
import { shouldAssertWeblog } from '../../global-configs'

/**
 * 大运河埋点插件的扩展属性
 */
export interface PluginCanalTrackPropertiesExt {
  runtimeConstructorOptions: {
    /**
     * 配置
     */
    config?: {
      /**
       * Weblog 实例，由外部传入
       */
      weblog?: Weblog
    }
  }
  container: {
    /**
     * 埋点接口，埋点 ID -> 埋点接口
     */
    trackApis: Record<string, TrackApi>
    /**
     * 触发埋点
     * @param trackId 埋点 ID
     * @param transformTrack 转换埋点
     */
    track(
      trackId: string,
      transformTrack?: Partial<Track> | ((track: Track) => Track | void),
    ): void
  }
}

/**
 * 埋点接口
 */
export type TrackApi = (arg?: unknown) => void

/**
 * 大运河埋点插件
 */
export const pluginCanalTrack: Plugin<
  PluginCanalTrackPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_TRACK',
  extendsContainer(container) {
    let apis: typeof container.trackApis = {}
    let lastTracks: typeof container.schema.tracks
    Object.defineProperty(container, 'trackApis', {
      get() {
        if (container.schema.tracks === lastTracks) {
          return apis
        }
        lastTracks = container.schema.tracks
        apis = {}
        const weblog = container.runtime.constructorOptions.config?.weblog
        for (const track of container.schema.tracks || []) {
          let fn: TrackApi = (arg) => {
            let finalTrack = track
            const transformTrack = get(arg, 'transformTrack')
            if (isFunction(transformTrack)) {
              finalTrack = cloneDeep(finalTrack)
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              finalTrack = (transformTrack as any)(finalTrack) || finalTrack
            } else if (isObject(transformTrack)) {
              finalTrack = merge(cloneDeep(finalTrack), transformTrack)
            }
            if (!weblog) {
              if (shouldAssertWeblog()) {
                throw new Error(`Please new Runtime with weblog.`)
              } else {
                console.warn('Can not track without weblog.', finalTrack)
                return
              }
            }
            weblog.sendImmediately(
              finalTrack.eventType,
              finalTrack.eventOptions,
            )
          }
          const { canRepeat = true } = track
          if (!canRepeat) {
            fn = once(fn)
          }
          apis[track.id] = fn
        }
        return apis
      },
    })
    container.track = (trackId, transformTrack): void => {
      container.trackApis[trackId]?.({ transformTrack })
    }
  },
}
