import { E2ESchema } from '@ad/e2e-schema'
import { ComponentController, Plugin } from '@kael/runtime'
import {
  PluginReactRendererPropertiesExt,
  ReactComponent,
} from '@kael/runtime-plugin-react-renderer'
import { RendererComponentsContext } from '@kael/runtime-plugin-react-renderer/esm/context'
import { ReactComponentControllerProps } from '@kael/runtime-plugin-react-renderer/esm/react-component-controller'
import React, { FC, useContext } from 'react'

/**
 * 大运河渲染器插件的扩展属性
 */
export type PluginCanalRendererPropertiesExt =
  PluginReactRendererPropertiesExt & {
    container: {
      /**
       * 组件控制器动态（渲染器）
       */
      ComponentControllerDynamic: FC<ComponentControllerDynamicProps>
    }
  }

/**
 * 组件控制器动态（渲染器）属性
 */
export interface ComponentControllerDynamicProps {
  /**
   * 组件控制器
   */
  componentController: ComponentController
}

/**
 * 大运河渲染器插件
 */
export const pluginCanalRenderer: Plugin<
  PluginCanalRendererPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_RENDERER',
  extendsContainer(container) {
    container.rendererComponentsHocs.componentControllerHocs.push(
      () => CanalReactComponentController,
    )
    container.ComponentControllerDynamic = ComponentControllerDynamic
  },
}

/**
 * 大运河组件控制器对应的 React 渲染组件，重写 kael 的 ReactComponentController，支持父组件劫持子组件的 props
 */
const CanalReactComponentController: FC<ReactComponentControllerProps> = ({
  componentController,
  ...restOutsideProps
}) => {
  const { ReactComponent: FinalReactComponent = ReactComponent } = useContext(
    RendererComponentsContext,
  )
  const { instances } = componentController
  return !instances.length ? null : instances.length === 1 ? (
    <FinalReactComponent
      key={instances[0].key}
      {...restOutsideProps}
      component={instances[0]}
    />
  ) : (
    <>
      {instances.map((instance) => (
        <FinalReactComponent key={instance.key} component={instance} />
      ))}
    </>
  )
}

/**
 * 组件控制器动态（渲染器）
 */
const ComponentControllerDynamic: FC<ComponentControllerDynamicProps> = ({
  componentController,
  ...restProps
}) => {
  return (
    <RendererComponentsContext.Consumer>
      {({
        ReactComponentController: FinalReactComponentController,
      }): JSX.Element | null => {
        if (!FinalReactComponentController) {
          return null
        }
        return (
          <FinalReactComponentController
            {...restProps}
            componentController={componentController}
          />
        )
      }}
    </RendererComponentsContext.Consumer>
  )
}
