import { px2rem, px2vw } from '@ad/canal-shared'
import { E2ESchema } from '@ad/e2e-schema'
import {
  Plugin,
  PluginComponentProps,
  PluginComponentPropsPropertiesExt,
} from '@kael/runtime'
import { get, set } from 'lodash'

/**
 * 大运河组件属性插件的扩展属性
 */
export type PluginCanalComponentPropsPropertiesExt =
  PluginComponentPropsPropertiesExt & {
    component: {
      /**
       * 最近一次计算的组件属性，用于做一些缓存
       */
      lastProps?: Record<string, unknown>
    }
  }

/**
 * 大运河组件属性插件
 */
export const pluginCanalComponentProps: Plugin<
  PluginCanalComponentPropsPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_COMPONENT_PROPS',
  position: {
    type: 'after',
    targetId: PluginComponentProps.id,
    replaceIds: [PluginComponentProps.id],
  },
  extendsComponent(component) {
    Object.defineProperty(component, 'props', {
      get() {
        const props: typeof component.props = {}
        const {
          schema: { type: schemaType, props: schemaProps = {} },
          runtime: {
            container: {
              schema: { px2vw: schemaPx2vw, px2rem: schemaPx2rem },
            },
          },
        } = component
        for (const key in schemaProps) {
          props[key] = component.evalExpression(schemaProps[key], {
            silent: true,
            relativePath: ['props', key],
          })
        }
        // 计算 px2vw 或 px2rem
        if (schemaPx2vw) {
          for (const logicPath of schemaPx2vw.propLogicPaths[schemaType] ||
            []) {
            const value = get(props, logicPath)
            const newValue = px2vw(value, schemaPx2vw.designWidth)
            if (newValue !== value) {
              set(props, logicPath, newValue)
            }
          }
        } else if (schemaPx2rem) {
          for (const logicPath of schemaPx2rem.propLogicPaths[schemaType] ||
            []) {
            const value = get(props, logicPath)
            const newValue = px2rem(value, schemaPx2rem.rootElementFontSize)
            if (newValue !== value) {
              set(props, logicPath, newValue)
            }
          }
        }
        return (component.lastProps = props)
      },
    })
  },
}

/**
 * 获取缓存的组件属性
 */
export function getCachedComponentProps(component: {
  props: Record<string, unknown>
  lastProps?: Record<string, unknown>
}): Record<string, unknown> {
  return component.lastProps || component.props
}
