import { E2ESchema, E2ESchemaExpressionBind } from '@ad/e2e-schema'
import {
  ExpressionExtension,
  Plugin,
  PluginEvalExpressionPropertiesExt,
  PluginExpressionExtension,
} from '@kael/runtime'
import { isUndefined } from 'lodash'
import { PluginCanalDataPropertiesExt } from '../plugin-canal-data'

/**
 * 大运河绑定表达式
 */
export const canalExpressionExtensionBind: ExpressionExtension<
  E2ESchemaExpressionBind,
  PluginEvalExpressionPropertiesExt
> = {
  type: 'bind',
  eval({ fn: fnExpr, this: thisExpr, args: argExprs }, ctx, options) {
    const relativePath = options?.relativePath || []
    const fn = ctx.evalExpression(fnExpr, {
      ...options,
      relativePath: [...relativePath, 'fn'],
    }) as (...args: unknown[]) => unknown
    const that = isUndefined(thisExpr)
      ? undefined
      : ctx.evalExpression(thisExpr, {
          ...options,
          relativePath: [...relativePath, 'this'],
        })
    const args = argExprs
      ? argExprs.map((argExpr, i) =>
          ctx.evalExpression(argExpr, {
            ...options,
            relativePath: [...relativePath, 'args', i],
          }),
        )
      : []
    return fn.bind(that, ...args)
  },
}

/**
 * 大运河绑定表达式扩展插件
 */
export const pluginCanalExpressionExtensionBind: Plugin<
  PluginCanalDataPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_EXPRESSION_EXTENSION_BIND',
  position: {
    type: 'after',
    targetId: PluginExpressionExtension.id,
  },
  extendsContainer(container) {
    container.registerExpressionExtension(canalExpressionExtensionBind)
  },
}
