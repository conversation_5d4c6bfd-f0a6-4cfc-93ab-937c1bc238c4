import { E2ESchema } from '@ad/e2e-schema'
import { Plugin, PluginSchema } from '@kael/runtime'
import { isEqual, isFunction } from 'lodash'
import { observable, runInAction } from 'mobx'
import { assignOrProduce } from '../../utils'
import { PluginCanalParentRuntimePropertiesExt } from '../plugin-canal-parent-runtime'

/**
 * 大运河数据插件的扩展属性
 */
export type PluginCanalDataPropertiesExt =
  PluginCanalParentRuntimePropertiesExt & {
    runtimeConstructorOptions: {
      /**
       * 配置
       */
      config?: {
        /**
         * （初始）数据，优先级仅高于初始 Schema 里的 data 字段
         */
        data?:
          | Record<string, unknown>
          | ((oldData: Record<string, unknown>) => Record<string, unknown>)
      }
    }
    container: {
      /**
       * 自动刷新启用
       */
      autoRefreshEnabled: boolean
      /**
       * 副作用启用
       */
      effectEnabled: boolean
      /**
       * （全局）数据
       */
      data: Record<string, unknown>
      /**
       * 设置（全局）数据
       * @param partialData 部分数据
       * @param options 选项
       */
      setData(
        partialData?:
          | Record<string, unknown>
          | null
          | ((
              oldData: Record<string, unknown>,
            ) => Record<string, unknown> | null | void),
        options?: SetDataOptions,
      ): void
      /**
       * 准备在所有运行时里设置（全局）数据
       * @param options 选项
       * @param cb 回调函数
       */
      prepareSetDataInAllRuntimes(options: SetDataOptions, cb: () => void): void
    }
  }

/**
 * 设置（全局）数据选项
 */
export interface SetDataOptions {
  /**
   * 自动刷新启用，默认: true
   */
  autoRefreshEnabled?: boolean
  /**
   * 副作用启用，默认: true
   */
  effectEnabled?: boolean
}

/**
 * 大运河数据插件
 */
export const pluginCanalData: Plugin<PluginCanalDataPropertiesExt, E2ESchema> =
  {
    id: 'CANAL_DATA',
    position: {
      type: 'after',
      targetId: PluginSchema.id,
    },
    extendsContainer(container) {
      const data = assignOrProduce(
        { ...container.schema.data },
        container.runtime.constructorOptions.config?.data,
      )
      container.data = observable(data)
      container.setData = (
        partialData,
        { autoRefreshEnabled = true, effectEnabled = true } = {},
        // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      ) => {
        const oldAutoRefreshEnabled = container.autoRefreshEnabled
        const oldEffectEnabled = container.effectEnabled
        try {
          container.autoRefreshEnabled = autoRefreshEnabled
          container.effectEnabled = effectEnabled
          // console.log('before setData runInAction', {
          //   partialData,
          //   autoRefreshEnabled,
          //   effectEnabled,
          // })
          runInAction(() => {
            const pData = isFunction(partialData)
              ? partialData(container.data)
              : partialData
            if (pData) {
              for (const key in pData) {
                if (!isEqual(container.data[key], pData[key])) {
                  container.data[key] = pData[key]
                }
              }
            }
          })
          // console.log('after setData runInAction', {
          //   partialData,
          //   autoRefreshEnabled,
          //   effectEnabled,
          // })
        } finally {
          container.autoRefreshEnabled = oldAutoRefreshEnabled
          container.effectEnabled = oldEffectEnabled
        }
      }
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      container.prepareSetDataInAllRuntimes = (options, cb) => {
        let fn = cb
        for (const r of container.genAllRuntimes()) {
          const oldFn = fn
          // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
          fn = () => {
            ;(r.container as typeof container).setData(oldFn, options)
          }
        }
        fn()
      }
    },
  }
