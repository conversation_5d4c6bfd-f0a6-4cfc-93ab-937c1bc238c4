import { E2ESchema, E2ESchemaExpressionTrack } from '@ad/e2e-schema'
import {
  ExpressionExtension,
  Plugin,
  PluginEvalExpressionPropertiesExt,
  PluginExpressionExtension,
} from '@kael/runtime'
import { PluginCanalDataPropertiesExt } from '../plugin-canal-data'
import { PluginCanalTrackPropertiesExt } from '../plugin-canal-track'

/**
 * 大运河埋点表达式
 */
export const canalExpressionExtensionTrack: ExpressionExtension<
  E2ESchemaExpressionTrack,
  PluginEvalExpressionPropertiesExt & PluginCanalTrackPropertiesExt
> = {
  type: 'track',
  eval({ trackId }, ctx) {
    return ctx.runtime.container.trackApis[trackId]
  },
}

/**
 * 大运河埋点表达式扩展插件
 */
export const pluginCanalExpressionExtensionTrack: Plugin<
  PluginCanalDataPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_EXPRESSION_EXTENSION_TRACK',
  position: {
    type: 'after',
    targetId: PluginExpressionExtension.id,
  },
  extendsContainer(container) {
    container.registerExpressionExtension(canalExpressionExtensionTrack)
  },
}
