import { E2ESchema, E2ESchemaExpressionOpenUrl } from '@ad/e2e-schema'
import {
  ExpressionExtension,
  Plugin,
  PluginEvalExpressionPropertiesExt,
  PluginExpressionExtension,
} from '@kael/runtime'
import { PluginCanalDataPropertiesExt } from '../plugin-canal-data'

/**
 * 大运河打开 URL 表达式
 */
export const canalExpressionExtensionOpenUrl: ExpressionExtension<
  E2ESchemaExpressionOpenUrl,
  PluginEvalExpressionPropertiesExt
> = {
  type: 'open-url',
  eval({ url: urlExpr, inPlace = false }, ctx, options) {
    const relativePath = options?.relativePath || []
    return () => {
      const url = ctx.evalExpression(urlExpr, {
        ...options,
        relativePath: [...relativePath, 'url'],
      })
      if (inPlace) {
        window.location = url
      } else {
        window.open(url)
      }
    }
  },
}

/**
 * 大运河打开 URL 表达式扩展插件
 */
export const pluginCanalExpressionExtensionOpenUrl: Plugin<
  PluginCanalDataPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_EXPRESSION_EXTENSION_OPEN_URL',
  position: {
    type: 'after',
    targetId: PluginExpressionExtension.id,
  },
  extendsContainer(container) {
    container.registerExpressionExtension(canalExpressionExtensionOpenUrl)
  },
}
