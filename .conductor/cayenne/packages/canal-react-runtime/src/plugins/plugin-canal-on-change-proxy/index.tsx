import { E2ESchema } from '@ad/e2e-schema'
import { Component, DefaultPluginsPropertiesExt, Plugin } from '@kael/runtime'
import { PluginIdPropertiesExt } from '@kael/runtime-plugin-id'
import { pluginMobxReactRenderer } from '@kael/runtime-plugin-mobx-react-renderer'
import {
  PluginReactRendererPropertiesExt,
  ReactComponentProps,
} from '@kael/runtime-plugin-react-renderer'
import { isUndefined } from 'lodash'
import { reaction } from 'mobx'
import { observer } from 'mobx-react-lite'
import React, { useCallback, useEffect } from 'react'
import { useLatest } from 'react-use'
import { PluginCanalDataPropertiesExt } from '../plugin-canal-data'

/**
 * 大运河 onChange 代理插件的扩展属性
 */
export type PluginCanalOnChangeProxyPropertiesExt =
  PluginReactRendererPropertiesExt

/**
 * 大运河 onChange 代理插件
 */
export const pluginCanalOnChangeProxy: Plugin<
  PluginCanalOnChangeProxyPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_ON_CHANGE_PROXY',
  position: {
    type: 'before',
    targetId: pluginMobxReactRenderer.id,
  },
  extendsContainer(container) {
    container.rendererComponentsHocs.componentHocs.push((Comp) => {
      return observer((rawProps) => {
        const propsWithValueAndOnChange =
          rawProps as ReactComponentPropsWithValueAndOnChange
        const {
          value: outsideValue,
          onChange: outsideOnChange,
          ...restProps
        } = propsWithValueAndOnChange
        void outsideValue
        const refLatestOutsideOnChange = useLatest(outsideOnChange)
        const component = restProps.component as Component<
          DefaultPluginsPropertiesExt &
            PluginIdPropertiesExt &
            PluginCanalDataPropertiesExt
        >
        const finalContainer = component.runtime.container
        const componentId = component.id
        useEffect(() => {
          // 不接受外部劫持，但可以反向更新
          if (refLatestOutsideOnChange.current) {
            return reaction(
              () => finalContainer.data[componentId],
              (value) => {
                refLatestOutsideOnChange.current?.(value)
              },
              {
                // 如果有初始值，第一次就需要触发
                fireImmediately: !isUndefined(finalContainer.data[componentId]),
              },
            )
          }
        }, [componentId, finalContainer, refLatestOutsideOnChange])
        const onChange = useCallback(
          (v: unknown) => {
            finalContainer.setData({
              [componentId]: v,
            })
          },
          [componentId, finalContainer],
        )
        const finalProps = {
          ...restProps,
          // 注入 value 和 onChange
          value: finalContainer.data[componentId],
          onChange,
        }
        return <Comp {...finalProps} />
      })
    })
  },
}

/**
 * 组件对应的 React 渲染组件属性，加上父组件可能传入的 value 和 onChange
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface ReactComponentPropsWithValueAndOnChange<T = any>
  extends ReactComponentProps {
  value?: T
  onChange?: (newValue: T) => void
}
