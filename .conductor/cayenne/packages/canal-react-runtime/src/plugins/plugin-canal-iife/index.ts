import { E2ESchema, E2ESchemaExpression } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { isFunction } from 'lodash'

/**
 * 大运河立即执行函数表达式插件的扩展属性
 */
export interface PluginCanalIIFEPropertiesExt {
  container: {
    /**
     * 执行立即执行的函数表达式
     * @param iife 立即执行的函数表达式
     */
    executeIIFE: (iife?: E2ESchemaExpression) => void
  }
}

/**
 * 大运河立即执行函数表达式插件
 */
export const pluginCanalIIFE: Plugin<PluginCanalIIFEPropertiesExt, E2ESchema> =
  {
    id: 'CANAL_IIFE',
    extendsContainer(container) {
      container.executeIIFE = ((iife) => {
        if (!iife) {
          return
        }
        setTimeout(() => {
          const ret = container.evalExpression(iife, {
            silent: true,
            relativePath: ['iife'],
          })
          if (isFunction(ret)) {
            ret()
          }
        })
      }) as typeof container.executeIIFE
      const iife = container.schema.iife
      container.executeIIFE(iife)
    },
  }
