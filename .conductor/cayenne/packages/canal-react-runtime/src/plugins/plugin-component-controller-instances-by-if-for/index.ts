import {
  Plugin,
  PluginComponentControllerInstances,
  PluginComponentKeyPropertiesExt,
} from '@kael/runtime'
import { observable, reaction } from 'mobx'
import {
  PluginComponentControllerForPropertiesExt,
  SchemaForResult,
} from '../plugin-component-controller-for'
import { PluginComponentControllerIfPropertiesExt } from '../plugin-component-controller-if'

/**
 * 组件控制器实例 if & for 插件的扩展属性
 */
export type PluginComponentControllerInstancesByIfForPropertiesExt =
  PluginComponentControllerIfPropertiesExt &
    PluginComponentControllerForPropertiesExt &
    PluginComponentKeyPropertiesExt & {
      componentConstructorOptions: {
        /**
         * 需要初始化的数据
         */
        dataInit?: Record<string, unknown>
      }
      componentController: {
        // /**
        //  * 组件实例
        //  */
        // instances: Component<DefaultPropertiesExt>
      }
    }

/**
 * 单实例
 */
export const SYMBOL_ONLY_ONE_INSTANCE = Symbol('ONLY_ONE_INSTANCE')

/**
 * 组件控制器实例 if & for 插件
 */
export const pluginComponentControllerInstancesByIfFor: Plugin<PluginComponentControllerInstancesByIfForPropertiesExt> =
  {
    id: 'COMPONENT_CONTROLLER_INSTANCES_BY_IF_FOR',
    position: {
      type: 'before',
      targetId: PluginComponentControllerInstances.id,
      replaceIds: [PluginComponentControllerInstances.id],
    },
    extendsComponentController(componentController) {
      componentController.instances = observable([])

      /**
       * 销毁所有实例
       */
      function disposeInstances(): void {
        for (const instance of componentController.instances) {
          instance.dispose()
        }
        componentController.instances.length = 0
      }

      const disposer = reaction(
        (): SchemaForResult => {
          if (!componentController.if) {
            return {
              items: [],
            }
          }
          return (
            componentController.for || {
              items: [SYMBOL_ONLY_ONE_INSTANCE],
            }
          )
        },
        ({
          items,
          indexName = 'index',
          itemName = 'item',
          itemKey = (...[, i]): number => i,
        }: SchemaForResult) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const finalItemKey = (item: any, index: number): string => {
            const ret =
              typeof itemKey === 'function'
                ? itemKey(item, index)
                : item[`${itemKey}`]
            return `${componentController.key}|${ret}`
          }

          disposeInstances()
          for (let i = 0; i < items.length; i++) {
            const item = items[i]
            componentController.instances.push(
              componentController.runtime.createComponent({
                parent: componentController,
                schema: componentController.schema,
                ...(item === SYMBOL_ONLY_ONE_INSTANCE
                  ? {}
                  : {
                      dataInit: {
                        [indexName]: i,
                        [itemName]: item,
                      },
                    }),
                key: finalItemKey(item, i),
              }),
            )
          }
        },
        {
          fireImmediately: true,
        },
      )
      return (): void => {
        disposer()
        disposeInstances()
      }
    },
  }
