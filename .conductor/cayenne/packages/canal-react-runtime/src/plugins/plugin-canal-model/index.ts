import { E2ESchema, E2ESchemaExpressionJS } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { isFunction, once } from 'lodash'

/**
 * 大运河模型插件的扩展属性
 */
export type PluginCanalModelPropertiesExt = {
  container: {
    /**
     * 全局模型
     */
    globalModel?: unknown
    /**
     * 检查并创建全局模型
     */
    checkAndCreateGlobalModel(): void
    /**
     * 模型
     */
    model?: unknown
    /**
     * 检查并创建模型
     */
    checkAndCreateModel(): void
  }
}

/**
 * 大运河模型插件
 */
export const pluginCanalModel: Plugin<
  PluginCanalModelPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_MODEL',
  extendsContainer(container) {
    let globalModel: unknown
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    container.checkAndCreateGlobalModel = () => {
      if (globalModel) return
      const globalModelCode = container.schema.globalFrontModel?.code
      if (!globalModelCode) return
      const globalModelCls = container.evalExpression(
        {
          type: 'js',
          code: globalModelCode,
        } satisfies E2ESchemaExpressionJS,
        {
          silent: true,
          relativePath: ['globalModel'],
        },
      )
      if (!isFunction(globalModelCls)) return
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      globalModel = new (globalModelCls as any)(container)
    }
    Object.defineProperty(container, 'globalModel', {
      get: once(() => {
        container.checkAndCreateGlobalModel()
        return globalModel
      }),
    })

    let model: unknown
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    container.checkAndCreateModel = () => {
      if (model) return
      const modelCode = container.schema.model?.code
      if (!modelCode) return
      const modelCls = container.evalExpression(
        {
          type: 'js',
          code: modelCode,
        } satisfies E2ESchemaExpressionJS,
        {
          silent: true,
          relativePath: ['model'],
        },
      )
      if (!isFunction(modelCls)) return
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      model = new (modelCls as any)(container)
    }
    Object.defineProperty(container, 'model', {
      get: once(() => {
        container.checkAndCreateModel()
        return model
      }),
    })
  },
}
