import { E2ESchema } from '@ad/e2e-schema'
import {
  Component,
  Plugin,
  PluginEvalExpression,
  PluginEvalExpressionPropertiesExt,
} from '@kael/runtime'
import { PluginIdPropertiesExt } from '@kael/runtime-plugin-id'
import { PropertyPath, normalizeExpression } from '@kael/schema-utils'
import { captureException } from '../../weblog'
import { PluginCanalExpressionExtensionRefreshPropertiesExt } from '../plugin-canal-expr-ext-refresh'

/**
 * 执行信息
 */
export interface EvalInfo {
  /**
   * 模块 ID
   */
  moduleId: string
  /**
   * 组件 ID
   */
  componentId?: string
  /**
   * 相对路径
   */
  relativePath: PropertyPath
  /**
   * 表达式类型
   */
  expressionType: string
}

/**
 * 大运河表达式执行插件
 */
export const pluginCanalEvalExpression: Plugin<
  PluginEvalExpressionPropertiesExt &
    PluginCanalExpressionExtensionRefreshPropertiesExt & {
      container: {
        /**
         * 执行信息
         */
        evalInfo?: EvalInfo
      }
    },
  E2ESchema
> = {
  id: 'CANAL_EVAL_EXPRESSION',
  position: {
    type: 'after',
    targetId: PluginEvalExpression.id,
    replaceIds: [PluginEvalExpression.id],
  },
  extendsContext(ctx) {
    const { schemaId } = ctx.runtime.constructorOptions.config || {}
    const moduleId = `${schemaId}`
    ctx.evalExpression = (expression, options): unknown => {
      const { silent } = options || {}
      expression = normalizeExpression(expression)
      const evalInfo: EvalInfo = {
        moduleId,
        relativePath: options?.relativePath || [],
        expressionType: expression.type,
      }
      if (ctx instanceof Component) {
        evalInfo.componentId = (
          ctx as unknown as Component<PluginIdPropertiesExt>
        ).id
      }
      const oldEvalInfo = ctx.runtime.container.evalInfo
      ctx.runtime.container.evalInfo = evalInfo
      try {
        const container = ctx.runtime.container
        const fn = container.expressionExtensions[expression.type]
        if (!fn) {
          throw new Error(`invalid expression type: ${expression.type}`)
        }
        return fn(normalizeExpression(expression), ctx, options)
      } catch (err) {
        /* istanbul ignore next */
        if (process.env.NODE_ENV !== 'test') {
          if (err instanceof Error) {
            err.message = `[${JSON.stringify(evalInfo)}] ${err.message}`
            captureException(err)
          }
          console.error('eval expression failed: ', {
            ...evalInfo,
            err,
            ctx,
            expression,
            options,
          })
        } else {
          console.log('eval expression failed: ', (err as Error).message)
        }
        if (!silent) {
          throw err
        }
      } finally {
        ctx.runtime.container.evalInfo = oldEvalInfo
      }
    }
  },
}
