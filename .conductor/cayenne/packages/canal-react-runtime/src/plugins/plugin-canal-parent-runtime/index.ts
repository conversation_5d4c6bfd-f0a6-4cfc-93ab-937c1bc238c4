import { E2ESchema } from '@ad/e2e-schema'
import { Container, Plugin, Runtime } from '@kael/runtime'
import { CanalReactRuntime } from '../../runtime'

/**
 * 大运河父运行时插件的扩展属性
 */
export interface PluginCanalParentRuntimePropertiesExt {
  runtimeConstructorOptions: {
    /**
     * 配置
     */
    config?: {
      /**
       * 父运行时，类型为 {@link CanalReactRuntime}，类型循环引用了
       */
      parentRuntime?: Runtime
      /**
       * 子模块组件 ID
       */
      submoduleComponentId?: string
    }
  }
  container: {
    /**
     * 父运行时，类型为 {@link CanalReactRuntime}，类型循环引用了
     */
    parentRuntime?: Runtime
    /**
     * 父模块上下文
     */
    parentModuleCtx?: Container
    /**
     * 子模块上下文，子模块组件 ID -> 上下文
     */
    childModuleCtxs: Record<string, Container>
    /**
     * 生成（可以通过父子关系遍历到的）所有运行时
     */
    genAllRuntimes(): Generator<Runtime, void, unknown>
  }
}

/**
 * 大运河父运行时插件
 */
export const pluginCanalParentRuntime: Plugin<
  PluginCanalParentRuntimePropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_PARENT_RUNTIME',
  extendsContainer(container) {
    const { parentRuntime: initParentRuntime, submoduleComponentId } =
      container.runtime.constructorOptions.config || {}
    const parentRuntime = (container.parentRuntime = initParentRuntime as
      | CanalReactRuntime
      | undefined)
    container.parentModuleCtx = parentRuntime?.container
    container.childModuleCtxs = {}
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    container.genAllRuntimes = function* () {
      let rs = [getRootRuntime()]
      while (rs.length) {
        const nextRs: typeof rs = []
        for (const r of rs) {
          yield r
          nextRs.push(
            ...Object.values(r.container.childModuleCtxs).map(
              (c) => c.runtime as typeof container.runtime,
            ),
          )
        }
        rs = nextRs
      }
    }
    if (submoduleComponentId && parentRuntime) {
      parentRuntime.container.childModuleCtxs[submoduleComponentId] = container
      return () => {
        delete parentRuntime.container.childModuleCtxs[submoduleComponentId]
      }
    }

    function getRootRuntime(): typeof container.runtime {
      let r = container.runtime
      while (r.container.parentRuntime) {
        r = r.container.parentRuntime as typeof container.runtime
      }
      return r
    }
  },
}
