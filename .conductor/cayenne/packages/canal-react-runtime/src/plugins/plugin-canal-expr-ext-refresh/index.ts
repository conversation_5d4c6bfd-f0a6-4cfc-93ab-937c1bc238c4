import {
  E2ESchema,
  E2ESchemaExpressionRefresh,
  RefreshType,
} from '@ad/e2e-schema'
import {
  b2fE2ESchema,
  validateE2ESchema,
  validateE2EServerSchema,
} from '@ad/e2e-schema-utils'
import {
  DefaultPluginsPropertiesExt,
  ExpressionExtension,
  Plugin,
  PluginExpressionExtension,
} from '@kael/runtime'
import {
  cloneDeep,
  get,
  isFunction,
  isObject,
  last,
  merge,
  once,
  uniqueId,
} from 'lodash'
import { extendObservable, observable, runInAction } from 'mobx'
import { FetchSchema, FetchSchemaEnv, defaultFetchSchema } from '../../apis'
import { cdnDtReplaceJson } from '../../global-configs'
import { assignOrProduce } from '../../utils'
import { captureException } from '../../weblog'
import { PluginCanalHotUpdateSchemaPropertiesExt } from '../plugin-canal-hot-update-schema'
import { PluginCanalIIFEPropertiesExt } from '../plugin-canal-iife'
import { PluginCanalLinkagePropertiesExt } from '../plugin-canal-linkage'
import { PluginCanalRuntimeIdPropertiesExt } from '../plugin-canal-runtime-id'

/**
 * 大运河刷新表达式扩展插件的扩展属性
 */
export type PluginCanalExpressionExtensionRefreshPropertiesExt =
  PluginCanalRuntimeIdPropertiesExt &
    PluginCanalLinkagePropertiesExt &
    PluginCanalHotUpdateSchemaPropertiesExt &
    PluginCanalIIFEPropertiesExt & {
      runtimeConstructorOptions: {
        /**
         * 配置
         */
        config?: {
          /**
           * Schema 文件 ID
           */
          schemaId?: string | number
          /**
           * 获取 Schema 文件
           */
          fetchSchema?: FetchSchema
          /**
           * 参数，优先级最高
           */
          params?: ConfigParams
          /**
           * 环境，默认走生产环境
           */
          env?: FetchSchemaEnv
          /**
           * 开启 Mock
           */
          mock?: boolean
        }
      }
      container: {
        /**
         * 刷新中的类型，刷新 Promise + 刷新类型
         */
        refreshingTypes: [Promise<unknown>, RefreshType][]
        /**
         * 刷新中的类型
         */
        refreshingType?: RefreshType
        /**
         * 是否刷新中
         */
        isRefreshing: boolean
        /**
         * 首次刷新 Promise
         */
        firstRefreshPromise?: Promise<void>
        /**
         * 初始化参数，ctx.runtime.constructorOptions.config?.params 的首次赋值
         */
        initParams?: ConfigParams
      }
    }

/**
 * 配置中的参数
 */
export type ConfigParams =
  | Record<string, unknown>
  | ((oldParams: Record<string, unknown>) => Record<string, unknown>)

/**
 * 大运河刷新表达式
 */
export const canalExpressionExtensionRefresh: ExpressionExtension<
  E2ESchemaExpressionRefresh,
  PluginCanalExpressionExtensionRefreshPropertiesExt &
    DefaultPluginsPropertiesExt<E2ESchema>
> = {
  type: 'refresh',
  eval({ refreshType = 'default', params }, ctx) {
    const {
      schemaId,
      fetchSchema = defaultFetchSchema,
      env = 'production',
      mock = false,
    } = ctx.runtime.constructorOptions.config || {}
    const { runtimeId } = ctx.runtime.container
    async function refresh(...args: unknown[]): Promise<void> {
      // console.log('refresh', schemaId, args)
      if (!schemaId) {
        console.warn('Can not refresh module without schemaId.')
        return
      }
      let finalParams = {
        ...ctx.runtime.container.getRefreshParamsByLinkage(refreshType),
        ...params,
      }
      const transformParams = get(args[0], 'transformParams')
      if (isFunction(transformParams)) {
        finalParams = cloneDeep(finalParams)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        finalParams = (transformParams as any)(finalParams) || finalParams
      } else if (isObject(transformParams)) {
        finalParams = merge(cloneDeep(finalParams), transformParams)
      }
      finalParams = assignOrProduce(
        finalParams,
        ctx.runtime.constructorOptions.config?.params,
      )
      const promise = fetchSchema({
        runtimeId,
        schemaId,
        refreshType,
        params: finalParams,
        env,
        mock,
        version: uniqueId(),
      })
      runInAction(() => {
        ctx.runtime.container.refreshingTypes =
          ctx.runtime.container.refreshingTypes = [
            ...ctx.runtime.container.refreshingTypes,
            [promise, refreshType],
          ]
      })
      const finishRefreshing = once((): void => {
        runInAction(() => {
          ctx.runtime.container.refreshingTypes =
            ctx.runtime.container.refreshingTypes.filter(
              (item) => item[0] !== promise,
            )
        })
      })
      try {
        const newSchema = cdnDtReplaceJson(await promise)
        ctx.runtime.container.executeIIFE(newSchema.iife)
        let finalSchema: E2ESchema | null = null
        if (validateE2ESchema(newSchema)) {
          finalSchema = newSchema
        } else if (validateE2EServerSchema(newSchema)) {
          finalSchema = b2fE2ESchema(newSchema)
        }
        runInAction(() => {
          if (finalSchema) {
            ctx.runtime.container.hotUpdateSchema(finalSchema)
            finishRefreshing()
          }
        })
      } catch (err) {
        if (err instanceof Error) {
          captureException(err)
        }
        throw err
      } finally {
        finishRefreshing()
      }
    }
    return async (...args: unknown[]) => {
      // console.log('refresh wrapper', schemaId, args)
      if (ctx.runtime.container.firstRefreshPromise) {
        await ctx.runtime.container.firstRefreshPromise
      }
      const p = refresh(...args)
      if (!ctx.runtime.container.firstRefreshPromise) {
        ctx.runtime.container.firstRefreshPromise = p
      }
      await p
    }
  },
}

/**
 * 大运河刷新表达式扩展插件
 */
export const pluginCanalExpressionExtensionRefresh: Plugin<
  PluginCanalExpressionExtensionRefreshPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_EXPRESSION_EXTENSION_REFRESH',
  position: {
    type: 'after',
    targetId: PluginExpressionExtension.id,
  },
  extendsContainer(container) {
    container.initParams = container.runtime.constructorOptions.config?.params
    extendObservable(
      container,
      {
        refreshingTypes: [],
      },
      {
        refreshingTypes: observable.ref,
      },
    )
    Object.defineProperties(container, {
      refreshingType: {
        get() {
          return last(container.refreshingTypes)?.[1]
        },
      },
      isRefreshing: {
        get() {
          return !!container.refreshingTypes.length
        },
      },
    })
    container.registerExpressionExtension(canalExpressionExtensionRefresh)
  },
}
