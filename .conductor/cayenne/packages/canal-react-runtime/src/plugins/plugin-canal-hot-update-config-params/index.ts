import { E2ESchema } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { isEqual } from 'lodash'
import {
  ConfigParams,
  PluginCanalExpressionExtensionRefreshPropertiesExt,
} from '../plugin-canal-expr-ext-refresh'

/**
 * 大运河热更新配置参数插件的扩展属性
 */
export type PluginCanalHotUpdateConfigParamsPropertiesExt =
  PluginCanalExpressionExtensionRefreshPropertiesExt & {
    container: {
      /**
       * 热更新配置参数
       * @params newParams 新的配置参数
       * @params refreshImmediately 立即刷新，默认为 `true`
       */
      hotUpdateConfigParams: (
        newParams?: ConfigParams,
        refreshImmediately?: boolean,
      ) => Promise<void>
    }
  }

/**
 * 大运河热更新配置参数插件
 */
export const pluginCanalHotUpdateConfigParams: Plugin<
  PluginCanalHotUpdateConfigParamsPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_HOT_UPDATE_CONFIG_PARAMS',
  extendsContainer(container) {
    container.hotUpdateConfigParams = (async (
      newParams,
      refreshImmediately = true,
    ) => {
      // 处理 Runtime
      const config = container.runtime.constructorOptions.config
      if (isEqual(config?.params, newParams)) {
        return
      }
      if (config) {
        config.params = newParams
      }
      if (refreshImmediately) {
        await container.evalExpression(
          {
            type: 'refresh',
            refreshType: 'outside-params',
          },
          {
            silent: true,
            relativePath: ['hotUpdateConfigParams|outside-params'],
          },
        )()
      }
    }) as typeof container.hotUpdateConfigParams
  },
}
