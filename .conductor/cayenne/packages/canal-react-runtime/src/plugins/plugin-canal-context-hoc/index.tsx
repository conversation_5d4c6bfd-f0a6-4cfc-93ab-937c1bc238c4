import { canalReactRuntimeComponentContext } from '@ad/canal-react-component-context'
import { E2ESchema } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { PluginReactRendererPropertiesExt } from '@kael/runtime-plugin-react-renderer'
import React from 'react'

/**
 * 大运河上下文高阶组件插件的扩展属性
 */
export type PluginCanalContextHocPropertiesExt =
  PluginReactRendererPropertiesExt

/**
 * 大运河上下文高阶组件插件
 */
export const pluginCanalContextHoc: Plugin<
  PluginCanalContextHocPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_CONTEXT_HOC',
  extendsContainer(container) {
    container.rendererComponentsHocs.componentHocs.unshift((Comp) => {
      return (props) => {
        return (
          <canalReactRuntimeComponentContext.Provider value={props.component}>
            <Comp {...props} />
          </canalReactRuntimeComponentContext.Provider>
        )
      }
    })
  },
}
