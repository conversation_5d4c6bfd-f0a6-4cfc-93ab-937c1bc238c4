import '@kael/runtime'
import { Plugin } from '@kael/runtime'
import { pluginExpressionExtensionArray } from '@kael/runtime-plugin-expr-ext-array'
import { PluginExpressionExtensionJS } from '@kael/runtime-plugin-expr-ext-js'
import { pluginExpressionExtensionObject } from '@kael/runtime-plugin-expr-ext-object'
import { pluginId } from '@kael/runtime-plugin-id'
import { pluginMobxContainerView } from '@kael/runtime-plugin-mobx-container-view'
import { pluginMobxReactRenderer } from '@kael/runtime-plugin-mobx-react-renderer'
import '@kael/runtime-plugin-react-renderer'
import { pluginReactRenderer } from '@kael/runtime-plugin-react-renderer'
import { pluginCanalComponentProps } from './plugin-canal-component-props'
import { pluginCanalContextHoc } from './plugin-canal-context-hoc'
import { pluginCanalData } from './plugin-canal-data'
import { pluginCanalEffect } from './plugin-canal-effect'
import { pluginCanalEvalExpression } from './plugin-canal-eval-expression'
import { pluginCanalExpressionExtensionActions } from './plugin-canal-expr-ext-actions'
import { pluginCanalExpressionExtensionBind } from './plugin-canal-expr-ext-bind'
import { pluginCanalExpressionExtensionGetData } from './plugin-canal-expr-ext-get-data'
import { pluginCanalExpressionExtensionOpenUrl } from './plugin-canal-expr-ext-open-url'
import { pluginCanalExpressionExtensionRefresh } from './plugin-canal-expr-ext-refresh'
import { pluginCanalExpressionExtensionTrack } from './plugin-canal-expr-ext-track'
import { pluginCanalHotUpdateConfigParams } from './plugin-canal-hot-update-config-params'
import { pluginCanalHotUpdateSchema } from './plugin-canal-hot-update-schema'
import { pluginCanalIIFE } from './plugin-canal-iife'
import { pluginCanalInitRefresh } from './plugin-canal-init-refresh'
import { pluginCanalLinkage } from './plugin-canal-linkage'
import { pluginCanalLoadComponentCodes } from './plugin-canal-load-component-codes'
import { pluginCanalLoading } from './plugin-canal-loading'
import { pluginCanalModel } from './plugin-canal-model'
import { pluginCanalOnChangeProxy } from './plugin-canal-on-change-proxy'
import { pluginCanalParentRuntime } from './plugin-canal-parent-runtime'
import { pluginCanalRenderer } from './plugin-canal-renderer'
import { pluginCanalRuntimeId } from './plugin-canal-runtime-id'
import { pluginCanalTrack } from './plugin-canal-track'
import { pluginComponentChildrenByIfFor } from './plugin-component-children-by-if-for'
import { pluginComponentControllerFor } from './plugin-component-controller-for'
import { pluginComponentControllerIf } from './plugin-component-controller-if'
import { pluginComponentControllerInstancesByIfFor } from './plugin-component-controller-instances-by-if-for'
import { pluginExpressionExtensionComponentReact } from './plugin-expr-ext-component-react'

export * from './plugin-canal-component-props'
export * from './plugin-canal-context-hoc'
export * from './plugin-canal-data'
export * from './plugin-canal-effect'
export * from './plugin-canal-expr-ext-actions'
export * from './plugin-canal-expr-ext-bind'
export * from './plugin-canal-expr-ext-get-data'
export * from './plugin-canal-expr-ext-open-url'
export * from './plugin-canal-expr-ext-refresh'
export * from './plugin-canal-expr-ext-track'
export * from './plugin-canal-hot-update-config-params'
export * from './plugin-canal-hot-update-schema'
export * from './plugin-canal-iife'
export * from './plugin-canal-init-refresh'
export * from './plugin-canal-linkage'
export * from './plugin-canal-load-component-codes'
export * from './plugin-canal-loading'
export * from './plugin-canal-model'
export * from './plugin-canal-on-change-proxy'
export * from './plugin-canal-parent-runtime'
export * from './plugin-canal-renderer'
export * from './plugin-canal-runtime-id'
export * from './plugin-canal-track'
export * from './plugin-component-children-by-if-for'
export * from './plugin-component-controller-for'
export * from './plugin-component-controller-if'
export * from './plugin-component-controller-instances-by-if-for'
export * from './plugin-expr-ext-component-react'

/**
 * 大运河 React 运行时额外使用的插件
 */
export const canalReactPlugins = [
  // 公共插件
  pluginExpressionExtensionArray,
  pluginExpressionExtensionObject,
  pluginId,
  pluginMobxContainerView,
  new PluginExpressionExtensionJS({ cache: true }) as unknown as Plugin,
  // 公共插件：复制重写
  pluginComponentChildrenByIfFor,
  pluginComponentControllerFor,
  pluginComponentControllerIf,
  pluginComponentControllerInstancesByIfFor,
  pluginExpressionExtensionComponentReact,
  // 公共插件：保留顺序
  pluginReactRenderer,
  pluginMobxReactRenderer,
  // 内部插件
  pluginCanalComponentProps,
  pluginCanalContextHoc,
  pluginCanalData,
  pluginCanalEvalExpression,
  pluginCanalExpressionExtensionActions,
  pluginCanalExpressionExtensionBind,
  pluginCanalExpressionExtensionGetData,
  pluginCanalExpressionExtensionOpenUrl,
  pluginCanalExpressionExtensionRefresh,
  pluginCanalExpressionExtensionTrack,
  pluginCanalHotUpdateConfigParams,
  pluginCanalHotUpdateSchema,
  pluginCanalInitRefresh,
  pluginCanalLoadComponentCodes,
  pluginCanalLoading,
  pluginCanalModel,
  pluginCanalOnChangeProxy,
  pluginCanalParentRuntime,
  pluginCanalRenderer,
  pluginCanalRuntimeId,
  pluginCanalTrack,
  // 内部插件：保留顺序
  pluginCanalEffect,
  pluginCanalLinkage,
  pluginCanalIIFE,
] as const
