import {
  CanalReactRuntimeLoadingInfo,
  canalReactRuntimeLoadingInfoContext,
} from '@ad/canal-react-component-context'
import { LoadingProps } from '@ad/canal-react-components'
import { INTERNAL_COMPONENT_LIB_NAME } from '@ad/e2e-material-schema-utils'
import { E2ESchema } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { pluginMobxReactRenderer } from '@kael/runtime-plugin-mobx-react-renderer'
import { computed } from 'mobx'
import { observer } from 'mobx-react-lite'
import React, { FC, useEffect } from 'react'
import { PluginCanalExpressionExtensionRefreshPropertiesExt } from '../plugin-canal-expr-ext-refresh'
import { PluginCanalLoadComponentCodesPropertiesExt } from '../plugin-canal-load-component-codes'

/**
 * 大运河加载中插件的扩展属性
 */
export type PluginCanalLoadingPropertiesExt =
  PluginCanalLoadComponentCodesPropertiesExt &
    PluginCanalExpressionExtensionRefreshPropertiesExt & {
      runtimeConstructorOptions: {
        /**
         * 配置
         */
        config?: {
          /**
           * 加载之后的渲染副作用回调
           */
          onRenderEffectAfterLoading?(moduleId: string): void
          /**
           * 首次加载之后的渲染副作用回调
           */
          onRenderEffectAfterFirstLoading?(moduleId: string): void
        }
      }
      container: {
        /**
         * 是否加载中
         */
        isLoading: boolean
        /**
         * 加载之后的挂载次数
         */
        mountAfterLoadingCount: number
      }
    }

/**
 * 大运河加载中插件
 */
export const pluginCanalLoading: Plugin<
  PluginCanalLoadingPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_LOADING',
  position: {
    type: 'before',
    targetId: pluginMobxReactRenderer.id,
  },
  extendsContainer(container) {
    const {
      schemaId,
      onRenderEffectAfterLoading,
      onRenderEffectAfterFirstLoading,
    } = container.runtime.constructorOptions.config || {}

    const computedIsLoading = computed(() => {
      return !!(
        container.isLoadingDependencies || container.refreshingTypes.length
      )
    })
    Object.defineProperty(container, 'isLoading', {
      get() {
        return computedIsLoading.get()
      },
    })
    container.mountAfterLoadingCount = 0
    container.rendererComponentsHocs.containerHocs.push((Comp) => {
      return observer((props) => {
        const Loading = (
          container.dependencies?.[INTERNAL_COMPONENT_LIB_NAME] as Record<
            string,
            unknown
          >
        )['Loading'] as FC<LoadingProps>
        const {
          isLoading,
          isRefreshing,
          refreshingType,
          isLoadingDependencies,
          schema,
        } = container
        const loadingInfo: CanalReactRuntimeLoadingInfo = {
          isLoading,
          isRefreshing,
          refreshingType,
          isLoadingDependencies,
          Loading,
          schema,
        }
        useEffect(() => {
          if (!isLoading) {
            if (!container.mountAfterLoadingCount++) {
              onRenderEffectAfterFirstLoading?.(schemaId as string)
            }
            onRenderEffectAfterLoading?.(schemaId as string)
          }
        }, [isLoading])
        return (
          <canalReactRuntimeLoadingInfoContext.Provider value={loadingInfo}>
            {isLoadingDependencies ? null : <Comp {...props} />}
            {isLoading ? (
              <Loading
                isRefreshing={isRefreshing}
                refreshingType={refreshingType}
                isLoadingDependencies={isLoadingDependencies}
                schema={schema}
                position="runtime"
              />
            ) : null}
          </canalReactRuntimeLoadingInfoContext.Provider>
        )
      })
    })
  },
}
