import {
  Plugin,
  PluginComponentControllerInstances,
  PluginEvalExpressionPropertiesExt,
  PluginSchemaPropertiesExt,
} from '@kael/runtime'
import { isUndefined } from 'lodash'
import { autorun, extendObservable, observable } from 'mobx'

/**
 * SchemaComponent 组件循环配置的表达式返回值
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface SchemaForResult<T = any> {
  /**
   * 扩展字段
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
  /**
   * 需要循环的数据
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  items: T[]
  /**
   * 每一项数据的变量名，默认为 'item'
   */
  itemName?: string
  /**
   * 每一项数据下标的变量名，默认为 'index'
   */
  indexName?: string
  /**
   * 每一项数据的唯一标识。
   * 字符串表示 item => item['the string']。
   * 默认为：(_, i) => i
   */
  itemKey?: string | ((item: T, index: number) => string | number)
}

/**
 * 组件控制器条件渲染插件的扩展属性
 */
export type PluginComponentControllerForPropertiesExt =
  PluginSchemaPropertiesExt &
    PluginEvalExpressionPropertiesExt & {
      componentController: {
        /**
         * 条件渲染计算结果
         */
        for?: SchemaForResult
      }
    }

/**
 * 组件控制器条件渲染插件
 */
export const pluginComponentControllerFor: Plugin<PluginComponentControllerForPropertiesExt> =
  {
    id: 'COMPONENT_CONTROLLER_FOR',
    position: {
      type: 'before',
      targetId: PluginComponentControllerInstances.id,
    },
    extendsComponentController(componentController) {
      extendObservable(
        componentController,
        {
          for: undefined,
        },
        {
          for: observable.ref,
        },
      )
      const {
        schema: { for: schemaFor },
      } = componentController
      if (!isUndefined(schemaFor)) {
        return autorun(() => {
          const ret = componentController.evalExpression(schemaFor, {
            silent: true,
            relativePath: ['for'],
          })
          const forRet: Partial<SchemaForResult> = Array.isArray(ret)
            ? {
                items: ret,
              }
            : ret
          componentController.for = {
            ...forRet,
            items: [...(forRet.items || [])],
          }
        })
      }
    },
  }
