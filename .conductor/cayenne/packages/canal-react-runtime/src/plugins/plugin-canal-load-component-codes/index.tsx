import { Promisable, waitPromiseSlient } from '@ad/canal-shared'
import { E2ESchema } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { PluginReactRendererPropertiesExt } from '@kael/runtime-plugin-react-renderer'
import isPromise from 'is-promise'
import { extendObservable, runInAction } from 'mobx'
import {
  ComponentCodeDependencies,
  ComponentCodeFallback,
  loadComponentCodes,
} from '../../utils'
import { captureException } from '../../weblog'

/**
 * 大运河加载组件代码插件的扩展属性
 */
export type PluginCanalLoadComponentCodesPropertiesExt =
  PluginReactRendererPropertiesExt & {
    runtimeConstructorOptions: {
      /**
       * 配置
       */
      config?: {
        /**
         * 组件代码回退
         */
        componentCodeFallback?: ComponentCodeFallback
      }
    }
    container: {
      /**
       * 是否依赖加载中
       */
      isLoadingDependencies: boolean
      /**
       * 加载组件代码
       */
      loadComponentCodes: () => Promisable<void>
    }
  }

/**
 * 大运河加载组件代码插件
 */
export const pluginCanalLoadComponentCodes: Plugin<
  PluginCanalLoadComponentCodesPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_LOAD_COMPONENT_CODES',
  extendsContainer(container) {
    extendObservable(container, {
      isLoadingDependencies: false,
    })
    let lastDeps: Promisable<ComponentCodeDependencies> = {}
    container.loadComponentCodes = (() => {
      try {
        const deps = loadComponentCodes(
          container.schema.componentCodes,
          container.dependencies,
          container.runtime.constructorOptions.config?.componentCodeFallback,
        )
        const addToDependencies = (ds: ComponentCodeDependencies): void => {
          if (!container.runtime.constructorOptions.config) {
            container.runtime.constructorOptions.config = {}
          }
          const config = container.runtime.constructorOptions.config
          if (!config.dependencies) {
            config.dependencies = {}
          }
          Object.assign(config.dependencies, ds)
        }
        const waitDeps = lastDeps
        lastDeps = deps
        if (isPromise(deps)) {
          runInAction(() => {
            container.isLoadingDependencies = true
          })
          return (async (): Promise<void> => {
            await waitPromiseSlient(waitDeps)
            try {
              addToDependencies(await deps)
            } catch (err) {
              if (err instanceof Error) {
                captureException(err)
              }
              throw err
            } finally {
              runInAction(() => {
                container.isLoadingDependencies = false
              })
            }
          })()
        } else {
          addToDependencies(deps)
        }
      } catch (err) {
        if (err instanceof Error) {
          captureException(err)
        }
        throw err
      }
    }) as typeof container.loadComponentCodes
    container.loadComponentCodes()
  },
}
