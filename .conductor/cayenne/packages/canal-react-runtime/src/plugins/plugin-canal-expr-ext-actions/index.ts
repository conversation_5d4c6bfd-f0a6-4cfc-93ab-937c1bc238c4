import { E2ESchema, E2ESchemaExpressionActions } from '@ad/e2e-schema'
import {
  ExpressionExtension,
  Plugin,
  PluginEvalExpressionPropertiesExt,
  PluginExpressionExtension,
} from '@kael/runtime'
import { PluginCanalDataPropertiesExt } from '../plugin-canal-data'

/**
 * 大运河多操作表达式
 */
export const canalExpressionExtensionActions: ExpressionExtension<
  E2ESchemaExpressionActions,
  PluginEvalExpressionPropertiesExt
> = {
  type: 'actions',
  eval({ fns: fnExprs, parallel = true }, ctx, options) {
    const relativePath = options?.relativePath || []
    const fns = fnExprs.map((fnExpr, i) =>
      ctx.evalExpression(fnExpr, {
        ...options,
        relativePath: [...relativePath, 'fns', i],
      }),
    ) as ((...args: unknown[]) => unknown)[]
    if (parallel) {
      return function (this: unknown, ...args: unknown[]) {
        return fns.map((fn) => fn.call(this, ...args))
      }
    } else {
      return async function (this: unknown, ...args: unknown[]) {
        const rets: unknown[] = []
        for (const fn of fns) {
          rets.push(await fn.call(this, ...args))
        }
        return rets
      }
    }
  },
}

/**
 * 大运河多操作表达式扩展插件
 */
export const pluginCanalExpressionExtensionActions: Plugin<
  PluginCanalDataPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_EXPRESSION_EXTENSION_ACTIONS',
  position: {
    type: 'after',
    targetId: PluginExpressionExtension.id,
  },
  extendsContainer(container) {
    container.registerExpressionExtension(canalExpressionExtensionActions)
  },
}
