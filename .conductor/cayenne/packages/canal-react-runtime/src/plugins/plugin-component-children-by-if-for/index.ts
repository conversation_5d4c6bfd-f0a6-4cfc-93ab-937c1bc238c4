import {
  Component,
  ComponentController,
  Plugin,
  PluginComponentChildren,
  PluginComponentKeyPropertiesExt,
  PluginEvalExpressionPropertiesExt,
  PluginSchemaPropertiesExt,
} from '@kael/runtime'
import { IReactionDisposer, autorun, observable } from 'mobx'

/**
 * 组件属性插件的扩展属性
 */
export type PluginComponentChildrenByIfForPropertiesExt =
  PluginSchemaPropertiesExt &
    PluginComponentKeyPropertiesExt &
    PluginEvalExpressionPropertiesExt

/**
 * 子组件 if & for 插件
 */
export const pluginComponentChildrenByIfFor: Plugin<PluginComponentChildrenByIfForPropertiesExt> =
  {
    id: 'COMPONENT_CHILDREN_BY_IF_FOR',
    position: {
      type: 'before',
      targetId: PluginComponentChildren.id,
      replaceIds: [PluginComponentChildren.id],
    },
    extendsComponent(component) {
      const {
        schema: { children: schemaChildren = [] },
      } = component
      const childControllers: ComponentController<PluginComponentChildrenByIfForPropertiesExt>[] =
        []
      const childString = observable.box('')
      let childStringDisposer: IReactionDisposer | undefined

      if (Array.isArray(schemaChildren)) {
        for (let i = 0; i < schemaChildren.length; i++) {
          childControllers.push(
            component.runtime.createComponentController({
              parent: component,
              schema: schemaChildren[i],
              key: `${i}`,
            }),
          )
        }
      } else {
        childStringDisposer = autorun(() => {
          childString.set(
            `${component.evalExpression(schemaChildren, {
              silent: true,
              relativePath: ['children'],
            })}`,
          )
        })
      }

      Object.defineProperty(component, 'children', {
        get() {
          if (Array.isArray(schemaChildren)) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const ret: Component<any>[] = []
            for (const c of childControllers) {
              ret.push(...c.instances)
            }
            return ret
          } else {
            return childString.get()
          }
        },
      })

      return (): void => {
        childStringDisposer?.()
        for (const controller of childControllers) {
          controller.dispose()
        }
      }
    },
  }
