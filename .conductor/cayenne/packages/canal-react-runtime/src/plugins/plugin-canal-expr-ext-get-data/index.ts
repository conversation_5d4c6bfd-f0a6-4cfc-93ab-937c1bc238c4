import { E2ESchema, E2ESchemaExpressionGetData } from '@ad/e2e-schema'
import {
  ExpressionExtension,
  Plugin,
  PluginExpressionExtension,
} from '@kael/runtime'
import { get } from 'lodash'
import { PluginCanalDataPropertiesExt } from '../plugin-canal-data'

/**
 * 大运河获取数据表达式
 */
export const canalExpressionExtensionGetData: ExpressionExtension<
  E2ESchemaExpressionGetData,
  PluginCanalDataPropertiesExt
> = {
  type: 'get-data',
  eval({ path }, ctx) {
    return get(ctx.runtime.container.data, path)
  },
}

/**
 * 大运河获取数据表达式扩展插件
 */
export const pluginCanalExpressionExtensionGetData: Plugin<
  PluginCanalDataPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_EXPRESSION_EXTENSION_GET_DATA',
  position: {
    type: 'after',
    targetId: PluginExpressionExtension.id,
  },
  extendsContainer(container) {
    container.registerExpressionExtension(canalExpressionExtensionGetData)
  },
}
