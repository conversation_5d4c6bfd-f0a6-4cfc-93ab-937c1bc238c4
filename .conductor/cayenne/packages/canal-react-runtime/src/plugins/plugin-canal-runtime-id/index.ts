import { E2ESchema } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { uniqueId } from 'lodash'

/**
 * 大运河运行时 ID 插件的扩展属性
 */
export interface PluginCanalRuntimeIdPropertiesExt {
  container: {
    /**
     * 运行时 ID
     */
    runtimeId: string
  }
}

/**
 * 大运河运行时 ID 插件
 */
export const pluginCanalRuntimeId: Plugin<
  PluginCanalRuntimeIdPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_RUNTIME_ID',
  extendsContainer(container) {
    const { schemaId } = (container.runtime.constructorOptions.config ||
      {}) as { schemaId?: string | number }
    container.runtimeId = uniqueId(`${schemaId}:`)
  },
}
