import { E2ESchema } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { PluginMobxContainerViewPropertiesExt } from '@kael/runtime-plugin-mobx-container-view'
import { action } from 'mobx'
import { PluginCanalEffectPropertiesExt } from '../plugin-canal-effect'
import { PluginCanalLinkagePropertiesExt } from '../plugin-canal-linkage'
import { PluginCanalLoadComponentCodesPropertiesExt } from '../plugin-canal-load-component-codes'
import { PluginCanalModelPropertiesExt } from '../plugin-canal-model'

/**
 * 大运河热更新 Schema 插件的扩展属性
 */
export type PluginCanalHotUpdateSchemaPropertiesExt =
  PluginMobxContainerViewPropertiesExt &
    PluginCanalLinkagePropertiesExt &
    PluginCanalModelPropertiesExt &
    PluginCanalEffectPropertiesExt &
    PluginCanalLoadComponentCodesPropertiesExt & {
      runtimeConstructorOptions: {
        /**
         * 配置
         */
        config?: {
          /**
           * 版本对比
           */
          versionCompare?: VersionCompare
        }
      }
      container: {
        /**
         * 热更新 Schema
         * @param newSchema 新的 Schema
         */
        hotUpdateSchema: (newSchema: E2ESchema) => void
      }
    }

/**
 * 版本对比
 * @returns
 * - `0` if `v1` == `v2`
 * - `1` if `v1` is greater
 * - `-1` if `v2` is greater.
 */
export type VersionCompare = (v1?: string, v2?: string) => 1 | 0 | -1

/**
 * 默认的版本对比
 */
export const defaultVersionCompare: VersionCompare = (v1, v2) => {
  const v1n = v1 ? Number(v1) : 0
  const v2n = v2 ? Number(v2) : 0
  return v1n > v2n ? 1 : v1n < v2n ? -1 : 0
}

/**
 * 大运河热更新 Schema 插件
 */
export const pluginCanalHotUpdateSchema: Plugin<
  PluginCanalHotUpdateSchemaPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_HOT_UPDATE_SCHEMA',
  extendsContainer(container) {
    const { versionCompare = defaultVersionCompare } =
      container.runtime.constructorOptions.config || {}
    container.hotUpdateSchema = action((newSchema) => {
      if (versionCompare(newSchema.version, container.schema.version) === -1) {
        // 跳过老版本
        return
      }
      // 处理 Runtime
      container.runtime.constructorOptions.schema = newSchema
      // 处理 PluginSchema
      container.schema = newSchema
      // 加载组件代码
      container.loadComponentCodes()
      // 处理 pluginCanalModel
      container.checkAndCreateGlobalModel()
      container.checkAndCreateModel()
      // 处理 pluginMobxContainerView
      container.recreateView()
      // 处理 pluginCanalData
      container.setData(newSchema.data, {
        autoRefreshEnabled: false,
        effectEnabled: false,
      })
      // 处理 pluginCanalEffect
      container.hotUpdateEffects()
      // 处理 pluginCanalLinkage
      container.hotUpdateLinkage()
    }) as typeof container.hotUpdateSchema
  },
}
