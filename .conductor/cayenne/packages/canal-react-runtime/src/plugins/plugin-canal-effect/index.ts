import { runAfterAction } from '@ad/canal-shared'
import { E2ESchema } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { dfsGenComponentDetailBySchema } from '@kael/schema-utils'
import { isFunction } from 'lodash'
import { IReactionDisposer, reaction } from 'mobx'
import { PluginCanalDataPropertiesExt } from '../plugin-canal-data'

/**
 * 大运河副作用插件的扩展属性
 */
export type PluginCanalEffectPropertiesExt = PluginCanalDataPropertiesExt & {
  container: {
    /**
     * 热更新副作用
     */
    hotUpdateEffects: () => void
  }
}

/**
 * 大运河副作用插件
 */
export const pluginCanalEffect: Plugin<
  PluginCanalEffectPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_EFFECT',
  extendsContainer(container) {
    container.effectEnabled = true
    /**
     * 初始化联动
     */
    function initEffects(): () => void {
      const disposers: IReactionDisposer[] = []
      for (const cd of dfsGenComponentDetailBySchema(container.schema)) {
        const effect = cd.component.effect
        if (!effect) continue
        disposers.push(
          reaction(
            () => container.data[cd.component.id],
            (componentValue, prevComponentValue) => {
              if (!container.autoRefreshEnabled) {
                return
              }
              // 脱离当前 action，避免 setData options 不生效
              runAfterAction(() => {
                // console.log('initEffects reaction runAfterAction', {
                //   cd,
                //   componentValue,
                //   prevComponentValue,
                // })
                container.prepareSetDataInAllRuntimes(
                  {
                    autoRefreshEnabled: false,
                    effectEnabled: false,
                  },
                  () => {
                    const fn = container.evalExpression(effect)
                    if (isFunction(fn)) {
                      fn(componentValue, prevComponentValue, container)
                    }
                  },
                )
              })
            },
          ),
        )
      }
      return () => {
        for (const d of disposers) {
          d()
        }
      }
    }
    let disposer = initEffects()
    container.hotUpdateEffects = (() => {
      disposer()
      disposer = initEffects()
    }) as typeof container.hotUpdateEffects
    return () => {
      disposer()
    }
  },
}
