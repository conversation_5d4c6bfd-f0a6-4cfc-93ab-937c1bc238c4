import { E2ESchema } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { PluginCanalExpressionExtensionRefreshPropertiesExt } from '../plugin-canal-expr-ext-refresh'

/**
 * 大运河初始化刷新插件的扩展属性
 */
export type PluginCanalInitRefreshPropertiesExt =
  PluginCanalExpressionExtensionRefreshPropertiesExt & {
    container: {
      /**
       * 初始化刷新，用于通过 schemaId 的首次加载
       */
      initRefresh: () => Promise<void>
    }
  }

/**
 * 大运河初始化刷新插件
 */
export const pluginCanalInitRefresh: Plugin<
  PluginCanalInitRefreshPropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_INIT_REFRESH',
  extendsContainer(container) {
    container.initRefresh = (() => {
      return container.evalExpression(
        {
          type: 'refresh',
          refreshType: 'default',
        },
        {
          silent: true,
          relativePath: ['initRefresh|default'],
        },
      )()
    }) as typeof container.initRefresh
  },
}
