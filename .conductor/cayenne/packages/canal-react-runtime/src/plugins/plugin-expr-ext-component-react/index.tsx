import {
  ComponentController,
  DefaultPluginsPropertiesExt,
  ExpressionExtension,
  Plugin,
  PluginExpressionExtension,
} from '@kael/runtime'
import { SchemaComponent, SchemaNormalizedExpression } from '@kael/schema'
import React, { ReactNode } from 'react'
import { PluginCanalRendererPropertiesExt } from '../plugin-canal-renderer'

/**
 * 组件表达式扩展 React 插件的扩展属性
 */
export type PluginExpressionExtensionComponentReactPropertiesExt =
  DefaultPluginsPropertiesExt &
    PluginCanalRendererPropertiesExt & {
      context: {
        /**
         * 组件表达式缓存，表达式路径 -> 缓存项
         */
        // eslint-disable-next-line @typescript-eslint/naming-convention
        _componentExprCache: Record<string, ComponentExprCacheItem>
      }
    }

/**
 * 组件表达式缓存项
 */
export interface ComponentExprCacheItem {
  /**
   * 表达式返回的 JSX
   */
  jsx: ReactNode
  /**
   * 动态创建的组件控制器
   */
  componentController: ComponentController
}

/**
 * 组件表达式
 */
export const expressionExtensionComponentReact: ExpressionExtension<
  SchemaNormalizedExpression & {
    /**
     * 组件
     */
    value: SchemaComponent
  },
  PluginExpressionExtensionComponentReactPropertiesExt
> = {
  type: 'component',
  eval({ value }, ctx, options) {
    const relativePath = options?.relativePath
    if (!relativePath) {
      throw new Error(
        `relativePath is undefined while evaling component expression`,
      )
    }
    const relativePathStr = JSON.stringify(relativePath)
    const cacheItem = ctx._componentExprCache[relativePathStr]
    if (cacheItem) {
      return cacheItem.jsx
    }
    const componentController = ctx.runtime.createComponentController({
      parent: ctx,
      schema: value,
    })
    // const jsx = <componentController.DynamicReactRenderer /> 会导致 refresh 时 VDOM 子树整体销毁重建
    // const jsx = componentController.DynamicReactRenderer({}) 则导致父组件无法覆盖子组件的 props
    const jsx = (
      <ctx.runtime.container.ComponentControllerDynamic
        componentController={componentController}
      />
    )
    ctx._componentExprCache[relativePathStr] = {
      jsx,
      componentController,
    }
    return jsx
  },
}

/**
 * 组件表达式扩展 React 插件
 */
export const pluginExpressionExtensionComponentReact: Plugin<PluginExpressionExtensionComponentReactPropertiesExt> =
  {
    id: 'EXPRESSION_EXTENSION_COMPONENT_REACT',
    position: {
      type: 'after',
      targetId: PluginExpressionExtension.id,
    },
    extendsContainer(container) {
      container.registerExpressionExtension(expressionExtensionComponentReact)
    },
    extendsContext(ctx) {
      ctx._componentExprCache = {}
      return () => {
        for (const key in ctx._componentExprCache) {
          ctx._componentExprCache[key].componentController.dispose()
        }
      }
    },
  }
