import { runAfterAction } from '@ad/canal-shared'
import { E2ESchema, RefreshType } from '@ad/e2e-schema'
import { Plugin } from '@kael/runtime'
import { pick } from 'lodash'
import { reaction } from 'mobx'
import { PluginCanalDataPropertiesExt } from '../plugin-canal-data'

/**
 * 大运河联动插件的扩展属性
 */
export type PluginCanalLinkagePropertiesExt = PluginCanalDataPropertiesExt & {
  container: {
    /**
     * 通过联动（字段）获取刷新参数
     */
    getRefreshParamsByLinkage: (
      refreshType: RefreshType,
    ) => Record<string, unknown>
    /**
     * 热更新联动
     */
    hotUpdateLinkage: () => void
  }
}

/**
 * 大运河联动插件
 */
export const pluginCanalLinkage: Plugin<
  PluginCanalLinkagePropertiesExt,
  E2ESchema
> = {
  id: 'CANAL_LINKAGE',
  extendsContainer(container) {
    container.autoRefreshEnabled = true
    /**
     * 初始化联动
     */
    function initLinkage(): () => void {
      const {
        linkage: {
          commonParams = {},
          componentDataParams: { common = [], byRefreshType = {} } = {},
          autoRefreshByComponent = [],
        } = {},
      } = container.schema
      container.getRefreshParamsByLinkage = ((refreshType) => {
        return {
          ...commonParams,
          ...pick(container.data, common, byRefreshType[refreshType] || []),
        }
      }) as typeof container.getRefreshParamsByLinkage
      const autoRefreshReactionDisposers = autoRefreshByComponent.map(
        (componentId) =>
          reaction(
            () => container.data[componentId],
            (componentValue) => {
              if (!container.autoRefreshEnabled) {
                return
              }
              // 对齐 pluginCanalEffect，保持在副作用之后执行
              runAfterAction(() => {
                // console.log('initLinkage reaction runAfterAction', {
                //   componentId,
                //   componentValue,
                // })
                container.evalExpression(
                  {
                    type: 'refresh',
                    refreshType: 'auto',
                    params: {
                      [componentId]: componentValue,
                    },
                  },
                  {
                    silent: true,
                    relativePath: ['linkage|auto'],
                  },
                )()
              })
            },
          ),
      )
      return () => {
        for (const d of autoRefreshReactionDisposers) {
          d()
        }
      }
    }
    let disposer = initLinkage()
    container.hotUpdateLinkage = (() => {
      disposer()
      disposer = initLinkage()
    }) as typeof container.hotUpdateLinkage
    return () => {
      disposer()
    }
  },
}
