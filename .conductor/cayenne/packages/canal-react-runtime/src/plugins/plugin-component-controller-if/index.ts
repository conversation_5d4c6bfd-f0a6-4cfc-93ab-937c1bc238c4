import {
  Plugin,
  PluginComponentControllerInstances,
  PluginEvalExpressionPropertiesExt,
  PluginSchemaPropertiesExt,
} from '@kael/runtime'
import { isUndefined } from 'lodash'
import { autorun, extendObservable } from 'mobx'

/**
 * 组件控制器条件渲染插件的扩展属性
 */
export type PluginComponentControllerIfPropertiesExt =
  PluginSchemaPropertiesExt &
    PluginEvalExpressionPropertiesExt & {
      componentController: {
        /**
         * 条件渲染计算结果
         */
        if: boolean
      }
    }

/**
 * 组件控制器条件渲染插件
 */
export const pluginComponentControllerIf: Plugin<PluginComponentControllerIfPropertiesExt> =
  {
    id: 'COMPONENT_CONTROLLER_IF',
    position: {
      type: 'before',
      targetId: PluginComponentControllerInstances.id,
    },
    extendsComponentController(componentController) {
      extendObservable(componentController, {
        if: true,
      })
      const {
        schema: { if: schemaIf },
      } = componentController
      if (!isUndefined(schemaIf)) {
        return autorun(() => {
          componentController.if = !!componentController.evalExpression(
            schemaIf,
            { silent: true, relativePath: ['if'] },
          )
        })
      }
    },
  }
