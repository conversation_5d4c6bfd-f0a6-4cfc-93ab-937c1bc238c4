# canal

大运河

# 开发

## 项目初始化

```sh
# 安装依赖
pnpm i
# 编译一次，支持后续 start:* 相关命令正常运行
pnpm build
```

## 文件命名方式

所有文件、文件夹都使用小写字母（a-z）、数字（0-9）、`-`、`.`命名，包括 readme.md。

例外：

- `node_modules`
- `__tests__`: 单测文件夹

## 子包

- packages
  - canal-backend: 大运河后端，Node.js
  - canal-platform: 大运河平台，管理端
  - canal-react-component-context: 大运河 React 组件上下文
  - canal-react-components: 大运河 React 组件
  - canal-react-runtime: 大运河 React 运行时
  - canal-react-runtime-demo: 大运河 React 运行时 Demo
  - canal-shared: 大运河内的共享代码
  - canal-shared-ui: 大运河内的前端共享代码
  - e2e-material-schema: 端到端物料协议
  - e2e-schema: 端到端协议
  - e2e-schema-utils: 端到端协议工具集

## 脚本

| 脚本                     | 说明                                                          |
| ------------------------ | ------------------------------------------------------------- |
| add-owner                | 给其他人添加 npm 包权限，用法：`pnpm run add-owner lishuihua` |
| build                    | 编译所有子包                                                  |
| build:backend            | 编译大运河后端（包括依赖）                                    |
| build:devtools           | 编译大运河开发者工具（包括依赖）                              |
| build:npm                | 编译 npm 相关子包                                             |
| build:platform:online    | 编译大运河平台（包括依赖）线上环境                            |
| build:platform:staging   | 编译大运河平台（包括依赖）Staging 环境                        |
| build:react-runtime-demo | 编译大运河 React 运行时 Demo （包括依赖）                     |
| deploy:jinx              | 部署 Jinx                                                     |
| lint                     | 全局运行 eslint，检查 ts/tsx 文件                             |
| publish-npm              | 发布所有 npm 相关子包                                         |
| publish-npm:pre-release  | 发布所有 npm 相关子包（预发布版本）                           |
| start                    | 开始开发所有子包                                              |
| start:backend            | 开始开发大运河后端（包括依赖）                                |
| start:bp                 | 开始开发大运河后端和平台（包括依赖）                          |
| start:platform           | 开始开发大运河平台（包括依赖）                                |
| start:react-runtime-demo | 开始开发大运河 React 运行时 Demo （包括依赖）                 |
| test                     | 针对所有子包运行单测                                          |
| test:w                   | 针对所有子包以 watch 模式运行单测                             |

## lint

- 全局 eslint 规则降级需要先在群里讨论，达成共识后再改
  - 全局 eslint 规则：根目录 .eslintrc.js 文件里指定的规则
  - 降级：error 改 warn 或关掉规则
- 没有特殊情况，子包里不创建 .eslintrc.js 文件来覆盖全局 eslint 规则

## 分支

采用 [git-flow](https://nvie.com/posts/a-successful-git-branching-model/) 的方式维护分支：

![git-flow](https://static.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2022-04-01/1648793291308.92a2b518ac6526d9.png)

# NPM 发包

- 使用 [pnpm 集成的 changeset 命令](https://pnpm.io/zh/using-changesets)发包
- 所有包统一版本号
  - 即使只改了一个包，也要把所有包都发一遍
- 不需要发包的子包，在 `package.json` 里注明 `"private": true` 即可
- 手动发布步骤

  - 编译需要发 npm 的包：`pnpm build:npm`
  - 运行所有单测，确保没有问题：`pnpm test`
  - 生成变更描述：`pnpm changeset`
    - changed/unchanged packages 选择所有
    - major、minor 修 bug 的时候不选，需要选的时候，选择所有
  - 根据变更描述生成 changelog 以及修改版本号：`pnpm changeset version`
  - 生成 commit：先执行 `git add -A` 再执行 `git cz` 或 `git commit`
  - 发布所有 npm 包：
    - 预发布版本：`pnpm publish-npm:pre-release`
    - 正式版本：`pnpm publish-npm`
  - 提交代码： `git push`
