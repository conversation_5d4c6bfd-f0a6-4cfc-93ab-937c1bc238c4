import { argv } from 'zx'
import 'zx/globals'
import { getPackagesInfo } from './utils'

const owners = argv._

if (!owners.length) {
  console.error('Please entry owner name.')
  process.exit(-1)
}

;(async (): Promise<void> => {
  const { pkgNames } = getPackagesInfo()
  await $`npm cache clean --force`

  for (const owner of owners) {
    for (const pkgName of pkgNames) {
      try {
        await $`pnpm owner add ${owner} ${pkgName}`
        console.log(`${owner} now has the owner permission of ${pkgName}.`)
      } catch (err) {
        await $`npm cache clean --force`
        pkgNames.push(pkgName)
      }
    }
  }
})()
