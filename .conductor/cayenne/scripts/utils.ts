import * as glob from 'glob'
import yaml from 'js-yaml'
import fs from 'node:fs'
import path from 'path'

/**
 * 所有包的信息，比如:
 * ```ts
 * {
 *   relativePaths: [ 'packages/e2e-schema' ],
 *   folderNames: [ 'e2e-schema' ],
 *   pkgNames: [ '@ad/e2e-schema' ]
 * }
 * ```
 */
export interface PackagesInfo {
  /**
   * 相对路径
   */
  relativePaths: string[]
  /**
   * 文件夹名
   */
  folderNames: string[]
  /**
   * 包名
   */
  pkgNames: string[]
}

/**
 * 获取所有包的信息
 */
export function getPackagesInfo(): PackagesInfo {
  const relativePaths: string[] = []
  const folderNames: string[] = []
  const pkgNames: string[] = []
  const rootPath = path.resolve(__dirname, '../')

  try {
    const doc = yaml.load(
      fs.readFileSync(path.resolve(rootPath, 'pnpm-workspace.yaml'), 'utf8'),
    ) as {
      packages: string[]
    }

    if (doc.packages?.length) {
      doc.packages.forEach((matcher) => {
        matcher = matcher.replace(/\/\*+$/, '/*')

        if (matcher.endsWith('/*') || matcher.endsWith('/**')) {
          const scope = matcher.split('/')[0]
          const partialPkgPaths = glob.sync(matcher, {
            cwd: rootPath,
            ignore: '*/*.*',
          })

          partialPkgPaths.forEach((item) => {
            relativePaths.push(item)
            folderNames.push(item.replace(`${scope}/`, ''))

            const pkgJSONPath = path.resolve(rootPath, `${item}/package.json`)

            if (!fs.existsSync(pkgJSONPath)) {
              return
            }

            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const pkgJSON = require(pkgJSONPath)

            if (pkgJSON.private) {
              return
            }

            pkgNames.push(pkgJSON.name)
          })
        } else if (fs.existsSync(path.resolve(rootPath, matcher))) {
          relativePaths.push(matcher)
          folderNames.push(matcher)
        }
      })
    }
  } catch (err) {
    console.error('getPkgs err', err)
  }

  return {
    relativePaths,
    folderNames,
    pkgNames,
  }
}
