# E2E Schema 序列化工具

用于低代码开发流程中通过 schema 差异分析计算 AI 贡献覆盖率的 TypeScript 库。

## 概述

本库提供了分析 E2E schema 版本间变更并计算 AI 系统贡献百分比的工具。它使用基于策略的差异引擎来检测不同 schema 字段的变更，并采用消耗式匹配算法来准确测量 AI 覆盖率。

## 核心概念

### 差异引擎
`SchemaDiffEngine` 使用针对不同字段类型的可配置策略来分析 schema 版本间的差异：
- **视图策略**：检测组件层次结构变更
- **代码策略**：执行逐行代码分析
- **API 策略**：跟踪 API 接口修改
- **配置策略**：监控配置变更

### 覆盖率计算
`CoverageCalculator` 通过使用消耗式匹配将最终变更与 AI 生成的修改进行比较来确定 AI 贡献 - 每个变更只能匹配一次，类似于"连连看"算法。

### 策略系统
针对不同数据类型的字段特定差异策略：
- `ViewDiffStrategy`：组件树分析
- `CodeLineDiffStrategy`：源代码行比较
- `APIDiffStrategy`：API 端点跟踪
- `ConfigDiffStrategy`：配置对象分析

## 安装

```bash
npm install @ad/e2e-schema-serialization
```

## 基础用法

### 简单差异计算

```typescript
import { SchemaDiffEngine } from '@ad/e2e-schema-serialization'

const engine = new SchemaDiffEngine()
const diff = engine.diff(oldSchema, newSchema)

console.log(`总变更数: ${diff.stats.total}`)
console.log(`按类型分布:`, diff.stats.byType)
```

### AI 覆盖率分析

```typescript
import { CoverageCalculator } from '@ad/e2e-schema-serialization'

// 计算多个 AI 修改的覆盖率
const coverage = CoverageCalculator.calculateAICoverage(finalDiff, [aiDiff1, aiDiff2])
console.log(`AI 覆盖率: ${coverage.toFixed(2)}%`)

// 获取详细分析
const detailed = CoverageCalculator.calculateDetailedCoverage(finalDiff, [aiDiff1, aiDiff2])
console.log('保留的变更:', detailed.preservedChanges.length)
console.log('未保留的变更:', detailed.missedChanges.length)
```

### 配置系统

```typescript
import { SchemaDiffEngine, getPresetConfig } from '@ad/e2e-schema-serialization'

// 使用预设配置
const minimalEngine = new SchemaDiffEngine(getPresetConfig('minimal'))
const comprehensiveEngine = new SchemaDiffEngine(getPresetConfig('comprehensive'))

// 自定义配置
const customConfig = {
  strategies: [
    { path: 'view', strategy: 'ViewDiffStrategy' },
    { path: 'model.codeTS', strategy: 'CodeLineDiffStrategy' },
    { path: 'apis', strategy: 'APIDiffStrategy' }
  ]
}
const customEngine = new SchemaDiffEngine(customConfig)
```

## 真实场景示例

考虑一个开发工作流，其中变更 alpha 包含修改 A、B、C、D、E：

```typescript
// 初始版本
const initV = { /* 基础 schema */ }

// AI 修改 A 后
const AV = { /* 包含 AI 变更的 schema */ }

// 人工修改 B 后
const BV = { /* 包含人工变更的 schema */ }

// 人工修改 C 后
const CV = { /* 包含更多人工变更的 schema */ }

// AI 修改 D 后
const DV = { /* 包含更多 AI 变更的 schema */ }

// 人工修改 E 后的最终版本
const EV = { /* 最终 schema */ }

// 计算总变更
const alphaDiff = engine.diff(initV, EV)

// 计算 AI 贡献
const ADiff = engine.diff(initV, AV)  // AI 修改 A
const DDiff = engine.diff(CV, DV)    // AI 修改 D

// 确定 AI 覆盖率
const coverage = CoverageCalculator.calculateAICoverage(alphaDiff, [ADiff, DDiff])
```

## 高级功能

### 差异合并

```typescript
import { DiffMerger } from '@ad/e2e-schema-serialization'

const merged = DiffMerger.mergeArray([diff1, diff2, diff3])
console.log(`合并后变更数: ${merged.stats.total}`)
```

### 自定义策略

```typescript
import { StrategyFactory } from '@ad/e2e-schema-serialization'

class CustomStrategy extends BaseFieldDiffStrategy {
  // 实现细节
}

StrategyFactory.register('CustomStrategy', CustomStrategy)
```

### 工具函数

```typescript
import { createTargetKey, generateUnitId } from '@ad/e2e-schema-serialization'

const target = { type: 'component', fieldPath: 'view.children[0]' }
const key = createTargetKey(target)
const id = generateUnitId(target)
```

## 配置选项

### 预设配置

- **minimal**：仅核心策略（view、apis）
- **default**：标准开发设置
- **comprehensive**：所有可用策略

### 策略配置

```typescript
interface StrategyConfig {
  path: string                                    // 要分析的字段路径
  strategy: string | FieldDiffStrategy<unknown>  // 策略名称或实例
  options?: Record<string, unknown>              // 策略特定选项
}
```

## API 参考

### SchemaDiffEngine

- `diff(oldSchema, newSchema)`：计算 schema 间的差异
- `setStrategy(path, strategy)`：为特定字段路径配置策略

### CoverageCalculator

- `calculateAICoverage(finalDiff, aiDiffs)`：计算 AI 贡献百分比
- `calculateDetailedCoverage(finalDiff, aiDiffs)`：获取详细覆盖率分析

### DiffMerger

- `merge(diff1, diff2)`：合并两个差异结果
- `mergeArray(diffs)`：合并多个差异结果

## 测试

库包含演示真实使用场景的综合测试：

```bash
npm test
```

测试输出显示详细的覆盖率分析：
```
总变更数: 38
AI 修改 A 贡献: 9
AI 修改 D 贡献: 15
AI 覆盖率: 63.16%
```

## 架构

库采用模块化架构，职责分离清晰：

- **Core**：引擎、类型、工具
- **Strategies**：字段特定差异实现
- **Config**：配置系统和预设
- **Coverage**：AI 贡献分析工具

## 许可证

MIT
