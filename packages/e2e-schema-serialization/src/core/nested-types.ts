/**
 * 通用嵌套范式 - 核心类型定义
 * 提供声明式的嵌套结构描述和处理能力
 */

import type { FieldDiffStrategy } from './types'

/**
 * 嵌套类型枚举
 */
export type NestingType = 'object' | 'array' | 'tree'

/**
 * 树遍历配置
 */
export interface TreeTraversalConfig {
  /**
   * 子节点字段名
   */
  childrenField: string
  /**
   * 节点ID字段名
   */
  idField: string
  /**
   * 遍历模式
   */
  traversalMode: 'BFS' | 'DFS'
  /**
   * 最大遍历深度 (可选，防止无限递归)
   */
  maxDepth?: number
}

/**
 * 嵌套描述符
 * 用于声明式地描述字段的嵌套结构和处理策略
 */
export interface NestedDescriptor {
  /**
   * 字段路径 (相对路径)
   */
  path: string
  /**
   * 处理策略名称或实例
   */
  strategy: string | FieldDiffStrategy<unknown>
  /**
   * 嵌套类型
   */
  nestingType: NestingType
  /**
   * 子字段描述 (仅 object 和 tree 类型)
   */
  children?: Record<string, NestedDescriptor>
  /**
   * 数组元素描述 (仅 array 类型)
   */
  itemDescriptor?: NestedDescriptor
  /**
   * 树遍历配置 (仅 tree 类型)
   */
  treeConfig?: TreeTraversalConfig
  /**
   * 策略选项
   */
  options?: Record<string, unknown>
  /**
   * 是否跳过处理 (用于 TODO 字段)
   */
  skip?: boolean
}

/**
 * 嵌套处理上下文
 */
export interface NestedProcessingContext {
  /**
   * 当前路径
   */
  currentPath: string
  /**
   * 父节点信息 (仅 tree 类型)
   */
  parentInfo?: {
    id: string
    order: number
  }
  /**
   * 当前深度 (仅 tree 类型)
   */
  depth?: number
  /**
   * 数组索引 (仅 array 类型)
   */
  arrayIndex?: number
}

/**
 * 队列项 (用于树遍历)
 */
export interface QueueItem<T = unknown> {
  /**
   * 节点数据
   */
  node: T
  /**
   * 处理上下文
   */
  context: NestedProcessingContext
  /**
   * 嵌套描述符
   */
  descriptor: NestedDescriptor
}

/**
 * 嵌套配置构建器
 * 提供类型安全的配置构建方法
 */
export class NestedConfigBuilder {
  /**
   * 构建对象类型的嵌套配置
   */
  public static forObject(
    children: Record<string, NestedDescriptor>,
  ): Omit<NestedDescriptor, 'path' | 'strategy'> {
    return {
      nestingType: 'object',
      children,
    }
  }

  /**
   * 构建数组类型的嵌套配置
   */
  public static forArray(
    itemDescriptor: NestedDescriptor,
  ): Omit<NestedDescriptor, 'path' | 'strategy'> {
    return {
      nestingType: 'array',
      itemDescriptor,
    }
  }

  /**
   * 构建树类型的嵌套配置
   * 使用字符串字段名，避免泛型约束问题
   */
  public static forTree(config: {
    childrenField: string
    idField: string
    nodeConfig: Record<string, NestedDescriptor>
    traversalMode?: 'BFS' | 'DFS'
    maxDepth?: number
  }): Omit<NestedDescriptor, 'path' | 'strategy'> {
    return {
      nestingType: 'tree',
      treeConfig: {
        childrenField: config.childrenField,
        idField: config.idField,
        traversalMode: config.traversalMode || 'BFS',
        maxDepth: config.maxDepth,
      },
      children: config.nodeConfig,
    }
  }

  /**
   * 创建完整的嵌套描述符
   */
  public static create(
    path: string,
    strategy: string | FieldDiffStrategy<unknown>,
    config: Omit<NestedDescriptor, 'path' | 'strategy'>,
  ): NestedDescriptor {
    return {
      path,
      strategy,
      ...config,
    }
  }

  /**
   * 创建简单字段描述符
   */
  public static simple(
    path: string,
    strategy: string | FieldDiffStrategy<unknown>,
    options?: Record<string, unknown>,
  ): NestedDescriptor {
    return {
      path,
      strategy,
      nestingType: 'object',
      options,
    }
  }

  /**
   * 创建 TODO 字段描述符 (跳过处理)
   */
  public static todo(path: string): NestedDescriptor {
    return {
      path,
      strategy: 'TodoStrategy',
      nestingType: 'object',
      skip: true,
    }
  }
}

/**
 * 路径构建工具
 */
export class PathBuilder {
  /**
   * 构建基础路径
   */
  public static buildPath(basePath: string, fieldPath: string): string {
    if (!basePath) return `.${fieldPath}`
    if (!fieldPath) return basePath
    return `${basePath}.${fieldPath}`
  }

  /**
   * 构建数组路径
   */
  public static buildArrayPath(
    basePath: string,
    index: number,
    fieldPath?: string,
  ): string {
    const arrayPath = basePath ? `${basePath}[${index}]` : `[${index}]`
    return fieldPath ? `${arrayPath}.${fieldPath}` : arrayPath
  }

  /**
   * 构建树节点路径
   */
  public static buildTreePath(
    basePath: string,
    childrenField: string,
    index: number,
    fieldPath?: string,
  ): string {
    const treePath = basePath
      ? `${basePath}.${childrenField}[${index}]`
      : `.${childrenField}[${index}]`
    return fieldPath ? `${treePath}.${fieldPath}` : treePath
  }

  /**
   * 标准化路径 (移除开头的点)
   */
  public static normalizePath(path: string): string {
    return path.startsWith('.') ? path.slice(1) : path
  }
}

/**
 * 嵌套处理错误
 */
export class NestedProcessingError extends Error {
  public constructor(
    message: string,
    public readonly context: NestedProcessingContext,
    public readonly descriptor: NestedDescriptor,
    public readonly cause?: Error,
  ) {
    super(message)
    this.name = 'NestedProcessingError'
  }
}

/**
 * 嵌套处理选项
 */
export interface NestedProcessingOptions {
  /**
   * 是否启用性能优化 (策略缓存等)
   */
  enableOptimization?: boolean
  /**
   * 最大处理深度 (防止无限递归)
   */
  maxDepth?: number
  /**
   * 错误处理模式
   */
  errorMode?: 'throw' | 'skip' | 'collect'
  /**
   * 调试模式
   */
  debug?: boolean
}

/**
 * 默认嵌套处理选项
 */
export const DEFAULT_NESTED_OPTIONS: Required<NestedProcessingOptions> = {
  enableOptimization: true,
  maxDepth: 50,
  errorMode: 'throw',
  debug: false,
}
