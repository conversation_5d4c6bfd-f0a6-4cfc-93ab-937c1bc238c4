/**
 * AI覆盖率计算工具
 * 专门负责AI贡献覆盖率的计算逻辑
 */

import { BaseFieldDiffStrategy } from './base'
import type { DiffResult } from './engine'
import type { Change } from './types'

/**
 * 覆盖率计算器
 * 提供AI覆盖率计算的各种方法
 */
export class CoverageCalculator {
  /**
   * 计算 AI 覆盖率
   * 使用消耗式匹配：像"连连看"一样匹配finalDiff和aiDiffs中的变更
   *
   * @param finalDiff 最终版本的 diff
   * @param aiDiffs AI 产生的 diff 数组
   * @returns 覆盖率百分比
   */
  public static calculateAICoverage(
    finalDiff: DiffResult,
    aiDiffs: DiffResult[],
  ): number {
    if (finalDiff.stats.total === 0) {
      return 0
    }

    // 简单组装所有 AI 的 changes
    const allAIChanges: Change[] = []
    aiDiffs.forEach((diff) => {
      allAIChanges.push(...diff.changes)
    })

    // 创建可消耗的AI变更池
    const availableAIChanges = [...allAIChanges]

    // 计算在最终版本中保留的 AI 变更数
    let preservedCount = 0

    finalDiff.changes.forEach((finalChange) => {
      // 寻找匹配的AI变更（相同ID且相同值）
      const matchIndex = availableAIChanges.findIndex(
        (aiChange) =>
          aiChange.unit.id === finalChange.unit.id &&
          BaseFieldDiffStrategy.unitValuesEqual(
            aiChange.unit,
            finalChange.unit,
          ),
      )

      if (matchIndex !== -1) {
        // 找到匹配，消耗这个AI变更
        availableAIChanges.splice(matchIndex, 1)
        preservedCount++
      }
    })

    return (preservedCount / finalDiff.stats.total) * 100
  }

  /**
   * 计算详细的覆盖率统计
   * 返回更详细的覆盖率信息
   *
   * @param finalDiff 最终版本的 diff
   * @param aiDiffs AI 产生的 diff 数组
   * @returns 详细的覆盖率统计
   */
  public static calculateDetailedCoverage(
    finalDiff: DiffResult,
    aiDiffs: DiffResult[],
  ): {
    totalCoverage: number
    byType: Record<string, number>
    byField: Record<string, number>
    byStrategy: Record<string, number>
    preservedChanges: Change[]
    missedChanges: Change[]
  } {
    if (finalDiff.stats.total === 0) {
      return {
        totalCoverage: 0,
        byType: {},
        byField: {},
        byStrategy: {},
        preservedChanges: [],
        missedChanges: [],
      }
    }

    // 简单组装所有 AI 的 changes
    const allAIChanges: Change[] = []
    aiDiffs.forEach((diff) => {
      allAIChanges.push(...diff.changes)
    })

    // 创建可消耗的AI变更池
    const availableAIChanges = [...allAIChanges]
    const preservedChanges: Change[] = []
    const missedChanges: Change[] = []

    finalDiff.changes.forEach((finalChange) => {
      // 寻找匹配的AI变更（相同ID且相同值）
      const matchIndex = availableAIChanges.findIndex(
        (aiChange) =>
          aiChange.unit.id === finalChange.unit.id &&
          BaseFieldDiffStrategy.unitValuesEqual(
            aiChange.unit,
            finalChange.unit,
          ),
      )

      if (matchIndex !== -1) {
        // 找到匹配，消耗这个AI变更
        availableAIChanges.splice(matchIndex, 1)
        preservedChanges.push(finalChange)
      } else {
        missedChanges.push(finalChange)
      }
    })

    // 计算各维度的覆盖率
    const totalCoverage =
      (preservedChanges.length / finalDiff.stats.total) * 100

    const byType: Record<string, number> = {}
    const byField: Record<string, number> = {}
    const byStrategy: Record<string, number> = {}

    // 统计保留的变更
    preservedChanges.forEach((change) => {
      byType[change.type] = (byType[change.type] || 0) + 1

      const field = change.path.split('.')[0].replace(/\[\d+\]/, '')
      byField[field] = (byField[field] || 0) + 1

      byStrategy[change.unit.strategy] =
        (byStrategy[change.unit.strategy] || 0) + 1
    })

    return {
      totalCoverage,
      byType,
      byField,
      byStrategy,
      preservedChanges,
      missedChanges,
    }
  }
}
