/**
 * 字段 Diff 策略类型定义
 */

import type { E2EAPI, E2ESchemaComponent, Model } from '@ad/e2e-schema'

/**
 * 变更目标 - 用于识别新旧 schema 中的同一个变更目标
 * 注意：为了保证索引的稳定性，value只能是基本类型
 */
export interface ChangeTarget {
  /** 目标类型 */
  type: string
  /** 字段路径 - 所有策略都必须包含，用于区分不同字段 */
  fieldPath: string
  /** 其他目标标识信息 - 只允许基本类型 */
  [key: string]: string | number | boolean
}

/**
 * 变更单元 - 表示一个最小的可追踪变更
 */
export interface ChangeUnit {
  /** 唯一标识符 */
  id: string
  /** 变更目标（用于跨版本匹配） */
  target: ChangeTarget
  /** 在 schema 中的路径 */
  path: string
  /** 实际值 */
  value: unknown
  /** 产生此单元的策略 */
  strategy: string
  /** 元数据 */
  metadata?: Record<string, unknown>
}

/**
 * 单元类型 - 仅用于 metadata 中的描述性信息
 * 不是核心逻辑的一部分，可自由扩展
 */
export type UnitType = string

/**
 * 变更类型
 */
export type ChangeType = 'add' | 'modify' | 'delete'

/**
 * 单个变更
 */
export interface Change {
  type: ChangeType
  unit: ChangeUnit
  oldUnit?: ChangeUnit
  path: string
}

/**
 * 字段 Diff 策略接口
 * 每个字段类型都应该实现这个接口
 */
export interface FieldDiffStrategy<T> {
  /**
   * 策略名称
   */
  readonly STRATEGY_NAME: string

  /**
   * 提取变更单元
   * 将字段值分解为可追踪的最小单元
   */
  extractUnits(value: T | undefined): ChangeUnit[]

  /**
   * 判断两个单元的值是否相等
   */
  unitValuesEqual(oldUnit: ChangeUnit, newUnit: ChangeUnit): boolean
}

/**
 * 字段类型到具体类型的映射
 */
export interface FieldTypeMap {
  view: E2ESchemaComponent
  model: Model
  backModel: Model
  linkage: Record<string, unknown>
  apis: E2EAPI[]
  data: Record<string, unknown>
}
