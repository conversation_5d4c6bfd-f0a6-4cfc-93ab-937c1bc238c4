/**
 * DiffResult 合并工具
 * 专门负责diff结果的合并逻辑
 */

import type { DiffResult, DiffStats } from './engine'
import type { Change } from './types'

/**
 * Diff合并器
 * 提供各种diff合并策略
 */
export class DiffMerger {
  /**
   * 合并两个 DiffResult
   * 简单合并：直接合并所有变更，允许重复ID
   *
   * @param diff1 第一个 DiffResult
   * @param diff2 第二个 DiffResult
   * @returns 合并后的 DiffResult
   */
  public static merge(diff1: DiffResult, diff2: DiffResult): DiffResult {
    // 简单合并，允许重复
    const mergedChanges = [...diff1.changes, ...diff2.changes]

    // 重新计算统计信息
    const stats = this._calculateStats(mergedChanges)

    return {
      changes: mergedChanges,
      stats,
    }
  }

  /**
   * 合并多个 DiffResult
   * 从左到右依次合并，后面的覆盖前面的
   *
   * @param diffs DiffResult 数组
   * @returns 合并后的 DiffResult
   */
  public static mergeArray(diffs: DiffResult[]): DiffResult {
    if (diffs.length === 0) {
      return {
        changes: [],
        stats: {
          total: 0,
          byType: { add: 0, modify: 0, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }
    }

    if (diffs.length === 1) {
      return diffs[0]
    }

    return diffs.reduce((acc, current) => this.merge(acc, current))
  }

  /**
   * 计算统计信息
   * @private
   */
  private static _calculateStats(changes: Change[]): DiffStats {
    const stats: DiffStats = {
      total: changes.length,
      byType: { add: 0, modify: 0, delete: 0 },
      byField: {},
      byStrategy: {},
    }

    changes.forEach((change) => {
      stats.byType[change.type]++

      const field = change.path.split('.')[0].replace(/\[\d+\]/, '')
      stats.byField[field] = (stats.byField[field] || 0) + 1

      stats.byStrategy[change.unit.strategy] =
        (stats.byStrategy[change.unit.strategy] || 0) + 1
    })

    return stats
  }
}
