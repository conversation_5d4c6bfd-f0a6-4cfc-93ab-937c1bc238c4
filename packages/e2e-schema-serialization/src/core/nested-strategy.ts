/**
 * 通用嵌套策略实现
 * 提供统一的嵌套字段处理能力
 */

import { StrategyFactory } from '../strategies/factory'
import { BaseFieldDiffStrategy } from './base'
import {
  DEFAULT_NESTED_OPTIONS,
  NestedProcessingError,
  PathBuilder,
  type NestedDescriptor,
  type NestedProcessingContext,
  type NestedProcessingOptions,
  type QueueItem,
} from './nested-types'
import type { ChangeUnit, FieldDiffStrategy } from './types'

/**
 * 策略委托工具
 */
class StrategyDelegate {
  private static _strategyCache = new Map<string, FieldDiffStrategy<unknown>>()

  /**
   * 创建或获取缓存的策略实例
   */
  public static getStrategy(
    strategyName: string | FieldDiffStrategy<unknown>,
    fieldPath: string,
    options?: Record<string, unknown>,
    enableCache = true,
  ): FieldDiffStrategy<unknown> {
    if (typeof strategyName !== 'string') {
      return strategyName
    }

    const cacheKey = `${strategyName}:${fieldPath}:${JSON.stringify(
      options || {},
    )}`

    if (enableCache && this._strategyCache.has(cacheKey)) {
      const cached = this._strategyCache.get(cacheKey)
      if (cached) {
        return cached
      }
    }

    const strategy = StrategyFactory.create(strategyName, fieldPath, options)

    if (enableCache) {
      this._strategyCache.set(cacheKey, strategy)
    }

    return strategy
  }

  /**
   * 委托策略提取单元，并调整路径前缀
   */
  public static delegateExtraction(
    strategy: FieldDiffStrategy<unknown>,
    value: unknown,
    pathPrefix: string,
  ): ChangeUnit[] {
    try {
      const units = strategy.extractUnits(value)
      return units.map((unit) => ({
        ...unit,
        path: pathPrefix + unit.path,
      }))
    } catch (error) {
      throw new Error(
        `Strategy extraction failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
      )
    }
  }

  /**
   * 清空策略缓存 (主要用于测试)
   */
  public static clearCache(): void {
    this._strategyCache.clear()
  }
}

/**
 * 通用嵌套字段策略
 */
export class NestedFieldStrategy<T> extends BaseFieldDiffStrategy<T> {
  public readonly STRATEGY_NAME: string = 'NESTED_FIELD_STRATEGY'

  private readonly _options: Required<NestedProcessingOptions>

  public constructor(
    fieldPath: string,
    private readonly _descriptor: NestedDescriptor,
    options?: NestedProcessingOptions,
  ) {
    super(fieldPath)
    this._options = { ...DEFAULT_NESTED_OPTIONS, ...options }
  }

  /**
   * 提取变更单元
   */
  public extractUnits(value: T | undefined): ChangeUnit[] {
    if (value === undefined || value === null) {
      return []
    }

    try {
      const context: NestedProcessingContext = {
        currentPath: '',
      }

      return this._processNested(value, this._descriptor, context)
    } catch (error) {
      if (this._options.errorMode === 'throw') {
        throw error
      } else if (this._options.errorMode === 'skip') {
        if (this._options.debug) {
          console.warn('Nested processing error (skipped):', error)
        }
        return []
      } else {
        // collect mode - 可以在未来扩展为收集错误信息
        return []
      }
    }
  }

  /**
   * 处理嵌套结构
   */
  private _processNested(
    value: unknown,
    descriptor: NestedDescriptor,
    context: NestedProcessingContext,
  ): ChangeUnit[] {
    // 检查是否跳过处理
    if (descriptor.skip) {
      return []
    }

    // 检查深度限制
    if (context.depth !== undefined && context.depth > this._options.maxDepth) {
      throw new NestedProcessingError(
        `Maximum nesting depth exceeded: ${this._options.maxDepth}`,
        context,
        descriptor,
      )
    }

    switch (descriptor.nestingType) {
      case 'object':
        return this._processObject(value, descriptor, context)
      case 'array':
        return this._processArray(value, descriptor, context)
      case 'tree':
        return this._processTree(value, descriptor, context)
      default:
        throw new NestedProcessingError(
          `Unknown nesting type: ${descriptor.nestingType as string}`,
          context,
          descriptor,
        )
    }
  }

  /**
   * 处理对象类型
   */
  private _processObject(
    value: unknown,
    descriptor: NestedDescriptor,
    context: NestedProcessingContext,
  ): ChangeUnit[] {
    const allUnits: ChangeUnit[] = []

    // 处理当前值本身 (如果有策略)
    if (descriptor.strategy) {
      const strategy = StrategyDelegate.getStrategy(
        descriptor.strategy,
        this.fieldPath,
        descriptor.options,
        this._options.enableOptimization,
      )

      const units = StrategyDelegate.delegateExtraction(
        strategy,
        value,
        context.currentPath,
      )
      allUnits.push(...units)
    }

    // 只有对象类型才处理子字段
    if (!value || typeof value !== 'object') {
      return allUnits
    }

    const obj = value as Record<string, unknown>

    // 处理子字段
    if (descriptor.children) {
      for (const [fieldName, childDescriptor] of Object.entries(
        descriptor.children,
      )) {
        const fieldValue = obj[fieldName]
        if (fieldValue === undefined) continue

        const childContext: NestedProcessingContext = {
          ...context,
          currentPath: PathBuilder.buildPath(context.currentPath, fieldName),
        }

        const childUnits = this._processNested(
          fieldValue,
          childDescriptor,
          childContext,
        )
        allUnits.push(...childUnits)
      }
    }

    return allUnits
  }

  /**
   * 处理数组类型
   */
  private _processArray(
    value: unknown,
    descriptor: NestedDescriptor,
    context: NestedProcessingContext,
  ): ChangeUnit[] {
    if (!Array.isArray(value)) {
      return []
    }

    if (!descriptor.itemDescriptor) {
      throw new NestedProcessingError(
        'Array descriptor must have itemDescriptor',
        context,
        descriptor,
      )
    }

    const allUnits: ChangeUnit[] = []

    // 处理数组本身 (如果有策略)
    if (descriptor.strategy) {
      const strategy = StrategyDelegate.getStrategy(
        descriptor.strategy,
        this.fieldPath,
        descriptor.options,
        this._options.enableOptimization,
      )

      const units = StrategyDelegate.delegateExtraction(
        strategy,
        value,
        context.currentPath,
      )
      allUnits.push(...units)
    }

    // 处理数组元素
    value.forEach((item, index) => {
      if (item === undefined || item === null) return

      const itemContext: NestedProcessingContext = {
        ...context,
        currentPath: PathBuilder.buildArrayPath(context.currentPath, index),
        arrayIndex: index,
      }

      if (!descriptor.itemDescriptor) {
        throw new NestedProcessingError(
          'Array descriptor must have itemDescriptor',
          context,
          descriptor,
        )
      }

      const itemUnits = this._processNested(
        item,
        descriptor.itemDescriptor,
        itemContext,
      )
      allUnits.push(...itemUnits)
    })

    return allUnits
  }

  /**
   * 处理树类型
   */
  private _processTree(
    value: unknown,
    descriptor: NestedDescriptor,
    context: NestedProcessingContext,
  ): ChangeUnit[] {
    if (!value || typeof value !== 'object') {
      return []
    }

    if (!descriptor.treeConfig) {
      throw new NestedProcessingError(
        'Tree descriptor must have treeConfig',
        context,
        descriptor,
      )
    }

    const { traversalMode } = descriptor.treeConfig

    if (traversalMode === 'BFS') {
      return this._processTreeBFS(value, descriptor, context)
    } else {
      return this._processTreeDFS(value, descriptor, context)
    }
  }

  /**
   * 广度优先遍历处理树
   */
  private _processTreeBFS(
    root: unknown,
    descriptor: NestedDescriptor,
    initialContext: NestedProcessingContext,
  ): ChangeUnit[] {
    const allUnits: ChangeUnit[] = []
    const queue: QueueItem[] = [
      {
        node: root,
        context: { ...initialContext, depth: 0 },
        descriptor,
      },
    ]

    while (queue.length > 0) {
      const item = queue.shift()
      if (!item) continue

      const { node, context, descriptor: currentDescriptor } = item

      // 处理当前节点
      const nodeUnits = this._processTreeNode(node, currentDescriptor, context)
      allUnits.push(...nodeUnits)

      // 添加子节点到队列
      this._enqueueChildren(node, currentDescriptor, context, queue)
    }

    return allUnits
  }

  /**
   * 深度优先遍历处理树 (递归实现)
   */
  private _processTreeDFS(
    node: unknown,
    descriptor: NestedDescriptor,
    context: NestedProcessingContext,
  ): ChangeUnit[] {
    const allUnits: ChangeUnit[] = []

    // 处理当前节点
    const nodeUnits = this._processTreeNode(node, descriptor, context)
    allUnits.push(...nodeUnits)

    // 递归处理子节点
    if (node && typeof node === 'object' && descriptor.treeConfig) {
      const obj = node as Record<string, unknown>
      const children = obj[descriptor.treeConfig.childrenField]

      if (Array.isArray(children)) {
        children.forEach((child, index) => {
          if (child && typeof child === 'object') {
            const childContext: NestedProcessingContext = {
              ...context,
              currentPath: PathBuilder.buildTreePath(
                context.currentPath,
                descriptor.treeConfig?.childrenField || 'children',
                index,
              ),
              depth: (context.depth || 0) + 1,
            }

            const childUnits = this._processTreeDFS(
              child,
              descriptor,
              childContext,
            )
            allUnits.push(...childUnits)
          }
        })
      }
    }

    return allUnits
  }

  /**
   * 处理树节点
   */
  private _processTreeNode(
    node: unknown,
    descriptor: NestedDescriptor,
    context: NestedProcessingContext,
  ): ChangeUnit[] {
    if (!node || typeof node !== 'object') {
      return []
    }

    const allUnits: ChangeUnit[] = []
    const obj = node as Record<string, unknown>

    // 处理节点本身 (如果有策略)
    if (descriptor.strategy) {
      const strategy = StrategyDelegate.getStrategy(
        descriptor.strategy,
        this.fieldPath,
        descriptor.options,
        this._options.enableOptimization,
      )

      const units = StrategyDelegate.delegateExtraction(
        strategy,
        node,
        context.currentPath,
      )
      allUnits.push(...units)
    }

    // 处理节点的子字段 (非 children 字段)
    if (descriptor.children) {
      for (const [fieldName, childDescriptor] of Object.entries(
        descriptor.children,
      )) {
        // 跳过 children 字段，它由树遍历逻辑处理
        if (fieldName === descriptor.treeConfig?.childrenField) continue

        const fieldValue = obj[fieldName]
        if (fieldValue === undefined) continue

        const childContext: NestedProcessingContext = {
          ...context,
          currentPath: PathBuilder.buildPath(context.currentPath, fieldName),
        }

        const childUnits = this._processNested(
          fieldValue,
          childDescriptor,
          childContext,
        )
        allUnits.push(...childUnits)
      }
    }

    return allUnits
  }

  /**
   * 将子节点加入队列 (BFS 使用)
   */
  private _enqueueChildren(
    node: unknown,
    descriptor: NestedDescriptor,
    context: NestedProcessingContext,
    queue: QueueItem[],
  ): void {
    if (!node || typeof node !== 'object' || !descriptor.treeConfig) {
      return
    }

    const obj = node as Record<string, unknown>
    const children = obj[descriptor.treeConfig.childrenField]

    if (Array.isArray(children)) {
      children.forEach((child, index) => {
        if (child && typeof child === 'object') {
          const childContext: NestedProcessingContext = {
            ...context,
            currentPath: PathBuilder.buildTreePath(
              context.currentPath,
              descriptor.treeConfig?.childrenField || 'children',
              index,
            ),
            depth: (context.depth || 0) + 1,
          }

          queue.push({
            node: child,
            context: childContext,
            descriptor,
          })
        }
      })
    }
  }
}
