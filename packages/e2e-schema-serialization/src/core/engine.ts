/**
 * Schema Diff 引擎 - 通用版本
 * 管道式处理：Extract -> Index -> Compare -> Aggregate
 */

import type { E2ESchema } from '@ad/e2e-schema'
import { DEFAULT_CONFIG } from '../config/default'
import type { DiffEngineConfig } from '../config/types'
import { StrategyFactory } from '../strategies/factory'
import '../strategies/registry' // 确保策略已注册
import { BaseFieldDiffStrategy } from './base'
import type {
  Change,
  ChangeTarget,
  ChangeType,
  ChangeUnit,
  FieldDiffStrategy,
} from './types'
import { createTargetKey } from './utils'

/**
 * 简单的对象属性获取函数，替代 lodash.get
 */
function get(obj: unknown, path: string): unknown {
  if (!obj || typeof obj !== 'object') return undefined

  const keys = path.split('.')
  let current: unknown = obj

  for (const key of keys) {
    if (current == null || typeof current !== 'object') return undefined
    current = (current as Record<string, unknown>)[key]
  }

  return current
}

/**
 * Diff 统计
 */
export interface DiffStats {
  total: number
  byType: Record<ChangeType, number>
  byField: Record<string, number>
  byStrategy: Record<string, number>
}

/**
 * Diff 结果
 */
export interface DiffResult {
  changes: Change[]
  stats: DiffStats
}

/**
 * 字段策略配置
 */
interface FieldStrategyConfig {
  path: string
  strategy: FieldDiffStrategy<unknown>
}

/**
 * Diff 引擎
 */
export class SchemaDiffEngine {
  private _strategies: Map<string, FieldStrategyConfig>

  public constructor(config: DiffEngineConfig = DEFAULT_CONFIG) {
    this._strategies = new Map()
    this._initializeStrategies(config)
  }

  /**
   * 初始化策略
   */
  private _initializeStrategies(config: DiffEngineConfig): void {
    for (const strategyConfig of config.strategies) {
      const strategy =
        typeof strategyConfig.strategy === 'string'
          ? StrategyFactory.create(
              strategyConfig.strategy,
              strategyConfig.path,
              strategyConfig.options,
            )
          : strategyConfig.strategy

      this.setStrategy(strategyConfig.path, strategy)
    }

    // 注册自定义策略
    if (config.customStrategies) {
      for (const [name, strategyClass] of config.customStrategies) {
        StrategyFactory.register(name, strategyClass)
      }
    }
  }

  /**
   * 设置字段策略
   */
  public setStrategy(path: string, strategy: FieldDiffStrategy<unknown>): void {
    this._strategies.set(path, { path, strategy })
  }

  /**
   * 主入口：计算两个 Schema 的差异
   */
  public diff(oldSchema: E2ESchema, newSchema: E2ESchema): DiffResult {
    // Extract: 提取单元
    const oldUnits = this._extractAll(oldSchema)
    const newUnits = this._extractAll(newSchema)

    // Index: 建立索引
    const oldIndex = this._index(oldUnits)
    const newIndex = this._index(newUnits)

    // Compare: 比较变更
    const changes = this._compare(oldIndex, newIndex)

    // Aggregate: 聚合统计
    const stats = this._aggregate(changes)

    return { changes, stats }
  }

  /**
   * Extract: 提取所有字段的变更单元
   */
  private _extractAll(schema: E2ESchema): ChangeUnit[] {
    const allUnits: ChangeUnit[] = []

    for (const [path, config] of this._strategies) {
      const value = get(schema, path)
      if (value === undefined) continue

      const units = config.strategy.extractUnits(value)

      // 添加字段路径前缀
      const prefixedUnits = units.map((unit) => ({
        ...unit,
        path: path + unit.path, // 直接拼接，让策略自己控制分隔符
      }))

      allUnits.push(...prefixedUnits)
    }

    return allUnits
  }

  /**
   * Index: 创建单元索引
   * 支持消耗式匹配：同一个key可能对应多个单元
   */
  private _index(units: ChangeUnit[]): Map<string, ChangeUnit[]> {
    const index = new Map<string, ChangeUnit[]>()

    for (const unit of units) {
      const key = this._createTargetKey(unit.target)
      if (!index.has(key)) {
        index.set(key, [])
      }
      index.get(key)?.push(unit)
    }

    return index
  }

  /**
   * 创建稳定的target key
   * 使用统一的工具函数
   */
  private _createTargetKey(target: ChangeTarget): string {
    return createTargetKey(target)
  }

  /**
   * Compare: 比较两个索引，生成变更列表
   * 消耗式匹配：支持相同ID的多个单元，像"连连看"一样进行匹配
   */
  private _compare(
    oldIndex: Map<string, ChangeUnit[]>,
    newIndex: Map<string, ChangeUnit[]>,
  ): Change[] {
    const changes: Change[] = []

    // 收集所有旧单元和新单元
    const allOldUnits: ChangeUnit[] = []
    const allNewUnits: ChangeUnit[] = []

    for (const units of oldIndex.values()) {
      allOldUnits.push(...units)
    }

    for (const units of newIndex.values()) {
      allNewUnits.push(...units)
    }

    // 创建可消耗的旧单元池
    const availableOldUnits = [...allOldUnits]

    // 对每个新单元寻找匹配的旧单元
    for (const newUnit of allNewUnits) {
      // 寻找相同ID的旧单元
      const matchIndex = availableOldUnits.findIndex(
        (oldUnit) => oldUnit.id === newUnit.id,
      )

      if (matchIndex !== -1) {
        // 找到匹配，消耗这个旧单元
        const oldUnit = availableOldUnits.splice(matchIndex, 1)[0]

        // 比较值
        if (!this._unitsEqual(oldUnit, newUnit)) {
          // 修改
          changes.push({
            type: 'modify',
            unit: newUnit,
            oldUnit,
            path: newUnit.path,
          })
        }
        // 如果值相等，不生成change（无变化）
      } else {
        // 没找到匹配，新增
        changes.push({
          type: 'add',
          unit: newUnit,
          path: newUnit.path,
        })
      }
    }

    // 剩余未消耗的旧单元都是删除
    for (const oldUnit of availableOldUnits) {
      changes.push({
        type: 'delete',
        unit: oldUnit,
        path: oldUnit.path,
      })
    }

    return changes
  }

  /**
   * 比较两个单元的值是否相等
   */
  private _unitsEqual(oldUnit: ChangeUnit, newUnit: ChangeUnit): boolean {
    // 找到对应的策略
    const strategyName = oldUnit.strategy
    for (const config of this._strategies.values()) {
      if (config.strategy.STRATEGY_NAME === strategyName) {
        return config.strategy.unitValuesEqual(oldUnit, newUnit)
      }
    }

    // 默认使用BaseFieldDiffStrategy的统一比较方法
    return BaseFieldDiffStrategy.unitValuesEqual(oldUnit, newUnit)
  }

  /**
   * Aggregate: 聚合统计信息
   */
  private _aggregate(changes: Change[]): DiffStats {
    const byType: Record<ChangeType, number> = {
      add: 0,
      modify: 0,
      delete: 0,
    }

    const byField: Record<string, number> = {}
    const byStrategy: Record<string, number> = {}

    for (const change of changes) {
      // 类型统计
      byType[change.type]++

      // 字段统计
      // 处理数组索引，例如 apis[0].name -> apis
      const pathParts = change.path.split('.')
      let field = pathParts[0]
      // 移除数组索引
      field = field.replace(/\[\d+\]/, '')
      byField[field] = (byField[field] || 0) + 1

      // 策略统计
      const strategy = change.unit.strategy
      byStrategy[strategy] = (byStrategy[strategy] || 0) + 1
    }

    return {
      total: changes.length,
      byType,
      byField,
      byStrategy,
    }
  }
}
