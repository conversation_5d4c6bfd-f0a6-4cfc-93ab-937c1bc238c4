/**
 * 基础 Diff 策略
 * 提供策略接口的抽象基类
 */

import type { ChangeUnit, FieldDiffStrategy } from './types'

/**
 * 深度比较两个值是否相等，只忽略对象键的顺序
 * 其他情况下严格相等
 */
function deepEqual(a: unknown, b: unknown): boolean {
  // 严格相等检查
  if (a === b) return true

  // null/undefined 检查
  if (a == null || b == null) return a === b

  // 类型检查
  if (typeof a !== typeof b) return false

  // 数组检查
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false
    return a.every((item, index) => deepEqual(item, b[index]))
  }

  // 数组 vs 非数组
  if (Array.isArray(a) || Array.isArray(b)) {
    return false // 一个是数组一个不是，不相等
  }

  // 对象检查（忽略键的顺序）
  if (typeof a === 'object' && typeof b === 'object') {
    const aKeys = Object.keys(a as Record<string, unknown>).sort()
    const bKeys = Object.keys(b as Record<string, unknown>).sort()

    // 键数量和名称必须相同
    if (aKeys.length !== bKeys.length) return false
    if (!aKeys.every((key, index) => key === bKeys[index])) return false

    // 递归比较每个键的值
    return aKeys.every((key) =>
      deepEqual(
        (a as Record<string, unknown>)[key],
        (b as Record<string, unknown>)[key],
      ),
    )
  }

  // 其他类型严格相等
  return false
}

export abstract class BaseFieldDiffStrategy<T> implements FieldDiffStrategy<T> {
  /**
   * 字段路径
   */
  protected fieldPath: string

  /**
   * 策略名称
   */
  public abstract readonly STRATEGY_NAME: string

  /**
   * 构造函数
   */
  public constructor(fieldPath: string) {
    this.fieldPath = fieldPath
  }

  /**
   * 提取变更单元 - 子类必须实现
   */
  public abstract extractUnits(value: T | undefined): ChangeUnit[]

  /**
   * 静态方法：比较两个单元的值是否相等
   * 只忽略对象键的顺序，其他情况严格相等
   *
   * 可以在没有实例的情况下使用
   */
  public static unitValuesEqual(
    oldUnit: ChangeUnit,
    newUnit: ChangeUnit,
  ): boolean {
    return deepEqual(oldUnit.value, newUnit.value)
  }

  /**
   * 实例方法：比较两个单元的值是否相等
   * 默认调用静态方法
   *
   * 子类可以覆盖此方法以实现特殊比较逻辑
   */
  public unitValuesEqual(oldUnit: ChangeUnit, newUnit: ChangeUnit): boolean {
    // 调用静态方法
    return BaseFieldDiffStrategy.unitValuesEqual(oldUnit, newUnit)
  }
}
