/**
 * 核心工具函数
 * 统一管理所有策略共用的工具函数
 */

import type { ChangeTarget } from './types'

/**
 * 创建稳定的target key
 * 通过排序键名保证相同内容不同顺序的对象产生相同的key
 */
export function createTargetKey(target: ChangeTarget): string {
  return Object.keys(target)
    .sort()
    .map((key) => `${key}:${target[key]}`)
    .join('|')
}

/**
 * 生成变更单元的唯一ID
 * 基于target创建稳定的ID
 */
export function generateUnitId(target: ChangeTarget): string {
  return createTargetKey(target)
}

/**
 * 生成内容哈希
 * 确保相同内容产生相同哈希
 */
export function generateContentHash(content: string): string {
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash + char) | 0 // 使用位运算保证一致性
  }
  return Math.abs(hash).toString(36)
}

/**
 * 规范化代码行
 * 统一的代码行处理逻辑
 */
export function normalizeLine(line: string): {
  normalized: string
  isComment: boolean
} {
  // 检测是否为注释行
  const isComment = /^\s*(\/\/|\/\*|\*|#)/.test(line)

  // 默认规范化空白：trim 并压缩多个空格
  let normalized = line.trim()
  if (normalized) {
    normalized = normalized.replace(/\s+/g, ' ')
  }

  return { normalized, isComment }
}
