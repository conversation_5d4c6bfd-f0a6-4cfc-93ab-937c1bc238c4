/**
 * Iife Diff 策略
 * 使用通用嵌套范式处理 IIFE 表达式的变更追踪
 * IIFE 结构: { type, apiIds, transform: { type, code, codeTS, codeES } }
 */

import { NestedFieldStrategy } from '../../core/nested-strategy'
import { NestedConfigBuilder } from '../../core/nested-types'
import type { ChangeTarget, ChangeUnit } from '../../core/types'
import { STRATEGY_NAMES } from '../constants'

/**
 * IIFE 表达式类型定义
 * 基于 E2ESchemaExpressionAPI<E2ESchemaExpressionJS>
 */
interface IifeExpression {
  type: string // "apis"
  apiIds: string[]
  transform?: {
    type: string // "js"
    code: string
    codeTS?: string
    codeES?: string
  }
}

/**
 * Iife 策略的 ChangeTarget 类型
 */
interface IifeChangeTarget extends ChangeTarget {
  type: typeof STRATEGY_NAMES.IIFE_DIFF
  fieldPath: string
  componentId: string
  attributePath: string
}

/**
 * Iife 策略的 metadata 类型
 */
interface IifeChangeMetadata extends Record<string, unknown> {
  path: string
  isSpecial?: boolean
}

/**
 * Iife 策略的 ChangeUnit 类型
 */
export interface IifeChangeUnit extends ChangeUnit {
  target: IifeChangeTarget
  strategy: typeof STRATEGY_NAMES.IIFE_DIFF
  metadata: IifeChangeMetadata
}

/**
 * IIFE 字段的嵌套配置
 */
const IIFE_NESTED_CONFIG = NestedConfigBuilder.forObject({
  // 基础属性
  type: NestedConfigBuilder.simple('type', 'ConfigDiffStrategy'),

  // apiIds 数组
  apiIds: NestedConfigBuilder.create(
    'apiIds',
    'IDListDiffStrategy',
    NestedConfigBuilder.forArray(
      NestedConfigBuilder.simple('', 'ConfigDiffStrategy'),
    ),
  ),

  // transform 对象 - JS 表达式嵌套处理
  transform: NestedConfigBuilder.create(
    'transform',
    'ConfigDiffStrategy',
    NestedConfigBuilder.forObject({
      type: NestedConfigBuilder.simple('type', 'ConfigDiffStrategy'),
      code: NestedConfigBuilder.simple('code', 'CodeLineDiffStrategy'),
      codeTS: NestedConfigBuilder.simple('codeTS', 'CodeLineDiffStrategy'),
      codeES: NestedConfigBuilder.simple('codeES', 'CodeLineDiffStrategy'),
    }),
  ),
})

/**
 * Iife Diff 策略
 * 继承通用嵌套策略，处理 IIFE 表达式
 */
export class IifeDiffStrategy extends NestedFieldStrategy<IifeExpression> {
  public readonly STRATEGY_NAME: string = STRATEGY_NAMES.IIFE_DIFF

  /**
   * 类型守卫：判断是否为 IifeChangeUnit
   */
  public static isIifeChangeUnit(unit: ChangeUnit): unit is IifeChangeUnit {
    return (
      unit.strategy === STRATEGY_NAMES.IIFE_DIFF &&
      'fieldPath' in unit.target &&
      'componentId' in unit.target &&
      'attributePath' in unit.target
    )
  }

  public constructor(fieldPath: string) {
    super(fieldPath, {
      path: fieldPath,
      strategy: '', // 不使用根策略，由子策略处理
      ...IIFE_NESTED_CONFIG,
    })
  }

  /**
   * 提取 IIFE 中的所有变更单元
   * 使用通用嵌套范式处理
   */
  public extractUnits(value: IifeExpression | undefined): IifeChangeUnit[] {
    if (!value) return []

    // 使用父类的嵌套处理能力
    const nestedUnits = super.extractUnits(value)

    // 转换为 IifeChangeUnit
    return nestedUnits.map((unit) => this._convertToIifeUnit(unit))
  }

  /**
   * 转换为 Iife 变更单元
   */
  private _convertToIifeUnit(unit: ChangeUnit): IifeChangeUnit {
    // 如果已经是 IifeChangeUnit，直接返回
    if (IifeDiffStrategy.isIifeChangeUnit(unit)) {
      return unit
    }

    // 从路径中提取组件ID和属性路径
    const { componentId, attributePath } = this._parseUnitPath(unit.path)

    const target: IifeChangeTarget = {
      type: this.STRATEGY_NAME as typeof STRATEGY_NAMES.IIFE_DIFF,
      fieldPath: this.fieldPath,
      componentId,
      attributePath,
    }

    return {
      ...unit,
      target,
      strategy: this.STRATEGY_NAME as typeof STRATEGY_NAMES.IIFE_DIFF,
      metadata: {
        ...unit.metadata,
        path: unit.path,
      },
    } as IifeChangeUnit
  }

  /**
   * 从单元路径中解析组件ID和属性路径
   */
  private _parseUnitPath(path: string): {
    componentId: string
    attributePath: string
  } {
    // 简化的路径解析逻辑
    const parts = path.split('.')

    const componentId = 'iife'
    let attributePath = path

    // 尝试从路径中提取组件信息
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i]
      if (part.includes('[') && part.includes(']')) {
        // 可能是数组索引，跳过
        continue
      }
      if (i === parts.length - 1) {
        attributePath = part
      }
    }

    return { componentId, attributePath }
  }
}
