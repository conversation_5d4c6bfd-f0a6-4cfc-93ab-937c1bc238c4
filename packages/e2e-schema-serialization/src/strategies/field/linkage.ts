/**
 * Linkage Diff 策略
 * 处理联动配置的变更追踪
 * 组合使用 ConfigDiffStrategy 和 IDListDiffStrategy
 */

import type { Linkage, LinkageComponentDataParams } from '@ad/e2e-schema'
import { BaseFieldDiffStrategy } from '../../core/base'
import type { ChangeUnit } from '../../core/types'
import { ConfigDiffStrategy } from '../basic/config'
import { IDListDiffStrategy } from '../basic/id-list'
import { STRATEGY_NAMES } from '../constants'

export class LinkageDiffStrategy extends BaseFieldDiffStrategy<Linkage> {
  public readonly STRATEGY_NAME = STRATEGY_NAMES.LINKAGE_DIFF

  public constructor(fieldPath: string) {
    super(fieldPath)
  }

  /**
   * 提取 Linkage 的所有变更单元
   * 组合使用不同的策略处理不同部分
   */
  public extractUnits(value: Linkage | undefined): ChangeUnit[] {
    if (!value || typeof value !== 'object') {
      return []
    }

    const allUnits: ChangeUnit[] = []

    // 1. 处理 commonParams - 使用 ConfigDiffStrategy
    if (value.commonParams && typeof value.commonParams === 'object') {
      const configStrategy = new ConfigDiffStrategy(
        `${this.fieldPath}.commonParams`,
        'commonParams',
      )
      const configUnits = configStrategy.extractUnits(value.commonParams)

      // 调整路径
      const adjustedConfigUnits = configUnits.map((unit) => ({
        ...unit,
        path: `.commonParams${unit.path}`,
      }))

      allUnits.push(...adjustedConfigUnits)
    }

    // 2. 处理 autoRefreshByComponent - 使用 IDListDiffStrategy
    if (
      value.autoRefreshByComponent &&
      Array.isArray(value.autoRefreshByComponent)
    ) {
      const idListStrategy = new IDListDiffStrategy(
        `${this.fieldPath}.autoRefreshByComponent`,
        'autoRefreshByComponent',
      )
      const idListUnits = idListStrategy.extractUnits(
        value.autoRefreshByComponent,
      )

      // 调整路径
      const adjustedIdListUnits = idListUnits.map((unit) => ({
        ...unit,
        path: `.autoRefreshByComponent${unit.path}`,
      }))

      allUnits.push(...adjustedIdListUnits)
    }

    // 3. 处理 componentDataParams
    if (value.componentDataParams) {
      const cdpUnits = this._extractComponentDataParams(
        value.componentDataParams,
      )
      allUnits.push(...cdpUnits)
    }

    return allUnits
  }

  /**
   * 提取 componentDataParams 的变更单元
   */
  private _extractComponentDataParams(
    cdp: LinkageComponentDataParams,
  ): ChangeUnit[] {
    const units: ChangeUnit[] = []

    // 3.1 处理 componentDataParams.common - 使用 IDListDiffStrategy
    if (cdp.common && Array.isArray(cdp.common)) {
      const idListStrategy = new IDListDiffStrategy(
        `${this.fieldPath}.componentDataParams.common`,
        'componentDataParams.common',
      )
      const commonUnits = idListStrategy.extractUnits(cdp.common)

      // 调整路径
      const adjustedUnits = commonUnits.map((unit) => ({
        ...unit,
        path: `.componentDataParams.common${unit.path}`,
      }))

      units.push(...adjustedUnits)
    }

    // 3.2 处理 componentDataParams.byRefreshType
    if (cdp.byRefreshType && typeof cdp.byRefreshType === 'object') {
      Object.entries(cdp.byRefreshType).forEach(([refreshType, ids]) => {
        if (!ids || !Array.isArray(ids)) {
          return
        }

        // 每个 refreshType 的 ID 列表单独处理
        const idListStrategy = new IDListDiffStrategy(
          `${this.fieldPath}.componentDataParams.byRefreshType.${refreshType}`,
          `byRefreshType.${refreshType}`,
        )
        const refreshTypeUnits = idListStrategy.extractUnits(ids)

        // 调整路径
        const adjustedUnits = refreshTypeUnits.map((unit) => ({
          ...unit,
          path: `.componentDataParams.byRefreshType.${refreshType}${unit.path}`,
        }))

        units.push(...adjustedUnits)
      })
    }

    return units
  }

  /**
   * 比较两个单元的值是否相等
   * 委托给具体策略处理
   */
  public unitValuesEqual(oldUnit: ChangeUnit, newUnit: ChangeUnit): boolean {
    // 根据策略类型委托给相应的策略
    if (oldUnit.strategy === 'CONFIG_DIFF_STRATEGY') {
      return ConfigDiffStrategy.unitValuesEqual(oldUnit, newUnit)
    }

    if (oldUnit.strategy === 'ID_LIST_DIFF_STRATEGY') {
      // 委托给 IDListDiffStrategy 处理
      const idListStrategy = new IDListDiffStrategy('', '')
      return idListStrategy.unitValuesEqual(oldUnit, newUnit)
    }

    // 默认使用基类的比较
    return BaseFieldDiffStrategy.unitValuesEqual(oldUnit, newUnit)
  }
}
