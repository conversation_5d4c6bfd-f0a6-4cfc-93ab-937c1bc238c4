/**
 * View 字段 Diff 策略
 * 使用通用嵌套范式处理组件树的变更追踪
 * 支持 effect、apis、if 等嵌套字段
 */

import type { E2ESchemaComponent } from '@ad/e2e-schema'
import { NestedFieldStrategy } from '../../core/nested-strategy'
import { NestedConfigBuilder } from '../../core/nested-types'
import type { ChangeTarget, ChangeUnit } from '../../core/types'
import { createTargetKey } from '../../core/utils'
import { STRATEGY_NAMES } from '../constants'

/**
 * View 策略的 ChangeTarget 类型
 */
interface ViewChangeTarget extends ChangeTarget {
  type: typeof STRATEGY_NAMES.VIEW_DIFF
  fieldPath: string
  componentId: string
  attributePath: string
}

/**
 * View 策略的 metadata 类型
 */
interface ViewChangeMetadata extends Record<string, unknown> {
  path: string
  isSpecial?: boolean
  nestingLevel?: number
}

/**
 * View 策略的 ChangeUnit 类型
 */
export interface ViewChangeUnit extends ChangeUnit {
  target: ViewChangeTarget
  strategy: typeof STRATEGY_NAMES.VIEW_DIFF
  metadata: ViewChangeMetadata
}

/**
 * View 字段的嵌套配置
 */
const VIEW_NESTED_CONFIG = NestedConfigBuilder.forTree({
  childrenField: 'children',
  idField: 'id',
  traversalMode: 'BFS',
  nodeConfig: {
    // 基础属性 - 每个组件的type和name应该作为独立的变更单位
    // ConfigDiffStrategy现在支持原始值处理
    type: NestedConfigBuilder.simple('type', 'ConfigDiffStrategy'),
    name: NestedConfigBuilder.simple('name', 'ConfigDiffStrategy'),

    // Props 处理 (保留现有 TODO 注释的逻辑)
    props: NestedConfigBuilder.simple('props', 'ConfigDiffStrategy'),

    // Effect 字段 - JS 表达式嵌套处理
    effect: NestedConfigBuilder.create(
      'effect',
      'ConfigDiffStrategy', // 处理 effect 对象本身
      NestedConfigBuilder.forObject({
        type: NestedConfigBuilder.simple('type', 'ConfigDiffStrategy'),
        code: NestedConfigBuilder.simple('code', 'CodeLineDiffStrategy'),
        codeTS: NestedConfigBuilder.simple('codeTS', 'CodeLineDiffStrategy'),
        codeES: NestedConfigBuilder.simple('codeES', 'CodeLineDiffStrategy'),
      }),
    ),

    // APIs 字段 - 复用现有 API 策略
    apis: NestedConfigBuilder.create(
      'apis',
      'APIDiffStrategy', // 直接使用现有的 API 策略
      { nestingType: 'object' }, // APIs 策略内部已经处理数组逻辑
    ),

    // If 字段 - JS 表达式嵌套处理 (与 effect 相同结构)
    if: NestedConfigBuilder.create(
      'if',
      'ConfigDiffStrategy',
      NestedConfigBuilder.forObject({
        type: NestedConfigBuilder.simple('type', 'ConfigDiffStrategy'),
        code: NestedConfigBuilder.simple('code', 'CodeLineDiffStrategy'),
        codeTS: NestedConfigBuilder.simple('codeTS', 'CodeLineDiffStrategy'),
        codeES: NestedConfigBuilder.simple('codeES', 'CodeLineDiffStrategy'),
      }),
    ),

    // TODO 字段 - 暂时跳过处理
    backFor: NestedConfigBuilder.todo('backFor'),
    mountArea: NestedConfigBuilder.todo('mountArea'),
    ab: NestedConfigBuilder.todo('ab'),
  },
})

/**
 * View Diff 策略
 * 继承通用嵌套策略，添加 View 特有的处理逻辑
 */
export class ViewDiffStrategy extends NestedFieldStrategy<E2ESchemaComponent> {
  public readonly STRATEGY_NAME: string = STRATEGY_NAMES.VIEW_DIFF

  /**
   * 路径到组件ID的映射缓存
   */
  private _pathToComponentIdMap: Map<string, string> = new Map()

  /**
   * 类型守卫：判断是否为 ViewChangeUnit
   */
  public static isViewChangeUnit(unit: ChangeUnit): unit is ViewChangeUnit {
    return (
      unit.strategy === STRATEGY_NAMES.VIEW_DIFF &&
      'fieldPath' in unit.target &&
      'componentId' in unit.target &&
      'attributePath' in unit.target
    )
  }

  public constructor(fieldPath: string) {
    super(fieldPath, {
      path: fieldPath,
      strategy: '', // 不使用根策略，由子策略处理
      ...VIEW_NESTED_CONFIG,
    })
  }

  /**
   * 提取 View 中的所有变更单元
   * 重写以添加 View 特有的处理逻辑
   */
  public extractUnits(value: E2ESchemaComponent | undefined): ViewChangeUnit[] {
    if (!value) return []

    // 构建路径到组件ID的映射
    this._buildPathToComponentIdMap(value)

    // 使用父类的嵌套处理能力
    const nestedUnits = super.extractUnits(value)

    // 添加 View 特有的处理逻辑
    const viewSpecificUnits = this._extractViewSpecificUnits(value)

    // 合并并转换为 ViewChangeUnit
    const allUnits = [...nestedUnits, ...viewSpecificUnits]
    return allUnits.map((unit) => this._convertToViewUnit(unit))
  }

  /**
   * 提取 View 特有的变更单元
   * 主要处理组件树结构相关的逻辑
   */
  private _extractViewSpecificUnits(root: E2ESchemaComponent): ChangeUnit[] {
    const units: ChangeUnit[] = []
    const queue: Array<{
      component: E2ESchemaComponent
      parentId?: string
      order?: number
      path: string
    }> = [
      {
        component: root,
        path: '',
      },
    ]

    while (queue.length > 0) {
      const item = queue.shift()
      if (!item) continue

      const { component, parentId, order, path } = item
      const { id, children } = component

      // 处理父子关系信息 (View 特有逻辑)
      if (parentId !== undefined && order !== undefined) {
        units.push(this._createParentRelationUnit(id, parentId, order, path))
      }

      // 将子组件加入队列
      if (children && Array.isArray(children)) {
        children.forEach((child, index) => {
          if (typeof child === 'object' && child !== null) {
            queue.push({
              component: child as E2ESchemaComponent,
              parentId: id,
              order: index,
              path: path ? `${path}.children[${index}]` : `.children[${index}]`,
            })
          }
        })
      }
    }

    return units
  }

  /**
   * 创建父子关系变更单元
   */
  private _createParentRelationUnit(
    componentId: string,
    parentId: string,
    order: number,
    basePath: string,
  ): ChangeUnit {
    const path = basePath ? `${basePath}._parent_` : `._parent_`

    const target: ViewChangeTarget = {
      type: this.STRATEGY_NAME as typeof STRATEGY_NAMES.VIEW_DIFF,
      fieldPath: this.fieldPath,
      componentId,
      attributePath: '_parent_',
    }

    return {
      id: createTargetKey(target),
      target,
      path,
      value: `${parentId}[${order}]`,
      strategy: this.STRATEGY_NAME,
      metadata: {
        path,
        isSpecial: true,
      },
    }
  }

  /**
   * 转换为 View 变更单元
   */
  private _convertToViewUnit(unit: ChangeUnit): ViewChangeUnit {
    // 如果已经是 ViewChangeUnit，直接返回
    if (ViewDiffStrategy.isViewChangeUnit(unit)) {
      return unit
    }

    // 从路径中提取组件ID和属性路径
    const { componentId, attributePath } = this._parseUnitPath(unit.path)

    const target: ViewChangeTarget = {
      type: this.STRATEGY_NAME as typeof STRATEGY_NAMES.VIEW_DIFF,
      fieldPath: this.fieldPath,
      componentId,
      attributePath,
    }

    return {
      ...unit,
      id: createTargetKey(target), // 重新生成ID，包含组件信息
      target,
      strategy: this.STRATEGY_NAME,
      metadata: {
        ...unit.metadata,
        path: unit.path,
      },
    } as ViewChangeUnit
  }

  /**
   * 构建路径到组件ID的映射
   */
  private _buildPathToComponentIdMap(value: E2ESchemaComponent): void {
    this._pathToComponentIdMap.clear()

    const traverse = (node: unknown, basePath: string): void => {
      if (!node || typeof node !== 'object') return

      const obj = node as Record<string, unknown>

      // 如果有ID，记录映射
      if (typeof obj.id === 'string') {
        this._pathToComponentIdMap.set(basePath, obj.id)
      }

      // 递归处理children
      if (Array.isArray(obj.children)) {
        obj.children.forEach((child: unknown, index: number) => {
          const childPath = basePath
            ? `${basePath}.children[${index}]`
            : `children[${index}]`
          traverse(child, childPath)
        })
      }
    }

    traverse(value, this.fieldPath)
  }

  /**
   * 从单元路径中解析组件ID和属性路径
   */
  private _parseUnitPath(path: string): {
    componentId: string
    attributePath: string
  } {
    // 将相对路径转换为绝对路径
    const absolutePath = path.startsWith('.')
      ? `${this.fieldPath}${path}`
      : path

    const parts = absolutePath.split('.')
    let componentPath = ''
    let attributePath = ''

    // 构建组件路径（到数组索引为止）
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i]

      if (part.includes('[') && part.includes(']')) {
        // 这是数组索引部分，包含在组件路径中
        componentPath += (componentPath ? '.' : '') + part
      } else if (componentPath) {
        // 已经有组件路径了，剩下的就是属性路径
        attributePath = parts.slice(i).join('.')
        break
      } else {
        // 还在构建组件路径
        componentPath += (componentPath ? '.' : '') + part
      }
    }

    // 从映射中获取组件ID
    const componentId =
      this._pathToComponentIdMap.get(componentPath) || 'unknown'

    return { componentId, attributePath }
  }

  /**
   * 比较两个单元的值是否相等
   * 继承自基类的实现
   */
  public unitValuesEqual(oldUnit: ChangeUnit, newUnit: ChangeUnit): boolean {
    return super.unitValuesEqual(oldUnit, newUnit)
  }
}
