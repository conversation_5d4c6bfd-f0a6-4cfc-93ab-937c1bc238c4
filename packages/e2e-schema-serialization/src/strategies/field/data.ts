/**
 * Data 字段 Diff 策略
 * 处理全局数据对象的变更
 */

import { BaseFieldDiffStrategy } from '../../core/base'
import type { ChangeTarget, ChangeUnit } from '../../core/types'

import { createTargetKey } from '../../core/utils'
import { STRATEGY_NAMES } from '../constants'

export class DataDiffStrategy extends BaseFieldDiffStrategy<
  Record<string, unknown>
> {
  public readonly STRATEGY_NAME = STRATEGY_NAMES.DATA_DIFF

  public constructor(fieldPath: string) {
    super(fieldPath)
  }

  /**
   * 提取 data 对象中的所有顶层字段
   * 每个顶层字段作为一个单元，便于追踪数据变更
   */
  public extractUnits(
    value: Record<string, unknown> | undefined,
  ): ChangeUnit[] {
    if (!value || typeof value !== 'object') return []

    const units: ChangeUnit[] = []

    // 只提取顶层字段，避免过度细化
    for (const [key, fieldValue] of Object.entries(value)) {
      const target = this._createTarget(key)
      units.push({
        id: createTargetKey(target),
        target,
        path: key,
        value: fieldValue,
        strategy: this.STRATEGY_NAME,
        metadata: {
          unitType: 'data-field',
          key,
          valueType: this._getValueType(fieldValue),
          // 如果是对象或数组，记录其大小
          size: this._getValueSize(fieldValue),
        },
      })
    }

    return units
  }

  /**
   * 获取值的类型
   */
  private _getValueType(value: unknown): string {
    if (value === null) return 'null'
    if (value === undefined) return 'undefined'
    if (Array.isArray(value)) return 'array'
    return typeof value
  }

  /**
   * 获取值的大小（对象的键数或数组的长度）
   */
  private _getValueSize(value: unknown): number | undefined {
    if (Array.isArray(value)) {
      return value.length
    }
    if (value && typeof value === 'object') {
      return Object.keys(value).length
    }
    return undefined
  }

  /**
   * 创建变更目标
   */
  private _createTarget(fieldName: string): ChangeTarget {
    return {
      type: 'data-field',
      fieldPath: this.fieldPath,
      fieldName,
    }
  }
}
