/**
 * Track Diff 策略
 * 使用通用嵌套范式处理 Track[] 数组的变更追踪
 * 每个 Track 使用 ConfigDiffStrategy 处理其属性
 */

import type { Track } from '@ad/e2e-schema'
import { NestedFieldStrategy } from '../../core/nested-strategy'
import { NestedConfigBuilder } from '../../core/nested-types'
import type { ChangeTarget, ChangeUnit } from '../../core/types'
import { STRATEGY_NAMES } from '../constants'

/**
 * Track 策略的 ChangeTarget 类型
 */
interface TrackChangeTarget extends ChangeTarget {
  type: typeof STRATEGY_NAMES.TRACK_DIFF
  fieldPath: string
  componentId: string
  attributePath: string
}

/**
 * Track 策略的 metadata 类型
 */
interface TrackChangeMetadata extends Record<string, unknown> {
  path: string
  trackId?: string
}

/**
 * Track 策略的 ChangeUnit 类型
 */
export interface TrackChangeUnit extends ChangeUnit {
  target: TrackChangeTarget
  strategy: typeof STRATEGY_NAMES.TRACK_DIFF
  metadata: TrackChangeMetadata
}

/**
 * Track 字段的嵌套配置
 * 处理 Track[] 数组，每个 Track 对象使用 ConfigDiffStrategy
 */
const TRACK_NESTED_CONFIG = NestedConfigBuilder.forArray(
  NestedConfigBuilder.simple('', 'ConfigDiffStrategy'),
)

/**
 * Track Diff 策略
 * 继承通用嵌套策略，处理 Track 数组
 */
export class TrackDiffStrategy extends NestedFieldStrategy<Track[]> {
  public readonly STRATEGY_NAME: string = STRATEGY_NAMES.TRACK_DIFF

  /**
   * 类型守卫：判断是否为 TrackChangeUnit
   */
  public static isTrackChangeUnit(unit: ChangeUnit): unit is TrackChangeUnit {
    return (
      unit.strategy === STRATEGY_NAMES.TRACK_DIFF &&
      'fieldPath' in unit.target &&
      'componentId' in unit.target &&
      'attributePath' in unit.target
    )
  }

  public constructor(fieldPath: string) {
    super(fieldPath, {
      path: fieldPath,
      strategy: '', // 不使用根策略，由子策略处理
      ...TRACK_NESTED_CONFIG,
    })
  }

  /**
   * 提取 Track 数组中的所有变更单元
   * 使用通用嵌套范式处理
   */
  public extractUnits(value: Track[] | undefined): TrackChangeUnit[] {
    if (!value) return []

    // 使用父类的嵌套处理能力
    const nestedUnits = super.extractUnits(value)

    // 转换为 TrackChangeUnit
    return nestedUnits.map((unit) => this._convertToTrackUnit(unit))
  }

  /**
   * 转换为 Track 变更单元
   */
  private _convertToTrackUnit(unit: ChangeUnit): TrackChangeUnit {
    // 如果已经是 TrackChangeUnit，直接返回
    if (TrackDiffStrategy.isTrackChangeUnit(unit)) {
      return unit
    }

    // 从路径中提取组件ID和属性路径
    const { componentId, attributePath, trackId } = this._parseUnitPath(
      unit.path,
    )

    const target: TrackChangeTarget = {
      type: this.STRATEGY_NAME as typeof STRATEGY_NAMES.TRACK_DIFF,
      fieldPath: this.fieldPath,
      componentId,
      attributePath,
    }

    return {
      ...unit,
      target,
      strategy: this.STRATEGY_NAME as typeof STRATEGY_NAMES.TRACK_DIFF,
      metadata: {
        ...unit.metadata,
        path: unit.path,
        trackId,
      },
    } as TrackChangeUnit
  }

  /**
   * 从单元路径中解析组件ID、属性路径和Track ID
   */
  private _parseUnitPath(path: string): {
    componentId: string
    attributePath: string
    trackId?: string
  } {
    // 解析路径，例如：[0].eventType, [1].id, [2].name
    const parts = path.split('.')

    let componentId = 'track'
    let attributePath = path
    let trackId: string | undefined

    // 查找数组索引和属性
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i]
      if (part.includes('[') && part.includes(']')) {
        // 提取数组索引作为 componentId
        const match = part.match(/\[(\d+)\]/)
        if (match) {
          componentId = `track-${match[1]}`
        }
      } else if (i === parts.length - 1) {
        attributePath = part
      }
    }

    return { componentId, attributePath, trackId }
  }
}
