/**
 * API Diff 策略
 * 使用通用嵌套范式处理 E2EAPI[] 数组的变更追踪
 * 每个 API 使用 ConfigDiffStrategy 处理其属性
 */

import type { E2EAPI } from '@ad/e2e-schema'
import { NestedFieldStrategy } from '../../core/nested-strategy'
import { NestedConfigBuilder } from '../../core/nested-types'
import type { ChangeTarget, ChangeUnit } from '../../core/types'
import { STRATEGY_NAMES } from '../constants'

/**
 * API 策略的 ChangeTarget 类型
 */
interface APIChangeTarget extends ChangeTarget {
  type: typeof STRATEGY_NAMES.API_DIFF
  fieldPath: string
  componentId: string
  attributePath: string
}

/**
 * API 策略的 metadata 类型
 */
interface APIChangeMetadata extends Record<string, unknown> {
  path: string
  apiId?: string
}

/**
 * API 策略的 ChangeUnit 类型
 */
export interface APIChangeUnit extends ChangeUnit {
  target: APIChangeTarget
  strategy: typeof STRATEGY_NAMES.API_DIFF
  metadata: APIChangeMetadata
}

/**
 * API 字段的嵌套配置
 * 处理 E2EAPI[] 数组，每个 API 对象使用 ConfigDiffStrategy
 */
const API_NESTED_CONFIG = NestedConfigBuilder.forArray(
  NestedConfigBuilder.simple('', 'ConfigDiffStrategy'),
)

/**
 * API Diff 策略
 * 继承通用嵌套策略，处理 API 数组
 */
export class APIDiffStrategy extends NestedFieldStrategy<E2EAPI[]> {
  public readonly STRATEGY_NAME: string = STRATEGY_NAMES.API_DIFF

  /**
   * 类型守卫：判断是否为 APIChangeUnit
   */
  public static isAPIChangeUnit(unit: ChangeUnit): unit is APIChangeUnit {
    return (
      unit.strategy === STRATEGY_NAMES.API_DIFF &&
      'fieldPath' in unit.target &&
      'componentId' in unit.target &&
      'attributePath' in unit.target
    )
  }

  public constructor(fieldPath: string) {
    super(fieldPath, {
      path: fieldPath,
      strategy: '', // 不使用根策略，由子策略处理
      ...API_NESTED_CONFIG,
    })
  }

  /**
   * 提取 API 数组中的所有变更单元
   * 使用通用嵌套范式处理
   */
  public extractUnits(value: E2EAPI[] | undefined): APIChangeUnit[] {
    if (!value) return []

    // 使用父类的嵌套处理能力
    const nestedUnits = super.extractUnits(value)

    // 转换为 APIChangeUnit
    return nestedUnits.map((unit) => this._convertToAPIUnit(unit))
  }

  /**
   * 转换为 API 变更单元
   */
  private _convertToAPIUnit(unit: ChangeUnit): APIChangeUnit {
    // 如果已经是 APIChangeUnit，直接返回
    if (APIDiffStrategy.isAPIChangeUnit(unit)) {
      return unit
    }

    // 从路径中提取组件ID和属性路径
    const { componentId, attributePath, apiId } = this._parseUnitPath(unit.path)

    const target: APIChangeTarget = {
      type: this.STRATEGY_NAME as typeof STRATEGY_NAMES.API_DIFF,
      fieldPath: this.fieldPath,
      componentId,
      attributePath,
    }

    return {
      ...unit,
      target,
      strategy: this.STRATEGY_NAME as typeof STRATEGY_NAMES.API_DIFF,
      metadata: {
        ...unit.metadata,
        path: unit.path,
        apiId,
      },
    } as APIChangeUnit
  }

  /**
   * 从单元路径中解析组件ID、属性路径和API ID
   */
  private _parseUnitPath(path: string): {
    componentId: string
    attributePath: string
    apiId?: string
  } {
    // 解析路径，例如：[0].id, [1].name, [2].url
    const parts = path.split('.')

    let componentId = 'api'
    let attributePath = path
    let apiId: string | undefined

    // 查找数组索引和属性
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i]
      if (part.includes('[') && part.includes(']')) {
        // 提取数组索引作为 componentId
        const match = part.match(/\[(\d+)\]/)
        if (match) {
          componentId = `api-${match[1]}`
        }
      } else if (i === parts.length - 1) {
        attributePath = part
      }
    }

    return { componentId, attributePath, apiId }
  }
}
