/**
 * 策略注册表
 * 自动注册所有内置策略
 */

import { CodeLineDiffStrategy } from './basic/code-line'
import { ConfigDiffStrategy } from './basic/config'
import { IDListDiffStrategy } from './basic/id-list'
import { TodoStrategy } from './basic/todo'
import { StrategyFactory } from './factory'
import { APIDiffStrategy } from './field/api'
import { DataDiffStrategy } from './field/data'
import { IifeDiffStrategy } from './field/iife'
import { LinkageDiffStrategy } from './field/linkage'
import { TrackDiffStrategy } from './field/track'
import { ViewDiffStrategy } from './field/view'

/**
 * 注册所有内置策略
 */
export function registerBuiltinStrategies(): void {
  // 基础策略
  StrategyFactory.register('CodeLineDiffStrategy', CodeLineDiffStrategy)
  StrategyFactory.register('ConfigDiffStrategy', ConfigDiffStrategy)
  StrategyFactory.register('IDListDiffStrategy', IDListDiffStrategy)
  StrategyFactory.register('TodoStrategy', TodoStrategy)

  // 字段策略
  StrategyFactory.register('APIDiffStrategy', APIDiffStrategy)
  StrategyFactory.register('DataDiffStrategy', DataDiffStrategy)
  StrategyFactory.register('IifeDiffStrategy', IifeDiffStrategy)
  StrategyFactory.register('LinkageDiffStrategy', LinkageDiffStrategy)
  StrategyFactory.register('TrackDiffStrategy', TrackDiffStrategy)
  StrategyFactory.register('ViewDiffStrategy', ViewDiffStrategy)
}

// 自动注册所有内置策略
registerBuiltinStrategies()
