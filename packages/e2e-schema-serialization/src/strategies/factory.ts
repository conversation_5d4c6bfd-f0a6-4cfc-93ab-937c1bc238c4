/**
 * 策略工厂
 * 负责策略的注册和创建
 */

import type { FieldDiffStrategy } from '../core/types'

/**
 * 策略构造函数类型
 */
type StrategyConstructor = new (
  path: string,
  options?: Record<string, unknown>,
) => FieldDiffStrategy<unknown>

/**
 * 策略工厂类
 */
export class StrategyFactory {
  private static _strategies = new Map<string, StrategyConstructor>()

  /**
   * 注册策略
   */
  public static register(
    name: string,
    strategyClass: StrategyConstructor,
  ): void {
    this._strategies.set(name, strategyClass)
  }

  /**
   * 创建策略实例
   */
  public static create(
    name: string,
    path: string,
    options?: Record<string, unknown>,
  ): FieldDiffStrategy<unknown> {
    const StrategyClass = this._strategies.get(name)
    if (!StrategyClass) {
      throw new Error(
        `Strategy ${name} not found. Available strategies: ${Array.from(
          this._strategies.keys(),
        ).join(', ')}`,
      )
    }
    return new StrategyClass(path, options)
  }

  /**
   * 检查策略是否已注册
   */
  public static has(name: string): boolean {
    return this._strategies.has(name)
  }

  /**
   * 获取所有已注册的策略名称
   */
  public static getRegisteredStrategies(): string[] {
    return Array.from(this._strategies.keys())
  }

  /**
   * 清空所有注册的策略（主要用于测试）
   */
  public static clear(): void {
    this._strategies.clear()
  }
}
