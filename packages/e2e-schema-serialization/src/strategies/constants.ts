/**
 * 策略常量定义
 * 统一管理所有策略的名称和标识符
 */

export const STRATEGY_NAMES = {
  // 基础策略
  CONFIG_DIFF: 'CONFIG_DIFF_STRATEGY',
  CODE_LINE_DIFF: 'CODE_LINE_DIFF_STRATEGY',
  ID_LIST_DIFF: 'ID_LIST_DIFF_STRATEGY',
  TODO: 'TODO_STRATEGY',

  // 字段策略
  VIEW_DIFF: 'VIEW_DIFF_STRATEGY',
  API_DIFF: 'API_DIFF_STRATEGY',
  DATA_DIFF: 'DATA_DIFF_STRATEGY',
  IIFE_DIFF: 'IIFE_DIFF_STRATEGY',
  LINKAGE_DIFF: 'LINKAGE_DIFF_STRATEGY',
  TRACK_DIFF: 'TRACK_DIFF_STRATEGY',

  // 嵌套策略
  NESTED_FIELD: 'NESTED_FIELD_STRATEGY',
} as const

export type StrategyName = (typeof STRATEGY_NAMES)[keyof typeof STRATEGY_NAMES]

/**
 * 策略类型标识符
 */
export const STRATEGY_TYPES = {
  BASIC: 'basic',
  FIELD: 'field',
} as const

export type StrategyType = (typeof STRATEGY_TYPES)[keyof typeof STRATEGY_TYPES]
