/**
 * 策略模式 Diff 工具导出
 */

// 核心类型
export type {
  Change,
  ChangeTarget,
  ChangeType,
  ChangeUnit,
  FieldDiffStrategy,
} from '../core/types'

// 核心基类
export { BaseFieldDiffStrategy } from '../core/base'

// 嵌套处理核心
export { NestedFieldStrategy } from '../core/nested-strategy'
export {
  NestedConfigBuilder,
  PathBuilder,
  type NestedDescriptor,
  type NestedProcessingContext,
  type NestedProcessingOptions,
  type TreeTraversalConfig,
} from '../core/nested-types'

// 基础策略
export {
  CodeLineDiffStrategy,
  type CodeLineChangeUnit,
} from './basic/code-line'
export { ConfigDiffStrategy, type ConfigChangeUnit } from './basic/config'
export { IDListDiffStrategy, type IDListChangeUnit } from './basic/id-list'
export { TodoStrategy } from './basic/todo'

// 字段策略
export { APIDiffStrategy } from './field/api'
export { DataDiffStrategy } from './field/data'
export { IifeDiffStrategy, type IifeChangeUnit } from './field/iife'
export { LinkageDiffStrategy } from './field/linkage'
export { TrackDiffStrategy, type TrackChangeUnit } from './field/track'
export { ViewDiffStrategy, type ViewChangeUnit } from './field/view'

// Diff 引擎
export {
  SchemaDiffEngine,
  type DiffResult,
  type DiffStats,
} from '../core/engine'

// 便捷函数
import type { E2ESchema } from '@ad/e2e-schema'
import { SchemaDiffEngine, type DiffResult } from '../core/engine'

/**
 * 使用配置计算 Schema 差异
 */
export function diffSchemas(
  oldSchema: E2ESchema,
  newSchema: E2ESchema,
  config?: import('../config/types').DiffEngineConfig,
): DiffResult {
  const engine = new SchemaDiffEngine(config)
  return engine.diff(oldSchema, newSchema)
}

// 重新导出工具函数
export { createTargetKey, generateUnitId } from '../core/utils'
