/**
 * TODO 策略
 * 用于标记暂时跳过处理的字段
 * 不产生任何变更单元，主要用于占位和文档目的
 */

import { BaseFieldDiffStrategy } from '../../core/base'
import type { ChangeUnit } from '../../core/types'
import { STRATEGY_NAMES } from '../constants'

/**
 * TODO 策略实现
 * 所有方法都返回空结果，表示跳过处理
 */
export class TodoStrategy extends BaseFieldDiffStrategy<unknown> {
  public readonly STRATEGY_NAME = STRATEGY_NAMES.TODO

  /**
   * 类型守卫：判断是否为 TODO 策略的单元
   * 由于 TODO 策略不产生单元，这个方法总是返回 false
   */
  public static isTodoChangeUnit(unit: ChangeUnit): boolean {
    return unit.strategy === STRATEGY_NAMES.TODO
  }

  public constructor(fieldPath: string) {
    super(fieldPath)
  }

  /**
   * 提取变更单元
   * TODO 策略不提取任何单元，直接返回空数组
   */
  public extractUnits(): ChangeUnit[] {
    // TODO 策略跳过所有处理
    return []
  }

  /**
   * 比较两个单元的值是否相等
   * 由于 TODO 策略不产生单元，这个方法不会被调用
   * 但为了完整性，提供一个默认实现
   */
  public unitValuesEqual(oldUnit: ChangeUnit, newUnit: ChangeUnit): boolean {
    // 使用基类的默认比较逻辑
    return BaseFieldDiffStrategy.unitValuesEqual(oldUnit, newUnit)
  }
}
