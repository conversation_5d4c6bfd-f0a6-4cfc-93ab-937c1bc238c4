/**
 * ID List Diff 策略
 * 处理 ID 数组的变更，顺序不敏感
 * 每个 ID 的增删作为独立的 ChangeUnit
 */

import { BaseFieldDiffStrategy } from '../../core/base'
import type { ChangeTarget, ChangeUnit } from '../../core/types'

/**
 * 创建稳定的target key
 */
function createTargetKey(target: ChangeTarget): string {
  return Object.keys(target)
    .sort()
    .map((key) => `${key}:${target[key]}`)
    .join('|')
}

/**
 * IDList 策略的 ChangeTarget 类型
 */
interface IDListChangeTarget extends ChangeTarget {
  type: 'ID_LIST_DIFF_STRATEGY'
  fieldPath: string // 字段路径
  listId: string // 列表标识（用于区分不同的列表）
  itemId: string // 具体的 ID 值
}

/**
 * IDList 策略的 metadata 类型
 */
interface IDListChangeMetadata extends Record<string, unknown> {
  listId: string
  itemId: string
  position?: number // 原始位置，仅用于调试
}

/**
 * IDList 策略的 ChangeUnit 类型
 */
export interface IDListChangeUnit extends ChangeUnit {
  target: IDListChangeTarget
  strategy: 'ID_LIST_DIFF_STRATEGY'
  metadata: IDListChangeMetadata
}

export class IDListDiffStrategy extends BaseFieldDiffStrategy<string[]> {
  public readonly STRATEGY_NAME = 'ID_LIST_DIFF_STRATEGY' as const
  private readonly _listId: string

  /**
   * 类型守卫：判断是否为 IDListChangeUnit
   */
  public static isIDListChangeUnit(unit: ChangeUnit): unit is IDListChangeUnit {
    return (
      unit.strategy === 'ID_LIST_DIFF_STRATEGY' &&
      'fieldPath' in unit.target &&
      'listId' in unit.target &&
      'itemId' in unit.target
    )
  }

  /**
   * @param fieldPath 字段路径
   * @param listId 列表标识，用于区分不同的 ID 列表
   * @param options 可选配置，支持从options.listId获取listId
   */
  public constructor(
    fieldPath: string,
    listId?: string | Record<string, unknown>,
    options?: Record<string, unknown>,
  ) {
    super(fieldPath)

    // 支持多种参数形式
    if (typeof listId === 'string') {
      this._listId = listId || fieldPath
    } else if (typeof listId === 'object' && listId?.listId) {
      this._listId = String(listId.listId)
    } else if (options?.listId) {
      this._listId = String(options.listId)
    } else {
      this._listId = fieldPath // 默认使用fieldPath作为listId
    }
  }

  /**
   * 提取 ID 列表中的每个 ID 作为独立的 ChangeUnit
   * 顺序不敏感，使用 ID 本身作为唯一标识
   */
  public extractUnits(value: string[] | undefined): IDListChangeUnit[] {
    if (!value || !Array.isArray(value)) {
      return []
    }

    const units: IDListChangeUnit[] = []

    // 遍历数组，每个 ID 生成一个 ChangeUnit
    value.forEach((id, index) => {
      // 跳过空字符串或非字符串值
      if (!id || typeof id !== 'string') {
        return
      }

      const target = {
        type: this.STRATEGY_NAME,
        fieldPath: this.fieldPath,
        listId: this._listId,
        itemId: id,
      }

      units.push({
        id: createTargetKey(target),
        target,
        path: `[${index}]`, // 相对路径，包含位置信息（仅用于调试）
        value: id, // ID 本身作为值
        strategy: this.STRATEGY_NAME,
        metadata: {
          listId: this._listId,
          itemId: id,
          position: index, // 记录原始位置，仅用于调试
        },
      })
    })

    return units
  }

  /**
   * 比较两个单元的值是否相等
   * 对于 ID 列表，只要 ID 相同就认为相等（位置无关）
   */
  public unitValuesEqual(oldUnit: ChangeUnit, newUnit: ChangeUnit): boolean {
    // ID 本身就是值，直接比较
    return oldUnit.value === newUnit.value
  }
}
