/**
 * Config Diff 策略
 * 处理配置对象的第一层属性变更
 * 每个属性作为独立的 ChangeUnit
 */

import { BaseFieldDiffStrategy } from '../../core/base'
import type { ChangeTarget, ChangeUnit } from '../../core/types'
import { createTargetKey, generateUnitId } from '../../core/utils'
import { STRATEGY_NAMES } from '../constants'

/**
 * Config 策略的 ChangeTarget 类型
 */
interface ConfigChangeTarget extends ChangeTarget {
  type: 'CONFIG_DIFF_STRATEGY'
  fieldPath: string // 字段路径，如 'apis[0]' 或 'skeleton'
  configId: string // 配置对象ID（对于API就是 apiId）
  propertyKey: string // 属性键名
}

/**
 * Config 策略的 metadata 类型
 */
interface ConfigChangeMetadata extends Record<string, unknown> {
  configId: string
  propertyKey: string
  valueType: string // 值的类型：用于调试
}

/**
 * Config 策略的 ChangeUnit 类型
 */
export interface ConfigChangeUnit extends ChangeUnit {
  target: ConfigChangeTarget
  strategy: 'CONFIG_DIFF_STRATEGY'
  metadata: ConfigChangeMetadata
}

export class ConfigDiffStrategy extends BaseFieldDiffStrategy<
  Record<string, unknown>
> {
  public readonly STRATEGY_NAME = STRATEGY_NAMES.CONFIG_DIFF
  private readonly _configId: string

  /**
   * 类型守卫：判断是否为 ConfigChangeUnit
   */
  public static isConfigChangeUnit(unit: ChangeUnit): unit is ConfigChangeUnit {
    return (
      unit.strategy === STRATEGY_NAMES.CONFIG_DIFF &&
      'fieldPath' in unit.target &&
      'configId' in unit.target &&
      'propertyKey' in unit.target
    )
  }

  /**
   * @param fieldPath 字段路径
   * @param configId 配置对象ID，对于API就是 apiId
   * @param options 可选配置，支持从options.configId获取configId
   */
  public constructor(
    fieldPath: string,
    configId?: string | Record<string, unknown>,
    options?: Record<string, unknown>,
  ) {
    super(fieldPath)

    // 支持多种参数形式
    if (typeof configId === 'string') {
      this._configId = configId
    } else if (typeof configId === 'object' && configId?.configId) {
      this._configId = String(configId.configId)
    } else if (options?.configId) {
      this._configId = String(options.configId)
    } else {
      this._configId = fieldPath // 默认使用fieldPath作为configId
    }
  }

  /**
   * 提取配置对象的第一层属性，或处理原始值
   * 每个属性生成一个独立的 ChangeUnit
   */
  public extractUnits(
    value: Record<string, unknown> | unknown | undefined,
  ): ConfigChangeUnit[] {
    // 处理原始值（字符串、数字、布尔值等）
    if (value !== null && value !== undefined && typeof value !== 'object') {
      return this._extractPrimitiveValue(value)
    }

    // 处理对象
    if (!value || typeof value !== 'object' || Array.isArray(value)) {
      return []
    }

    const units: ConfigChangeUnit[] = []

    // 遍历对象的第一层属性
    Object.entries(value).forEach(([key, propValue]) => {
      // 跳过 undefined 值
      if (propValue === undefined) {
        return
      }

      const relativePath = `.${key}` // 相对路径格式

      const target = {
        type: this.STRATEGY_NAME,
        fieldPath: this.fieldPath, // 添加字段路径
        configId: this._configId,
        propertyKey: key,
      }

      units.push({
        id: generateUnitId(target),
        target,
        path: relativePath,
        value: propValue, // 属性值作为整体，不深入遍历
        strategy: this.STRATEGY_NAME,
        metadata: {
          configId: this._configId,
          propertyKey: key,
          valueType: Array.isArray(propValue)
            ? 'array'
            : typeof propValue === 'object' && propValue !== null
            ? 'object'
            : typeof propValue,
        },
      })
    })

    return units
  }

  /**
   * 处理原始值（字符串、数字、布尔值等）
   * 将原始值作为单个配置项处理
   */
  private _extractPrimitiveValue(value: unknown): ConfigChangeUnit[] {
    const relativePath = '' // 原始值本身，无相对路径

    const target = {
      type: this.STRATEGY_NAME,
      fieldPath: this.fieldPath,
      configId: this._configId,
      propertyKey: '_primitive_', // 使用特殊键标识原始值
    }

    const unit: ConfigChangeUnit = {
      id: createTargetKey(target),
      target,
      path: relativePath,
      value,
      strategy: this.STRATEGY_NAME,
      metadata: {
        configId: this._configId,
        propertyKey: '_primitive_',
        valueType: typeof value, // 添加必需的 valueType 字段
        isPrimitive: true, // 标记为原始值
      },
    }

    return [unit]
  }
}
