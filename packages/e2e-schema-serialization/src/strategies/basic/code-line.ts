/**
 * Code Line Diff 策略
 * 按行处理代码字段的变更追踪
 */

import { BaseFieldDiffStrategy } from '../../core/base'
import type { ChangeTarget, ChangeUnit } from '../../core/types'
import {
  generateContentHash,
  generateUnitId,
  normalizeLine,
} from '../../core/utils'

/**
 * CodeLine 策略的 ChangeTarget 类型
 */
interface CodeLineChangeTarget extends ChangeTarget {
  type: 'CODE_LINE_DIFF_STRATEGY'
  fieldPath: string // 添加字段路径，确保只在同字段内比较
  contentHash: string
}

/**
 * CodeLine 策略的 metadata 类型
 */
interface CodeLineChangeMetadata extends Record<string, unknown> {
  lineNumber: number
  originalLine: string
  normalizedLine: string
  isComment?: boolean
}

/**
 * CodeLine 策略的 ChangeUnit 类型
 */
export interface CodeLineChangeUnit extends ChangeUnit {
  target: CodeLineChangeTarget
  strategy: 'CODE_LINE_DIFF_STRATEGY'
  metadata: CodeLineChangeMetadata
}

export class CodeLineDiffStrategy extends BaseFieldDiffStrategy<string> {
  public readonly STRATEGY_NAME = 'CODE_LINE_DIFF_STRATEGY' as const

  /**
   * 类型守卫：判断是否为 CodeLineChangeUnit
   */
  public static isCodeLineChangeUnit(
    unit: ChangeUnit,
  ): unit is CodeLineChangeUnit {
    return (
      unit.strategy === 'CODE_LINE_DIFF_STRATEGY' &&
      'fieldPath' in unit.target &&
      'contentHash' in unit.target
    )
  }

  public constructor(fieldPath: string) {
    super(fieldPath)
  }

  /**
   * 提取代码行单元
   * 每一行代码作为一个单元
   */
  public extractUnits(value: string | undefined): CodeLineChangeUnit[] {
    if (value === undefined || value === null || typeof value !== 'string')
      return []

    const lines = value.split('\n')
    const units: CodeLineChangeUnit[] = []

    lines.forEach((line, index) => {
      const lineNumber = index + 1
      const { normalized, isComment } = normalizeLine(line)

      // 默认忽略空行
      if (normalized === '') {
        return
      }

      const contentHash = generateContentHash(normalized)
      const relativePath = `:${index}` // 相对路径，只包含行号

      const target = {
        type: this.STRATEGY_NAME,
        fieldPath: this.fieldPath, // 添加字段路径到target
        contentHash,
      }

      units.push({
        id: generateUnitId(target),
        target,
        path: relativePath,
        value: normalized,
        strategy: this.STRATEGY_NAME,
        metadata: {
          lineNumber,
          originalLine: line,
          normalizedLine: normalized,
          ...(isComment && { isComment: true }),
        },
      })
    })

    return units
  }
}
