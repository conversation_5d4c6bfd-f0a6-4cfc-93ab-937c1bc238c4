/**
 * unitValuesEqual 函数测试
 * 验证值比较逻辑：只忽略对象键的顺序，其他情况严格相等
 */

import { BaseFieldDiffStrategy } from '../core/base'
import type { ChangeUnit } from '../core/types'

// 创建测试用的 ChangeUnit
function createChangeUnit(value: unknown): ChangeUnit {
  return {
    id: 'test',
    value,
    target: { type: 'test', fieldPath: 'test' },
    path: 'test',
    strategy: 'test',
  }
}

describe('BaseFieldDiffStrategy.unitValuesEqual', () => {
  describe('✅ 应该相等的情况', () => {
    it('相同的原始值', () => {
      const unit1 = createChangeUnit('hello')
      const unit2 = createChangeUnit('hello')
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('相同的数字', () => {
      const unit1 = createChangeUnit(42)
      const unit2 = createChangeUnit(42)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('相同的布尔值', () => {
      const unit1 = createChangeUnit(true)
      const unit2 = createChangeUnit(true)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('相同的 null/undefined', () => {
      const unit1 = createChangeUnit(null)
      const unit2 = createChangeUnit(null)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)

      const unit3 = createChangeUnit(undefined)
      const unit4 = createChangeUnit(undefined)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit3, unit4)).toBe(true)
    })

    it('相同的数组', () => {
      const unit1 = createChangeUnit([1, 2, 3])
      const unit2 = createChangeUnit([1, 2, 3])
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('相同的对象（键顺序相同）', () => {
      const unit1 = createChangeUnit({ a: 1, b: 2 })
      const unit2 = createChangeUnit({ a: 1, b: 2 })
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('🎯 相同的对象（键顺序不同）- 核心测试', () => {
      const unit1 = createChangeUnit({ a: 1, b: 2, c: 3 })
      const unit2 = createChangeUnit({ c: 3, a: 1, b: 2 })
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('嵌套对象（键顺序不同）', () => {
      const unit1 = createChangeUnit({
        user: { name: 'John', age: 30 },
        settings: { theme: 'dark', lang: 'en' },
      })
      const unit2 = createChangeUnit({
        settings: { lang: 'en', theme: 'dark' },
        user: { age: 30, name: 'John' },
      })
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('复杂嵌套结构（键顺序不同）', () => {
      const unit1 = createChangeUnit({
        view: {
          id: 'form',
          props: { layout: 'vertical', style: 'margin: 10px' },
          children: [{ id: 'input', type: 'Input' }],
        },
      })
      const unit2 = createChangeUnit({
        view: {
          children: [{ type: 'Input', id: 'input' }],
          id: 'form',
          props: { style: 'margin: 10px', layout: 'vertical' },
        },
      })
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })
  })

  describe('❌ 应该不相等的情况', () => {
    it('不同的原始值', () => {
      const unit1 = createChangeUnit('hello')
      const unit2 = createChangeUnit('world')
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('不同的类型', () => {
      const unit1 = createChangeUnit('42')
      const unit2 = createChangeUnit(42)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('null vs undefined', () => {
      const unit1 = createChangeUnit(null)
      const unit2 = createChangeUnit(undefined)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('不同长度的数组', () => {
      const unit1 = createChangeUnit([1, 2, 3])
      const unit2 = createChangeUnit([1, 2])
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('相同长度但不同内容的数组', () => {
      const unit1 = createChangeUnit([1, 2, 3])
      const unit2 = createChangeUnit([1, 2, 4])
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('🎯 数组顺序不同 - 应该不相等', () => {
      const unit1 = createChangeUnit([1, 2, 3])
      const unit2 = createChangeUnit([3, 2, 1])
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('不同键的对象', () => {
      const unit1 = createChangeUnit({ a: 1, b: 2 })
      const unit2 = createChangeUnit({ a: 1, c: 2 })
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('相同键但不同值的对象', () => {
      const unit1 = createChangeUnit({ a: 1, b: 2 })
      const unit2 = createChangeUnit({ a: 1, b: 3 })
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('不同键数量的对象', () => {
      const unit1 = createChangeUnit({ a: 1, b: 2 })
      const unit2 = createChangeUnit({ a: 1, b: 2, c: 3 })
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('🎯 字符串 vs 数字（严格类型检查）', () => {
      const unit1 = createChangeUnit('123')
      const unit2 = createChangeUnit(123)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('🎯 布尔值 vs 数字（严格类型检查）', () => {
      const unit1 = createChangeUnit(true)
      const unit2 = createChangeUnit(1)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })
  })

  describe('🔍 边界情况', () => {
    it('空对象', () => {
      const unit1 = createChangeUnit({})
      const unit2 = createChangeUnit({})
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('空数组', () => {
      const unit1 = createChangeUnit([])
      const unit2 = createChangeUnit([])
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('对象 vs 数组', () => {
      const unit1 = createChangeUnit({})
      const unit2 = createChangeUnit([])
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })

    it('深度嵌套的相同结构', () => {
      const deep1 = { a: { b: { c: { d: { e: 'deep' } } } } }
      const deep2 = { a: { b: { c: { d: { e: 'deep' } } } } }
      const unit1 = createChangeUnit(deep1)
      const unit2 = createChangeUnit(deep2)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(true)
    })

    it('深度嵌套的不同结构', () => {
      const deep1 = { a: { b: { c: { d: { e: 'deep1' } } } } }
      const deep2 = { a: { b: { c: { d: { e: 'deep2' } } } } }
      const unit1 = createChangeUnit(deep1)
      const unit2 = createChangeUnit(deep2)
      expect(BaseFieldDiffStrategy.unitValuesEqual(unit1, unit2)).toBe(false)
    })
  })
})
