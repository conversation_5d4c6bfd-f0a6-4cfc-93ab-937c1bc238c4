/**
 * 🎯 AI覆盖率计算真实场景测试
 *
 * 测试场景：用户注册表单的完整开发流程
 * - initV → AV：AI添加头像组件
 * - AV → BV：人工调整布局
 * - BV → CV：人工添加按钮
 * - CV → DV：AI添加验证逻辑
 * - DV → EV：人工优化提示
 *
 * 验证AI覆盖率计算的准确性和一致性
 */

import type { E2ESchema } from '@ad/e2e-schema'
import { CoverageCalculator } from '../core/coverage'
import { SchemaDiffEngine } from '../core/engine'

describe('🎯 AI覆盖率计算真实场景', () => {
  let engine: SchemaDiffEngine

  beforeEach(() => {
    engine = new SchemaDiffEngine()
  })

  describe('📖 用户注册表单开发流程', () => {
    // 测试数据：模拟真实开发中的Schema演进过程
    const initV: E2ESchema = {
      schemaVersion: '1.0.0',
      version: '1.0.0',
      view: {
        id: 'registerForm',
        type: 'Form',
        name: '用户注册表单',
        props: { layout: 'vertical' },
        children: [
          {
            id: 'usernameInput',
            type: 'Input',
            name: '用户名',
            props: { placeholder: '请输入用户名' },
            effect: {
              type: 'js',
              code: 'console.log("username input mounted")',
              codeTS: 'console.log("username input mounted");',
              codeES: 'console.log("username input mounted");',
            },
          },
          {
            id: 'emailInput',
            type: 'Input',
            name: '邮箱',
            props: { placeholder: '请输入邮箱', type: 'email' },
            if: {
              type: 'js',
              code: 'return showEmailField',
              codeTS: 'return showEmailField as boolean',
            },
            apis: [
              {
                id: 'validateEmail',
                name: '邮箱验证',
                type: 'rpc',
                service: '/api/validate/email',
              },
            ],
          },
        ],
      },
      apis: [
        {
          id: 'register',
          name: '用户注册',
          type: 'rpc',
          service: '/api/user/register',
        },
      ],
      model: {
        code: `var UserForm = {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
}`,
      },
    }

    // AV：AI添加头像组件
    const AV: E2ESchema = {
      ...initV,
      view: {
        ...initV.view,
        children: [
          {
            id: 'avatarUpload',
            type: 'Upload',
            name: '头像上传',
            props: {
              accept: 'image/*',
              maxSize: 2048,
              placeholder: '点击上传头像',
            },
          },
          ...(initV.view.children || []),
        ],
      },
      model: {
        code: `var UserForm = {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
  avatar?: string;
}`,
      },
    }

    // BV：人工调整布局
    const BV: E2ESchema = {
      ...AV,
      view: {
        ...AV.view,
        props: {
          layout: 'horizontal',
          style: 'margin: 10px',
        },
      },
    }

    // CV：人工添加按钮
    const CV: E2ESchema = {
      ...BV,
      view: {
        ...BV.view,
        children: [
          ...(BV.view.children || []),
          {
            id: 'submitButton',
            type: 'Button',
            name: '注册按钮',
            props: {
              type: 'primary',
              text: '立即注册',
              htmlType: 'submit',
            },
          },
        ],
      },
    }

    // DV：AI添加验证逻辑
    const DV: E2ESchema = {
      ...CV,
      model: {
        code: `var UserForm = {}; var validateForm = function() {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
  avatar?: string;
}

// AI生成的验证逻辑
const validateForm = (form: UserForm): string[] => {
  const errors: string[] = [];

  if (!form.username || form.username.length < 3) {
    errors.push('用户名至少3个字符');
  }

  if (!form.email || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(form.email)) {
    errors.push('请输入有效的邮箱地址');
  }

  return errors;
};`,
      },
      apis: [
        ...(CV.apis || []),
        {
          id: 'validateUser',
          name: '验证用户信息',
          type: 'rpc',
          service: '/api/user/validate',
        },
      ],
    }

    // EV：人工优化提示
    const EV: E2ESchema = {
      ...DV,
      view: {
        ...DV.view,
        children: [
          ...(DV.view.children || []),
          {
            id: 'errorMessage',
            type: 'Alert',
            name: '错误提示',
            props: {
              type: 'error',
              showIcon: true,
              closable: true,
            },
          },
        ],
      },
    }

    it('📊 验证各阶段变更数量', () => {
      const diffA = engine.diff(initV, AV) // AI添加头像
      const diffB = engine.diff(AV, BV) // 人工调整布局
      const diffC = engine.diff(BV, CV) // 人工添加按钮
      const diffD = engine.diff(CV, DV) // AI添加验证
      const diffE = engine.diff(DV, EV) // 人工优化提示

      // 验证各阶段变更数量符合预期
      expect(diffA.stats.total).toBe(9) // 头像组件 + model字段
      expect(diffB.stats.total).toBe(2) // 布局属性变更
      expect(diffC.stats.total).toBe(6) // 按钮组件
      expect(diffD.stats.total).toBe(15) // 验证逻辑 + API
      expect(diffE.stats.total).toBe(6) // 提示组件

      // 验证AI贡献总量
      const aiContribution = diffA.stats.total + diffD.stats.total
      expect(aiContribution).toBe(24) // 9 + 15
    })

    it('🧮 计算AI覆盖率', () => {
      // 计算整个变更的总修改量
      const totalDiff = engine.diff(initV, EV)

      // 计算AI贡献的变更
      const aiDiffA = engine.diff(initV, AV) // AI添加头像
      const aiDiffD = engine.diff(CV, DV) // AI添加验证

      // 计算覆盖率
      const coverage = CoverageCalculator.calculateAICoverage(totalDiff, [
        aiDiffA,
        aiDiffD,
      ])

      // 验证结果合理性
      expect(totalDiff.stats.total).toBe(38)
      expect(aiDiffA.stats.total).toBe(9)
      expect(aiDiffD.stats.total).toBe(15)
      expect(coverage).toBeCloseTo(63.16, 1) // 24/38 ≈ 63.16%
    })

    it('🔍 详细覆盖率分析', () => {
      const totalDiff = engine.diff(initV, EV)
      const aiDiffA = engine.diff(initV, AV)
      const aiDiffD = engine.diff(CV, DV)

      const detailed = CoverageCalculator.calculateDetailedCoverage(totalDiff, [
        aiDiffA,
        aiDiffD,
      ])

      // 验证详细统计信息结构完整
      expect(detailed.totalCoverage).toBeGreaterThan(0)
      expect(detailed.preservedChanges.length).toBeGreaterThan(0)
      expect(detailed.byType).toBeDefined()
      expect(detailed.byField).toBeDefined()
      expect(detailed.byStrategy).toBeDefined()
    })

    it('🎯 验证策略类型覆盖', () => {
      const totalDiff = engine.diff(initV, EV)
      const strategies = new Set(totalDiff.changes.map((c) => c.unit.strategy))

      // 验证包含多种策略类型
      expect(strategies.size).toBeGreaterThan(1)

      // 验证包含视图相关策略
      const hasViewStrategy = Array.from(strategies).some(
        (s) =>
          s.includes('VIEW') || s.includes('Config') || s.includes('Primitive'),
      )
      expect(hasViewStrategy).toBe(true)
    })
  })

  describe('📖 边界情况', () => {
    it('🚫 空变更返回0%覆盖率', () => {
      const emptyDiff = {
        changes: [],
        stats: {
          total: 0,
          byType: { add: 0, modify: 0, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(emptyDiff, [])
      expect(coverage).toBe(0)
    })

    it('💯 完全匹配返回100%覆盖率', () => {
      const testDiff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'test1',
              value: 'value1',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
        ],
        stats: {
          total: 1,
          byType: { add: 1, modify: 0, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(testDiff, [
        testDiff,
      ])
      expect(coverage).toBe(100)
    })

    it('🔄 消耗式匹配机制', () => {
      const finalDiff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'same-id',
              value: 'final-value',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
          {
            type: 'modify' as const,
            unit: {
              id: 'same-id',
              value: 'another-value',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path2',
              strategy: 'test',
            },
            path: 'path2',
          },
        ],
        stats: {
          total: 2,
          byType: { add: 1, modify: 1, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const aiDiff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'same-id',
              value: 'final-value', // 匹配
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
          {
            type: 'modify' as const,
            unit: {
              id: 'same-id',
              value: 'different-value', // 不匹配
              target: { type: 'test', fieldPath: 'test' },
              path: 'path3',
              strategy: 'test',
            },
            path: 'path3',
          },
        ],
        stats: {
          total: 2,
          byType: { add: 1, modify: 1, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(finalDiff, [
        aiDiff,
      ])
      expect(coverage).toBe(50) // 只有一个匹配
    })
  })
})
