/**
 * 🎯 AI覆盖率计算真实场景测试
 *
 * 基于实际开发流程的完整测试场景：
 *
 * 📖 场景描述：
 * 一个变更alpha包含5个修改操作：A、B、C、D、E
 * - initV → AV：AI修改A（添加用户头像组件）
 * - AV → BV：人工修改B（调整布局样式）
 * - BV → CV：人工修改C（添加提交按钮）
 * - CV → DV：AI修改D（添加表单验证逻辑）
 * - DV → EV：人工修改E（优化错误提示）
 *
 * 🧮 计算逻辑：
 * 1. alphaDiff = diff(initV, EV) - 整个变更的总修改量
 * 2. ADiff = diff(initV, AV) - AI修改A的贡献
 * 3. DDiff = diff(CV, DV) - AI修改D的贡献
 * 4. 通过消耗式匹配计算 (ADiff + DDiff) 在 alphaDiff 中的占比
 */

import type { E2ESchema } from '@ad/e2e-schema'
import { CoverageCalculator } from '../core/coverage'
import { SchemaDiffEngine } from '../core/engine'

describe('🎯 AI覆盖率计算真实场景', () => {
  let engine: SchemaDiffEngine

  beforeEach(() => {
    engine = new SchemaDiffEngine()
  })

  describe('📖 完整开发流程：用户注册表单', () => {
    // ========== Schema版本定义 ==========

    // 初始版本：基础表单
    const initV: E2ESchema = {
      schemaVersion: '1.0.0',
      version: '1.0.0',
      view: {
        id: 'registerForm',
        type: 'Form',
        name: '用户注册表单',
        props: { layout: 'vertical' },
        children: [
          {
            id: 'usernameInput',
            type: 'Input',
            name: '用户名',
            props: { placeholder: '请输入用户名' },
            // 测试 effect 字段
            effect: {
              type: 'js',
              code: 'console.log("username input mounted")',
              codeTS: 'console.log("username input mounted");',
              codeES: 'console.log("username input mounted");',
            },
          },
          {
            id: 'emailInput',
            type: 'Input',
            name: '邮箱',
            props: { placeholder: '请输入邮箱', type: 'email' },
            // 测试 if 字段
            if: {
              type: 'js',
              code: 'return showEmailField',
              codeTS: 'return showEmailField as boolean',
            },
            // 测试 apis 字段
            apis: [
              {
                id: 'validateEmail',
                name: '邮箱验证',
                type: 'rpc',
                service: '/api/validate/email',
              },
            ],
          },
        ],
      },
      apis: [
        {
          id: 'register',
          name: '用户注册',
          type: 'rpc',
          service: '/api/user/register',
        },
      ],
      model: {
        code: `var UserForm = {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
}`,
      },
    }

    // AV：AI修改A - 添加用户头像上传组件
    // 手动计算变更数：initV → AV = 9个变更
    // avatarUpload: type(add), name(add), props.accept(add), props.maxSize(add), props.placeholder(add), _parent_(add) = 6个
    // usernameInput: _parent_(modify) = 1个 (内容相同的属性不产生变更)
    // emailInput: _parent_(modify) = 1个 (内容相同的属性不产生变更)
    // model.codeTS: avatar字段(add) = 1个
    const AV: E2ESchema = {
      ...initV,
      view: {
        ...initV.view,
        children: [
          {
            id: 'avatarUpload',
            type: 'Upload',
            name: '头像上传',
            props: {
              accept: 'image/*',
              maxSize: 2048,
              placeholder: '点击上传头像',
            },
          },
          ...(initV.view.children || []),
        ],
      },
      model: {
        code: `var UserForm = {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
  avatar?: string;
}`,
      },
    }
    it('📊 验证 initV → AV 变更数量 (AI修改A)', () => {
      const diff = engine.diff(initV, AV)

      console.log('\n=== initV → AV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)
      console.log('按策略分布:', diff.stats.byStrategy)

      expect(diff.stats.total).toBe(9)
      expect(diff.stats.byField.view || 0).toBe(8) // View变更
      expect(diff.stats.byField.model || 0).toBe(1) // Model变更
    })

    // BV：人工修改B - 调整表单布局
    // 手动计算变更数：AV → BV = 2个变更
    //   - props.layout: 'vertical' → 'horizontal' (modify) = 1个
    //   - props.style: undefined → 'margin: 10px' (add) = 1个
    const BV: E2ESchema = {
      ...AV,
      view: {
        ...AV.view,
        props: {
          layout: 'horizontal',
          style: 'margin: 10px',
        },
      },
    }
    it('📊 验证 AV → BV 变更数量 (人工修改B)', () => {
      // 预期：2个变更 (2个View)
      const diff = engine.diff(AV, BV)

      console.log('\n=== AV → BV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)

      expect(diff.stats.total).toBe(2)
      expect(diff.stats.byField.view || 0).toBe(2) // View变更
    })

    // CV：人工修改C - 添加提交按钮
    // 修复后程序计算变更数：BV → CV = 10个变更
      // submitButton组件: type(add), name(add), props.type(add), props.text(add), props.htmlType(add), _parent_(add) = 6个变更

    const CV: E2ESchema = {
      ...BV,
      view: {
        ...BV.view,
        children: [
          ...(BV.view.children || []),
          {
            id: 'submitButton',
            type: 'Button',
            name: '注册按钮',
            props: {
              type: 'primary',
              text: '立即注册',
              htmlType: 'submit',
            },
          },
        ],
      },
    }
    it('📊 验证 BV → CV 变更数量 (人工修改C)', () => {
      // 预期：根据注释应该计算出具体数量
      const diff = engine.diff(BV, CV)

      console.log('\n=== BV → CV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)
      console.log('按策略分布:', diff.stats.byStrategy)

      expect(diff.stats.total).toBe(6)
      expect(diff.stats.byField.view || 0).toBe(6) // View变更
    })

    // DV：AI修改D - 添加表单验证逻辑
    // 手动计算变更数：CV → DV = 15个变更
    // Model变更(11个)：
    //   - code: 从"var UserForm = {};" → "var UserForm = {}; var validateForm = function() {};" = 1个整体变更
    //   - codeTS按行计算(CodeLineDiffStrategy忽略空行)：
    //     * 原4行内容相同，不产生change
    //     * 第5行空行被忽略
    //     * 第6-18行: 注释2行 + 函数定义11行 - 空行3行 = 10个新增行
    // APIs变更(4个)：
    //   - validateUser.id: 'validateUser' (add) = 1个
    //   - validateUser.name: '验证用户信息' (add) = 1个
    //   - validateUser.type: 'rpc' (add) = 1个
    //   - validateUser.service: '/api/user/validate' (add) = 1个
    const DV: E2ESchema = {
      ...CV,
      model: {
        code: `var UserForm = {}; var validateForm = function() {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
  avatar?: string;
}

// AI生成的验证逻辑
const validateForm = (form: UserForm): string[] => {
  const errors: string[] = [];

  if (!form.username || form.username.length < 3) {
    errors.push('用户名至少3个字符');
  }

  if (!form.email || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(form.email)) {
    errors.push('请输入有效的邮箱地址');
  }

  return errors;
};`,
      },
      apis: [
        ...(CV.apis || []),
        {
          id: 'validateUser',
          name: '验证用户信息',
          type: 'rpc',
          service: '/api/user/validate',
        },
      ],
    }
    it('📊 验证 CV → DV 变更数量 (AI修改D)', () => {
      // 预期：15个变更 (11个Model + 4个APIs)
      const diff = engine.diff(CV, DV)

      console.log('\n=== CV → DV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)
      console.log('按策略分布:', diff.stats.byStrategy)

      expect(diff.stats.total).toBe(15)
      expect(diff.stats.byField.model || 0).toBe(11) // Model变更
      expect(diff.stats.byField.apis || 0).toBe(4) // APIs变更
    })

    // EV：人工修改E - 优化错误提示样式
    // 修复后程序计算变更数：DV → EV = 6个变更
    // View 变更(6个)：
    //   - errorMessage 组件: type(add), name(add), props.type(add), props.showIcon(add), props.closable(add), _parent_(add) = 6个
    // 注：修复后 type 和 name 字段被正确计算为独立变更单位
    const EV: E2ESchema = {
      ...DV,
      view: {
        ...DV.view,
        children: [
          ...(DV.view.children || []),
          {
            id: 'errorMessage',
            type: 'Alert',
            name: '错误提示',
            props: {
              type: 'error',
              showIcon: true,
              closable: true,
            },
          },
        ],
      },
    }
    it('📊 验证 DV → EV 变更数量 (人工修改E)', () => {
      // 预期：根据注释应该计算出具体数量
      const diff = engine.diff(DV, EV)

      console.log('\n=== DV → EV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)
      console.log('按策略分布:', diff.stats.byStrategy)

      // errorMessage组件: type(add), name(add), props.type(add), props.showIcon(add), props.closable(add), _parent_(add) = 6个变更
      expect(diff.stats.total).toBe(6)
      expect(diff.stats.byField.view || 0).toBe(6) // View变更
    })

    it('📊 验证总变更数量一致性', () => {
      // 验证各阶段变更累加是否等于直接计算的总变更
      const diff1 = engine.diff(initV, AV) // AI修改A: 15
      const diff2 = engine.diff(AV, BV) // 人工修改B: 2
      const diff3 = engine.diff(BV, CV) // 人工修改C: 6
      const diff4 = engine.diff(CV, DV) // AI修改D: 15
      const diff5 = engine.diff(DV, EV) // 人工修改E: 6

      const totalDiff = engine.diff(initV, EV) // 直接计算总变更

      const stageSum =
        diff1.stats.total +
        diff2.stats.total +
        diff3.stats.total +
        diff4.stats.total +
        diff5.stats.total

      console.log('\n=== 变更数量一致性验证 ===')
      console.log('各阶段累加:', stageSum)
      console.log('直接计算:', totalDiff.stats.total)
      console.log('差异:', stageSum - totalDiff.stats.total)

      // 注意：阶段累加可能与直接计算不同，因为可能存在重复或抵消的变更
      // 但我们可以验证直接计算的结果是否与预期一致
      // 修正后的总变更数：9 + 2 + 6 + 15 + 6 = 38个
      expect(totalDiff.stats.total).toBe(38)

      // 验证AI贡献
      const aiContribution = diff1.stats.total + diff4.stats.total // A + D
      expect(aiContribution).toBe(24) // 9 + 15
    })

    it('🧮 应该正确计算AI覆盖率', () => {
      // 修复后的手动计算总结：
      // =====================================
      // 修复内容：
      //   - 修复了ViewDiffStrategy中type和name字段的处理
      //   - 新增PrimitiveValueDiffStrategy处理原始值
      //   - 修复了NestedFieldStrategy对原始值的处理逻辑
      //
      // 修复后的程序计算结果：
      //   - 总变更数：44个
      //   - AI修改A贡献：15个
      //   - AI修改D贡献：15个
      //   - AI总贡献：30个
      //   - 覆盖率：30/44 = 68.18%
      //
      // 关键计算规则：
      // 1. ViewDiffStrategy会生成父子关系(_parent_)的ChangeUnit
      // 2. ConfigDiffStrategy把props的每个属性作为独立unit
      // 3. PrimitiveValueDiffStrategy把type和name作为独立的变更单位
      // 4. CodeLineDiffStrategy按行比较，忽略空行，相同内容不产生change
      // 5. 消耗式匹配：相同ID的unit按值匹配，完全相同不产生change

      // Step 1: 计算整个变更的总修改量
      const alphaDiff = engine.diff(initV, EV)
      console.log('📊 整个变更总修改量:', alphaDiff.stats.total)

      // Step 2: 计算AI修改A的贡献（预期7个变更）
      const ADiff = engine.diff(initV, AV)
      console.log('🤖 AI修改A贡献:', ADiff.stats.total)

      // Step 3: 计算AI修改D的贡献（预期19个变更）
      const DDiff = engine.diff(CV, DV)
      console.log('🤖 AI修改D贡献:', DDiff.stats.total)

      // Step 4: 计算AI覆盖率
      const coverage = CoverageCalculator.calculateAICoverage(alphaDiff, [
        ADiff,
        DDiff,
      ])
      console.log('📈 AI覆盖率:', coverage.toFixed(2) + '%')

      // 验证结果合理性
      expect(alphaDiff.stats.total).toBeGreaterThan(0)
      expect(ADiff.stats.total).toBeGreaterThan(0)
      expect(DDiff.stats.total).toBeGreaterThan(0)
      expect(coverage).toBeGreaterThan(0)
      expect(coverage).toBeLessThanOrEqual(100)

      // AI应该有实质性贡献
      // 修正后的覆盖率：24/38 = 63.16%
      // 这个值反映了正确的变更统计逻辑（只计算真实的内容变更）
      expect(coverage).toBeCloseTo(63.16, 1)
    })

    it('🔍 应该提供详细的覆盖率分析', () => {
      const alphaDiff = engine.diff(initV, EV)
      const ADiff = engine.diff(initV, AV)
      const DDiff = engine.diff(CV, DV)

      const detailed = CoverageCalculator.calculateDetailedCoverage(alphaDiff, [
        ADiff,
        DDiff,
      ])

      // 验证详细统计信息
      expect(detailed.totalCoverage).toBeGreaterThan(0)
      expect(detailed.preservedChanges.length).toBeGreaterThan(0)
      expect(detailed.byType).toBeDefined()
      expect(detailed.byField).toBeDefined()
      expect(detailed.byStrategy).toBeDefined()

      console.log('📊 详细覆盖率分析:')
      console.log('  总覆盖率:', detailed.totalCoverage.toFixed(2) + '%')
      console.log('  保留的AI变更:', detailed.preservedChanges.length)
      console.log('  未保留的变更:', detailed.missedChanges.length)
      console.log('  按类型分布:', detailed.byType)
      console.log('  按字段分布:', detailed.byField)
      console.log('  按策略分布:', detailed.byStrategy)
    })

    it('🎯 应该正确识别不同策略的贡献', () => {
      const alphaDiff = engine.diff(initV, EV)

      // 验证包含了不同类型的变更
      const strategies = new Set(alphaDiff.changes.map((c) => c.unit.strategy))

      console.log('🔧 涉及的策略类型:', Array.from(strategies))

      // 验证至少包含一些核心策略
      expect(strategies.size).toBeGreaterThan(0)

      // 检查是否包含视图变更
      const hasViewStrategy = Array.from(strategies).some((s) =>
        s.includes('VIEW'),
      )
      expect(hasViewStrategy).toBe(true)
    })
  })

  describe('📖 边界情况测试', () => {
    it('🚫 空diff应该返回0%覆盖率', () => {
      const emptyDiff = {
        changes: [],
        stats: {
          total: 0,
          byType: { add: 0, modify: 0, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(emptyDiff, [])
      expect(coverage).toBe(0)
    })

    it('💯 完全匹配应该返回100%覆盖率', () => {
      const diff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'test1',
              value: 'value1',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
        ],
        stats: {
          total: 1,
          byType: { add: 1, modify: 0, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(diff, [diff])
      expect(coverage).toBe(100)
    })

    it('🔄 重复ID的消耗式匹配', () => {
      const finalDiff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'same-id',
              value: 'final-value',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
          {
            type: 'modify' as const,
            unit: {
              id: 'same-id',
              value: 'another-value',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path2',
              strategy: 'test',
            },
            path: 'path2',
          },
        ],
        stats: {
          total: 2,
          byType: { add: 1, modify: 1, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const aiDiff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'same-id',
              value: 'final-value',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
          {
            type: 'modify' as const,
            unit: {
              id: 'same-id',
              value: 'different-value',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path3',
              strategy: 'test',
            },
            path: 'path3',
          },
        ],
        stats: {
          total: 2,
          byType: { add: 1, modify: 1, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(finalDiff, [
        aiDiff,
      ])

      // 只有一个匹配（相同ID且相同值），所以覆盖率是50%
      expect(coverage).toBe(50)
    })
  })
})
