/**
 * 配置系统类型定义
 */

import type { FieldDiffStrategy } from '../core/types'

/**
 * 策略配置项
 */
export interface StrategyConfig {
  /** 字段路径 */
  path: string
  /** 策略名称或策略实例 */
  strategy: string | FieldDiffStrategy<unknown>
  /** 策略选项 */
  options?: Record<string, unknown>
}

/**
 * Diff引擎配置
 */
export interface DiffEngineConfig {
  /** 策略配置列表 */
  strategies: StrategyConfig[]
  /** 自定义策略映射 */
  customStrategies?: Map<
    string,
    new (
      path: string,
      options?: Record<string, unknown>,
    ) => FieldDiffStrategy<unknown>
  >
}

/**
 * 预设配置类型
 */
export type PresetConfig = 'default' | 'minimal' | 'comprehensive'
