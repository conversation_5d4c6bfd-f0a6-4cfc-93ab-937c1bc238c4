/**
 * 默认配置
 * 定义各种预设配置
 */

import type { DiffEngineConfig, PresetConfig } from './types'

/**
 * 默认策略配置
 */
export const DEFAULT_CONFIG: DiffEngineConfig = {
  strategies: [
    // 视图策略
    { path: 'view', strategy: 'ViewDiffStrategy' },

    // 代码字段策略
    { path: 'model.codeTS', strategy: 'CodeLineDiffStrategy' },
    { path: 'model.codeES', strategy: 'CodeLineDiffStrategy' },
    { path: 'backModel.codeTS', strategy: 'CodeLineDiffStrategy' },
    { path: 'backModel.codeES', strategy: 'CodeLineDiffStrategy' },
    { path: 'globalFrontModel.codeTS', strategy: 'CodeLineDiffStrategy' },
    { path: 'globalFrontModel.codeES', strategy: 'CodeLineDiffStrategy' },
    { path: 'globalBackModel.codeTS', strategy: 'CodeLineDiffStrategy' },
    { path: 'globalBackModel.codeES', strategy: 'CodeLineDiffStrategy' },

    // API策略
    { path: 'apis', strategy: 'APIDiffStrategy' },

    // 配置策略
    {
      path: 'data',
      strategy: 'ConfigDiffStrategy',
      options: { configId: 'data' },
    },
    {
      path: 'skeleton',
      strategy: 'ConfigDiffStrategy',
      options: { configId: 'skeleton' },
    },
    {
      path: 'px2vw',
      strategy: 'ConfigDiffStrategy',
      options: { configId: 'px2vw' },
    },
    {
      path: 'px2rem',
      strategy: 'ConfigDiffStrategy',
      options: { configId: 'px2rem' },
    },

    // 其他策略
    { path: 'linkage', strategy: 'LinkageDiffStrategy' },
    { path: 'tracks', strategy: 'TrackDiffStrategy' },
    { path: 'iife', strategy: 'IifeDiffStrategy' },
  ],
}

/**
 * 最小配置 - 只包含核心策略
 */
export const MINIMAL_CONFIG: DiffEngineConfig = {
  strategies: [
    { path: 'view', strategy: 'ViewDiffStrategy' },
    { path: 'apis', strategy: 'APIDiffStrategy' },
  ],
}

/**
 * 全面配置 - 包含所有可能的策略
 */
export const COMPREHENSIVE_CONFIG: DiffEngineConfig = {
  strategies: [
    ...DEFAULT_CONFIG.strategies,
    // 可以在这里添加更多策略
  ],
}

/**
 * 获取预设配置
 */
export function getPresetConfig(preset: PresetConfig): DiffEngineConfig {
  switch (preset) {
    case 'minimal':
      return MINIMAL_CONFIG
    case 'comprehensive':
      return COMPREHENSIVE_CONFIG
    case 'default':
    default:
      return DEFAULT_CONFIG
  }
}

/**
 * 合并配置
 * 将用户配置与默认配置合并
 */
export function mergeConfig(
  userConfig: Partial<DiffEngineConfig>,
  baseConfig: DiffEngineConfig = DEFAULT_CONFIG,
): DiffEngineConfig {
  return {
    strategies: userConfig.strategies || baseConfig.strategies,
    customStrategies: new Map([
      ...(baseConfig.customStrategies || []),
      ...(userConfig.customStrategies || []),
    ]),
  }
}
